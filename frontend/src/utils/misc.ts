import dateFormat from "dateformat";
import dayjs from "dayjs";
import arraySupport from "dayjs/plugin/arraySupport";
import utc from "dayjs/plugin/utc";
import { LoadOptions } from "devextreme/data";
import { ComponentPublicInstance, DefineComponent } from "vue";
import {
  ActionProgressPhaseName,
  ComputerApiRoutes,
  DatabaseType,
  DesiredSoftwareState,
  ExpirationTime,
  HealthStatus,
  IApplicationPreferences,
  IGetGlobalMaintenanceTaskResponse,
  IGetGlobalSoftwareResponse,
  IGetGlobalSoftwareVersionResponse,
  IGetLocalMaintenanceTaskResponse,
  IGetLocalSoftwareResponse,
  IGetLocalSoftwareVersionResponse,
  IGetProviderLinkResponse,
  IntegrationTag,
  IOauth2AccessToken,
  ITenantPreferences,
  IUserPreferences,
  MaintenanceActionReason,
  MaintenanceActionResult,
  MaintenanceActionStatus,
  MaintenanceActionType,
  MaintenanceTaskCategory,
  MaintenanceTaskMode,
  MaintenanceTaskParameterType,
  MaintenanceType,
  SessionStatus,
  SoftwareProviderType,
  SoftwareType,
  TargetCategory,
  TargetType,
  TimelineEventType,
} from "@/api/backend/generated/contracts";
import EnumTextHelpers from "@/helpers/enums/EnumTextHelpers";
import { GetConstants } from "./constants";

dayjs.extend(utc);
dayjs.extend(arraySupport);

type DayjsInput = number | dayjs.Dayjs | Date | null | undefined | string;

// hack until vue supports this natively: https://github.com/vuejs/language-tools/issues/3206
export type ComponentInstance<T> = T extends new (...args: any[]) => infer R
  ? R
  : T extends (...args: any[]) => infer R
    // eslint-disable-next-line unused-imports/no-unused-vars
    ? R extends { __ctx?: infer K }
      ? Exclude<K, void> extends { expose: (...args: infer K) => void }
        ? K[0] & InstanceType<DefineComponent>
        : any
      : any
    : any;

export const NotCurrentAuthenticatedMessage = "Not currently authenticated";

export const emailRegex = /^[a-z0-9]+(?:[._-][a-z0-9]+)*@[a-z0-9]+(?:[._-][a-z0-9]+)*\.[a-z]{2,}$/i;

export const USER_EXPIRATION_OPTIONS = [
  { value: ExpirationTime.OneHour, text: "One Hour" },
  { value: ExpirationTime.OneDay, text: "One Day" },
  { value: ExpirationTime.ThreeDays, text: "Three Days" },
  { value: ExpirationTime.Indefinite, text: "Never" },
];

export const DETECTION_PHASES = [
  ActionProgressPhaseName.DetectInstalledAgentVersion,
  ActionProgressPhaseName.DetectInstalledVersion,
  ActionProgressPhaseName.DetermineDesiredVersion,
  ActionProgressPhaseName.TestSoftware,
  ActionProgressPhaseName.CheckForSoftwareLicense,
  ActionProgressPhaseName.AddConfigTask,
  ActionProgressPhaseName.TestTask,
  ActionProgressPhaseName.TestTaskBeforeExecution,
  ActionProgressPhaseName.MonitorTask,
  ActionProgressPhaseName.AuditTask,
];

export const EXECUTION_PHASES = [
  ActionProgressPhaseName.UpdateAgentVersion,
  ActionProgressPhaseName.FetchDesiredVersion,
  ActionProgressPhaseName.EnforceTask,
  ActionProgressPhaseName.ApplyWindowsPatch,
  ActionProgressPhaseName.DownloadConfigTaskFiles,
  ActionProgressPhaseName.FetchDependentVersion,
  ActionProgressPhaseName.InstallDependentVersion,
  ActionProgressPhaseName.InstallSoftware,
  ActionProgressPhaseName.RunUpgradeSoftwareScript,
  ActionProgressPhaseName.UninstallSoftware,
  ActionProgressPhaseName.RepairSoftware,
  ActionProgressPhaseName.DownloadInstaller,
  ActionProgressPhaseName.PostUninstallSoftware,
  ActionProgressPhaseName.PostInstallSoftware,
  ActionProgressPhaseName.TestSoftwareAfterExecution,
];

export const VERIFY_PHASES = [
  ActionProgressPhaseName.VerifyAgentVersionUpdated,
  ActionProgressPhaseName.VerifyTask,
  ActionProgressPhaseName.VerifySoftwareIsInDesiredState,
];

export type SelectableSoftwareTypes
  = | SoftwareType.GlobalSoftware
    | SoftwareType.LocalSoftware
    | SoftwareType.Chocolatey
    | SoftwareType.Ninite;

export type TaskMaintenanceTypes
  = | MaintenanceType.LocalMaintenanceTask
    | MaintenanceType.GlobalMaintenanceTask;

export type SoftwareMaintenanceTypes
  = | MaintenanceType.LocalSoftware
    | MaintenanceType.GlobalSoftware
    | MaintenanceType.NiniteSoftware
    | MaintenanceType.ChocolateySoftware;

export const DATABASE_TYPE_OPTIONS = [
  {
    text: "Local",
    value: DatabaseType.Local,
  },
  {
    text: "Global",
    value: DatabaseType.Global,
  },
];

export const COMPLETE_SESSION_STATUSES = [
  SessionStatus.Passed,
  SessionStatus.Failed,
  SessionStatus.Cancelled,
  SessionStatus.Postponed,
  SessionStatus.PartialPassed,
];

export const PASSED_SESSION_STATUSES = [SessionStatus.Passed, SessionStatus.PartialPassed];

export function isDesiredSoftwareStateForInstallation(desiredSoftwareState: DesiredSoftwareState) {
  return [
    DesiredSoftwareState.LatestVersion,
    DesiredSoftwareState.AnyVersion,
    DesiredSoftwareState.NewerOrEqualVersion,
    DesiredSoftwareState.OlderOrEqualVersion,
    DesiredSoftwareState.ThisVersion,
  ].includes(desiredSoftwareState);
}

export function wildcardMatch(str: string, rule: string) {
  const escapeRegex = (str: string) => str.replace(/([.*+?^=!:${}()|[\]/\\])/g, "\\$1");
  return new RegExp(`^${rule.split("*").map(escapeRegex).join(".*")}$`).test(str);
}

export function isVersionRequiredForDesiredState(desiredState: DesiredSoftwareState) {
  return [
    DesiredSoftwareState.NewerOrEqualVersion,
    DesiredSoftwareState.OlderOrEqualVersion,
    DesiredSoftwareState.ThisVersion,
  ].includes(desiredState);
}

export interface ICheckSoftwareDeployabilityArgs {
  softwareType: SoftwareType;
  softwareIdentifier: string;
  desiredState: DesiredSoftwareState | null;
  semanticVersion: string | null;
}

export function checkSoftwareDeployability(args: ICheckSoftwareDeployabilityArgs) {
  if (args.softwareType == null || !args.softwareIdentifier)
    return false;
  const versionRequired
    = args.desiredState != null && isVersionRequiredForDesiredState(args.desiredState);
  if (versionRequired && !args.semanticVersion)
    return false;
  return true;
}

export function makeUid() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

const guidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

export function isGuid(guid: string) {
  return guidPattern.test(guid);
}

export function isUrl(val: string) {
  try {
    // eslint-disable-next-line no-new
    new URL(val);
    return true;
  }
  catch {
    return false;
  }
}

export function download(url: string, fileName: string | undefined, newTab = false) {
  const link = document.createElement("a");
  link.href = url;
  link.download = fileName ?? "undefined";
  if (newTab)
    link.target = "_blank";
  link.click();
}

export function escapeRegExp(string: string) {
  return string.replace(/[.*+\-?^${}()|[\]\\]/g, "\\$&"); // $& means the whole matched string
}

export function softwareTypeFromMaintenanceType(
  type: MaintenanceType | undefined,
): SoftwareType | undefined {
  if (type === MaintenanceType.GlobalSoftware)
    return SoftwareType.GlobalSoftware;
  else if (type === MaintenanceType.LocalSoftware)
    return SoftwareType.LocalSoftware;
  else if (type === MaintenanceType.NiniteSoftware)
    return SoftwareType.Ninite;
  else if (type === MaintenanceType.ChocolateySoftware)
    return SoftwareType.Chocolatey;
}

export function databaseTypeFromMaintenanceType(type: MaintenanceType | undefined) {
  if (type === MaintenanceType.LocalMaintenanceTask)
    return DatabaseType.Local;
  else if (type === MaintenanceType.GlobalMaintenanceTask)
    return DatabaseType.Global;
  else if (type === MaintenanceType.GlobalSoftware)
    return DatabaseType.Global;
  else if (type === MaintenanceType.LocalSoftware)
    return DatabaseType.Local;
}

export function maintenanceTypeFromSoftwareType(
  type: SoftwareType | undefined,
): SoftwareMaintenanceTypes | undefined {
  if (type === SoftwareType.LocalSoftware)
    return MaintenanceType.LocalSoftware;
  if (type === SoftwareType.GlobalSoftware)
    return MaintenanceType.GlobalSoftware;
}

export function maintenanceTypeFromMaintenanceTaskType(
  type: DatabaseType | undefined,
): TaskMaintenanceTypes | undefined {
  if (type === DatabaseType.Local)
    return MaintenanceType.LocalMaintenanceTask;
  if (type === DatabaseType.Global)
    return MaintenanceType.GlobalMaintenanceTask;
}

export function getHumanReadableDate(date: DayjsInput) {
  if (!date || (typeof date === "string" && date.includes("0001-01-01")))
    return "Unavailable";
  const m = dayjs(date).utc().local();
  const diff = dayjs().diff(m, "seconds");
  // if date is in the next 5 seconds, don't let it say "in a few seconds"
  if (diff < 1 && diff > -5)
    return "a few seconds ago";
  return dayjs(date).utc().local().fromNow();
}

export function getlastUpdatedAt(date: DayjsInput) {
  if (!date || (typeof date === "string" && date.includes("0001-01-01")))
    return "Unavailable";
  return `Updated ${getHumanReadableDate(dayjs.utc(date).local())}`;
}

export function isDateInPast(date: DayjsInput) {
  if (!date || (typeof date === "string" && date.includes("0001-01-01")))
    return false;
  return dayjs(date).isBefore(dayjs());
}

export function isDateInFuture(date: DayjsInput) {
  if (!date || (typeof date === "string" && date.includes("0001-01-01")))
    return false;
  return dayjs(date).isAfter(dayjs());
}

export function isLessThanXMinutesAgo(date: DayjsInput, numMinutes: number) {
  if (!date || (typeof date === "string" && date.includes("0001-01-01")))
    return false;
  return !dayjs(date).isBefore(dayjs().subtract(numMinutes, "minute"));
}

/**
 * @example dateFormat('2017-12-05','mmm d, yyyy h:MM:sstt Z' ) -> "Dec 5, 2017 12:00:00am GMT+0000"
 * @param {Date} datetime
 * @param {string} format
 * @returns {string} date formatted as a string
 */
export function formatDate(datetime: string | number | Date | undefined, format: string = "mmm d, yyyy h:MM:sstt Z", utc = false, defaultText?: string): string {
  if (!datetime || (typeof datetime === "string" && datetime.includes("0001-01-01")))
    return defaultText ?? "n/a";

  if (typeof datetime === "string" && datetime.match(/\d+-\d{2,} /))
    throw new Error("Invalid date");

  return dateFormat(datetime, format, utc);
}

// helper function to parse TimeSpan string format "HH:mm:ss.fffffff" into milliseconds
function parseDurationToMs(duration: string): number {
  if (!duration) {
    return 0;
  }

  const parts = duration.split(":");
  if (parts.length !== 3) {
    return 0;
  }

  const [hours, minutes, secondsWithFraction] = parts;
  const [seconds, fraction] = secondsWithFraction.split(".");

  // take only the first 3 digits for milliseconds
  return (Number.parseInt(hours) * 3600000)
    + (Number.parseInt(minutes) * 60000)
    + (Number.parseInt(seconds) * 1000)
    + (fraction ? Math.round(Number.parseFloat(`0.${fraction}`) * 1000) : 0);
}

export function getTimeRunningForSession(
  start: string | Date,
  end: string | Date,
  sessionStatus: SessionStatus,
  duration?: string,
) {
  if (!duration) {
    if (sessionStatus === SessionStatus.Running) {
      const startMs = new Date(start).valueOf();
      const elapsedMs = Date.now() - startMs;
      const seconds = Math.floor(elapsedMs / 1000);
      return toHHMMSS(seconds);
    }
    return toHHMMSS(0);
  }

  const elapsedMs = parseDurationToMs(duration);
  if (sessionStatus === SessionStatus.Running) {
    const endMs = new Date(end).valueOf();
    const currentMs = Date.now() - endMs;
    const seconds = Math.floor((elapsedMs + currentMs) / 1000);
    return toHHMMSS(seconds);
  }

  const seconds = Math.floor(elapsedMs / 1000);
  return toHHMMSS(seconds);
}

export function getElapsedTime(start: string | Date, end: string | Date | null = null) {
  const startMs = new Date(start).valueOf();
  const endMs = end ? new Date(end).valueOf() : Date.now();
  const diffMs = endMs - startMs;
  const seconds = Math.floor(diffMs / 1000);
  return toHHMMSS(seconds);
}

export function getTimeRunningForAction(
  start: string,
  end: string,
  actionStatus: MaintenanceActionStatus,
) {
  let date = null;
  if (
    actionStatus === MaintenanceActionStatus.NotStarted
    || end == null
    || end.includes("0001-01-01")
    || start == null
    || start.includes("0001-01-01")
  ) {
    return "00:00:00";
  }
  else if (actionStatus === MaintenanceActionStatus.Complete) {
    const startMs = new Date(start).valueOf();
    const endMs = new Date(end).valueOf();
    date = endMs - startMs;
  }
  else {
    const startMs = new Date(start).valueOf();
    date = Date.now() - startMs;
  }
  let seconds = Math.floor(date / 1000);
  if (seconds < 0)
    seconds = 0;
  return toHHMMSS(seconds);
}

export function copyToClipboard(text = "", el: ComponentPublicInstance | null = null) {
  // https://stackoverflow.com/questions/33855641/copy-output-of-a-javascript-variable-to-the-clipboard
  const dummy = document.createElement("textarea");
  if (el)
    (el.$el.body as HTMLElement).appendChild(dummy);
  else document.body.appendChild(dummy);

  dummy.value = text;
  dummy.select();
  document.execCommand("copy");
  document.body.removeChild(dummy);
}

export function isWithin24Hours(date: DayjsInput) {
  return dayjs().diff(dayjs(date), "hours") < 24;
}

export function formatBytes(bytes: number, decimals = 2) {
  if (bytes === 0)
    return "0 Bytes";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${Number.parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
}

export function validateIPaddress(ipaddress: string) {
  if (
    // eslint-disable-next-line regexp/no-unused-capturing-group
    /^(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/.test(
      ipaddress,
    )
  )
    return true;
  else return false;
}

export function toHHMMSS(timeInSeconds?: number | null) {
  if (timeInSeconds == null || timeInSeconds < 0)
    return "00:00:00";
  const sec_num = Number(timeInSeconds); // don't forget the second param
  const hours = Math.floor(sec_num / 3600);
  const minutes = Math.floor((sec_num - hours * 3600) / 60);
  const seconds = Math.round(sec_num - hours * 3600 - minutes * 60);

  let hoursText = hours.toString();
  let minutesText = minutes.toString();
  let secondsText = seconds.toString();

  if (hours < 10)
    hoursText = `0${hours}`;

  if (minutes < 10)
    minutesText = `0${minutes}`;

  if (seconds < 10)
    secondsText = `0${seconds}`;

  return `${hoursText}:${minutesText}:${secondsText}`;
}

export function groupByToMap<T, Q>(
  array: T[],
  predicate: (value: T, index: number, array: T[]) => Q,
) {
  return array.reduce((map, value, index, array) => {
    const key = predicate(value, index, array);
    const _ = map.get(key)?.push(value) ?? map.set(key, [value]);
    return map;
  }, new Map<Q, T[]>());
}

export function getMajorFromSemVer(ver: string) {
  if (!ver)
    return undefined;
  const split = ver.split(".");
  return split.length > 0 ? split[0] : undefined;
}

export const MaintenanceActionTimelineEvents = [
  TimelineEventType.MaintenanceActionStarted,
  TimelineEventType.MaintenanceActionCompleted,
];

export const AgentConnectionTimelineEvents = [
  TimelineEventType.AgentConnected,
  TimelineEventType.AgentDisconnected,
  TimelineEventType.EphemeralAgentSessionAlreadyConnected,
  TimelineEventType.EphemeralAgentSessionConnected,
  TimelineEventType.EphemeralAgentSessionDisconnected,
];

export const AgentTimelineEvents = [
  TimelineEventType.AgentConnected,
  TimelineEventType.AgentDisconnected,
  TimelineEventType.AgentCreated,
  TimelineEventType.AgentDeleted,
  TimelineEventType.RemoteSessionStarted,
  TimelineEventType.RemoteSessionEnded,
  TimelineEventType.AgentUpdated,
  TimelineEventType.EphemeralAgentSessionAlreadyConnected,
  TimelineEventType.EphemeralAgentSessionConnected,
  TimelineEventType.EphemeralAgentSessionDisconnected,
  TimelineEventType.EphemeralAgentSessionConnectionLost,
];

export const ComputerOnlineTimelineEvents = [
  TimelineEventType.ComputerOnline,
  TimelineEventType.ComputerOffline,
];

export const ALL_SOFTWARE_TYPES = [
  MaintenanceType.LocalSoftware,
  MaintenanceType.GlobalSoftware,
  MaintenanceType.NiniteSoftware,
  MaintenanceType.ChocolateySoftware,
];

export const ALL_MAINTENANCE_TASK_TYPES = [
  MaintenanceType.LocalMaintenanceTask,
  MaintenanceType.GlobalMaintenanceTask,
];

export function isMaintenanceActionTimelineEvent(eventType: TimelineEventType) {
  return MaintenanceActionTimelineEvents.includes(eventType);
}

export function isAgentConnectionTimelineEvent(eventType: TimelineEventType) {
  return AgentConnectionTimelineEvents.includes(eventType);
}

export function isAgentTimelineEvent(eventType: TimelineEventType) {
  return AgentTimelineEvents.includes(eventType);
}

export function isComputerOnlineTimelineEvent(eventType: TimelineEventType) {
  return ComputerOnlineTimelineEvents.includes(eventType);
}

export function isSoftware(maintenanceType: MaintenanceType) {
  return ALL_SOFTWARE_TYPES.includes(maintenanceType);
}

export function isTask(maintenanceType: MaintenanceType) {
  return ALL_MAINTENANCE_TASK_TYPES.includes(maintenanceType);
}

export interface IGetDesiredStateText {
  desiredSoftwareState?: DesiredSoftwareState;
  desiredVersionString?: string;
  maintenanceType: MaintenanceType;
  maintenanceTaskMode?: MaintenanceTaskMode;
}

export function getDesiredStateText(action: IGetDesiredStateText) {
  if (
    ALL_SOFTWARE_TYPES.includes(action.maintenanceType)
    || action.maintenanceType === MaintenanceType.AgentUpdate
  ) {
    if (action.desiredSoftwareState === DesiredSoftwareState.NotPresent)
      return "Should not be present";

    if (action.desiredSoftwareState === DesiredSoftwareState.LatestVersion) {
      const version = action.desiredVersionString;
      return `Should be the latest version${version ? ` ${version}` : ""}`;
    }
    if (action.desiredSoftwareState === DesiredSoftwareState.NoAction)
      return "Ignore";

    if (action.desiredSoftwareState === DesiredSoftwareState.UpdateIfFound)
      return "Update if found";

    if (action.desiredSoftwareState === DesiredSoftwareState.NewerOrEqualVersion) {
      const version = action.desiredVersionString;
      return `Should be at least version${version ? ` ${version}` : ""}`;
    }
    if (action.desiredSoftwareState === DesiredSoftwareState.OlderOrEqualVersion) {
      const version = action.desiredVersionString;
      return `Should be at most version${version ? ` ${version}` : ""}`;
    }
    if (action.desiredSoftwareState === DesiredSoftwareState.AnyVersion)
      return "Should be any version";

    if (action.desiredSoftwareState === DesiredSoftwareState.ThisVersion) {
      const version = action.desiredVersionString;
      return `Should be version${version ? ` ${version}` : ""}`;
    }
  }
  else if (
    ALL_MAINTENANCE_TASK_TYPES.includes(action.maintenanceType)
    && action.maintenanceTaskMode
  ) {
    return EnumTextHelpers.MaintenanceTaskMode.GetTextByValue(action.maintenanceTaskMode);
  }
}

export function getDesiredStateShortText(action: IGetDesiredStateText) {
  if (
    ALL_SOFTWARE_TYPES.includes(action.maintenanceType)
    || action.maintenanceType === MaintenanceType.AgentUpdate
  ) {
    if (action.desiredSoftwareState === DesiredSoftwareState.NotPresent)
      return "not present";

    if (action.desiredSoftwareState === DesiredSoftwareState.LatestVersion) {
      const version = action.desiredVersionString;
      return `latest version${version ? ` ${version}` : ""}`;
    }
    if (action.desiredSoftwareState === DesiredSoftwareState.NoAction)
      return "ignored";

    if (action.desiredSoftwareState === DesiredSoftwareState.UpdateIfFound)
      return "updated if found";

    if (action.desiredSoftwareState === DesiredSoftwareState.NewerOrEqualVersion) {
      const version = action.desiredVersionString;
      return `at least version${version ? ` ${version}` : ""}`;
    }
    if (action.desiredSoftwareState === DesiredSoftwareState.OlderOrEqualVersion) {
      const version = action.desiredVersionString;
      return `at most version${version ? ` ${version}` : ""}`;
    }
    if (action.desiredSoftwareState === DesiredSoftwareState.AnyVersion)
      return "any version";

    if (action.desiredSoftwareState === DesiredSoftwareState.ThisVersion) {
      const version = action.desiredVersionString;
      return `version${version ? ` ${version}` : ""}`;
    }
  }
  else if (
    ALL_MAINTENANCE_TASK_TYPES.includes(action.maintenanceType)
    && action.maintenanceTaskMode
  ) {
    return EnumTextHelpers.MaintenanceTaskMode.GetOptionsByValue(action.maintenanceTaskMode)
      .desiredStateText;
  }
}

export function getActionTakenText(
  type: MaintenanceActionType,
  result: MaintenanceActionResult,
  startDate: DayjsInput | null,
  endDate: DayjsInput | null,
  createdDate: DayjsInput | null,
  version?: string | undefined,
) {
  if (result === MaintenanceActionResult.Pending)
    return "Pending";
  if (result === MaintenanceActionResult.Cancelled)
    return "Cancelled";
  if (result === MaintenanceActionResult.Resolved)
    return "Resolved";

  const actionSucceeded = result === MaintenanceActionResult.Success;
  const versionIncluded = !!version;

  let typeText = "";
  switch (type) {
    case MaintenanceActionType.NoAction:
      typeText = actionSucceeded ? "No action taken" : "Error occurred";
      break;
    case MaintenanceActionType.Install:
      typeText = actionSucceeded ? "Installed" : "Failed to install";
      if (versionIncluded)
        typeText += ` ${version}`;
      break;
    case MaintenanceActionType.Update:
      typeText = actionSucceeded ? "Updated" : "Failed to update";
      if (versionIncluded)
        typeText += ` to ${version}`;
      break;
    case MaintenanceActionType.Uninstall:
      typeText = actionSucceeded ? "Uninstalled" : "Failed to uninstall";
      if (versionIncluded)
        typeText += ` ${version}`;
      break;
    case MaintenanceActionType.Download:
      typeText = actionSucceeded ? "Download" : "Failed to download";
      break;
    case MaintenanceActionType.Reinstall:
      typeText = actionSucceeded ? "Reinstall" : "Failed to reinstall";
      break;
    case MaintenanceActionType.Downgrade:
      typeText = actionSucceeded ? "Downgrade" : "Failed to downgrade";
      if (versionIncluded)
        typeText += ` to ${version}`;
      break;
    case MaintenanceActionType.Undetermined:
      typeText = actionSucceeded ? "Undetermined" : "Detection failed";
      break;
    case MaintenanceActionType.TaskEnforce:
      typeText = actionSucceeded ? "Enforced" : "Failed to enforce";
      break;
    case MaintenanceActionType.TaskMonitor:
      typeText = actionSucceeded ? "Monitored" : "Failed to monitor";
      break;
    case MaintenanceActionType.TaskAudit:
      typeText = actionSucceeded ? "Audited" : "Failed to audit";
      break;
    default:
      typeText = "View session";
      break;
  }

  let formattedDate: string | null = null;
  if (endDate) {
    const d = getHumanReadableDate(endDate);
    if (d !== "Unavailable")
      formattedDate = d;
  }

  if (!formattedDate && startDate) {
    const d = getHumanReadableDate(startDate);
    if (d !== "Unavailable")
      formattedDate = d;
  }

  if (!formattedDate && createdDate) {
    const d = getHumanReadableDate(createdDate);
    if (d !== "Unavailable")
      formattedDate = d;
  }

  if (formattedDate !== null && formattedDate !== "Unavailable")
    typeText += ` ${formattedDate}`;
  return typeText;
}

export function getActionToTakeText(type: MaintenanceActionType, reason: MaintenanceActionReason) {
  let typeText = "";
  switch (type) {
    case MaintenanceActionType.NoAction:
      typeText = "No action to take";
      break;
    case MaintenanceActionType.Install:
      typeText = "Install the software";
      break;
    case MaintenanceActionType.Update:
      typeText = "Update the software";
      break;
    case MaintenanceActionType.Uninstall:
      typeText = "Uninstall the software";
      break;
    case MaintenanceActionType.Download:
      typeText = "Download the software";
      break;
    case MaintenanceActionType.Reinstall:
      typeText = "Reinstall the software";
      break;
    case MaintenanceActionType.Downgrade:
      typeText = "Downgrade the software";
      break;
    case MaintenanceActionType.Undetermined:
      typeText = "Determining...";
      break;
    case MaintenanceActionType.TaskEnforce:
      typeText = "Enforce the task";
      break;
    case MaintenanceActionType.TaskMonitor:
      typeText = "Monitor the task";
      break;
    case MaintenanceActionType.TaskAudit:
      typeText = "Audit the task";
      break;
    default:
      typeText = "Not available yet";
      break;
  }

  let reasonText = "";
  switch (reason) {
    case MaintenanceActionReason.SoftwareMissing:
      reasonText = "because the software was not detected";
      break;
    case MaintenanceActionReason.UpdateAvailable:
      reasonText = "because it has an update available";
      break;
    case MaintenanceActionReason.UpToDate:
      reasonText = "because it is already up to date";
      break;
    case MaintenanceActionReason.NotAssigned:
      reasonText = "because it  is not assigned";
      break;
    case MaintenanceActionReason.TestFailed:
      reasonText = "because the test script failed";
      break;
    case MaintenanceActionReason.SoftwareDetected:
      reasonText = "because it was detected";
      break;
    case MaintenanceActionReason.OlderVersionRequired:
      reasonText = "because an older version is required";
      break;
    case MaintenanceActionReason.NoApplicableSoftwareVersion:
      reasonText = "because no applicable software version was found";
      break;
    case MaintenanceActionReason.NoAction:
      reasonText = "because it is already compliant";
      break;
    case MaintenanceActionReason.Undetermined:
      reasonText = "";
      break;
    case MaintenanceActionReason.TaskEnforce:
      reasonText = "";
      break;
    case MaintenanceActionReason.TaskAudit:
      reasonText = "";
      break;
    case MaintenanceActionReason.TaskMonitor:
      reasonText = "";
      break;
    case MaintenanceActionReason.TaskNotCompliant:
      reasonText = "because the task is not compliant";
      break;
    case MaintenanceActionReason.TaskCompliant:
      reasonText = "because the task is already compliant";
      break;
    case MaintenanceActionReason.DeviceOffline:
      reasonText = "beacuse the device has no connected agents";
      break;
    case MaintenanceActionReason.RepairTriggered:
      reasonText = "because a repair was triggered";
      break;
    default:
      break;
  }

  return `${typeText} ${reasonText}`;
}

interface iconClassesType {
  [key: string]: string;
}

const ICON_CLASSES: iconClassesType = {
  // Media
  "image": "fa-file-image",
  "audio": "fa-file-audio",
  "video": "fa-file-video",
  // Documents
  "application/pdf": "fa-file-pdf",
  "application/msword": "fa-file-word",
  "application/vnd.ms-word": "fa-file-word",
  "application/vnd.oasis.opendocument.text": "fa-file-word",
  "application/vnd.openxmlformatsfficedocument.wordprocessingml": "fa-file-word",
  "application/vnd.ms-excel": "fa-file-excel",
  "application/vnd.openxmlformatsfficedocument.spreadsheetml": "fa-file-excel",
  "application/vnd.oasis.opendocument.spreadsheet": "fa-file-excel",
  "application/vnd.ms-powerpoint": "fa-file-powerpoint",
  "application/vnd.openxmlformatsfficedocument.presentationml": "fa-file-powerpoint",
  "application/vnd.oasis.opendocument.presentation": "fa-file-powerpoint",
  "text/plain": "fa-file-text",
  "text/html": "fa-file-code",
  "application/json": "fa-file-code",
  // Archives
  "application/gzip": "fa-file-archive",
  "application/zip": "fa-file-archive",
};

export function getFontAwesomeIconFromMIME(mimeType = ""): string {
  return ICON_CLASSES[mimeType?.split("/")[0]] || ICON_CLASSES[mimeType] || "fa-file";
}

const PrimaryDomainControllerRole = 5;
const SecondaryDomainControllerRole = 4;
const PortableChassisTypes = [8, 9, 10, 11, 12, 14, 30, 31, 32];

export function isPortable(chassisTypes: string | number | string[] | number[] | null | undefined) {
  if (chassisTypes === null || chassisTypes === undefined)
    return false;

  const parsed = JSON.parse(JSON.stringify(chassisTypes));

  // handle array
  if (Array.isArray(parsed))
    return parsed.some(t => PortableChassisTypes.includes(t));

  // handle number
  return PortableChassisTypes.includes(parsed);
}

export function isDesktop(
  chassisTypes: string | number | string[] | number[] | null | undefined,
  osName: string,
) {
  return chassisTypes != null && !isPortable(chassisTypes) && !isServer(osName);
}

export function isDomainController(role: number | undefined) {
  return role == PrimaryDomainControllerRole || role == SecondaryDomainControllerRole;
}

export function isServer(osName: string) {
  return osName?.toLowerCase()?.includes("server");
}

export const fullMaintenanceDescription
  = "Indicates a session will run detection and execution for all software and tasks";

export interface ISelectableSoftwareProviderTypesForSoftware {
  softwareType: SoftwareType;
  softwareVersions?:
    | IGetGlobalSoftwareVersionResponse[]
    | IGetLocalSoftwareVersionResponse[]
    | undefined;
  chocoProviderSoftwareId?: string | undefined;
  niniteProviderSoftwareId?: string | undefined;
  useDynamicVersions: boolean;
}

export function selectableSoftwareProviderTypesForSoftware(software: ISelectableSoftwareProviderTypesForSoftware | undefined | null, ignoreNinite = false) {
  if (software == null)
    return [];
  const ret = [];

  if (
    software.softwareType === SoftwareType.GlobalSoftware
    && (software.softwareVersions?.length || software.useDynamicVersions)
  ) {
    ret.push({
      label: "Global",
      value: SoftwareProviderType.Inherent,
    });
  }
  else if (
    software.softwareType === SoftwareType.LocalSoftware
    && (software.softwareVersions?.length || software.useDynamicVersions)
  ) {
    ret.push({
      label: "Local",
      value: SoftwareProviderType.Inherent,
    });
  }

  if (software.chocoProviderSoftwareId != null) {
    ret.push({
      label: "Chocolatey",
      value: SoftwareProviderType.Chocolatey,
    });
  }

  if (software.niniteProviderSoftwareId != null && (!ignoreNinite || !ret.length)) {
    ret.push({
      label: "Ninite",
      value: SoftwareProviderType.Ninite,
    });
  }

  return ret;
}

/**
 * A helper function that will poll until the provided condition is true
 * A valuable use-case is waiting to return an already loading api call.  See software-module / loadAll.
 * @param {Function} conditionFunction
 * @returns
 */

export function until(conditionFunction: () => boolean) {
  const poll = (resolve: () => void) => {
    if (conditionFunction())
      resolve();
    else
      setTimeout(() => poll(resolve), 400);
  };
  return new Promise<void>(poll);
}

export function getDatabaseTypeText(databaseType: DatabaseType, affix: string) {
  if (databaseType === DatabaseType.Local)
    return `Local${affix ? ` ${affix}` : ""}`;
  else return `Global${affix ? ` ${affix}` : ""}`;
}

export function getMaintenanceTaskTypeText(databaseType: DatabaseType, category: MaintenanceTaskCategory, isConfigurationTask: boolean) {
  let taskText = "Cloud";
  if (isConfigurationTask)
    taskText = "Configuration";
  if (category === MaintenanceTaskCategory.Computer)
    taskText = "Maintenance";
  if (category === MaintenanceTaskCategory.Person)
    taskText = "Person";

  return getDatabaseTypeText(databaseType, `${taskText} Task`);
}

export function getDatabaseTypeFilterOptions(includeRecommended = false) {
  const options: Array<{ text: string; value: string | number | null }> = [
    {
      text: "All",
      value: null,
    },
    {
      text: "Local",
      value: DatabaseType.Local,
    },
    {
      text: "Global",
      value: DatabaseType.Global,
    },
  ];

  if (includeRecommended) {
    options.push({
      text: "Recommended",
      value: "recommended",
    });
  }

  return options;
}

export function getReleaseStage() {
  return [
    {
      text: "All",
      value: null,
    },
    {
      text: "Developer",
      value: IntegrationTag.Developer,
    },
    {
      text: "Alpha",
      value: IntegrationTag.Alpha,
    },
    {
      text: "Beta",
      value: IntegrationTag.Beta,
    },
    {
      text: "Production",
      value: IntegrationTag.Production,
    },
  ];
}

export const taskCategoryFilterOptions = [
  {
    text: "All",
    value: null,
  },
  {
    text: "Computer",
    value: MaintenanceTaskCategory.Computer,
  },
  {
    text: "People",
    value: MaintenanceTaskCategory.Person,
  },
  {
    text: "Cloud",
    value: MaintenanceTaskCategory.Tenant,
  },
  {
    text: "Configuration",
    value: "configuration",
  },
];

export function isGlobalAssignment(assignment: { databaseType: DatabaseType }) {
  if (!assignment)
    return undefined;
  return assignment.databaseType === DatabaseType.Global;
}

/**
 * Populates the given item with a softwareType, maintenanceTaskType, maintenanceName, and icon property
 * if the item has a maintenanceIdentifier and maintenanceType field
 * @param data
 * @param localTasks
 * @param localSoftware
 * @param globalTasks
 * @param globalSoftware
 */
export function populateMaintenanceItemFields(data: any, localTasks: IGetLocalMaintenanceTaskResponse[], localSoftware: IGetLocalSoftwareResponse[], globalTasks: IGetGlobalMaintenanceTaskResponse[], globalSoftware: IGetGlobalSoftwareResponse[]) {
  if (data.maintenanceIdentifier != null && data.maintenanceType != null) {
    data.softwareType = softwareTypeFromMaintenanceType(data.maintenanceType);
    data.maintenanceTaskType = databaseTypeFromMaintenanceType(data.maintenanceType);
    if (
      data.maintenanceType === MaintenanceType.ChocolateySoftware
      || data.maintenanceType === MaintenanceType.NiniteSoftware
    ) {
      data.maintenanceName = data.maintenanceIdentifier;
    }
    else if (data.maintenanceType === MaintenanceType.LocalMaintenanceTask) {
      const task = localTasks.find(a => a.id == data.maintenanceIdentifier);
      if (task) {
        data.maintenanceName = task.name;
        data.icon = task.icon;
        data.deprecated = task.supersededByTaskId != null && task.supersededByTaskType != null;
      }
    }
    else if (data.maintenanceType === MaintenanceType.GlobalMaintenanceTask) {
      const task = globalTasks.find(a => a.id == data.maintenanceIdentifier);
      if (task) {
        data.maintenanceName = task.name;
        data.icon = task.icon;
        data.deprecated = task.supersededByTaskId != null && task.supersededByTaskType != null;
      }
    }
    else {
      const software
        = data.maintenanceType === MaintenanceType.GlobalSoftware
          ? globalSoftware.find(a => a.id == data.maintenanceIdentifier)
          : localSoftware.find(a => a.id == data.maintenanceIdentifier);
      if (software) {
        data.icon = software.softwareIcon;
        data.maintenanceName = software.name;
      }
    }
  }
  return data;
}

export interface IDisable {
  disable: boolean;
}
export type ProviderLink = IGetProviderLinkResponse & IDisable;

export function isProviderUnhealthy(link: ProviderLink) {
  return link.healthStatus === HealthStatus.Unhealthy;
}

export function isProviderHealthy(link: ProviderLink) {
  return link.healthStatus === HealthStatus.Healthy;
}

export function isProviderDegraded(link: ProviderLink) {
  return link.healthStatus === HealthStatus.Degraded;
}

export function isProviderDisabled(link: ProviderLink) {
  return link.disabled;
}

/**
 *
 * @param color
 * returns either black or white depending on whichever has the best contrast against the input color
 */
export function getContrastColor(color = "#000000") {
  // Get background color hex value. '#' is optional.
  let hex = color.charAt(0) === "#" ? color.substring(1, 7) : color;

  // Convert 3-digit hex to 6-digits.
  if (hex.length === 3)
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];

  // By this point, it should be 6 characters
  if (hex.length !== 6)
    throw new Error("Invalid HEX color.");

  const r = Number.parseInt(hex.slice(0, 2), 16);
  const g = Number.parseInt(hex.slice(2, 4), 16);
  const b = Number.parseInt(hex.slice(4, 6), 16);

  // Return light or dark class based on contrast calculation
  return r * 0.299 + g * 0.587 + b * 0.114 > 186 ? "#2f353a" : "#ffffff";
}

export function isNumberType(type: MaintenanceTaskParameterType) {
  return MaintenanceTaskParameterType.Number === type;
}

export function isTextType(type: MaintenanceTaskParameterType) {
  return MaintenanceTaskParameterType.Text === type;
}

export function isBooleanType(type: MaintenanceTaskParameterType) {
  return MaintenanceTaskParameterType.Boolean === type;
}

export function isSelectType(type: MaintenanceTaskParameterType) {
  return MaintenanceTaskParameterType.Select === type;
}

export function isPasswordType(type: MaintenanceTaskParameterType) {
  return MaintenanceTaskParameterType.Password === type;
}

export function isFileType(type: MaintenanceTaskParameterType) {
  return MaintenanceTaskParameterType.File === type;
}

export function isUriType(type: MaintenanceTaskParameterType) {
  return MaintenanceTaskParameterType.Uri === type;
}

export function isKeyValuePairType(type: MaintenanceTaskParameterType) {
  return MaintenanceTaskParameterType.KeyValuePair === type;
}

export const appPreferencePropertyNames: Required<{ [T in keyof IApplicationPreferences]: T }> = {
  id: "id",
  enableOnboarding: "enableOnboarding",
  enableAzureUserSync: "enableAzureUserSync",
  defaultEmailBccList: "defaultEmailBccList",
  enableNiniteIntegration: "enableNiniteIntegration",
  enableUserAffinitySync: "enableUserAffinitySync",
  enableSessionEmails: "enableSessionEmails",
  defaultBrandingId: "defaultBrandingId",
  defaultBranding: "defaultBranding",
  defaultScriptTimeouts: "defaultScriptTimeouts",
  useImmyBotChocolateyFeed: "useImmyBotChocolateyFeed",
  overwriteExistingDeviceIfOSIsNew: "overwriteExistingDeviceIfOSIsNew",
  enableNonEssentialDeviceInventory: "enableNonEssentialDeviceInventory",
  requireConsentForExternalSessionProviders: "requireConsentForExternalSessionProviders",
  allowNonAdminsToManageAssignments: "allowNonAdminsToManageAssignments",
  allowNonAdminsAndNonMspUsersToUseTerminalsAndEditScripts: "allowNonAdminsAndNonMspUsersToUseTerminalsAndEditScripts",
  showGettingStartedWizard: "showGettingStartedWizard",
  enableHistoricalInventory: "enableHistoricalInventory",
  defaultTimeZone: "defaultTimeZone",
  immyScriptPath: "immyScriptPath",
  enableRequestAccess: "enableRequestAccess",
  enableEphemeralAgentDebugMode: "enableEphemeralAgentDebugMode",
  staleComputersLastAgentConnectionAgeDays: "staleComputersLastAgentConnectionAgeDays",
  enablePreflightScripts: "enablePreflightScripts",
  enableImmyBotRemoteControl: "enableImmyBotRemoteControl",
  enableImmyBotRemoteControlRecording: "enableImmyBotRemoteControlRecording",
  enableProviderAuditLogging: "enableProviderAuditLogging",
  providerAuditLogRetentionMonths: "providerAuditLogRetentionMonths",
  mspNonAdminsRequireChangeRequestsForCrossTenantDeployments: "mspNonAdminsRequireChangeRequestsForCrossTenantDeployments",
  enableMaintenanceActionActivities: "enableMaintenanceActionActivities",
  enableAutomaticImmyBotReleaseUpdates: "enableAutomaticImmyBotReleaseUpdates",
  automaticImmyBotReleaseUpdateHour: "automaticImmyBotReleaseUpdateHour",
  daysToWaitBeforeAutomaticImmyBotUpdate: "daysToWaitBeforeAutomaticImmyBotUpdate",
  hideChocolateyPackages: "hideChocolateyPackages",
  runScheduledInventoryAsMaintenanceSessions: "runScheduledInventoryAsMaintenanceSessions",
  enableBetaDynamicIntegrationMigrations: "enableBetaDynamicIntegrationMigrations",
  enableUserImpersonation: "enableUserImpersonation",
  disconnectLeastActiveEditorServiceWhenLimitReached: "disconnectLeastActiveEditorServiceWhenLimitReached",
};

export const tenantPreferencePropertyNames: Required<{ [T in keyof ITenantPreferences]: T }> = {
  id: "id",
  tenantId: "tenantId",
  ownerTenant: "ownerTenant",
  enableOnboarding: "enableOnboarding",
  enableOnboardingPatching: "enableOnboardingPatching",
  defaultEmailBccList: "defaultEmailBccList",
  enableSessionEmails: "enableSessionEmails",
  overwriteExistingDeviceIfOSIsNew: "overwriteExistingDeviceIfOSIsNew",
  requireConsentForExternalSessionProviders: "requireConsentForExternalSessionProviders",
  timeZoneInfoId: "timeZoneInfoId",
  businessHoursStart: "businessHoursStart",
  businessHoursEnd: "businessHoursEnd",
  excludeFromCrossTenantDeploymentsAndSchedules: "excludeFromCrossTenantDeploymentsAndSchedules",
  enableUserAffinitySync: "enableUserAffinitySync",
  enableImmyBotRemoteControl: "enableImmyBotRemoteControl",
  enableImmyBotRemoteControlRecording: "enableImmyBotRemoteControlRecording",
};

export const userPreferencePropertyNames: Required<{ [T in keyof IUserPreferences]: T }> = {
  id: "id",
  userId: "userId",
  maskPiiData: "maskPiiData",
};

export const MaintainedComputerTitle
  = "Indicates whether one or more maintenance sessions have been run on this computer during this billing cycle.";

export function parseHashtableString(val: string) {
  const arr = JSON.parse(val) as [string, string][];

  if (Array.isArray(arr)) {
    return arr.reduce((agg, cur) => {
      const key = cur[0];
      const value = cur[1];
      agg[key] = value;
      return agg;
    }, {} as Record<string, unknown>);
  }
  else {
    return arr;
  }
}

export function camelCaseToWords(s: string) {
  const result = s.replace(/([A-Z])/g, " $1");
  return result.charAt(0).toUpperCase() + result.slice(1);
}

export function targetTypeSupportsPreviewing(targetType: TargetType) {
  return targetType !== TargetType.Metascript && targetType !== TargetType.TenantMetascript;
}

export type ComputerListTab = "all" | "new" | "pending" | "stale" | "devlab";

export type BootstrapVariant = "primary" | "secondary" | "success" | "danger" | "warning" | "link";

export function isRegExp(val: string) {
  try {
    // eslint-disable-next-line no-new
    new RegExp(val);
    return true;
  }
  catch {
    return false;
  }
}

export function dxExcelExport(
  table: "computers" | "user affinities" | "inventory",
  loadOptions?: LoadOptions,
) {
  const { backendURI } = GetConstants();
  const queryParams = new URLSearchParams();
  if (loadOptions?.filter) {
    queryParams.set("filter", JSON.stringify(loadOptions.filter));
  }
  let route: string;
  switch (table) {
    case "computers":
      route = ComputerApiRoutes.ExportComputers;
      break;
    case "user affinities":
      route = ComputerApiRoutes.ExportUserAffinities;
      break;
    case "inventory":
      route = ComputerApiRoutes.ExportComputerInventory;
      break;
    default:
      throw "invalid export table provided";
  }
  window.open(`${backendURI}/${route}?${queryParams}`, "_blank");
}

export function propertyOf<TObj>(name: keyof TObj) {
  return name;
}

export function getAuthProviderName(data: IOauth2AccessToken) {
  try {
    const url = new URL(data.consentData.authorizationEndpoint);
    const allowedHosts = ["login.microsoftonline.com"];
    if (allowedHosts.includes(url.host))
      return "Entra ID";
  }
  catch (e) {
    console.error("Invalid URL:", e);
  }
  return null;
}

export function isAuditOrMonitorTaskMode(mode: MaintenanceTaskMode | undefined) {
  return MaintenanceTaskMode.Audit === mode || MaintenanceTaskMode.Monitor === mode;
}

export function isAuditMode(mode: MaintenanceTaskMode | undefined) {
  return MaintenanceTaskMode.Audit === mode;
}

export function isMonitorMode(mode: MaintenanceTaskMode | undefined) {
  return MaintenanceTaskMode.Monitor === mode;
}

export function isEnforceMode(mode: MaintenanceTaskMode | undefined) {
  return MaintenanceTaskMode.Enforce === mode;
}

export function getTargetTypeTextByValue(targetType: TargetType, category: TargetCategory) {
  if ((targetType === TargetType.All || targetType === TargetType.AllForTenant) && category === TargetCategory.Person) {
    return "All People";
  }

  return EnumTextHelpers.TargetType.GetTextByValue(targetType);
}

/**
 * Builds a URL by combining a base URL with multiple path segments.
 * Handles trailing/leading slashes and ensures proper path joining.
 *
 * @param base - The base URL (e.g., "http://example.com")
 * @param paths - Additional path segments to append
 * @returns A properly formatted URL string
 */
export function buildUrl(base: string, ...paths: string[]): URL {
  // Trim the base URL and ensure it doesn't end with a slash
  const trimmedBase: string = base.trim().replace(/\/+$/, "");

  // Join all path segments and remove duplicate slashes
  const pathSegment: string = `/${paths
    .filter((segment): boolean => segment !== null && segment !== undefined && segment !== "")
    .join("/")
    .replace(/\/+/g, "/")
    .replace(/^\/+/, "")}`;

  return new URL(trimmedBase + pathSegment);
}

/**
 * Waits for the specified amount of milliseconds before returning
 * @param milliseconds
 */
export async function sleep(milliseconds: number): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, milliseconds));
}
