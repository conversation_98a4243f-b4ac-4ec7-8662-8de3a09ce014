import {
  IJoinSessionGroupsPayload,
  IRemoteControlRequest,
  IWindowsSessionsRequest,
} from "@/api/backend/generated/contracts";
import UserHub, { HubEvents } from "@/api/backend/signalr-hubs/user-hub";
import { useAuthStore } from "@/store/pinia/auth-store";

class UserHubEventInstance {
  eventCallbacks: { [T in keyof HubEvents]?: ((...args: HubEvents[T]) => void)[] } = {};
  reconnectCallbacks: (() => void)[] = [];

  get userHub() {
    const authStore = useAuthStore();
    if (!authStore.userHub)
      throw "user hub is not ready";
    return authStore.userHub;
  }

  onEvent<T extends keyof HubEvents>(event: T, cb: (...args: HubEvents[T]) => void) {
    if (!this.eventCallbacks[event])
      this.eventCallbacks[event] = [];
    this.eventCallbacks[event]?.push(cb);
    this.userHub.addEventCallback(event, cb);
  }

  offEvent<T extends keyof HubEvents>(event: T, cb: (...args: HubEvents[T]) => void) {
    const cbs = this.eventCallbacks[event];
    if (cbs) {
      const ind = cbs.indexOf(cb);
      if (ind !== -1)
        cbs.splice(ind, 1);
    }
    this.userHub.removeEventCallback(event, cb);
  }

  onHubReconnected(cb: () => void) {
    this.reconnectCallbacks.push(cb);
    this.userHub.onReconnect(cb);
  }

  async isImmyBotRemoteControlFeatureEnabled(computerId: number) {
    return await this.userHub.invoke("IsImmyBotRemoteControlFeatureEnabled", computerId);
  }

  async getWindowsSessions(request: IWindowsSessionsRequest) {
    return await this.userHub.invoke(UserHub.Server.GetWindowsSessions, request);
  }

  async JoinApplicationLogsGroup() {
    await this.userHub.invoke(UserHub.Server.JoinApplicationLogsGroup);
  }

  async LeaveApplicationLogsGroup() {
    await this.userHub.invoke(UserHub.Server.LeaveApplicationLogsGroup);
  }

  async joinSessionGroupGroup(requesterId: string) {
    await this.userHub.invoke(UserHub.Server.JoinSessionGroupGroup, requesterId);
  }

  async leaveSessionGroupGroup(requesterId: string) {
    await this.userHub.invoke(UserHub.Server.LeaveSessionGroupGroup, requesterId);
  }

  async joinMetascriptGroup(terminalId: string) {
    await this.userHub.invoke(UserHub.Server.JoinMetascriptGroup, terminalId);
  }

  async leaveMetascriptGroup(terminalId: string) {
    await this.userHub.invoke(UserHub.Server.LeaveMetascriptGroup, terminalId);
  }

  async joinCorrelatedOnboardingGroup(onboardingCorrelationId: string) {
    return await this.userHub.invoke("JoinCorrelatedOnboardingGroup", onboardingCorrelationId);
  }

  async leaveCorrelatedOnboardingGroup(onboardingCorrelationId: string) {
    return await this.userHub.invoke("LeaveCorrelatedOnboardingGroup", onboardingCorrelationId);
  }

  async joinComputerGroup(computerId: number) {
    return await this.userHub.invoke("JoinComputerGroup", computerId);
  }

  async leaveComputerGroup(computerId: number) {
    return await this.userHub.invoke("LeaveComputerGroup", computerId);
  }

  async joinTenantGroup(tenantId: number) {
    return await this.userHub.invoke("JoinTenantGroup", tenantId);
  }

  async leaveTenantGroup(tenantId: number) {
    return await this.userHub.invoke("LeaveTenantGroup", tenantId);
  }

  async joinProviderLinkGroup(providerLinkId: number) {
    return await this.userHub.invoke("JoinProviderLinkGroup", providerLinkId);
  }

  async leaveProviderLinkGroup(providerLinkId: number) {
    return await this.userHub.invoke("LeaveProviderLinkGroup", providerLinkId);
  }

  async joinAgentIdentificationLogsGroup() {
    return await this.userHub.invoke("JoinAgentIdentificationLogGroup");
  }

  async leaveAgentIdentificationLogsgroup() {
    return await this.userHub.invoke("LeaveAgentIdentificationLogGroup");
  }

  async joinOauthHookGroup(oauthHookId: string) {
    return await this.userHub.invoke(UserHub.Server.JoinOauthHookGroup, oauthHookId);
  }

  async leaveOauthHookGroup(oauthHookId: string) {
    return await this.userHub.invoke(UserHub.Server.LeaveOauthHookGroup, oauthHookId);
  }

  async joinAzureCustomerPreconsentGroups(partnerPrincipalId: string) {
    return await this.userHub.invoke("JoinAzureCustomerPreconsentGroups", partnerPrincipalId);
  }

  async leaveAzureCustomerPreconsentGroups(partnerPrincipalId: string) {
    return await this.userHub.invoke("LeaveAzureCustomerPreconsentGroups", partnerPrincipalId);
  }

  async joinSessions(
    sessionIds: number[],
    opts: {
      [Property in keyof IJoinSessionGroupsPayload as Exclude<
        Property,
        "sessionIds"
      >]?: IJoinSessionGroupsPayload[Property];
    } = {},
  ) {
    // short-circuit if we have no sessions
    if (!sessionIds || sessionIds.length === 0) {
      return {
        startedListeningOn: [],
        completedSessions: [],
        sessionsNotFound: [],
        alreadyListeningOn: [],
      };
    }

    // attempt to join session groups
    const payload = {
      sessionIds,
      includeStages: opts.includeStages ?? false,
      includeActions: opts.includeActions ?? false,
      includeLogs: opts.includeLogs ?? false,
      includeStageUpdates: opts.includeStageUpdates ?? false,
      includeActionUpdates: opts.includeActionUpdates ?? false,
      includeLogUpdates: opts.includeLogUpdates ?? false,
      includeActionActivities: opts.includeActionActivities ?? false,
    };
    return await this.userHub.invoke("JoinSessionGroups", payload);
  }

  async leaveSessions(sessionIds: number[]) {
    if (sessionIds?.length) {
      await Promise.all(
        sessionIds.map((s) => {
          return this.userHub.invoke("LeaveSessionGroup", s);
        }),
      );
    }
  }

  async startRemoteControlSession(remoteControlRequest: IRemoteControlRequest) {
    return await this.userHub.invoke(
      UserHub.Server.StartRemoteControlSession,
      remoteControlRequest,
    );
  }

  clearCallbacks() {
    const keys = Object.keys(this.eventCallbacks) as Array<keyof HubEvents>;
    for (const event of keys) {
      this.eventCallbacks[event]?.forEach((cb) => {
        this.userHub.removeEventCallback(event, cb as () => void); // sigh, why won't cb type correctly?
      });
    }

    if (this.reconnectCallbacks?.length) {
      this.reconnectCallbacks.forEach((cb) => {
        this.userHub.offReconnect(cb);
      });
    }
  }
}

export default UserHubEventInstance;
