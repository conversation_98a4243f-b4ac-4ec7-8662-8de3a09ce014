import { Hub<PERSON>onnection, HubConnectionBuilder, ISubscription, LogLevel } from "@microsoft/signalr";
import { GetConstants } from "@/utils/constants";
import { IImmyBotUserHub, IImmyBotUserHubClient } from "../generated/contracts";

export type ServerMethods = {
  [T in keyof IImmyBotUserHub]: [Parameters<IImmyBotUserHub[T]>, ReturnType<IImmyBotUserHub[T]>];
};

export type HubEvents = {
  [T in keyof IImmyBotUserHubClient]: Parameters<IImmyBotUserHubClient[T]>;
};

const serverMethods: { [T in keyof ServerMethods]: T } = {
  GetInitData: "GetInitData",
  JoinTenantGroup: "JoinTenantGroup",
  LeaveTenantGroup: "LeaveTenantGroup",
  JoinComputerGroup: "JoinComputerGroup",
  LeaveComputerGroup: "LeaveComputerGroup",
  JoinSessionGroups: "JoinSessionGroups",
  LeaveSessionGroup: "LeaveSessionGroup",
  JoinMetascriptGroup: "JoinMetascriptGroup",
  LeaveMetascriptGroup: "LeaveMetascriptGroup",
  JoinCorrelatedOnboardingGroup: "JoinCorrelatedOnboardingGroup",
  LeaveCorrelatedOnboardingGroup: "LeaveCorrelatedOnboardingGroup",
  CancelSession: "CancelSession",
  JoinProviderLinkGroup: "JoinProviderLinkGroup",
  LeaveProviderLinkGroup: "LeaveProviderLinkGroup",
  JoinAgentIdentificationLogGroup: "JoinAgentIdentificationLogGroup",
  LeaveAgentIdentificationLogGroup: "LeaveAgentIdentificationLogGroup",
  JoinSessionGroupGroup: "JoinSessionGroupGroup",
  LeaveSessionGroupGroup: "LeaveSessionGroupGroup",
  SearchWindowsRegistry: "SearchWindowsRegistry",
  StartRemoteControlSession: "StartRemoteControlSession",
  GetWindowsSessions: "GetWindowsSessions",
  IsImmyBotRemoteControlFeatureEnabled: "IsImmyBotRemoteControlFeatureEnabled",
  JoinOauthHookGroup: "JoinOauthHookGroup",
  LeaveOauthHookGroup: "LeaveOauthHookGroup",
  JoinApplicationLogsGroup: "JoinApplicationLogsGroup",
  LeaveApplicationLogsGroup: "LeaveApplicationLogsGroup",
  LeaveAzureCustomerPreconsentGroups: "LeaveAzureCustomerPreconsentGroups",
  JoinAzureCustomerPreconsentGroups: "JoinAzureCustomerPreconsentGroups",
};

const clientEvents: { [T in keyof HubEvents]: T } = {
  RecommendedTargetAssignmentApprovalsUpdated: "RecommendedTargetAssignmentApprovalsUpdated",
  UpdateTenantNumComputers: "UpdateTenantNumComputers",
  UpdateTenantNumOnboarding: "UpdateTenantNumOnboarding",
  UpdateNumComputers: "UpdateNumComputers",
  UpdateNumOnboarding: "UpdateNumOnboarding",
  AddLog: "AddLog",
  UpdateMaintenanceSession: "UpdateMaintenanceSession",
  UpdateMaintenanceAction: "UpdateMaintenanceAction",
  CreateMaintenanceAction: "CreateMaintenanceAction",
  UpdateMaintenanceSessionStage: "UpdateMaintenanceSessionStage",
  MetascriptMessage: "MetascriptMessage",
  DeploymentCreated: "DeploymentCreated",
  DeploymentUpdated: "DeploymentUpdated",
  DeploymentDeleted: "DeploymentDeleted",
  GlobalDeploymentCreated: "GlobalDeploymentCreated",
  GlobalDeploymentUpdated: "GlobalDeploymentUpdated",
  GlobalDeploymentDeleted: "GlobalDeploymentDeleted",
  GlobalMaintenanceTaskCreated: "GlobalMaintenanceTaskCreated",
  GlobalMaintenanceTaskUpdated: "GlobalMaintenanceTaskUpdated",
  GlobalMaintenanceTaskDeleted: "GlobalMaintenanceTaskDeleted",
  LocalMaintenanceTaskCreated: "LocalMaintenanceTaskCreated",
  LocalMaintenanceTaskUpdated: "LocalMaintenanceTaskUpdated",
  LocalMaintenanceTaskDeleted: "LocalMaintenanceTaskDeleted",
  UpdateComputer: "UpdateComputer",
  CorrelatedOnboardingPendingAgentCreated: "CorrelatedOnboardingPendingAgentCreated",
  CorrelatedOnboardingComputerCreated: "CorrelatedOnboardingComputerCreated",
  CorrelatedOnboardingSessionCreated: "CorrelatedOnboardingSessionCreated",
  CorrelatedOnboardingExistingComputerDetermined: "CorrelatedOnboardingExistingComputerDetermined",
  CorrelatedOnboardingAgentIdentificationFailed: "CorrelatedOnboardingAgentIdentificationFailed",
  AgentIdentificationResolved: "AgentIdentificationResolved",
  AgentIdentificationFailed: "AgentIdentificationFailed",
  ComputerInventoryKeyUpdating: "ComputerInventoryKeyUpdating",
  ComputerInventoryKeyUpdated: "ComputerInventoryKeyUpdated",
  ComputerInventoryKeyUpdateFailed: "ComputerInventoryKeyUpdateFailed",
  AccessRequested: "AccessRequested",
  EphemeralAgentCircuitBroken: "EphemeralAgentCircuitBroken",
  TimelineEventAdded: "TimelineEventAdded",
  RemoveMaintenanceAction: "RemoveMaintenanceAction",
  ProviderClientSyncProgress: "ProviderClientSyncProgress",
  UpdateSessionPhase: "UpdateSessionPhase",
  CreateSessionPhase: "CreateSessionPhase",
  AddAgentIdentificationLog: "AddAgentIdentificationLog",
  EphemeralAgentConnected: "EphemeralAgentConnected",
  EphemeralAgentDisconnected: "EphemeralAgentDisconnected",
  SessionGroupEvent: "SessionGroupEvent",
  ProviderAuditLogAdded: "ProviderAuditLogAdded",
  OauthHookFailed: "OauthHookFailed",
  OauthHookSucceeded: "OauthHookSucceeded",
  ApplicationLogAdded: "ApplicationLogAdded",
  AzureCustomerPreconsentStarted: "AzureCustomerPreconsentStarted",
  AzureCustomerPreconsentFinished: "AzureCustomerPreconsentFinished",
  AzureCustomerPreconsentProgressMessageAdded: "AzureCustomerPreconsentProgressMessageAdded",
  AzureMultiCustomerPreconsentFailed: "AzureMultiCustomerPreconsentFailed",
  AzureMultiCustomerPreconsentFinished: "AzureMultiCustomerPreconsentFinished",
  NotificationAdded: "NotificationAdded",
  AddMaintenanceActionActivity: "AddMaintenanceActionActivity",
};

export default class UserHub {
  connection: HubConnection | null = null;
  // an array of all callbacks to trigger on reconnection
  reconnectionCallbacks: (() => void)[] = [];
  // a map of all callbacks to trigger when specific events come in
  eventCallbacks: { [T in keyof HubEvents]?: ((...args: HubEvents[T]) => void)[] } = {};

  // call this method to register a callback for a particular event
  addEventCallback<T extends keyof HubEvents>(event: T, callback: (...args: HubEvents[T]) => void) {
    if (!this.eventCallbacks[event])
      this.eventCallbacks[event] = [];

    this.eventCallbacks[event]?.push(callback);
  }

  // call this method to un-register a callback for a particular event
  removeEventCallback<T extends keyof HubEvents>(
    event: T,
    callback: (...args: HubEvents[T]) => void,
  ) {
    // stop tracking the cb
    const cbs = this.eventCallbacks[event];
    if (cbs) {
      const ind = cbs.indexOf(callback);
      if (ind !== -1)
        cbs.splice(ind, 1);
    }
  }

  // call this method during initialization to trigger event callbacks
  // see store/actions around line 144
  async invokeEventCallbacks<T extends keyof HubEvents>(event: T, ...data: HubEvents[T]) {
    const cbs = this.eventCallbacks[event];
    if (cbs)
      await Promise.all(cbs.map(cb => Promise.resolve(cb(...data))));
  }

  on<T extends keyof HubEvents>(event: T, callback: (...args: HubEvents[T]) => void) {
    if (this.connection == null)
      throw new Error("Cannot attach event - signalr connection is null");
    // connect the callback with signalr
    this.connection.on(event, callback as (...args: any[]) => void);
  }

  off<T extends keyof HubEvents>(event: T, callback: (...args: HubEvents[T]) => void) {
    if (this.connection == null)
      throw new Error("Cannot detach event - signalr connection is null");
    // disconnect the callback with signalr
    this.connection.off(event, callback as (...args: any[]) => void);
  }

  onReconnect(callback: () => void) {
    this.reconnectionCallbacks.push(callback);
  }

  onceReconnect(callback: () => void) {
    const f = () => {
      this.offReconnect(f);
      callback();
    };
    this.onReconnect(f);
  }

  offReconnect(callback: () => void) {
    this.reconnectionCallbacks = this.reconnectionCallbacks.filter(cb => cb !== callback);
  }

  async invoke<T extends keyof ServerMethods>(
    method: T,
    ...args: ServerMethods[T][0]
  ): Promise<ServerMethods[T][1]> {
    if (this.connection == null)
      throw new Error("Cannot invoke server method - signalr connection is null");
    return await this.connection.invoke(method, ...args);
  }

  stream<TResult>(
    method: keyof ServerMethods,
    onNextCallback: (item: TResult) => void,
    onCompletedCallback: () => void,
    onErrorCallback: (error: any) => void,
    ...args: any[]
  ): ISubscription<TResult> {
    if (!this.connection)
      throw new Error("Cannot stream server method - signalr connection is null");

    return this.connection.stream<TResult>(method, ...args).subscribe({
      next: onNextCallback,
      complete: onCompletedCallback,
      error: onErrorCallback,
    });
  }

  async build() {
    // create and start websocket connection
    const { backendURI } = GetConstants();
    this.connection = new HubConnectionBuilder()
      .withUrl(`${backendURI}/UserHub`)
      .configureLogging(LogLevel.Debug)
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: () => {
          return 5000;
        },
      })
      .build();
    this.connection.onreconnected(() => {
      // invoke reconnection callbacks
      this.reconnectionCallbacks.forEach(cb => cb());
    });
  }

  setupEventListening() {
    if (this.connection == null)
      throw new Error("Cannot setup event listeners - signalr connection is null");
    // setup session events
    this.connection.on(clientEvents.MetascriptMessage, async (data) => {
      await this.invokeEventCallbacks(clientEvents.MetascriptMessage, data);
    });
    this.connection.on(clientEvents.UpdateMaintenanceSession, async (data) => {
      await this.invokeEventCallbacks(clientEvents.UpdateMaintenanceSession, data);
    });
    this.connection.on(clientEvents.CreateMaintenanceAction, async (data) => {
      await this.invokeEventCallbacks(clientEvents.CreateMaintenanceAction, data);
    });
    this.connection.on(clientEvents.UpdateMaintenanceAction, async (data) => {
      await this.invokeEventCallbacks(clientEvents.UpdateMaintenanceAction, data);
    });
    this.connection.on(clientEvents.AddLog, async (data) => {
      await this.invokeEventCallbacks(clientEvents.AddLog, data);
    });
    this.connection.on(clientEvents.CreateSessionPhase, async (data) => {
      await this.invokeEventCallbacks(clientEvents.CreateSessionPhase, data);
    });
    this.connection.on(clientEvents.UpdateSessionPhase, async (data) => {
      await this.invokeEventCallbacks(clientEvents.UpdateSessionPhase, data);
    });
    this.connection.on(clientEvents.AddMaintenanceActionActivity, async (data) => {
      await this.invokeEventCallbacks(clientEvents.AddMaintenanceActionActivity, data);
    });
    this.connection.on(clientEvents.UpdateMaintenanceSessionStage, async (data) => {
      await this.invokeEventCallbacks(clientEvents.UpdateMaintenanceSessionStage, data);
    });
    this.connection.on(clientEvents.UpdateComputer, async (data) => {
      await this.invokeEventCallbacks(clientEvents.UpdateComputer, data);
    });
    this.connection.on(clientEvents.ComputerInventoryKeyUpdating, async (data) => {
      await this.invokeEventCallbacks(clientEvents.ComputerInventoryKeyUpdating, data);
    });
    this.connection.on(clientEvents.ComputerInventoryKeyUpdated, async (data) => {
      await this.invokeEventCallbacks(clientEvents.ComputerInventoryKeyUpdated, data);
    });
    this.connection.on(clientEvents.ComputerInventoryKeyUpdateFailed, async (data) => {
      await this.invokeEventCallbacks(clientEvents.ComputerInventoryKeyUpdateFailed, data);
    });
    this.connection.on(clientEvents.AgentIdentificationResolved, async (data) => {
      await this.invokeEventCallbacks(clientEvents.AgentIdentificationResolved, data);
    });
    this.connection.on(clientEvents.AgentIdentificationFailed, async (data) => {
      await this.invokeEventCallbacks(clientEvents.AgentIdentificationFailed, data);
    });
    this.connection.on(clientEvents.CorrelatedOnboardingComputerCreated, async (data) => {
      await this.invokeEventCallbacks(clientEvents.CorrelatedOnboardingComputerCreated, data);
    });
    this.connection.on(clientEvents.CorrelatedOnboardingSessionCreated, async (data) => {
      await this.invokeEventCallbacks(clientEvents.CorrelatedOnboardingSessionCreated, data);
    });
    this.connection.on(clientEvents.CorrelatedOnboardingExistingComputerDetermined, async (data) => {
      await this.invokeEventCallbacks(
        clientEvents.CorrelatedOnboardingExistingComputerDetermined,
        data,
      );
    });
    this.connection.on(clientEvents.CorrelatedOnboardingPendingAgentCreated, async (data) => {
      await this.invokeEventCallbacks(clientEvents.CorrelatedOnboardingPendingAgentCreated, data);
    });
    this.connection.on(clientEvents.CorrelatedOnboardingAgentIdentificationFailed, async (data) => {
      await this.invokeEventCallbacks(
        clientEvents.CorrelatedOnboardingAgentIdentificationFailed,
        data,
      );
    });
    this.connection.on(clientEvents.AccessRequested, async (data) => {
      await this.invokeEventCallbacks(clientEvents.AccessRequested, data);
    });
    this.connection.on(clientEvents.EphemeralAgentConnected, async (computerId) => {
      await this.invokeEventCallbacks(clientEvents.EphemeralAgentConnected, computerId);
    });
    this.connection.on(clientEvents.EphemeralAgentDisconnected, async (computerId) => {
      await this.invokeEventCallbacks(clientEvents.EphemeralAgentDisconnected, computerId);
    });
    this.connection.on(clientEvents.EphemeralAgentCircuitBroken, async (computerId) => {
      await this.invokeEventCallbacks(clientEvents.EphemeralAgentCircuitBroken, computerId);
    });
    this.connection.on(clientEvents.TimelineEventAdded, async (computerId, timelineEvent) => {
      await this.invokeEventCallbacks(clientEvents.TimelineEventAdded, computerId, timelineEvent);
    });
    this.connection.on(clientEvents.ProviderClientSyncProgress, async (event) => {
      await this.invokeEventCallbacks(clientEvents.ProviderClientSyncProgress, event);
    });
    this.connection.on(clientEvents.AddAgentIdentificationLog, async (event) => {
      await this.invokeEventCallbacks(clientEvents.AddAgentIdentificationLog, event);
    });
    this.connection.on(clientEvents.SessionGroupEvent, async (event) => {
      await this.invokeEventCallbacks(clientEvents.SessionGroupEvent, event);
    });
    this.connection.on(clientEvents.ProviderAuditLogAdded, async (event) => {
      await this.invokeEventCallbacks(clientEvents.ProviderAuditLogAdded, event);
    });
    this.connection.on(clientEvents.OauthHookSucceeded, async (event) => {
      await this.invokeEventCallbacks(clientEvents.OauthHookSucceeded, event);
    });
    this.connection.on(clientEvents.OauthHookFailed, async (event) => {
      await this.invokeEventCallbacks(clientEvents.OauthHookFailed, event);
    });
    this.connection.on(clientEvents.ApplicationLogAdded, async (event) => {
      await this.invokeEventCallbacks(clientEvents.ApplicationLogAdded, event);
    });
    this.connection.on(clientEvents.AzureCustomerPreconsentStarted, async (event) => {
      await this.invokeEventCallbacks(clientEvents.AzureCustomerPreconsentStarted, event);
    });
    this.connection.on(clientEvents.AzureCustomerPreconsentFinished, async (event) => {
      await this.invokeEventCallbacks(clientEvents.AzureCustomerPreconsentFinished, event);
    });
    this.connection.on(clientEvents.AzureCustomerPreconsentProgressMessageAdded, async (event) => {
      await this.invokeEventCallbacks(
        clientEvents.AzureCustomerPreconsentProgressMessageAdded,
        event,
      );
    });
    this.connection.on(clientEvents.AzureMultiCustomerPreconsentFailed, async (event) => {
      await this.invokeEventCallbacks(clientEvents.AzureMultiCustomerPreconsentFailed, event);
    });
    this.connection.on(clientEvents.AzureMultiCustomerPreconsentFinished, async (event) => {
      await this.invokeEventCallbacks(clientEvents.AzureMultiCustomerPreconsentFinished, event);
    });
    this.connection.on(clientEvents.NotificationAdded, async (event) => {
      await this.invokeEventCallbacks(clientEvents.NotificationAdded, event);
    });
  }

  async start() {
    if (this.connection == null)
      throw new Error("Cannot start connection - signalr connection is null");
    await this.connection.start();
  }

  async dispose() {
    if (this.connection == null)
      throw new Error("Cannot dispose connection - signalr connection is null");
    await this.connection.stop();
  }

  static get Client() {
    return clientEvents;
  }

  static get Server() {
    return serverMethods;
  }
}
