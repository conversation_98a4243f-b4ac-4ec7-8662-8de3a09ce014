//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { SessionGroupEventType } from './enums';
import { ISessionGroupData } from './interfaces';
import { IGetMaintenanceSessionLogResponse } from './responses';
import { JoinedSessionGroupType } from './enums';
import { AgentIdentificationLogType } from './enums';
import { DatabaseType } from './enums';
import { MaintenanceType } from './enums';
import { MaintenanceActionType } from './enums';
import { MaintenanceActionReason } from './enums';
import { MaintenanceActionResult } from './enums';
import { MaintenanceActionStatus } from './enums';
import { MaintenanceActionResultReason } from './enums';
import { MaintenanceTaskMode } from './enums';
import { DesiredSoftwareState } from './enums';
import { SoftwareProviderType } from './enums';
import { SoftwareType } from './enums';
import { ActionProgressPhaseName } from './enums';
import { SessionPhaseStatus } from './enums';
import { SessionStatus } from './enums';
import { IGetMaintenanceActionResponse } from './responses';
import { IGetMaintenanceSessionStageResponse } from './responses';
import { IMaintenanceSession } from './interfaces';
import { ComputerOnboardingStatus } from './enums';
import { ISessionJobArgs } from './interfaces';
import { SessionStageType } from './enums';
import { IOpResult2 } from './interfaces';
import { ImmyBotRemoteControlFeatureStatus } from './enums';
import { IRegistrySearchRequestDto } from './interfaces';
import { IComputerInventoryKeyUpdatingEvent } from './interfaces';
import { IComputerInventoryKeyUpdatedEvent } from './interfaces';
import { IComputerInventoryKeyUpdateFailedEvent } from './interfaces';
import { ITimelineEvent } from './interfaces';
import { IAgentIdentificationResolutionEvent } from './interfaces';
import { IAgentIdentificationResolutionFailedEvent } from './interfaces';
import { IMaintenanceActionActivityResponse } from './responses';
import { ILocalTargetAssignmentResource } from './responses';
import { IGlobalTargetAssignmentResource } from './responses';
import { IGetGlobalMaintenanceTaskResponse } from './responses';
import { IGetLocalMaintenanceTaskResponse } from './responses';
import { IGetRecommendedApprovalResponse } from './responses';
import { IGetPendingAgentResponse } from './responses';
import { ICorrelatedOnboardingSessionCreatedEvent } from './interfaces';
import { IGetAgentIdentificationFailureResponse } from './responses';
import { IAccessRequestResponse } from './interfaces';
import { IProviderClientSyncProgressEvent } from './interfaces';
import { IProviderAuditLogAddedEvent } from './interfaces';
import { IOauthHookSucceededEvent } from './interfaces';
import { IOauthHookFailedEvent } from './interfaces';
import { INotification } from './interfaces';
import { IAzureCustomerPreconsentStartedEvent } from './interfaces';
import { IAzureCustomerPreconsentFinishedEvent } from './interfaces';
import { IAzureCustomerPreconsentProgressMessageAddedEvent } from './interfaces';
import { IAzureMultiCustomerPreconsentFailedEvent } from './interfaces';
import { IAzureMultiCustomerPreconsentFinishedEvent } from './interfaces';

export interface ISessionGroupEvent
{
  sessionGroupId: string;
  eventType: SessionGroupEventType;
  data: ISessionGroupData;
}
export interface IAddLogResource
{
  personId?: number;
  computerId?: number;
  tenantId?: number;
  log: IGetMaintenanceSessionLogResponse;
  joinedSessionGroupType: JoinedSessionGroupType;
}
export interface IAgentIdentificationLogResource
{
  id: number;
  providerAgentId: number;
  message: string;
  logType: AgentIdentificationLogType;
  timeUtc: string;
  deviceName: string;
}
export interface IApplicationLogResource
{
  date: string;
  message: string;
  exception?: unknown;
  sourceContext: string;
}
export interface ICreateMaintenanceActionResource
{
  id: number;
  parentId?: number;
  computerId?: number;
  personId?: number;
  tenantId?: number;
  maintenanceSessionId: number;
  maintenanceDisplayName: string;
  assignmentId?: number;
  assignmentType?: DatabaseType;
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  maintenanceTypeName: string;
  detectedVersionString: string;
  desiredVersionString: string;
  actionType: MaintenanceActionType;
  actionTypeName: string;
  startTime: string;
  endTime: string;
  reason: MaintenanceActionReason;
  result: MaintenanceActionResult;
  testResult: string;
  postMaintenanceTest: string;
  postMaintenanceTestType?: number;
  postMaintenanceTestResult?: boolean;
  postMaintenanceTestResultMessage: string;
  status: MaintenanceActionStatus;
  resultReasonMessage: string;
  resultReason?: MaintenanceActionResultReason;
  maintenanceTaskMode?: MaintenanceTaskMode;
  desiredSoftwareState?: DesiredSoftwareState;
  maintenanceTaskGetResult: string;
  softwareTableRegexString: string;
  softwareActionIdForConfigurationTask?: number;
  policyDescription?: string;
  createdDateUTC: string;
  softwareProviderType: SoftwareProviderType;
  softwareType?: SoftwareType;
  maintenanceTaskType?: DatabaseType;
  joinedSessionGroupType: JoinedSessionGroupType;
}
export interface ICreateSessionPhaseResource
{
  joinedSessionGroupType: JoinedSessionGroupType;
  id: number;
  phaseName: string;
  actionProgressPhaseName?: ActionProgressPhaseName;
  maintenanceSessionId: number;
  maintenanceSessionStageId: number;
  maintenanceActionId?: number;
  status: SessionPhaseStatus;
  progressPercentComplete?: number;
  progressStatus?: string;
  progressCompleted: boolean;
  dateStartedUtc?: string;
  dateCompletedUtc?: string;
}
export interface IJoinedSessionGroupResource
{
  joinedSessionGroupType: JoinedSessionGroupType;
}
export interface IInitDataResponse
{
  numComputers: number;
  numOnboarding: number;
}
export interface IJoinComputerGroupResponse
{
  runningSession?: ISession;
}
export interface IJoinPersonGroupResponse
{
  runningSession?: IJoinPersonGroupResponseSession;
}
export interface IJoinPersonGroupResponseSession
{
  id: number;
  tenantId?: number;
  personId?: number;
  sessionStatus: SessionStatus;
}
export interface IJoinSessionGroupsPayload
{
  sessionIds: number[];
  includeActions: boolean;
  includeLogs: boolean;
  includeStages: boolean;
  includeActionUpdates: boolean;
  includeLogUpdates: boolean;
  includeStageUpdates: boolean;
  includeActionActivities: boolean;
}
export interface IJoinSessionGroupsResponse
{
  startedListeningOn: IJoinSessionGroupsResponseSession[];
  completedSessions: IJoinSessionGroupsResponseSession[];
  sessionsNotFound: number[];
  alreadyListeningOn: IJoinSessionGroupsResponseSession[];
}
export interface IJoinSessionGroupsResponseSession
{
  id: number;
  computerId?: number;
  tenantId?: number;
  personId?: number;
  sessionStatus: SessionStatus;
  logs: IGetMaintenanceSessionLogResponse[];
  actions: IGetMaintenanceActionResponse[];
  stages: IGetMaintenanceSessionStageResponse[];
  PopulateFrom(session: IMaintenanceSession) : void;
}
export interface IJoinTenantGroupResponse
{
  runningSession?: IJoinTenantGroupResponseSession;
}
export interface IJoinTenantGroupResponseSession
{
  id: number;
  tenantId?: number;
  computerId?: number;
  sessionStatus: SessionStatus;
}
export interface IMetascriptMessageResource
{
  terminalId: string;
  message?: string;
}
export interface IRemoveMaintenanceActionResource
{
  maintenanceActionId: number;
  joinedSessionGroupType: JoinedSessionGroupType;
}
export interface IUpdateComputerResource
{
  id: number;
  detectionOutdated?: boolean;
  computerName?: string;
  onboardingStatus?: ComputerOnboardingStatus;
}
export interface IUpdateMaintenanceActionResource
{
  joinedSessionGroupType: JoinedSessionGroupType;
  id: number;
  parentId?: number;
  computerId?: number;
  personId?: number;
  tenantId?: number;
  maintenanceSessionId: number;
  maintenanceDisplayName: string;
  assignmentId?: number;
  assignmentType?: DatabaseType;
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  maintenanceTypeName: string;
  detectedVersionString: string;
  desiredVersionString: string;
  actionType: MaintenanceActionType;
  actionTypeName: string;
  startTime: string;
  endTime: string;
  reason: MaintenanceActionReason;
  result: MaintenanceActionResult;
  testResult: string;
  postMaintenanceTest: string;
  postMaintenanceTestType?: number;
  postMaintenanceTestResult?: boolean;
  postMaintenanceTestResultMessage: string;
  status: MaintenanceActionStatus;
  resultReasonMessage: string;
  resultReason?: MaintenanceActionResultReason;
  maintenanceTaskMode?: MaintenanceTaskMode;
  desiredSoftwareState?: DesiredSoftwareState;
  maintenanceTaskGetResult: string;
  softwareTableRegexString: string;
  softwareActionIdForConfigurationTask?: number;
  policyDescription?: string;
  createdDateUTC: string;
  softwareProviderType: SoftwareProviderType;
  softwareType?: SoftwareType;
  maintenanceTaskType?: DatabaseType;
}
export interface IUpdateMaintenanceSessionResource
{
  id: number;
  jobId: string;
  personId?: number;
  computerId?: number;
  tenantId?: number;
  createdById?: number;
  createdBy?: string;
  scheduledId?: number;
  sessionStatus: SessionStatus;
  onboarding: boolean;
  onboardingStageStatus?: SessionStatus;
  detectionStageStatus?: SessionStatus;
  executionStageStatus?: SessionStatus;
  agentUpdatesStageStatus?: SessionStatus;
  resolutionStageStatus?: SessionStatus;
  inventoryStageStatus?: SessionStatus;
  scheduledExecutionDate?: string;
  createdDateUTC: string;
  fullMaintenance: boolean;
  joinedSessionGroupType: JoinedSessionGroupType;
  sessionJobArgs: ISessionJobArgs;
}
export interface IUpdateMaintenanceSessionStageResource
{
  id: number;
  maintenanceSessionId: number;
  stageStatus: SessionStatus;
  type: SessionStageType;
  jobId: string;
  joinedSessionGroupType: JoinedSessionGroupType;
}
export interface ISession
{
  id: number;
  computerId: number;
  sessionStatus: SessionStatus;
}
export interface IRemoteControlRequest
{
  computerId: number;
  providerLinkId: number;
  targetSessionId: number;
}
export interface IWindowsSessionsRequest
{
  computerId: number;
  providerLinkId: number;
}
export interface IImmyBotUserHub
{
  GetInitData() : IInitDataResponse;
  IsImmyBotRemoteControlFeatureEnabled(computerId: number) : IOpResult2<ImmyBotRemoteControlFeatureStatus>;
  GetWindowsSessions(request: IWindowsSessionsRequest) : IOpResult2<any[]>;
  JoinApplicationLogsGroup() : void;
  LeaveApplicationLogsGroup() : void;
  JoinOauthHookGroup(oauthHookId: string) : void;
  LeaveOauthHookGroup(oauthHookId: string) : void;
  JoinAzureCustomerPreconsentGroups(partnerPrincipalId: string) : void;
  LeaveAzureCustomerPreconsentGroups(partnerPrincipalId: string) : void;
  JoinProviderLinkGroup(providerLinkId: number) : void;
  JoinSessionGroupGroup(sessionGroupId: string) : void;
  LeaveSessionGroupGroup(sessionGroupId: string) : void;
  LeaveProviderLinkGroup(providerLinkId: number) : void;
  JoinAgentIdentificationLogGroup() : void;
  LeaveAgentIdentificationLogGroup() : void;
  JoinTenantGroup(tenantId: number) : IJoinTenantGroupResponse;
  LeaveTenantGroup(tenantId: number) : void;
  JoinComputerGroup(computerId: number) : IOpResult2<IJoinComputerGroupResponse>;
  LeaveComputerGroup(computerId: number) : void;
  JoinSessionGroups(payload: IJoinSessionGroupsPayload) : IJoinSessionGroupsResponse;
  LeaveSessionGroup(sessionId: number) : void;
  CancelSession(sessionId: number) : void;
  JoinMetascriptGroup(id: string) : void;
  LeaveMetascriptGroup(id: string) : void;
  JoinCorrelatedOnboardingGroup(id: string) : void;
  LeaveCorrelatedOnboardingGroup(id: string) : void;
  SearchWindowsRegistry(searchRequest: IRegistrySearchRequestDto) : any[];
  StartRemoteControlSession(remoteControlRequest: IRemoteControlRequest) : IOpResult2<string>;
}
export abstract class ImmyBotUserHubMessages
{
  public static AgentNotUpToDateMessage: string = `ImmyBot Agent version must be at least 0.57.0 to use remote control.`;
  public static AgentToServerVersionMismatch: string = `The ImmyBot Agent version is different from the server.`;
}
export interface IImmyBotUserHubClient
{
  UpdateTenantNumComputers(tenantId: number, numComputers: number) : void;
  UpdateTenantNumOnboarding(tenantId: number, numOnboarding: number) : void;
  UpdateNumComputers(numComputers: number) : void;
  UpdateNumOnboarding(numOnboarding: number) : void;
  UpdateComputer(resource: IUpdateComputerResource) : void;
  ComputerInventoryKeyUpdating(resource: IComputerInventoryKeyUpdatingEvent) : void;
  ComputerInventoryKeyUpdated(resource: IComputerInventoryKeyUpdatedEvent) : void;
  ComputerInventoryKeyUpdateFailed(resource: IComputerInventoryKeyUpdateFailedEvent) : void;
  EphemeralAgentCircuitBroken(computerId: number) : void;
  TimelineEventAdded(computerId: number, timelineEvent: ITimelineEvent) : void;
  AgentIdentificationResolved(resource: IAgentIdentificationResolutionEvent) : void;
  AgentIdentificationFailed(resource: IAgentIdentificationResolutionFailedEvent) : void;
  AddAgentIdentificationLog(resource: IAgentIdentificationLogResource) : void;
  EphemeralAgentConnected(computerId: number) : void;
  EphemeralAgentDisconnected(computerId: number) : void;
  CreateMaintenanceAction(resource: ICreateMaintenanceActionResource) : void;
  UpdateMaintenanceAction(resource: IUpdateMaintenanceActionResource) : void;
  RemoveMaintenanceAction(resource: IRemoveMaintenanceActionResource) : void;
  UpdateMaintenanceSessionStage(resource: IUpdateMaintenanceSessionStageResource) : void;
  UpdateMaintenanceSession(resource: IUpdateMaintenanceSessionResource) : void;
  UpdateSessionPhase(resource: ICreateSessionPhaseResource) : void;
  CreateSessionPhase(resource: ICreateSessionPhaseResource) : void;
  AddLog(resource: IAddLogResource) : void;
  AddMaintenanceActionActivity(resource: IMaintenanceActionActivityResponse) : void;
  MetascriptMessage(resource: IMetascriptMessageResource) : void;
  DeploymentCreated(deployment: ILocalTargetAssignmentResource) : void;
  DeploymentUpdated(deployment: ILocalTargetAssignmentResource) : void;
  DeploymentDeleted(deploymentId: number) : void;
  GlobalDeploymentCreated(deployment: IGlobalTargetAssignmentResource) : void;
  GlobalDeploymentUpdated(deployment: IGlobalTargetAssignmentResource) : void;
  GlobalDeploymentDeleted(deloymentId: number) : void;
  GlobalMaintenanceTaskCreated(maintenanceTask: IGetGlobalMaintenanceTaskResponse) : void;
  GlobalMaintenanceTaskUpdated(maintenanceTask: IGetGlobalMaintenanceTaskResponse) : void;
  GlobalMaintenanceTaskDeleted(maintenanceTaskId: number) : void;
  LocalMaintenanceTaskCreated(maintenanceTask: IGetLocalMaintenanceTaskResponse) : void;
  LocalMaintenanceTaskUpdated(maintenanceTask: IGetLocalMaintenanceTaskResponse) : void;
  LocalMaintenanceTaskDeleted(maintenanceTaskId: number) : void;
  RecommendedTargetAssignmentApprovalsUpdated(approvals: IGetRecommendedApprovalResponse[]) : void;
  CorrelatedOnboardingPendingAgentCreated(agent: IGetPendingAgentResponse) : void;
  CorrelatedOnboardingComputerCreated(computerId: number) : void;
  CorrelatedOnboardingSessionCreated(ev: ICorrelatedOnboardingSessionCreatedEvent) : void;
  CorrelatedOnboardingExistingComputerDetermined(computerId: number) : void;
  CorrelatedOnboardingAgentIdentificationFailed(agentIdentificationFailure: IGetAgentIdentificationFailureResponse) : void;
  AccessRequested(res: IAccessRequestResponse) : void;
  ProviderClientSyncProgress(ev: IProviderClientSyncProgressEvent) : void;
  SessionGroupEvent(ev: ISessionGroupEvent) : void;
  ProviderAuditLogAdded(ev: IProviderAuditLogAddedEvent) : void;
  OauthHookSucceeded(ev: IOauthHookSucceededEvent) : void;
  OauthHookFailed(ev: IOauthHookFailedEvent) : void;
  ApplicationLogAdded(ev: IApplicationLogResource) : void;
  NotificationAdded(ev: INotification) : void;
  AzureCustomerPreconsentStarted(ev: IAzureCustomerPreconsentStartedEvent) : void;
  AzureCustomerPreconsentFinished(ev: IAzureCustomerPreconsentFinishedEvent) : void;
  AzureCustomerPreconsentProgressMessageAdded(ev: IAzureCustomerPreconsentProgressMessageAddedEvent) : void;
  AzureMultiCustomerPreconsentFailed(ev: IAzureMultiCustomerPreconsentFailedEvent) : void;
  AzureMultiCustomerPreconsentFinished(ev: IAzureMultiCustomerPreconsentFinishedEvent) : void;
}
