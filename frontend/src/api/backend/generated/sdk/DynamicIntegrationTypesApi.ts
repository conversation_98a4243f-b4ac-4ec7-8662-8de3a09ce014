//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IOpResult2 } from '../interfaces';
import { IDynamicFormBindResult } from '../interfaces';
import { ISetupTestIntegrationRequest } from '../requests';
import { ITestIntegrationMethodRequest } from '../requests';
import { ITestIntegrationBindConfigurationFormRequest } from '../requests';
import { IDynamicIntegrationTypeResponse } from '../responses';
import { ICreateDynamicIntegrationTypePayload } from '../interfaces';
import { IUpdateDynamicIntegrationTypePayload } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.DynamicIntegrationTypesController */
export class DynamicIntegrationTypesApi
{
  public async setupTestIntegration(terminalId: string, request: ISetupTestIntegrationRequest, cancelToken?: CancelToken) : Promise<IOpResult2<IDynamicFormBindResult>>
  {
    const route = `/api/v1/dynamic-provider-types/test-environment/${terminalId}`
    const res = await $post<IOpResult2<IDynamicFormBindResult>>(route, request, {cancelToken});
    return res.data;
  }
  public async removeTestIntegration(terminalId: string, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/dynamic-provider-types/test-environment/${terminalId}`
    await $delete(route, {cancelToken});
  }
  public async testIntegrationMethod(terminalId: string, method: string, request: ITestIntegrationMethodRequest, cancelToken?: CancelToken) : Promise<IOpResult2<any>>
  {
    const route = `/api/v1/dynamic-provider-types/test-environment/${terminalId}/execute-method/${method}`
    const res = await $post<IOpResult2<any>>(route, request, {cancelToken});
    return res.data;
  }
  public async testIntegrationBindConfigurationForm(terminalId: string, request: ITestIntegrationBindConfigurationFormRequest, cancelToken?: CancelToken) : Promise<IOpResult2<IDynamicFormBindResult>>
  {
    const route = `/api/v1/dynamic-provider-types/test-environment/${terminalId}/bind-configuration-form`
    const res = await $post<IOpResult2<IDynamicFormBindResult>>(route, request, {cancelToken});
    return res.data;
  }
  public async reload(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/dynamic-provider-types/reload`
    await $post(route, undefined, {cancelToken});
  }
  public async reloadByGlobalId(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/dynamic-provider-types/global/${id}/reload`
    await $post(route, undefined, {cancelToken});
  }
  public async reloadByLocalId(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/dynamic-provider-types/local/${id}/reload`
    await $post(route, undefined, {cancelToken});
  }
  public async getAll(cancelToken?: CancelToken) : Promise<IDynamicIntegrationTypeResponse[]>
  {
    const route = `/api/v1/dynamic-provider-types`
    const res = await $get<IDynamicIntegrationTypeResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async getGlobal(id: number, cancelToken?: CancelToken) : Promise<IDynamicIntegrationTypeResponse>
  {
    const route = `/api/v1/dynamic-provider-types/global/${id}`
    const res = await $get<IDynamicIntegrationTypeResponse>(route, {cancelToken});
    return res.data;
  }
  public async getLocal(id: number, cancelToken?: CancelToken) : Promise<IDynamicIntegrationTypeResponse>
  {
    const route = `/api/v1/dynamic-provider-types/local/${id}`
    const res = await $get<IDynamicIntegrationTypeResponse>(route, {cancelToken});
    return res.data;
  }
  public async createGlobal(payload: ICreateDynamicIntegrationTypePayload, cancelToken?: CancelToken) : Promise<IDynamicIntegrationTypeResponse>
  {
    const route = `/api/v1/dynamic-provider-types/global`
    const res = await $post<IDynamicIntegrationTypeResponse>(route, payload, {cancelToken});
    return res.data;
  }
  public async createLocal(payload: ICreateDynamicIntegrationTypePayload, cancelToken?: CancelToken) : Promise<IDynamicIntegrationTypeResponse>
  {
    const route = `/api/v1/dynamic-provider-types/local;`
    const res = await $post<IDynamicIntegrationTypeResponse>(route, payload, {cancelToken});
    return res.data;
  }
  public async updateGlobal(id: number, payload: IUpdateDynamicIntegrationTypePayload, cancelToken?: CancelToken) : Promise<IDynamicIntegrationTypeResponse>
  {
    const route = `/api/v1/dynamic-provider-types/global/${id}`
    const res = await $post<IDynamicIntegrationTypeResponse>(route, payload, {cancelToken});
    return res.data;
  }
  public async updateLocal(id: number, payload: IUpdateDynamicIntegrationTypePayload, cancelToken?: CancelToken) : Promise<IDynamicIntegrationTypeResponse>
  {
    const route = `/api/v1/dynamic-provider-types/local/${id}`
    const res = await $post<IDynamicIntegrationTypeResponse>(route, payload, {cancelToken});
    return res.data;
  }
  public async deleteGlobal(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/dynamic-provider-types/global/${id}`
    await $delete(route, {cancelToken});
  }
  public async deleteLocal(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/dynamic-provider-types/local/${id}`
    await $delete(route, {cancelToken});
  }
}
