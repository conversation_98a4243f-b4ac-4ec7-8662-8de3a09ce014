//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IChangeRequestDiff } from '../interfaces';
import { IChangeRequestResponse } from '../responses';
import { IUpdateLocalTargetAssignmentPayload } from '../interfaces';
import { ICreateLocalTargetAssignmentPayload } from '../interfaces';
import { IMaintenanceItemOrder } from '../interfaces';
import { IUpdateMaintenanceItemOrderPayload } from '../interfaces';
import { IGetRecommendedApprovalResponse } from '../responses';
import { IUpdateRecommendedApprovalsRequestBody } from '../requests';
import { IGetOptionalTargetAssignmentApprovalResponse } from '../responses';
import { IUpdateOptionalTargetAssignmentApprovalPayload } from '../interfaces';
import { IUpdateNotesPayload } from '../requests';
import { IGlobalTargetAssignmentResource } from '../responses';
import { IGetTargetAssignmentTypeResponse } from '../responses';
import { ICreateGlobalTargetAssignmentPayload } from '../interfaces';
import { IUpdateGlobalTargetAssignmentPayload } from '../interfaces';
import { ILocalTargetAssignmentResource } from '../responses';
import { IBatchUpdateAssignmentRequest } from '../interfaces';
import { ITargetAssignmentDuplicateResponse } from '../responses';
import { IDuplicateTargetAssignmentPayload } from '../interfaces';
import { ITargetAssignmentResource } from '../interfaces';
import { IResolveVisibilityTargetAssignmentsRequest } from '../requests';
import { IGetTenantResponse } from '../responses';
import { ICalculateTargetsRequest } from '../requests';
import { ICalculateTargetedComputerResponse } from '../responses';
import { ITargetedPerson } from '../interfaces';
import { IOverrideTargetAssignmentRequest } from '../requests';
import { IDuplicateAssignmentRequestBody } from '../requests';
import { IOpResult2 } from '../interfaces';
import { IMigrateToSupersedingAssignmentRequest } from '../interfaces';
import { IMigrateToSupersedingAssignmentWhatIfResponse } from '../responses';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.TargetAssignmentsController */
export class TargetAssignmentsApi
{
  public async getChangeRequestDiff(changeRequestId: number, cancelToken?: CancelToken) : Promise<IChangeRequestDiff>
  {
    const route = `/api/v1/target-assignments/change-request/${changeRequestId}/diff`
    const res = await $get<IChangeRequestDiff>(route, {cancelToken});
    return res.data;
  }
  public async createChangeRequestForExistingDeployment(deploymentId: number, body: IUpdateLocalTargetAssignmentPayload, cancelToken?: CancelToken) : Promise<IChangeRequestResponse>
  {
    const route = `/api/v1/target-assignments/${deploymentId}/change-request`
    const res = await $post<IChangeRequestResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async updateChangeRequestForExistingDeployment(deploymentId: number, changeRequestId: number, body: IUpdateLocalTargetAssignmentPayload, cancelToken?: CancelToken) : Promise<IChangeRequestResponse>
  {
    const route = `/api/v1/target-assignments/${deploymentId}/change-request/${changeRequestId}`
    const res = await $post<IChangeRequestResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async getDeploymentChangeRequest(changeRequestId: number, cancelToken?: CancelToken) : Promise<IChangeRequestResponse>
  {
    const route = `/api/v1/target-assignments/change-request/${changeRequestId}`
    const res = await $get<IChangeRequestResponse>(route, {cancelToken});
    return res.data;
  }
  public async getAllChangeRequests(cancelToken?: CancelToken) : Promise<IChangeRequestResponse[]>
  {
    const route = `/api/v1/target-assignments/change-requests`
    const res = await $get<IChangeRequestResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async getAllChangeRequestsForDeployment(deploymentId: number, cancelToken?: CancelToken) : Promise<IChangeRequestResponse[]>
  {
    const route = `/api/v1/target-assignments/${deploymentId}/change-requests`
    const res = await $get<IChangeRequestResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async createChangeRequestForNewDeployment(body: ICreateLocalTargetAssignmentPayload, cancelToken?: CancelToken) : Promise<IChangeRequestResponse>
  {
    const route = `/api/v1/target-assignments/change-request`
    const res = await $post<IChangeRequestResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async updateChangeRequestForNewDeployment(changeRequestId: number, payload: IUpdateLocalTargetAssignmentPayload, cancelToken?: CancelToken) : Promise<IChangeRequestResponse>
  {
    const route = `/api/v1/target-assignments/change-request/${changeRequestId}`
    const res = await $post<IChangeRequestResponse>(route, payload, {cancelToken});
    return res.data;
  }
  public async getMaintenanceItemOrder(cancelToken?: CancelToken) : Promise<IMaintenanceItemOrder[]>
  {
    const route = `/api/v1/target-assignments/maintenance-item-orders`
    const res = await $get<IMaintenanceItemOrder[]>(route, {cancelToken});
    return res.data;
  }
  public async updatePriority(payload: IUpdateMaintenanceItemOrderPayload, cancelToken?: CancelToken) : Promise<IMaintenanceItemOrder>
  {
    const route = `/api/v1/target-assignments/update-maintenance-item-order`
    const res = await $post<IMaintenanceItemOrder>(route, payload, {cancelToken});
    return res.data;
  }
  public async getRecommendedApprovals(cancelToken?: CancelToken) : Promise<IGetRecommendedApprovalResponse[]>
  {
    const route = `/api/v1/target-assignments/recommended-approvals`
    const res = await $get<IGetRecommendedApprovalResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async updateRecommendedApprovals(body: IUpdateRecommendedApprovalsRequestBody, cancelToken?: CancelToken) : Promise<IGetRecommendedApprovalResponse[]>
  {
    const route = `/api/v1/target-assignments/recommended-approvals/update`
    const res = await $post<IGetRecommendedApprovalResponse[]>(route, body, {cancelToken});
    return res.data;
  }
  public async getAllOptionalTargetAssignmentApprovalsForComputer(computerId: number, cancelToken?: CancelToken) : Promise<IGetOptionalTargetAssignmentApprovalResponse[]>
  {
    const route = `/api/v1/target-assignments/optional-target-assignment-approvals/computer/${computerId}`
    const res = await $get<IGetOptionalTargetAssignmentApprovalResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async updateOptionalTargetAssignmentApproval(id: number, body: IUpdateOptionalTargetAssignmentApprovalPayload, cancelToken?: CancelToken) : Promise<boolean>
  {
    const route = `/api/v1/target-assignments/optional-target-assignment-approvals/${id}`
    const res = await $post<boolean>(route, body, {cancelToken});
    return res.data;
  }
  public async updateNotesGlobal(id: number, body: IUpdateNotesPayload, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/target-assignments/global/${id}/notes`
    await $post(route, body, {cancelToken});
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.filters - Defaults to null in the controller
  * @param {string} queries.sorts - Defaults to null in the controller
  * @param {number} queries.page - Defaults to null in the controller
  * @param {number} queries.pageSize - Defaults to null in the controller
  */
  public async getAllGlobal(queries: {filters?: string, sorts?: string, page?: number, pageSize?: number} = {}, cancelToken?: CancelToken) : Promise<IGlobalTargetAssignmentResource[]>
  {
    const route = `/api/v1/target-assignments/global`
    const res = await $get<IGlobalTargetAssignmentResource[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async getGlobal(id: number, cancelToken?: CancelToken) : Promise<IGlobalTargetAssignmentResource>
  {
    const route = `/api/v1/target-assignments/global/${id}`
    const res = await $get<IGlobalTargetAssignmentResource>(route, {cancelToken});
    return res.data;
  }
  public async getGlobalTargetAssignmentType(id: number, cancelToken?: CancelToken) : Promise<IGetTargetAssignmentTypeResponse>
  {
    const route = `/api/v1/target-assignments/global/${id}/type`
    const res = await $get<IGetTargetAssignmentTypeResponse>(route, {cancelToken});
    return res.data;
  }
  public async createGlobal(body: ICreateGlobalTargetAssignmentPayload, cancelToken?: CancelToken) : Promise<IGlobalTargetAssignmentResource>
  {
    const route = `/api/v1/target-assignments/global/create`
    const res = await $post<IGlobalTargetAssignmentResource>(route, body, {cancelToken});
    return res.data;
  }
  public async updateGlobal(id: number, body: IUpdateGlobalTargetAssignmentPayload, cancelToken?: CancelToken) : Promise<IGlobalTargetAssignmentResource>
  {
    const route = `/api/v1/target-assignments/global/${id}`
    const res = await $put<IGlobalTargetAssignmentResource>(route, body, {cancelToken});
    return res.data;
  }
  public async deleteGlobal(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/target-assignments/global/${id}`
    await $delete(route, {cancelToken});
  }
  public async updateNotesLocal(id: number, body: IUpdateNotesPayload, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/target-assignments/${id}/notes`
    await $post(route, body, {cancelToken});
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.filters - Defaults to null in the controller
  * @param {string} queries.sorts - Defaults to null in the controller
  * @param {number} queries.page - Defaults to null in the controller
  * @param {number} queries.pageSize - Defaults to null in the controller
  */
  public async getAllLocal(queries: {filters?: string, sorts?: string, page?: number, pageSize?: number} = {}, cancelToken?: CancelToken) : Promise<ILocalTargetAssignmentResource[]>
  {
    const route = `/api/v1/target-assignments`
    const res = await $get<ILocalTargetAssignmentResource[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async batchUpdateLocal(request: IBatchUpdateAssignmentRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/target-assignments/batch-update`
    await $patch(route, request, {cancelToken});
  }
  public async getDuplicatesLocal(req: IDuplicateTargetAssignmentPayload, cancelToken?: CancelToken) : Promise<ITargetAssignmentDuplicateResponse>
  {
    const route = `/api/v1/target-assignments/duplicates`
    const res = await $post<ITargetAssignmentDuplicateResponse>(route, req, {cancelToken});
    return res.data;
  }
  public async get(id: number, cancelToken?: CancelToken) : Promise<ILocalTargetAssignmentResource>
  {
    const route = `/api/v1/target-assignments/${id}`
    const res = await $get<ILocalTargetAssignmentResource>(route, {cancelToken});
    return res.data;
  }
  public async resolveVisibilityTargetAssignments(request: IResolveVisibilityTargetAssignmentsRequest, cancelToken?: CancelToken) : Promise<ITargetAssignmentResource[]>
  {
    const route = `/api/v1/target-assignments/visibility`
    const res = await $post<ITargetAssignmentResource[]>(route, request, {cancelToken});
    return res.data;
  }
  public async calculateTargetedTenants(body: ICalculateTargetsRequest, cancelToken?: CancelToken) : Promise<IGetTenantResponse[]>
  {
    const route = `/api/v1/target-assignments/tenant-target-preview`
    const res = await $post<IGetTenantResponse[]>(route, body, {cancelToken});
    return res.data;
  }
  public async calculateTargetedComputers(body: ICalculateTargetsRequest, cancelToken?: CancelToken) : Promise<ICalculateTargetedComputerResponse[]>
  {
    const route = `/api/v1/target-assignments/target-preview`
    const res = await $post<ICalculateTargetedComputerResponse[]>(route, body, {cancelToken});
    return res.data;
  }
  public async calculateTargetedPersons(body: ICalculateTargetsRequest, cancelToken?: CancelToken) : Promise<ITargetedPerson[]>
  {
    const route = `/api/v1/target-assignments/persons-target-preview`
    const res = await $post<ITargetedPerson[]>(route, body, {cancelToken});
    return res.data;
  }
  public async getLocalTargetAssignmentType(id: number, cancelToken?: CancelToken) : Promise<IGetTargetAssignmentTypeResponse>
  {
    const route = `/api/v1/target-assignments/${id}/type`
    const res = await $get<IGetTargetAssignmentTypeResponse>(route, {cancelToken});
    return res.data;
  }
  public async create(body: ICreateLocalTargetAssignmentPayload, cancelToken?: CancelToken) : Promise<ILocalTargetAssignmentResource>
  {
    const route = `/api/v1/target-assignments`
    const res = await $post<ILocalTargetAssignmentResource>(route, body, {cancelToken});
    return res.data;
  }
  public async updateTargetAssignment(id: number, body: IUpdateLocalTargetAssignmentPayload, cancelToken?: CancelToken) : Promise<ILocalTargetAssignmentResource>
  {
    const route = `/api/v1/target-assignments/${id}`
    const res = await $put<ILocalTargetAssignmentResource>(route, body, {cancelToken});
    return res.data;
  }
  public async deleteTargetAssignment(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/target-assignments/${id}`
    await $delete(route, {cancelToken});
  }
  public async overrideLocalTargetAssignment(id: number, req: IOverrideTargetAssignmentRequest, cancelToken?: CancelToken) : Promise<ILocalTargetAssignmentResource>
  {
    const route = `/api/v1/target-assignments/${id}/override`
    const res = await $post<ILocalTargetAssignmentResource>(route, req, {cancelToken});
    return res.data;
  }
  public async overrideGlobal(id: number, req: IOverrideTargetAssignmentRequest, cancelToken?: CancelToken) : Promise<ILocalTargetAssignmentResource>
  {
    const route = `/api/v1/target-assignments/global/${id}/override`
    const res = await $post<ILocalTargetAssignmentResource>(route, req, {cancelToken});
    return res.data;
  }
  public async duplicate(req: IDuplicateAssignmentRequestBody, cancelToken?: CancelToken) : Promise<number>
  {
    const route = `/api/v1/target-assignments/duplicate`
    const res = await $post<number>(route, req, {cancelToken});
    return res.data;
  }
  public async migrateToSupersedingAssignment(req: IMigrateToSupersedingAssignmentRequest, cancelToken?: CancelToken) : Promise<IOpResult2<number>>
  {
    const route = `/api/v1/target-assignments/migrate-to-superseding-assignment`
    const res = await $post<IOpResult2<number>>(route, req, {cancelToken});
    return res.data;
  }
  public async migrateToSupersedingAssignmentWhatIf(req: IMigrateToSupersedingAssignmentRequest, cancelToken?: CancelToken) : Promise<IOpResult2<IMigrateToSupersedingAssignmentWhatIfResponse>>
  {
    const route = `/api/v1/target-assignments/migrate-to-superseding-assignment-what-if`
    const res = await $post<IOpResult2<IMigrateToSupersedingAssignmentWhatIfResponse>>(route, req, {cancelToken});
    return res.data;
  }
  public async migrateDeploymentsToProviderLinks(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/target-assignments/migrate-deployments-to-provider-links`
    await $post(route, undefined, {cancelToken});
  }
}
