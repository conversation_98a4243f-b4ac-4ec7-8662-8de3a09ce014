//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';
import { SessionType } from '../enums';
import { IGetMaintenanceSessionResponse } from '../responses';
import { IGetMaintenanceSessionLogResponse } from '../responses';
import { IGetMaintenanceSessionPhaseResponse } from '../responses';
import { ICancelSessionsRequestBody } from '../requests';
import { IRerunSessionsRequestBody } from '../requests';
import { IGetSessionStatusCountsPayload } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.MaintenanceSessionsController */
export class MaintenanceSessionsApi
{
  public dxGetAll(queries: {computerId?: number, tenantId?: number, personId?: number, sessionType?: SessionType} = {}) : CustomStore
  {
    const route = `/api/v1/maintenance-sessions/dx`
    return createStore(route, { queries });
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {boolean} queries.includeActions
  * @param {boolean} queries.includeStages
  * @param {boolean} queries.includeActionActivities
  */
  public async get(sessionId: number, queries: {includeActions: boolean, includeStages: boolean, includeActionActivities: boolean}, cancelToken?: CancelToken) : Promise<IGetMaintenanceSessionResponse>
  {
    const route = `/api/v1/maintenance-sessions/${sessionId}`
    const res = await $get<IGetMaintenanceSessionResponse>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async getSessionLogs(sessionId: number, cancelToken?: CancelToken) : Promise<IGetMaintenanceSessionLogResponse[]>
  {
    const route = `/api/v1/maintenance-sessions/${sessionId}/logs`
    const res = await $get<IGetMaintenanceSessionLogResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async getOldSessionLogs(sessionId: number, cancelToken?: CancelToken) : Promise<IGetMaintenanceSessionLogResponse[]>
  {
    const route = `/api/v1/maintenance-sessions/${sessionId}/old-logs`
    const res = await $get<IGetMaintenanceSessionLogResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async getSessionPhases(sessionId: number, cancelToken?: CancelToken) : Promise<IGetMaintenanceSessionPhaseResponse[]>
  {
    const route = `/api/v1/maintenance-sessions/${sessionId}/phases`
    const res = await $get<IGetMaintenanceSessionPhaseResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async getLastSessionLog(sessionId: number, cancelToken?: CancelToken) : Promise<IGetMaintenanceSessionLogResponse>
  {
    const route = `/api/v1/maintenance-sessions/${sessionId}/last-log`
    const res = await $get<IGetMaintenanceSessionLogResponse>(route, {cancelToken});
    return res.data;
  }
  public async cancelSession(sessionId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/maintenance-sessions/${sessionId}/cancel`
    await $get(route, {cancelToken});
  }
  public async cancelAllSessions(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/maintenance-sessions/cancel-all`
    await $post(route, undefined, {cancelToken});
  }
  public async cancelSessions(body: ICancelSessionsRequestBody, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/maintenance-sessions/cancel`
    await $post(route, body, {cancelToken});
  }
  public async rerunSession(sessionId: number, cancelToken?: CancelToken) : Promise<number>
  {
    const route = `/api/v1/maintenance-sessions/${sessionId}/rerun`
    const res = await $post<number>(route, undefined, {cancelToken});
    return res.data;
  }
  public async rerunSessions(body: IRerunSessionsRequestBody, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/maintenance-sessions/rerun`
    await $post(route, body, {cancelToken});
  }
  public async resumeSession(sessionId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/maintenance-sessions/${sessionId}/resume`
    await $post(route, undefined, {cancelToken});
  }
  public async cancelSessionsForSchedule(scheduleId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/maintenance-sessions/cancel-for-schedule/${scheduleId}`
    await $get(route, {cancelToken});
  }
  public async getSessionStatusCounts(cancelToken?: CancelToken) : Promise<IGetSessionStatusCountsPayload>
  {
    const route = `/api/v1/maintenance-sessions/status-counts`
    const res = await $get<IGetSessionStatusCountsPayload>(route, {cancelToken});
    return res.data;
  }
  public async rerunAction(sessionId: number, actionId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/maintenance-sessions/${sessionId}/actions/${actionId}/rerun`
    await $post(route, undefined, {cancelToken});
  }
}
