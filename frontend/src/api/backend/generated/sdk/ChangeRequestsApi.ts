//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IChangeRequestCommentResponse } from '../responses';
import { IAddChangeRequestCommentBody } from '../requests';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ChangeRequestsController */
export class ChangeRequestsApi
{
  public async deleteChangeRequest(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/change-requests/${id}`
    await $delete(route, {cancelToken});
  }
  public async approveChangeRequest(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/change-requests/${id}/approve`
    await $post(route, undefined, {cancelToken});
  }
  public async denyChangeRequest(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/change-requests/${id}/deny`
    await $post(route, undefined, {cancelToken});
  }
  public async requireChanges(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/change-requests/${id}/require-changes`
    await $post(route, undefined, {cancelToken});
  }
  public async commentOnChangeRequest(id: number, body: IAddChangeRequestCommentBody, cancelToken?: CancelToken) : Promise<IChangeRequestCommentResponse>
  {
    const route = `/api/v1/change-requests/${id}/comment`
    const res = await $post<IChangeRequestCommentResponse>(route, body, {cancelToken});
    return res.data;
  }
  public getAllDx() : CustomStore
  {
    const route = `/api/v1/change-requests/dx`
    return createStore(route);
  }
  public async getOpenCount(cancelToken?: CancelToken) : Promise<number>
  {
    const route = `/api/v1/change-requests/open-count`
    const res = await $get<number>(route, {cancelToken});
    return res.data;
  }
}
