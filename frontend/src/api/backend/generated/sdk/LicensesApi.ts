//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';
import { IGetLicenseResponse } from '../responses';
import { IUpdateLicenseRequestBody } from '../requests';
import { ICreateLicenseRequestBody } from '../requests';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.LicensesController */
export class LicensesApi
{
  public dxGet() : CustomStore
  {
    const route = `/api/v1/licenses/dx`
    return createStore(route);
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.filters - Defaults to null in the controller
  */
  public async getAll(queries: {filters?: string} = {}, cancelToken?: CancelToken) : Promise<IGetLicenseResponse[]>
  {
    const route = `/api/v1/licenses`
    const res = await $get<IGetLicenseResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async get(licenseId: number, cancelToken?: CancelToken) : Promise<IGetLicenseResponse>
  {
    const route = `/api/v1/licenses/${licenseId}`
    const res = await $get<IGetLicenseResponse>(route, {cancelToken});
    return res.data;
  }
  public async update(licenseId: number, body: IUpdateLicenseRequestBody, cancelToken?: CancelToken) : Promise<IGetLicenseResponse>
  {
    const route = `/api/v1/licenses/${licenseId}`
    const res = await $put<IGetLicenseResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async create(body: ICreateLicenseRequestBody, cancelToken?: CancelToken) : Promise<IGetLicenseResponse>
  {
    const route = `/api/v1/licenses`
    const res = await $post<IGetLicenseResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async delete(licenseId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/licenses/${licenseId}`
    await $delete(route, {cancelToken});
  }
  public async upload(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/licenses/upload`
    await $post(route, undefined, {cancelToken});
  }
  public async getDownloadUrl(licenseId: number, cancelToken?: CancelToken) : Promise<string>
  {
    const route = `/api/v1/licenses/${licenseId}/request-download`
    const res = await $get<string>(route, {cancelToken});
    return res.data;
  }
}
