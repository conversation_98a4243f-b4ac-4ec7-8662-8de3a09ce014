//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IMigrationPreviewResponse } from '../interfaces';
import { IGetLocalSoftwareResponse } from '../responses';
import { ICreateLocalSoftwareRequestBody } from '../requests';
import { IUpdateLocalSoftwareRequestBody } from '../requests';
import { IGetGlobalSoftwareResponse } from '../responses';
import { ICreateGlobalSoftwareRequestBody } from '../requests';
import { IUpdateGlobalSoftwareRequestBody } from '../requests';
import { IGetLocalSoftwareVersionResponse } from '../responses';
import { ICreateLocalSoftwareVersionRequestBody } from '../requests';
import { IUpdateLocalSoftwareVersionRequestBody } from '../requests';
import { ISoftwareFileUploadData } from '../interfaces';
import { IFastCreateLocalVersionRequestBody } from '../requests';
import { IAnalyzeLocalSoftwarePackageResponse } from '../responses';
import { IAnalyzePackageParams } from '../requests';
import { IGetGlobalSoftwareVersionResponse } from '../responses';
import { ICreateGlobalSoftwareVersionRequestBody } from '../requests';
import { IUpdateGlobalSoftwareVersionRequestBody } from '../requests';
import { IFastCreateGlobalVersionRequestBody } from '../requests';
import { IAnalyzeGlobalSoftwarePackageResponse } from '../responses';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.SoftwareController */
export class SoftwareApi
{
  public async migrateLocalToGlobalWhatIf(softwareIdentifier: number, cancelToken?: CancelToken) : Promise<IMigrationPreviewResponse>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}/migrate-local-to-global-what-if`
    const res = await $get<IMigrationPreviewResponse>(route, {cancelToken});
    return res.data;
  }
  public async migrateLocalToGlobal(softwareIdentifier: number, cancelToken?: CancelToken) : Promise<number>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}/migrate-local-to-global`
    const res = await $post<number>(route, undefined, {cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.include - Defaults to null in the controller
  */
  public async getAllLocalSoftware(queries: {include?: string} = {}, cancelToken?: CancelToken) : Promise<IGetLocalSoftwareResponse[]>
  {
    const route = `/api/v1/software/local`
    const res = await $get<IGetLocalSoftwareResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.include - Defaults to null in the controller
  */
  public async getLocalSoftware(softwareIdentifier: string, queries: {include?: string} = {}, cancelToken?: CancelToken) : Promise<IGetLocalSoftwareResponse>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}`
    const res = await $get<IGetLocalSoftwareResponse>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async createLocalSoftware(requestBody: ICreateLocalSoftwareRequestBody, cancelToken?: CancelToken) : Promise<IGetLocalSoftwareResponse>
  {
    const route = `/api/v1/software/local`
    const res = await $post<IGetLocalSoftwareResponse>(route, requestBody, {cancelToken});
    return res.data;
  }
  public async updateLocalSoftware(softwareIdentifier: string, requestBody: IUpdateLocalSoftwareRequestBody, cancelToken?: CancelToken) : Promise<IGetLocalSoftwareResponse>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}`
    const res = await $patch<IGetLocalSoftwareResponse>(route, requestBody, {cancelToken});
    return res.data;
  }
  public async deleteLocalSoftware(softwareIdentifier: string, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}`
    await $delete(route, {cancelToken});
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.include - Defaults to null in the controller
  */
  public async getAllGlobalSoftware(queries: {include?: string} = {}, cancelToken?: CancelToken) : Promise<IGetGlobalSoftwareResponse[]>
  {
    const route = `/api/v1/software/global`
    const res = await $get<IGetGlobalSoftwareResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.include - Defaults to null in the controller
  */
  public async getGlobalSoftware(softwareIdentifier: string, queries: {include?: string} = {}, cancelToken?: CancelToken) : Promise<IGetGlobalSoftwareResponse>
  {
    const route = `/api/v1/software/global/${softwareIdentifier}`
    const res = await $get<IGetGlobalSoftwareResponse>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async createGlobalSoftware(requestBody: ICreateGlobalSoftwareRequestBody, cancelToken?: CancelToken) : Promise<IGetGlobalSoftwareResponse>
  {
    const route = `/api/v1/software/global`
    const res = await $post<IGetGlobalSoftwareResponse>(route, requestBody, {cancelToken});
    return res.data;
  }
  public async updateGlobalSoftware(softwareIdentifier: string, requestBody: IUpdateGlobalSoftwareRequestBody, cancelToken?: CancelToken) : Promise<IGetGlobalSoftwareResponse>
  {
    const route = `/api/v1/software/global/${softwareIdentifier}`
    const res = await $patch<IGetGlobalSoftwareResponse>(route, requestBody, {cancelToken});
    return res.data;
  }
  public async deleteGlobalSoftware(softwareIdentifier: string, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/software/global/${softwareIdentifier}`
    await $delete(route, {cancelToken});
  }
  public async getLocalSoftwareVersions(softwareIdentifier: string, cancelToken?: CancelToken) : Promise<IGetLocalSoftwareVersionResponse[]>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}/versions`
    const res = await $get<IGetLocalSoftwareVersionResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async getLocalSoftwareVersion(softwareIdentifier: string, semanticVersion: string, cancelToken?: CancelToken) : Promise<IGetLocalSoftwareVersionResponse>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}/versions/${semanticVersion}`
    const res = await $get<IGetLocalSoftwareVersionResponse>(route, {cancelToken});
    return res.data;
  }
  public async getLatestVersionForLocalSoftware(softwareIdentifier: string, cancelToken?: CancelToken) : Promise<IGetLocalSoftwareVersionResponse>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}/latest`
    const res = await $get<IGetLocalSoftwareVersionResponse>(route, {cancelToken});
    return res.data;
  }
  public async createLocalSoftwareVersion(softwareIdentifier: string, requestBody: ICreateLocalSoftwareVersionRequestBody, cancelToken?: CancelToken) : Promise<IGetLocalSoftwareVersionResponse>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}/versions`
    const res = await $post<IGetLocalSoftwareVersionResponse>(route, requestBody, {cancelToken});
    return res.data;
  }
  public async updateLocalSoftwareVersion(softwareIdentifier: string, semanticVersion: string, requestBody: IUpdateLocalSoftwareVersionRequestBody, cancelToken?: CancelToken) : Promise<IGetLocalSoftwareVersionResponse>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}/versions/${semanticVersion}`
    const res = await $patch<IGetLocalSoftwareVersionResponse>(route, requestBody, {cancelToken});
    return res.data;
  }
  public async uploadLocalSoftwareVersionFile(cancelToken?: CancelToken) : Promise<ISoftwareFileUploadData>
  {
    const route = `/api/v1/software/local/upload`
    const res = await $post<ISoftwareFileUploadData>(route, undefined, {cancelToken});
    return res.data;
  }
  public async getDownloadUrlForLocalSoftwareVersion(softwareIdentifier: string, semanticVersion: string, cancelToken?: CancelToken) : Promise<string>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}/versions/${semanticVersion}/request-download`
    const res = await $get<string>(route, {cancelToken});
    return res.data;
  }
  public async fastCreateLocalVersion(body: IFastCreateLocalVersionRequestBody, cancelToken?: CancelToken) : Promise<IGetLocalSoftwareResponse>
  {
    const route = `/api/v1/software/local/fast-create`
    const res = await $post<IGetLocalSoftwareResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async deleteLocalSoftwareVersion(softwareIdentifier: string, semanticVersion: string, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/software/local/${softwareIdentifier}/versions/${semanticVersion}`
    await $delete(route, {cancelToken});
  }
  public async analyzeLocalPackage(packageParams: IAnalyzePackageParams, cancelToken?: CancelToken) : Promise<IAnalyzeLocalSoftwarePackageResponse>
  {
    const route = `/api/v1/software/local/analyze`
    const res = await $post<IAnalyzeLocalSoftwarePackageResponse>(route, packageParams, {cancelToken});
    return res.data;
  }
  public async getGlobalSoftwareVersions(softwareIdentifier: string, cancelToken?: CancelToken) : Promise<IGetGlobalSoftwareVersionResponse>
  {
    const route = `/api/v1/software/global/${softwareIdentifier}/versions`
    const res = await $get<IGetGlobalSoftwareVersionResponse>(route, {cancelToken});
    return res.data;
  }
  public async getGlobalSoftwareVersion(softwareIdentifier: string, semanticVersion: string, cancelToken?: CancelToken) : Promise<IGetGlobalSoftwareVersionResponse>
  {
    const route = `/api/v1/software/global/${softwareIdentifier}/versions/${semanticVersion}`
    const res = await $get<IGetGlobalSoftwareVersionResponse>(route, {cancelToken});
    return res.data;
  }
  public async getLatestVersionForGlobalSoftware(softwareIdentifier: string, cancelToken?: CancelToken) : Promise<IGetGlobalSoftwareVersionResponse>
  {
    const route = `/api/v1/software/global/${softwareIdentifier}/latest`
    const res = await $get<IGetGlobalSoftwareVersionResponse>(route, {cancelToken});
    return res.data;
  }
  public async createGlobalSoftwareVersion(softwareIdentifier: string, requestBody: ICreateGlobalSoftwareVersionRequestBody, cancelToken?: CancelToken) : Promise<IGetGlobalSoftwareVersionResponse>
  {
    const route = `/api/v1/software/global/${softwareIdentifier}/versions`
    const res = await $post<IGetGlobalSoftwareVersionResponse>(route, requestBody, {cancelToken});
    return res.data;
  }
  public async updateGlobalSoftwareVersion(softwareIdentifier: string, semanticVersion: string, requestBody: IUpdateGlobalSoftwareVersionRequestBody, cancelToken?: CancelToken) : Promise<IGetGlobalSoftwareVersionResponse>
  {
    const route = `/api/v1/software/global/${softwareIdentifier}/versions/${semanticVersion}`
    const res = await $patch<IGetGlobalSoftwareVersionResponse>(route, requestBody, {cancelToken});
    return res.data;
  }
  public async uploadGlobalSoftwareVersionFile(cancelToken?: CancelToken) : Promise<ISoftwareFileUploadData>
  {
    const route = `/api/v1/software/global/upload`
    const res = await $post<ISoftwareFileUploadData>(route, undefined, {cancelToken});
    return res.data;
  }
  public async getDownloadUrlForGlobalSoftwareVersion(softwareIdentifier: string, semanticVersion: string, cancelToken?: CancelToken) : Promise<string>
  {
    const route = `/api/v1/software/global/${softwareIdentifier}/versions/${semanticVersion}/request-download`
    const res = await $get<string>(route, {cancelToken});
    return res.data;
  }
  public async fastCreateGlobalVersion(body: IFastCreateGlobalVersionRequestBody, cancelToken?: CancelToken) : Promise<IGetGlobalSoftwareResponse>
  {
    const route = `/api/v1/software/global/fast-create`
    const res = await $post<IGetGlobalSoftwareResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async deleteGlobalSoftwareVersion(softwareIdentifier: string, semanticVersion: string, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/software/global/${softwareIdentifier}/versions/${semanticVersion}`
    await $delete(route, {cancelToken});
  }
  public async analyzeGlobalPackage(packageParams: IAnalyzePackageParams, cancelToken?: CancelToken) : Promise<IAnalyzeGlobalSoftwarePackageResponse>
  {
    const route = `/api/v1/software/global/analyze`
    const res = await $post<IAnalyzeGlobalSoftwarePackageResponse>(route, packageParams, {cancelToken});
    return res.data;
  }
}
