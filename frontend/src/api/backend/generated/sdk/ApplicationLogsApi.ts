//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IUpdateApplicationLogSourceContextRequest } from '../requests';
import { IClearApplicationLogSourceContextRequest } from '../requests';
import { IToggleApplicationLogStreamingRequest } from '../requests';
import { IGetSourceContextsResponse } from '../responses';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ApplicationLogsController */
export class ApplicationLogsApi
{
  public async updateSourceContext(request: IUpdateApplicationLogSourceContextRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/application-logs/source-context`
    await $post(route, request, {cancelToken});
  }
  public async clearSourceContext(request: IClearApplicationLogSourceContextRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/application-logs/source-context/clear`
    await $post(route, request, {cancelToken});
  }
  public async clearAllSourceContexts(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/application-logs/source-context/clear-all`
    await $post(route, undefined, {cancelToken});
  }
  public async toggleStreaming(request: IToggleApplicationLogStreamingRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/application-logs/streaming`
    await $post(route, request, {cancelToken});
  }
  public async getSourceContexts(cancelToken?: CancelToken) : Promise<IGetSourceContextsResponse>
  {
    const route = `/api/v1/application-logs/source-contexts`
    const res = await $get<IGetSourceContextsResponse>(route, {cancelToken});
    return res.data;
  }
}
