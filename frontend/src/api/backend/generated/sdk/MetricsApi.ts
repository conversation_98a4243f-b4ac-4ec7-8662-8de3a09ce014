//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IMetricData } from '../interfaces';
import { ICircuitBreakerState } from '../responses';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.MetricsController */
export class MetricsApi
{
  public async getAppMetrics(cancelToken?: CancelToken) : Promise<IMetricData>
  {
    const route = `/api/v1/metrics/app`
    const res = await $get<IMetricData>(route, {cancelToken});
    return res.data;
  }
  public async getCircuitBreakers(cancelToken?: CancelToken) : Promise<ICircuitBreakerState[]>
  {
    const route = `/api/v1/metrics/circuit-breakers`
    const res = await $get<ICircuitBreakerState[]>(route, {cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.policyName
  */
  public async isolateCircuitBreaker(queries: {policyName: string}, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/metrics/circuit-breakers/isolate`
    await $post(route, undefined, {params: queries, cancelToken});
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.policyName
  */
  public async resetCircuitBreaker(queries: {policyName: string}, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/metrics/circuit-breakers/reset`
    await $post(route, undefined, {params: queries, cancelToken});
  }
}
