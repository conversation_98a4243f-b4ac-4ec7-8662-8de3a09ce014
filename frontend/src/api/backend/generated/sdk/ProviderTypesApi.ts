//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IDynamicFormBindResult } from '../interfaces';
import { IIntegrationBindParametersRequest } from '../requests';
import { IProviderTypeDto } from '../interfaces';
import { IDeviceGroup } from '../interfaces';
import { IClientGroup } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ProviderTypesController */
export class ProviderTypesApi
{
  public async bindParameters(providerType: string, request: IIntegrationBindParametersRequest, cancelToken?: CancelToken) : Promise<IDynamicFormBindResult>
  {
    const route = `/api/v1/provider-types/${providerType}/bind-parameters`
    const res = await $post<IDynamicFormBindResult>(route, request, {cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {boolean} queries.includeLinkFormSchemas - Defaults to False in the controller
  */
  public async getAllProviderTypes(queries: {includeLinkFormSchemas?: boolean} = {}, cancelToken?: CancelToken) : Promise<IProviderTypeDto[]>
  {
    const route = `/api/v1/provider-types`
    const res = await $get<IProviderTypeDto[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {number} queries.providerLinkId
  * @param {string} queries.externalClientId - Defaults to null in the controller
  */
  public async getDeviceGroups(deviceGroupTypeId: string, queries: {providerLinkId: number, externalClientId?: string}, cancelToken?: CancelToken) : Promise<IDeviceGroup[]>
  {
    const route = `/api/v1/provider-types/device-group-types/${deviceGroupTypeId}/device-groups`
    const res = await $get<IDeviceGroup[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {number} queries.providerLinkId
  * @param {number} queries.tenantId - Defaults to null in the controller
  */
  public async getClientGroups(clientGroupTypeId: string, queries: {providerLinkId: number, tenantId?: number}, cancelToken?: CancelToken) : Promise<IClientGroup[]>
  {
    const route = `/api/v1/provider-types/client-group-types/${clientGroupTypeId}/client-groups`
    const res = await $get<IClientGroup[]>(route, {params: queries, cancelToken});
    return res.data;
  }
}
