//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetPreferencesResponse } from '../responses';
import { ITenantPreferences } from '../interfaces';
import { IApplicationPreferences } from '../interfaces';
import { IOperation } from '../interfaces';
import { IUserPreferences } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.PreferencesController */
export class PreferencesApi
{
  public async getPreferences(cancelToken?: CancelToken) : Promise<IGetPreferencesResponse>
  {
    const route = `/api/v1/preferences`
    const res = await $get<IGetPreferencesResponse>(route, {cancelToken});
    return res.data;
  }
  public async getTenantPreferences(tenantId: number, cancelToken?: CancelToken) : Promise<ITenantPreferences>
  {
    const route = `/api/v1/preferences/tenants/${tenantId}`
    const res = await $get<ITenantPreferences>(route, {cancelToken});
    return res.data;
  }
  public async updateAppPreferences(preferencePatch: IOperation<IApplicationPreferences>[], cancelToken?: CancelToken) : Promise<IApplicationPreferences>
  {
    const route = `/api/v1/preferences/application`
    const res = await $patch<IApplicationPreferences>(route, preferencePatch, {cancelToken});
    return res.data;
  }
  public async updateTenantPreferences(tenantId: number, preferencePatch: IOperation<ITenantPreferences>[], cancelToken?: CancelToken) : Promise<ITenantPreferences>
  {
    const route = `/api/v1/preferences/tenants/${tenantId}`
    const res = await $patch<ITenantPreferences>(route, preferencePatch, {cancelToken});
    return res.data;
  }
  public async updateUserPreferences(preferencePatch: IOperation<IUserPreferences>[], cancelToken?: CancelToken) : Promise<IUserPreferences>
  {
    const route = `/api/v1/preferences/my`
    const res = await $patch<IUserPreferences>(route, preferencePatch, {cancelToken});
    return res.data;
  }
}
