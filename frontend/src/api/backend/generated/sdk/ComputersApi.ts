//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IComputerAgentStatusDto } from '../interfaces';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';
import { IDxComputerInventoryScriptResult } from '../responses';
import { IDetectedComputerSoftwareResponse } from '../responses';
import { IGetEphemeralAgentResponse } from '../responses';
import { ICircuitBreakerState2 } from '../responses';
import { IOpResult } from '../interfaces';
import { IComputerSearch } from '../interfaces';
import { IComputerPageResponse } from '../responses';
import { ISetExcludedFromUserAffinityRequestBody } from '../requests';
import { IBatchSetExcludedFromUserAffinityRequestBody } from '../requests';
import { IGetComputerResponse } from '../responses';
import { IComputerUserAffinityResponse } from '../interfaces';
import { IGetComputerDeviceUpdateFormDataResponse } from '../responses';
import { IGetOnboardingComputerResponse } from '../responses';
import { IGetPersonResponse } from '../responses';
import { IUpdateComputerPrimaryPersonRequestBody } from '../requests';
import { IUpdateComputerAdditionalPersonsRequestBody } from '../requests';
import { IUpdateComputerRequestBody } from '../requests';
import { ICommandResult } from '../interfaces';
import { IChangeTenantsPayload } from '../interfaces';
import { ISkipOnboardingResponse } from '../responses';
import { ISkipOnboardingRequest } from '../requests';
import { IInventoryDeviceCmdResponse } from '../interfaces';
import { IResolveOnboardingOverridableAssignmentsResponseBody } from '../responses';
import { IBulkDeleteResponse } from '../responses';
import { IBulkDeleteRequest } from '../requests';
import { IMyComputerResponse } from '../responses';
import { IComputerInventorySoftware } from '../interfaces';
import { SoftwareTableNameSearchMode } from '../enums';
import { IComputerInventoryAllSoftware } from '../interfaces';
import { ITimelineEvent } from '../interfaces';
import { IAddTagsRequest } from '../requests';
import { IRemoveTagsRequest } from '../requests';
import { IExcludeFromMaintenanceRequest } from '../requests';
import { IOpResult2 } from '../interfaces';
import { IRegistryKeyDto } from '../interfaces';
import { IRegistryPayload } from '../requests';
import { IRegistryValueDto } from '../interfaces';
import { IParentTenantInfo } from '../interfaces';
import { IUpdateNotesPayload } from '../requests';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ComputersController */
export class ComputersApi
{
  public getComputerAgentStatusReport(queries: {tenantId?: number} = {}) : CustomStore<IComputerAgentStatusDto>
  {
    const route = `/api/v1/computers/agent-status`
    return createStore(route, { queries });
  }
  public computerInventoryDx() : CustomStore<IDxComputerInventoryScriptResult>
  {
    const route = `/api/v1/computers/inventory`
    return createStore(route);
  }
  public exportComputerInventory() : CustomStore
  {
    const route = `/api/v1/computers/inventory/export`
    return createStore(route);
  }
  public async getSoftwareFromInventoryDx(computerId: number, cancelToken?: CancelToken) : Promise<IDetectedComputerSoftwareResponse[]>
  {
    const route = `/api/v1/computers/${computerId}/detected-computer-software`
    const res = await $get<IDetectedComputerSoftwareResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async getEphemeralAgent(computerId: number, cancelToken?: CancelToken) : Promise<IGetEphemeralAgentResponse>
  {
    const route = `/api/v1/computers/${computerId}/ephemeral-agent`
    const res = await $get<IGetEphemeralAgentResponse>(route, {cancelToken});
    return res.data;
  }
  public async deleteEphemeralAgent(computerId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/computers/${computerId}/ephemeral-agent`
    await $delete(route, {cancelToken});
  }
  public async getEphemeralAgentCircuitBreaker(computerId: number, cancelToken?: CancelToken) : Promise<ICircuitBreakerState2<IOpResult>>
  {
    const route = `/api/v1/computers/${computerId}/ephemeral-agent-circuit-breaker`
    const res = await $get<ICircuitBreakerState2<IOpResult>>(route, {cancelToken});
    return res.data;
  }
  public async resetEphemeralAgentCircuitBreaker(computerId: number, cancelToken?: CancelToken) : Promise<ICircuitBreakerState2<IOpResult>>
  {
    const route = `/api/v1/computers/${computerId}/ephemeral-agent-circuit-breaker/reset`
    const res = await $post<ICircuitBreakerState2<IOpResult>>(route, undefined, {cancelToken});
    return res.data;
  }
  public async restoreComputers(computerIds: number[], cancelToken?: CancelToken) : Promise<boolean>
  {
    const route = `/api/v1/computers/restore`
    const res = await $post<boolean>(route, computerIds, {cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.name - Defaults to null in the controller
  * @param {number} queries.tenantId - Defaults to null in the controller
  * @param {boolean} queries.orderByUpdatedDate - Defaults to False in the controller
  * @param {number} queries.pageSize - Defaults to 25 in the controller
  */
  public async getAll(queries: {name?: string, tenantId?: number, orderByUpdatedDate?: boolean, pageSize?: number} = {}, cancelToken?: CancelToken) : Promise<IComputerSearch[]>
  {
    const route = `/api/v1/computers`
    const res = await $get<IComputerSearch[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.filter - Defaults to null in the controller
  * @param {number} queries.skip - Defaults to null in the controller
  * @param {string} queries.sort - Defaults to null in the controller
  * @param {number} queries.take - Defaults to 50 in the controller
  * @param {boolean} queries.sortDesc - Defaults to True in the controller
  * @param {boolean} queries.onboardingOnly - Defaults to False in the controller
  * @param {boolean} queries.staleOnly - Defaults to False in the controller
  * @param {boolean} queries.devLabOnly - Defaults to False in the controller
  * @param {boolean} queries.includeOffline - Defaults to True in the controller
  * @param {number} queries.tenantId - Defaults to null in the controller
  * @param {boolean} queries.licensedOnly - Defaults to False in the controller
  * @param {boolean} queries.deletedOnly - Defaults to False in the controller
  */
  public async getAllPaged(queries: {filter?: string, skip?: number, sort?: string, take?: number, sortDesc?: boolean, onboardingOnly?: boolean, staleOnly?: boolean, devLabOnly?: boolean, includeOffline?: boolean, tenantId?: number, licensedOnly?: boolean, deletedOnly?: boolean} = {}, cancelToken?: CancelToken) : Promise<IComputerPageResponse>
  {
    const route = `/api/v1/computers/paged`
    const res = await $get<IComputerPageResponse>(route, {params: queries, cancelToken});
    return res.data;
  }
  public dxGet() : CustomStore
  {
    const route = `/api/v1/computers/dx`
    return createStore(route);
  }
  public async setExcludedFromUserAffinity(computerId: number, request: ISetExcludedFromUserAffinityRequestBody, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/computers/${computerId}/exclude-from-user-affinity`
    await $post(route, request, {cancelToken});
  }
  public async setExcludedFromUserAffinityBatch(request: IBatchSetExcludedFromUserAffinityRequestBody, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/computers/set-excluded-from-user-affinity`
    await $post(route, request, {cancelToken});
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {boolean} queries.includeSessions - Defaults to False in the controller
  * @param {boolean} queries.includeAdditionalPersons - Defaults to True in the controller
  * @param {boolean} queries.includePrimaryPerson - Defaults to True in the controller
  * @param {boolean} queries.includeProviderAgents - Defaults to False in the controller
  * @param {boolean} queries.includeProviderAgentsDeviceUpdateFormData - Defaults to False in the controller
  */
  public async get(computerId: number, queries: {includeSessions?: boolean, includeAdditionalPersons?: boolean, includePrimaryPerson?: boolean, includeProviderAgents?: boolean, includeProviderAgentsDeviceUpdateFormData?: boolean} = {}, cancelToken?: CancelToken) : Promise<IGetComputerResponse>
  {
    const route = `/api/v1/computers/${computerId}`
    const res = await $get<IGetComputerResponse>(route, {params: queries, cancelToken});
    return res.data;
  }
  public exportUserAffinities() : CustomStore
  {
    const route = `/api/v1/computers/user-affinities/export`
    return createStore(route);
  }
  public getUserAffinitiesDx(queries: {computerId?: number} = {}) : CustomStore<IComputerUserAffinityResponse>
  {
    const route = `/api/v1/computers/user-affinities`
    return createStore(route, { queries });
  }
  public async getInventoryScriptResult(computerId: number, inventoryKey: string, cancelToken?: CancelToken) : Promise<any>
  {
    const route = `/api/v1/computers/${computerId}/inventory-script-results/${inventoryKey}`
    const res = await $get<any>(route, {cancelToken});
    return res.data;
  }
  public async getDeviceUpdateFormData(computerId: number, cancelToken?: CancelToken) : Promise<IGetComputerDeviceUpdateFormDataResponse>
  {
    const route = `/api/v1/computers/${computerId}/device-update-form-data`
    const res = await $get<IGetComputerDeviceUpdateFormDataResponse>(route, {cancelToken});
    return res.data;
  }
  public async getOnboarding(cancelToken?: CancelToken) : Promise<IGetOnboardingComputerResponse[]>
  {
    const route = `/api/v1/computers/onboarding`
    const res = await $get<IGetOnboardingComputerResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async getComputerOnlineStatus(computerId: number, cancelToken?: CancelToken) : Promise<boolean>
  {
    const route = `/api/v1/computers/${computerId}/status`
    const res = await $get<boolean>(route, {cancelToken});
    return res.data;
  }
  public async updatePrimaryPerson(computerId: number, viewModel: IUpdateComputerPrimaryPersonRequestBody, cancelToken?: CancelToken) : Promise<IGetPersonResponse>
  {
    const route = `/api/v1/computers/${computerId}/update-primary-person`
    const res = await $post<IGetPersonResponse>(route, viewModel, {cancelToken});
    return res.data;
  }
  public async updateAdditionalPersons(computerId: number, viewModel: IUpdateComputerAdditionalPersonsRequestBody, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/computers/${computerId}/update-additional-persons`
    await $post(route, viewModel, {cancelToken});
  }
  public async updateComputer(computerId: number, viewModel: IUpdateComputerRequestBody, cancelToken?: CancelToken) : Promise<IGetComputerResponse>
  {
    const route = `/api/v1/computers/${computerId}`
    const res = await $put<IGetComputerResponse>(route, viewModel, {cancelToken});
    return res.data;
  }
  public async setToNeedsOnboarding(computerId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/computers/${computerId}/set-to-needs-onboarding`
    await $post(route, undefined, {cancelToken});
  }
  public async changeTenant(request: IChangeTenantsPayload, cancelToken?: CancelToken) : Promise<ICommandResult>
  {
    const route = `/api/v1/computers/change-tenant`
    const res = await $post<ICommandResult>(route, request, {cancelToken});
    return res.data;
  }
  public async skipOnboarding(request: ISkipOnboardingRequest[], cancelToken?: CancelToken) : Promise<ISkipOnboardingResponse[]>
  {
    const route = `/api/v1/computers/skip-onboarding`
    const res = await $post<ISkipOnboardingResponse[]>(route, request, {cancelToken});
    return res.data;
  }
  public async getScreenShareUrl(computerId: number, providerLinkId: number, cancelToken?: CancelToken) : Promise<string>
  {
    const route = `/api/v1/computers/${computerId}/provider-links/${providerLinkId}/screen-share-url`
    const res = await $get<string>(route, {cancelToken});
    return res.data;
  }
  public async reinventoryComputer(computerId: number, cancelToken?: CancelToken) : Promise<IInventoryDeviceCmdResponse>
  {
    const route = `/api/v1/computers/${computerId}/reinventory`
    const res = await $post<IInventoryDeviceCmdResponse>(route, undefined, {cancelToken});
    return res.data;
  }
  public async resolveOnboardingOverridableTargetAssignments(computerId: number, cancelToken?: CancelToken) : Promise<IResolveOnboardingOverridableAssignmentsResponseBody>
  {
    const route = `/api/v1/computers/${computerId}/resolve-onboarding-overridable-target-assignments`
    const res = await $get<IResolveOnboardingOverridableAssignmentsResponseBody>(route, {cancelToken});
    return res.data;
  }
  public async deleteComputers(req: IBulkDeleteRequest, cancelToken?: CancelToken) : Promise<IBulkDeleteResponse>
  {
    const route = `/api/v1/computers/bulk-delete`
    const res = await $post<IBulkDeleteResponse>(route, req, {cancelToken});
    return res.data;
  }
  public async getMyComputers(cancelToken?: CancelToken) : Promise<IMyComputerResponse[]>
  {
    const route = `/api/v1/computers/my`
    const res = await $get<IMyComputerResponse[]>(route, {cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.q
  */
  public async searchInventorySoftwareByUpgradeCode(queries: {q: string}, cancelToken?: CancelToken) : Promise<IComputerInventorySoftware[]>
  {
    const route = `/api/v1/computers/inventory-software/search-by-upgrade-code`
    const res = await $get<IComputerInventorySoftware[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.q
  * @param {SoftwareTableNameSearchMode} queries.searchMode - Defaults to Contains in the controller
  */
  public async searchInventorySoftwareByName(queries: {q: string, searchMode?: SoftwareTableNameSearchMode}, cancelToken?: CancelToken) : Promise<IComputerInventorySoftware[]>
  {
    const route = `/api/v1/computers/inventory-software/search-by-name`
    const res = await $get<IComputerInventorySoftware[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.q
  * @param {number} queries.tenantId - Defaults to null in the controller
  * @param {SoftwareTableNameSearchMode} queries.searchMode - Defaults to Contains in the controller
  */
  public async searchAllInventorySoftwareByName(queries: {q: string, tenantId?: number, searchMode?: SoftwareTableNameSearchMode}, cancelToken?: CancelToken) : Promise<IComputerInventoryAllSoftware[]>
  {
    const route = `/api/v1/computers/all-inventory-software/search-by-name`
    const res = await $get<IComputerInventoryAllSoftware[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {number} queries.skip
  * @param {number} queries.take - Defaults to 25 in the controller
  */
  public async getComputerEvents(computerId: number, queries: {skip: number, take?: number}, cancelToken?: CancelToken) : Promise<ITimelineEvent[]>
  {
    const route = `/api/v1/computers/${computerId}/events`
    const res = await $get<ITimelineEvent[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async addTags(request: IAddTagsRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/computers/add-tags`
    await $post(route, request, {cancelToken});
  }
  public async removeTags(request: IRemoveTagsRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/computers/remove-tags`
    await $post(route, request, {cancelToken});
  }
  public async excludeFromMaintenance(computerId: number, req: IExcludeFromMaintenanceRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/computers/${computerId}/exclude-from-maintenance`
    await $post(route, req, {cancelToken});
  }
  public exportComputers() : CustomStore
  {
    const route = `/api/v1/computers/export`
    return createStore(route);
  }
  public async launchEphemeralAgent(computerId: number, cancelToken?: CancelToken) : Promise<IOpResult>
  {
    const route = `/api/v1/computers/${computerId}/launch-ephemeral-agent`
    const res = await $get<IOpResult>(route, {cancelToken});
    return res.data;
  }
  public async loadRegistryKeys(computerId: number, payload: IRegistryPayload, cancelToken?: CancelToken) : Promise<IOpResult2<IRegistryKeyDto[]>>
  {
    const route = `/api/v1/computers/${computerId}/registry/keys`
    const res = await $post<IOpResult2<IRegistryKeyDto[]>>(route, payload, {cancelToken});
    return res.data;
  }
  public async loadRegistryKeyValues(computerId: number, payload: IRegistryPayload, cancelToken?: CancelToken) : Promise<IOpResult2<IRegistryValueDto[]>>
  {
    const route = `/api/v1/computers/${computerId}/registry/values`
    const res = await $post<IOpResult2<IRegistryValueDto[]>>(route, payload, {cancelToken});
    return res.data;
  }
  public async getParentTenantInfo(computerId: number, cancelToken?: CancelToken) : Promise<IParentTenantInfo>
  {
    const route = `/api/v1/computers/${computerId}/parent-tenant-info`
    const res = await $get<IParentTenantInfo>(route, {cancelToken});
    return res.data;
  }
  public async updateNotes(computerId: number, request: IUpdateNotesPayload, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/computers/${computerId}/notes`
    await $post(route, request, {cancelToken});
  }
}
