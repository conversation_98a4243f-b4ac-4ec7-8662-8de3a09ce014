//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IInventoryTaskResource } from '../responses';
import { IInventoryTaskPayload } from '../requests';
import { IInventoryTaskScriptResource } from '../responses';
import { IInventoryTaskScriptPayload } from '../requests';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.InventoryTasksController */
export class InventoryTasksApi
{
  public async getAll(cancelToken?: CancelToken) : Promise<IInventoryTaskResource[]>
  {
    const route = `/api/v1/inventory-tasks`
    const res = await $get<IInventoryTaskResource[]>(route, {cancelToken});
    return res.data;
  }
  public async createLocal(payload: IInventoryTaskPayload, cancelToken?: CancelToken) : Promise<IInventoryTaskResource>
  {
    const route = `/api/v1/inventory-tasks/local`
    const res = await $post<IInventoryTaskResource>(route, payload, {cancelToken});
    return res.data;
  }
  public async updateLocal(id: number, payload: IInventoryTaskPayload, cancelToken?: CancelToken) : Promise<IInventoryTaskResource>
  {
    const route = `/api/v1/inventory-tasks/local/${id}`
    const res = await $post<IInventoryTaskResource>(route, payload, {cancelToken});
    return res.data;
  }
  public async deleteLocal(id: number, cancelToken?: CancelToken) : Promise<IInventoryTaskResource>
  {
    const route = `/api/v1/inventory-tasks/local/${id}`
    const res = await $delete<IInventoryTaskResource>(route, {cancelToken});
    return res.data;
  }
  public async addScriptToLocalInventoryTask(id: number, payload: IInventoryTaskScriptPayload, cancelToken?: CancelToken) : Promise<IInventoryTaskScriptResource>
  {
    const route = `/api/v1/inventory-tasks/local/${id}/scripts`
    const res = await $post<IInventoryTaskScriptResource>(route, payload, {cancelToken});
    return res.data;
  }
  public async deleteScriptFromLocalInventoryTask(taskId: number, inventoryKey: string, cancelToken?: CancelToken) : Promise<IInventoryTaskResource>
  {
    const route = `/api/v1/inventory-tasks/local/${taskId}/scripts/${inventoryKey}`
    const res = await $delete<IInventoryTaskResource>(route, {cancelToken});
    return res.data;
  }
}
