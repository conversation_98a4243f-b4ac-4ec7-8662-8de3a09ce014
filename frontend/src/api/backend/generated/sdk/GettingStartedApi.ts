//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGettingStartedChecklist } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.GettingStartedController */
export class GettingStartedApi
{
  public async checklist(cancelToken?: CancelToken) : Promise<IGettingStartedChecklist>
  {
    const route = `/api/v1/getting-started/checklist`
    const res = await $get<IGettingStartedChecklist>(route, {cancelToken});
    return res.data;
  }
  public async resetChecklist(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/getting-started/checklist/reset`
    await $post(route, undefined, {cancelToken});
  }
  public async completeChecklist(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/getting-started/checklist/complete`
    await $post(route, undefined, {cancelToken});
  }
}
