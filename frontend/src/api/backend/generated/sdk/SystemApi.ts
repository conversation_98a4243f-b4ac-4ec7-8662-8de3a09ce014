//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetReleasesResponse } from '../responses';
import { ITimeZoneResource } from '../responses';
import { IUpdateReleaseChannelRequest } from '../interfaces';
import { IMspInstanceImmySupportAccessGrantDetails } from '../interfaces';
import { IRequestSessionSupportRequestBody } from '../requests';
import { IRequestFormSupportBody } from '../requests';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.SystemController */
export class SystemApi
{
  public async getReleases(cancelToken?: CancelToken) : Promise<IGetReleasesResponse>
  {
    const route = `/api/v1/system/releases`
    const res = await $get<IGetReleasesResponse>(route, {cancelToken});
    return res.data;
  }
  public async updateInstance(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/system/pull-update`
    await $post(route, undefined, {cancelToken});
  }
  public async getTimezones(cancelToken?: CancelToken) : Promise<ITimeZoneResource[]>
  {
    const route = `/api/v1/system/timezones`
    const res = await $get<ITimeZoneResource[]>(route, {cancelToken});
    return res.data;
  }
  public async restartBackend(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/system/restart-backend`
    await $post(route, undefined, {cancelToken});
  }
  public async updateReleaseChannel(req: IUpdateReleaseChannelRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/system/update-release-channel`
    await $post(route, req, {cancelToken});
  }
  public async getImmySupportAccessGrantDetails(cancelToken?: CancelToken) : Promise<IMspInstanceImmySupportAccessGrantDetails>
  {
    const route = `/api/v1/system/immy-support-access-grant-details`
    const res = await $get<IMspInstanceImmySupportAccessGrantDetails>(route, {cancelToken});
    return res.data;
  }
  public async requestSessionSupport(body: IRequestSessionSupportRequestBody, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/system/request-session-support`
    await $post(route, body, {cancelToken});
  }
  public async requestFormSupport(body: IRequestFormSupportBody, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/system/request-form-support`
    await $post(route, body, {cancelToken});
  }
  public async isImmySupportAccessGranted(cancelToken?: CancelToken) : Promise<boolean>
  {
    const route = `/api/v1/system/is-immy-support-access-granted`
    const res = await $post<boolean>(route, undefined, {cancelToken});
    return res.data;
  }
  public async enableImmySupportAccess(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/system/enable-immy-support-access`
    await $post(route, undefined, {cancelToken});
  }
  public async disableImmySupportAccess(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/system/disable-immy-support-access`
    await $post(route, undefined, {cancelToken});
  }
  public async reset(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/system/reset`
    await $post(route, undefined, {cancelToken});
  }
}
