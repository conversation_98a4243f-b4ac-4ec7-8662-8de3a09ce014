//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetProviderAgentIdentificationLogResponse } from '../responses';
import { IBulkDeleteResponse } from '../responses';
import { IBulkDeleteRequest } from '../requests';
import { IGetPendingResponse } from '../responses';
import { ProviderAgentFilter } from '../enums';
import { IGetFailedPendingAgentResponse } from '../responses';
import { IResolveFailuresRequestBody } from '../requests';
import { AgentIdentificationManualResolutionDecision } from '../enums';
import { IGetPendingCountsResponse } from '../responses';
import { IOpResult } from '../interfaces';
import { IIdentifyAgentRequest } from '../requests';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ProviderAgentsController */
export class ProviderAgentsApi
{
  public async getIdentificationLogs(agentId: number, cancelToken?: CancelToken) : Promise<IGetProviderAgentIdentificationLogResponse[]>
  {
    const route = `/api/v1/provider-agents/${agentId}/identification-logs`
    const res = await $get<IGetProviderAgentIdentificationLogResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async bulkDeletePendingAgents(req: IBulkDeleteRequest, cancelToken?: CancelToken) : Promise<IBulkDeleteResponse>
  {
    const route = `/api/v1/provider-agents/bulk-delete-pending`
    const res = await $post<IBulkDeleteResponse>(route, req, {cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.filter - Defaults to null in the controller
  * @param {string} queries.sort - Defaults to null in the controller
  * @param {number} queries.take - Defaults to 50 in the controller
  * @param {number} queries.skip - Defaults to 0 in the controller
  * @param {boolean} queries.sortDesc - Defaults to True in the controller
  * @param {boolean} queries.includeOffline - Defaults to True in the controller
  * @param {number} queries.tenantId - Defaults to null in the controller
  * @param {number} queries.providerLinkId - Defaults to null in the controller
  * @param {ProviderAgentFilter} queries.agentFilter - Defaults to 2 in the controller
  */
  public async getPending(queries: {filter?: string, sort?: string, take?: number, skip?: number, sortDesc?: boolean, includeOffline?: boolean, tenantId?: number, providerLinkId?: number, agentFilter?: ProviderAgentFilter} = {}, cancelToken?: CancelToken) : Promise<IGetPendingResponse>
  {
    const route = `/api/v1/provider-agents/pending`
    const res = await $get<IGetPendingResponse>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async getPendingAgentConflictsForComputer(computerId: number, cancelToken?: CancelToken) : Promise<IGetFailedPendingAgentResponse[]>
  {
    const route = `/api/v1/provider-agents/${computerId}/pending-conflicts`
    const res = await $get<IGetFailedPendingAgentResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async resolveFailuresForAgents(body: IResolveFailuresRequestBody, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/provider-agents/resolve-failures`
    await $post(route, body, {cancelToken});
  }
  public async retryIdentification(agentId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/provider-agents/${agentId}/resolve-failures`
    await $post(route, undefined, {cancelToken});
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {AgentIdentificationManualResolutionDecision} queries.manualResolutionDecision
  */
  public async resolveFailure(failureId: number, queries: {manualResolutionDecision: AgentIdentificationManualResolutionDecision}, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/provider-agents/resolve-failure/${failureId}`
    await $post(route, undefined, {params: queries, cancelToken});
  }
  public async getPendingCounts(cancelToken?: CancelToken) : Promise<IGetPendingCountsResponse>
  {
    const route = `/api/v1/provider-agents/pending-counts`
    const res = await $get<IGetPendingCountsResponse>(route, {cancelToken});
    return res.data;
  }
  public async identifyAgents(request: IIdentifyAgentRequest, cancelToken?: CancelToken) : Promise<IOpResult>
  {
    const route = `/api/v1/provider-agents/identify`
    const res = await $post<IOpResult>(route, request, {cancelToken});
    return res.data;
  }
}
