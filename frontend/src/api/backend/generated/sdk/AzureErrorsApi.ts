//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IAzureErrorLogItem } from '../interfaces';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.AzureErrorsController */
export class AzureErrorsApi
{
  public getForTenantDx(tenantPrincipalId: string) : CustomStore<IAzureErrorLogItem>
  {
    const route = `/api/v1/azure-errors/for-tenant/${tenantPrincipalId}/dx`
    return createStore(route);
  }
  public getAllDx() : CustomStore<IAzureErrorLogItem>
  {
    const route = `/api/v1/azure-errors/dx`
    return createStore(route);
  }
}
