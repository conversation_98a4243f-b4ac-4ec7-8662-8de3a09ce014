//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IApplicationLocksResponse } from '../responses';
import { IEventStream } from '../responses';
import { IApplicationLockEvent } from '../responses';
import { IOpResult } from '../interfaces';
import { ICancelApplicationLockRequest } from '../requests';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ApplicationLocksController */
export class ApplicationLocksApi
{
  public async getAll(cancelToken?: CancelToken) : Promise<IApplicationLocksResponse[]>
  {
    const route = `/api/v1/application-locks`
    const res = await $get<IApplicationLocksResponse[]>(route, {cancelToken});
    return res.data;
  }
  public eventStreamAsync() : EventSource
  {
    const route = `/api/v1/application-locks/realtime-event-stream`
    return new EventSource(route, {
      withCredentials: true,
    });
  }
  public async requestKeyCancellation(request: ICancelApplicationLockRequest, cancelToken?: CancelToken) : Promise<IOpResult>
  {
    const route = `/api/v1/application-locks/request-cancellation`
    const res = await $post<IOpResult>(route, request, {cancelToken});
    return res.data;
  }
}
