//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IBeginAuthCodeFlowResponse } from '../requests';
import { IBeginAuthCodeFlowRequest } from '../requests';
import { IFailAuthCodeFlowRequest } from '../requests';
import { IFinishAuthCodeFlowRequest } from '../requests';
import { IOauth2AccessTokenWithTenantNameResponse } from '../responses';
import { IOauth2AccessToken } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.OauthController */
export class OauthApi
{
  public async beginAuthCodeFlow(body: IBeginAuthCodeFlowRequest, cancelToken?: CancelToken) : Promise<IBeginAuthCodeFlowResponse>
  {
    const route = `/api/v1/oauth/begin-auth-code-flow`
    const res = await $post<IBeginAuthCodeFlowResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async failAuthCodeFlow(body: IFailAuthCodeFlowRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/oauth/fail-auth-code-flow`
    await $post(route, body, {cancelToken});
  }
  public async receiveAuthCode(body: IFinishAuthCodeFlowRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/oauth/finish-auth-code-flow`
    await $post(route, body, {cancelToken});
  }
  public async listOauthAccessTokens(cancelToken?: CancelToken) : Promise<IOauth2AccessTokenWithTenantNameResponse[]>
  {
    const route = `/api/v1/oauth/oauth-access-tokens`
    const res = await $get<IOauth2AccessTokenWithTenantNameResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async getOauthAccessToken(id: number, accessTokenId: string, cancelToken?: CancelToken) : Promise<IOauth2AccessToken>
  {
    const route = `/api/v1/oauth/oauth-access-tokens/${id}/${accessTokenId}`
    const res = await $get<IOauth2AccessToken>(route, {cancelToken});
    return res.data;
  }
  public async refreshOauthAccessToken(id: number, cancelToken?: CancelToken) : Promise<IOauth2AccessToken>
  {
    const route = `/api/v1/oauth/oauth-access-tokens/${id}/refresh`
    const res = await $post<IOauth2AccessToken>(route, undefined, {cancelToken});
    return res.data;
  }
  public async deleteOauthAccessToken(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/oauth/oauth-access-tokens/${id}`
    await $delete(route, {cancelToken});
  }
}
