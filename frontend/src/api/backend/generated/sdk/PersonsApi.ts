//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetSimplePersonResponse } from '../responses';
import { PersonType } from '../enums';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';
import { IUpdatePersonPayload } from '../interfaces';
import { ICreatePersonPayload } from '../interfaces';
import { IAccessRequestResponse } from '../interfaces';
import { ICommandResult } from '../interfaces';
import { IGrantAccessRequest } from '../requests';
import { IAddTagsRequest } from '../requests';
import { IRemoveTagsRequest } from '../requests';
import { ITargetAssignmentResource } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.PersonsController */
export class PersonsApi
{
  public dxGet(queries: {tagId?: number, includeTenant?: boolean, includeUserData?: boolean, personType?: PersonType} = {}) : CustomStore<IGetSimplePersonResponse>
  {
    const route = `/api/v1/persons/dx`
    return createStore(route, { queries });
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.filters - Defaults to null in the controller
  * @param {string} queries.sorts - Defaults to null in the controller
  * @param {number} queries.page - Defaults to null in the controller
  * @param {number} queries.pageSize - Defaults to null in the controller
  */
  public async getAllPersons(queries: {filters?: string, sorts?: string, page?: number, pageSize?: number} = {}, cancelToken?: CancelToken) : Promise<IGetSimplePersonResponse[]>
  {
    const route = `/api/v1/persons`
    const res = await $get<IGetSimplePersonResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async get(id: number, cancelToken?: CancelToken) : Promise<IGetSimplePersonResponse>
  {
    const route = `/api/v1/persons/${id}`
    const res = await $get<IGetSimplePersonResponse>(route, {cancelToken});
    return res.data;
  }
  public async delete(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/persons/${id}`
    await $delete(route, {cancelToken});
  }
  public async put(id: number, payload: IUpdatePersonPayload, cancelToken?: CancelToken) : Promise<IGetSimplePersonResponse>
  {
    const route = `/api/v1/persons/${id}`
    const res = await $put<IGetSimplePersonResponse>(route, payload, {cancelToken});
    return res.data;
  }
  public async post(payload: ICreatePersonPayload, cancelToken?: CancelToken) : Promise<IGetSimplePersonResponse>
  {
    const route = `/api/v1/persons`
    const res = await $post<IGetSimplePersonResponse>(route, payload, {cancelToken});
    return res.data;
  }
  public async getPersonsRequestingAccess(cancelToken?: CancelToken) : Promise<IAccessRequestResponse[]>
  {
    const route = `/api/v1/persons/requesting-access`
    const res = await $get<IAccessRequestResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async grantAccess(personId: number, req: IGrantAccessRequest, cancelToken?: CancelToken) : Promise<ICommandResult>
  {
    const route = `/api/v1/persons/${personId}/grant-access`
    const res = await $post<ICommandResult>(route, req, {cancelToken});
    return res.data;
  }
  public async denyAccess(personId: number, cancelToken?: CancelToken) : Promise<ICommandResult>
  {
    const route = `/api/v1/persons/${personId}/deny-access`
    const res = await $post<ICommandResult>(route, undefined, {cancelToken});
    return res.data;
  }
  public async addTags(request: IAddTagsRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/persons/add-tags`
    await $post(route, request, {cancelToken});
  }
  public async removeTags(request: IRemoveTagsRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/persons/remove-tags`
    await $post(route, request, {cancelToken});
  }
  public async getSelfServiceItems(id: number, cancelToken?: CancelToken) : Promise<ITargetAssignmentResource[]>
  {
    const route = `/api/v1/persons/${id}/self-service`
    const res = await $get<ITargetAssignmentResource[]>(route, {cancelToken});
    return res.data;
  }
}
