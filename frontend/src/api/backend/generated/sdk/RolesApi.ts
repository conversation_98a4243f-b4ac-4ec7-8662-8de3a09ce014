//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetRoleResponse } from '../interfaces';
import { ICreateOrUpdateRoleRequest } from '../interfaces';
import { ICloneRoleRequest } from '../interfaces';
import { ISubjectMetadata } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.RBAC.RolesController */
export class RolesApi
{
  public async createRole(request: ICreateOrUpdateRoleRequest, cancelToken?: CancelToken) : Promise<IGetRoleResponse>
  {
    const route = `/api/v1/roles`
    const res = await $post<IGetRoleResponse>(route, request, {cancelToken});
    return res.data;
  }
  public async updateRole(roleId: number, request: ICreateOrUpdateRoleRequest, cancelToken?: CancelToken) : Promise<IGetRoleResponse>
  {
    const route = `/api/v1/roles/${roleId}`
    const res = await $put<IGetRoleResponse>(route, request, {cancelToken});
    return res.data;
  }
  public async cloneRole(roleId: number, request: ICloneRoleRequest, cancelToken?: CancelToken) : Promise<IGetRoleResponse>
  {
    const route = `/api/v1/roles/${roleId}/clone`
    const res = await $post<IGetRoleResponse>(route, request, {cancelToken});
    return res.data;
  }
  public async getRoles(cancelToken?: CancelToken) : Promise<IGetRoleResponse[]>
  {
    const route = `/api/v1/roles`
    const res = await $get<IGetRoleResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async getRole(roleId: number, cancelToken?: CancelToken) : Promise<IGetRoleResponse>
  {
    const route = `/api/v1/roles/${roleId}`
    const res = await $get<IGetRoleResponse>(route, {cancelToken});
    return res.data;
  }
  public async deleteRole(roleId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/roles/${roleId}`
    await $delete(route, {cancelToken});
  }
  public async getPermissions(cancelToken?: CancelToken) : Promise<ISubjectMetadata[]>
  {
    const route = `/api/v1/roles/permissions`
    const res = await $get<ISubjectMetadata[]>(route, {cancelToken});
    return res.data;
  }
}
