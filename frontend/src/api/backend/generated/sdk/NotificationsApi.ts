//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { INotification } from '../interfaces';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';
import { IAcknowledgeNotificationsRequest } from '../requests';
import { IUserSilencedNotificationResponse } from '../responses';
import { NotificationType } from '../notification-types';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.NotificationsController */
export class NotificationsApi
{
  public getDx() : CustomStore<INotification>
  {
    const route = `/api/v1/notifications/dx`
    return createStore(route);
  }
  public async acknowledge(request: IAcknowledgeNotificationsRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/notifications/acknowledge`
    await $post(route, request, {cancelToken});
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {number} queries.limit - Defaults to null in the controller
  */
  public async getUnacknowledgedNotifications(queries: {limit?: number} = {}, cancelToken?: CancelToken) : Promise<INotification[]>
  {
    const route = `/api/v1/notifications/unacknowledged`
    const res = await $get<INotification[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.objectId - Defaults to null in the controller
  */
  public async silenceNotification(type: NotificationType, queries: {objectId?: string} = {}, cancelToken?: CancelToken) : Promise<IUserSilencedNotificationResponse>
  {
    const route = `/api/v1/notifications/${type}/silence`
    const res = await $post<IUserSilencedNotificationResponse>(route, undefined, {params: queries, cancelToken});
    return res.data;
  }
  public async getSilencedNotificationsForUser(cancelToken?: CancelToken) : Promise<IUserSilencedNotificationResponse[]>
  {
    const route = `/api/v1/notifications`
    const res = await $get<IUserSilencedNotificationResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async removeSilencedNotification(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/notifications/${id}/unsilence`
    await $delete(route, {cancelToken});
  }
}
