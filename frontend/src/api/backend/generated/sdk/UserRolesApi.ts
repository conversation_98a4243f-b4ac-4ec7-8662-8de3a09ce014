//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { ISetUserRolesRequest } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.RBAC.UserRolesController */
export class UserRolesApi
{
  public async setUserRoles(userId: number, request: ISetUserRolesRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/user-roles/set/user/${userId}/roles`
    await $post(route, request, {cancelToken});
  }
  public async bulkAssignRolesToPeople(request: any, cancelToken?: CancelToken) : Promise<string>
  {
    const route = `/api/v1/user-roles/assign/bulk-assign-roles`
    const res = await $post<string>(route, request, {cancelToken});
    return res.data;
  }
  public async bulkRemoveRolesFromPeople(request: any, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/user-roles/remove/bulk-remove-roles`
    await $post(route, request, {cancelToken});
  }
  public async invalidateCache(userId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/user-roles/invalidate-cache/${userId}`
    await $post(route, undefined, {cancelToken});
  }
}
