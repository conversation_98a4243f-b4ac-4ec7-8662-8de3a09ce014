//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetScheduleResponse } from '../responses';
import { ICreateScheduleRequest } from '../requests';
import { IUpdateScheduleRequest } from '../requests';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.SchedulesController */
export class SchedulesApi
{
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {number} queries.tenantId - Defaults to null in the controller
  */
  public async get(queries: {tenantId?: number} = {}, cancelToken?: CancelToken) : Promise<IGetScheduleResponse[]>
  {
    const route = `/api/v1/schedules`
    const res = await $get<IGetScheduleResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async getById(scheduleId: number, cancelToken?: CancelToken) : Promise<IGetScheduleResponse>
  {
    const route = `/api/v1/schedules/${scheduleId}`
    const res = await $get<IGetScheduleResponse>(route, {cancelToken});
    return res.data;
  }
  public async create(body: ICreateScheduleRequest, cancelToken?: CancelToken) : Promise<IGetScheduleResponse>
  {
    const route = `/api/v1/schedules`
    const res = await $post<IGetScheduleResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async update(scheduleId: number, body: IUpdateScheduleRequest, cancelToken?: CancelToken) : Promise<IGetScheduleResponse>
  {
    const route = `/api/v1/schedules/${scheduleId}`
    const res = await $put<IGetScheduleResponse>(route, body, {cancelToken});
    return res.data;
  }
  public async delete(scheduleId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/schedules/${scheduleId}`
    await $delete(route, {cancelToken});
  }
  public async runScheduleNow(scheduleId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/schedules/${scheduleId}/run-now`
    await $post(route, undefined, {cancelToken});
  }
  public async getRunningScheduleIds(cancelToken?: CancelToken) : Promise<number[]>
  {
    const route = `/api/v1/schedules/running-schedule-ids`
    const res = await $get<number[]>(route, {cancelToken});
    return res.data;
  }
  public async cancelSchedule(scheduleId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/schedules/${scheduleId}/cancel`
    await $post(route, undefined, {cancelToken});
  }
  public async duplicateSchedule(scheduleId: number, cancelToken?: CancelToken) : Promise<IGetScheduleResponse>
  {
    const route = `/api/v1/schedules/${scheduleId}/duplicate`
    const res = await $post<IGetScheduleResponse>(route, undefined, {cancelToken});
    return res.data;
  }
}
