//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IAzureTenantCustomersResult } from '../interfaces';
import { ITenantInfoResult } from '../interfaces';
import { IAzureTenantUserSyncResult } from '../interfaces';
import { ISyncAzureDataForTenantsRequest } from '../requests';
import { IPreconsentCustomerTenantsRequest } from '../requests';
import { IAzureTenantDetailsSyncResult } from '../interfaces';
import { ITenantConsentResponseBody } from '../responses';
import { ITenantConsentRequestBody } from '../requests';
import { ICheckTenantPartnerStatusResponseBody } from '../responses';
import { IDisambiguateAzureTenantTypeRequestBody } from '../requests';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.AzureController */
export class AzureApi
{
  public async getDelegatedAdminCustomers(partnerPrincipalId: string, cancelToken?: CancelToken) : Promise<IAzureTenantCustomersResult[]>
  {
    const route = `/api/v1/azure/partner-tenant-customers/${partnerPrincipalId}`
    const res = await $get<IAzureTenantCustomersResult[]>(route, {cancelToken});
    return res.data;
  }
  public async getPartnerTenantInfos(cancelToken?: CancelToken) : Promise<ITenantInfoResult[]>
  {
    const route = `/api/v1/azure/partner-tenant-infos`
    const res = await $get<ITenantInfoResult[]>(route, {cancelToken});
    return res.data;
  }
  public async syncAzureUsersForTenants(request: ISyncAzureDataForTenantsRequest, cancelToken?: CancelToken) : Promise<IAzureTenantUserSyncResult[]>
  {
    const route = `/api/v1/azure/sync-users-from-azure-tenants`
    const res = await $post<IAzureTenantUserSyncResult[]>(route, request, {cancelToken});
    return res.data;
  }
  public async preconsentCustomerTenants(request: IPreconsentCustomerTenantsRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/azure/preconsent-customer-tenants`
    await $post(route, request, {cancelToken});
  }
  public async syncAzureDetailsForTenants(request: ISyncAzureDataForTenantsRequest, cancelToken?: CancelToken) : Promise<IAzureTenantDetailsSyncResult[]>
  {
    const route = `/api/v1/azure/sync-details-from-azure-tenants`
    const res = await $post<IAzureTenantDetailsSyncResult[]>(route, request, {cancelToken});
    return res.data;
  }
  public async handleTenantConsent(body: ITenantConsentRequestBody, cancelToken?: CancelToken) : Promise<ITenantConsentResponseBody>
  {
    const route = `/api/v1/azure/tenant-consented`
    const res = await $post<ITenantConsentResponseBody>(route, body, {cancelToken});
    return res.data;
  }
  public async disambiguateAzureTenantType(body: IDisambiguateAzureTenantTypeRequestBody, cancelToken?: CancelToken) : Promise<ICheckTenantPartnerStatusResponseBody>
  {
    const route = `/api/v1/azure/disambiguate-azure-tenant-type`
    const res = await $post<ICheckTenantPartnerStatusResponseBody>(route, body, {cancelToken});
    return res.data;
  }
}
