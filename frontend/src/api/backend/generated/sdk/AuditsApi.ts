//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IAudit } from '../interfaces';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.AuditsController */
export class AuditsApi
{
  public getLocalDx() : CustomStore<IAudit>
  {
    const route = `/api/v1/audits/local/dx`
    return createStore(route);
  }
  public getGlobalDx() : CustomStore<IAudit>
  {
    const route = `/api/v1/audits/global/dx`
    return createStore(route);
  }
}
