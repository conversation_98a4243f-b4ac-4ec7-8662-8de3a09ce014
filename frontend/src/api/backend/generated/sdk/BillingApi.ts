//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetCustomerPortalSessionResult } from '../interfaces';
import { IGetSubscriptionDetailsResponse } from '../responses';
import { IGetProductCatalogItemsResponse } from '../interfaces';
import { IBillingPlatformDetails } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.BillingController */
export class BillingApi
{
  public async createCustomerPortalSession(cancelToken?: CancelToken) : Promise<IGetCustomerPortalSessionResult>
  {
    const route = `/api/v1/billing/create-customer-portal-session`
    const res = await $post<IGetCustomerPortalSessionResult>(route, undefined, {cancelToken});
    return res.data;
  }
  public async getSubscriptionDetails(cancelToken?: CancelToken) : Promise<IGetSubscriptionDetailsResponse>
  {
    const route = `/api/v1/billing/subscription-details`
    const res = await $get<IGetSubscriptionDetailsResponse>(route, {cancelToken});
    return res.data;
  }
  public async getProductCatalogItems(cancelToken?: CancelToken) : Promise<IGetProductCatalogItemsResponse>
  {
    const route = `/api/v1/billing/product-catalog-items`
    const res = await $get<IGetProductCatalogItemsResponse>(route, {cancelToken});
    return res.data;
  }
  public async getBillingPlatformDetails(cancelToken?: CancelToken) : Promise<IBillingPlatformDetails>
  {
    const route = `/api/v1/billing/billing-platform-details`
    const res = await $get<IBillingPlatformDetails>(route, {cancelToken});
    return res.data;
  }
}
