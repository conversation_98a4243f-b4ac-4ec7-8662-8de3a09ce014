//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetMaintenanceActionResponse } from '../responses';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';
import { IGetLatestActionForComputersRequestBody } from '../requests';
import { IGetLatestActionForTenantsRequestBody } from '../requests';
import { IGetLatestActionForComputer } from '../responses';
import { SessionType } from '../enums';
import { MaintenanceType } from '../enums';
import { SoftwareType } from '../enums';
import { IGetMaintenanceSessionLogResponse } from '../responses';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.MaintenanceActionsController */
export class MaintenanceActionsApi
{
  public dxGetAll() : CustomStore<IGetMaintenanceActionResponse>
  {
    const route = `/api/v1/maintenance-actions/dx`
    return createStore(route);
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.dateUtc - Defaults to null in the controller
  */
  public async getLatestNonCompliantMaintenanceActionsForTenant(tenantId: number, queries: {dateUtc?: string} = {}, cancelToken?: CancelToken) : Promise<any[]>
  {
    const route = `/api/v1/maintenance-actions/latest-non-compliant-actions-for-tenant/${tenantId}`
    const res = await $get<any[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async getLatestActionForComputers(body: IGetLatestActionForComputersRequestBody, cancelToken?: CancelToken) : Promise<IGetMaintenanceActionResponse[]>
  {
    const route = `/api/v1/maintenance-actions/latest-action-for-computers`
    const res = await $post<IGetMaintenanceActionResponse[]>(route, body, {cancelToken});
    return res.data;
  }
  public async getLatestActionForTenants(body: IGetLatestActionForTenantsRequestBody, cancelToken?: CancelToken) : Promise<any[]>
  {
    const route = `/api/v1/maintenance-actions/latest-action-for-tenants`
    const res = await $post<any[]>(route, body, {cancelToken});
    return res.data;
  }
  public async getLatestActionsForComputer(computerId: number, cancelToken?: CancelToken) : Promise<IGetLatestActionForComputer[]>
  {
    const route = `/api/v1/maintenance-actions/latest-for-computer/${computerId}`
    const res = await $get<IGetLatestActionForComputer[]>(route, {cancelToken});
    return res.data;
  }
  public getLatestActionsForTenant(tenantId: number, queries: {createdDateUtc?: string, sessionType?: SessionType} = {}) : CustomStore<any>
  {
    const route = `/api/v1/maintenance-actions/latest-for-tenant/${tenantId}`
    return createStore(route, { queries });
  }
  public getActionsForComputer(computerId: number) : CustomStore
  {
    const route = `/api/v1/maintenance-actions/dx-for-computer/${computerId}`
    return createStore(route);
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {MaintenanceType} queries.maintenanceType
  * @param {string} queries.maintenanceIdentifier
  */
  public async getActionsForMaintenanceItem(queries: {maintenanceType: MaintenanceType, maintenanceIdentifier: string}, cancelToken?: CancelToken) : Promise<IGetMaintenanceActionResponse[]>
  {
    const route = `/api/v1/maintenance-actions/maintenance-item`
    const res = await $get<IGetMaintenanceActionResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {SoftwareType} queries.softwareType
  * @param {string} queries.softwareIdentifier
  * @param {string} queries.version
  */
  public async getActionsForSoftwareVersion(queries: {softwareType: SoftwareType, softwareIdentifier: string, version: string}, cancelToken?: CancelToken) : Promise<IGetMaintenanceActionResponse[]>
  {
    const route = `/api/v1/maintenance-actions/version`
    const res = await $get<IGetMaintenanceActionResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async getLogsForAction(actionId: number, cancelToken?: CancelToken) : Promise<IGetMaintenanceSessionLogResponse[]>
  {
    const route = `/api/v1/maintenance-actions/${actionId}/logs`
    const res = await $get<IGetMaintenanceSessionLogResponse[]>(route, {cancelToken});
    return res.data;
  }
  public async getActionsNeedingAttentionForComputer(computerId: number, cancelToken?: CancelToken) : Promise<IGetMaintenanceActionResponse[]>
  {
    const route = `/api/v1/maintenance-actions/computer/${computerId}/needs-attention`
    const res = await $get<IGetMaintenanceActionResponse[]>(route, {cancelToken});
    return res.data;
  }
}
