//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetAuthResponse } from '../responses';
import { IUpdateAzureTenantAuthDetailsCmdResult } from '../interfaces';
import { IUpdateAzureTenantAuthDetailsCmdPayload } from '../interfaces';
import { IDeleteAzureTenantAuthDetailsCmdResult } from '../interfaces';
import { IDeleteAzureTenantAuthDetailsCmdPayload } from '../interfaces';
import { IAzureTenantAuthDetails } from '../interfaces';
import { IImmyIpAndHostnamesResponse } from '../responses';
import { ICommandResult } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.AuthController */
export class AuthApi
{
  public async get(cancelToken?: CancelToken) : Promise<IGetAuthResponse>
  {
    const route = `/api/v1/auth`
    const res = await $get<IGetAuthResponse>(route, {cancelToken});
    return res.data;
  }
  public async updateAzureTenantAuthDetails(body: IUpdateAzureTenantAuthDetailsCmdPayload, cancelToken?: CancelToken) : Promise<IUpdateAzureTenantAuthDetailsCmdResult>
  {
    const route = `/api/v1/auth/update-azure-tenant-auth-details`
    const res = await $post<IUpdateAzureTenantAuthDetailsCmdResult>(route, body, {cancelToken});
    return res.data;
  }
  public async deleteAzureTenantAuthDetails(body: IDeleteAzureTenantAuthDetailsCmdPayload, cancelToken?: CancelToken) : Promise<IDeleteAzureTenantAuthDetailsCmdResult>
  {
    const route = `/api/v1/auth/delete-azure-tenant-auth-details`
    const res = await $post<IDeleteAzureTenantAuthDetailsCmdResult>(route, body, {cancelToken});
    return res.data;
  }
  public async getAzureTenantAuthDetails(azureTenantPrincipalId: string, cancelToken?: CancelToken) : Promise<IAzureTenantAuthDetails>
  {
    const route = `/api/v1/auth/get-azure-tenant-auth-details/${azureTenantPrincipalId}`
    const res = await $get<IAzureTenantAuthDetails>(route, {cancelToken});
    return res.data;
  }
  public async getImmybotIPAddresses(cancelToken?: CancelToken) : Promise<IImmyIpAndHostnamesResponse>
  {
    const route = `/api/v1/auth/get-ip-addresses`
    const res = await $get<IImmyIpAndHostnamesResponse>(route, {cancelToken});
    return res.data;
  }
  public async requestAccess(cancelToken?: CancelToken) : Promise<ICommandResult>
  {
    const route = `/api/v1/auth/request-access`
    const res = await $post<ICommandResult>(route, undefined, {cancelToken});
    return res.data;
  }
}
