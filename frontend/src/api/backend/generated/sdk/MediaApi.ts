//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetFileDownloadUrlRequest } from '../requests';
import { IMediaSearchResponse } from '../responses';
import CustomStore from 'devextreme/data/custom_store';
import { createStore } from '@/composables/DxServerDataSource';
import { MediaCategory } from '../enums';
import { ILocalMediaResponse } from '../responses';
import { IUpdateLocalMediaPayload } from '../interfaces';
import { IGlobalMediaResponse } from '../responses';
import { IUpdateGlobalMediaPayload } from '../interfaces';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.MediaController */
export class MediaApi
{
  public async getDownloadUrl(request: IGetFileDownloadUrlRequest, cancelToken?: CancelToken) : Promise<string>
  {
    const route = `/api/v1/media/requestFileDownloadUrl`
    const res = await $post<string>(route, request, {cancelToken});
    return res.data;
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {boolean} queries.globalOnly - Defaults to False in the controller
  * @param {string} queries.filters - Defaults to null in the controller
  * @param {string} queries.sorts - Defaults to null in the controller
  * @param {number} queries.page - Defaults to null in the controller
  * @param {number} queries.pageSize - Defaults to null in the controller
  */
  public async search(queries: {globalOnly?: boolean, filters?: string, sorts?: string, page?: number, pageSize?: number} = {}, cancelToken?: CancelToken) : Promise<IMediaSearchResponse>
  {
    const route = `/api/v1/media/search`
    const res = await $get<IMediaSearchResponse>(route, {params: queries, cancelToken});
    return res.data;
  }
  public getLocal(queries: {category?: MediaCategory} = {}) : CustomStore
  {
    const route = `/api/v1/media/local`
    return createStore(route, { queries });
  }
  public async getLocalById(id: number, cancelToken?: CancelToken) : Promise<ILocalMediaResponse>
  {
    const route = `/api/v1/media/local/${id}`
    const res = await $get<ILocalMediaResponse>(route, {cancelToken});
    return res.data;
  }
  public async updateLocal(id: number, payload: IUpdateLocalMediaPayload, cancelToken?: CancelToken) : Promise<ILocalMediaResponse>
  {
    const route = `/api/v1/media/local/${id}`
    const res = await $post<ILocalMediaResponse>(route, payload, {cancelToken});
    return res.data;
  }
  public async deleteLocal(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/media/local/${id}`
    await $delete(route, {cancelToken});
  }
  public async uploadLocalMedia(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/media/local/upload`
    await $post(route, undefined, {cancelToken});
  }
  public async uploadSupportMedia(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/media/support/upload`
    await $post(route, undefined, {cancelToken});
  }
  public async getLocalDownloadUrl(id: number, cancelToken?: CancelToken) : Promise<string>
  {
    const route = `/api/v1/media/local/${id}/download-url`
    const res = await $get<string>(route, {cancelToken});
    return res.data;
  }
  public getGlobal(queries: {category?: MediaCategory} = {}) : CustomStore
  {
    const route = `/api/v1/media/global`
    return createStore(route, { queries });
  }
  public async getGlobalById(id: number, cancelToken?: CancelToken) : Promise<IGlobalMediaResponse>
  {
    const route = `/api/v1/media/global/${id}`
    const res = await $get<IGlobalMediaResponse>(route, {cancelToken});
    return res.data;
  }
  public async updateGlobal(id: number, payload: IUpdateGlobalMediaPayload, cancelToken?: CancelToken) : Promise<IGlobalMediaResponse>
  {
    const route = `/api/v1/media/global/${id}`
    const res = await $post<IGlobalMediaResponse>(route, payload, {cancelToken});
    return res.data;
  }
  public async deleteGlobal(id: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/media/global/${id}`
    await $delete(route, {cancelToken});
  }
  public async uploadGlobalMedia(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/media/global/upload`
    await $post(route, undefined, {cancelToken});
  }
  public async getGlobalDownloadUrl(id: number, cancelToken?: CancelToken) : Promise<string>
  {
    const route = `/api/v1/media/global/${id}/download-url`
    const res = await $get<string>(route, {cancelToken});
    return res.data;
  }
}
