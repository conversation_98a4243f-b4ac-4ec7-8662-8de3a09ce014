//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetUserResponse } from '../responses';
import { IUpdateUserPayload } from '../interfaces';
import { ICreateUserFromPersonRequest } from '../requests';
import { ISubmitFeedbackRequestBody } from '../requests';
import { IOpResult } from '../interfaces';
import { IImpersonationRequest } from '../requests';
import { IGetClaimsResponse } from '../responses';
import { ICommandResult } from '../interfaces';
import { IGrantAccessRequestRbac } from '../requests';
import { IBulkUpdateRoleRequest } from '../requests';
import { IUpdateExpirationRequest } from '../requests';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.UsersController */
export class UsersApi
{
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.filters - Defaults to null in the controller
  * @param {string} queries.sorts - Defaults to null in the controller
  * @param {number} queries.page - Defaults to null in the controller
  * @param {number} queries.pageSize - Defaults to null in the controller
  */
  public async getAll(queries: {filters?: string, sorts?: string, page?: number, pageSize?: number} = {}, cancelToken?: CancelToken) : Promise<IGetUserResponse[]>
  {
    const route = `/api/v1/users`
    const res = await $get<IGetUserResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async get(userId: number, cancelToken?: CancelToken) : Promise<IGetUserResponse>
  {
    const route = `/api/v1/users/${userId}`
    const res = await $get<IGetUserResponse>(route, {cancelToken});
    return res.data;
  }
  public async update(userId: number, user: IUpdateUserPayload, cancelToken?: CancelToken) : Promise<IGetUserResponse>
  {
    const route = `/api/v1/users/${userId}`
    const res = await $post<IGetUserResponse>(route, user, {cancelToken});
    return res.data;
  }
  public async create(user: any, cancelToken?: CancelToken) : Promise<IGetUserResponse>
  {
    const route = `/api/v1/users`
    const res = await $post<IGetUserResponse>(route, user, {cancelToken});
    return res.data;
  }
  public async createFromPerson(request: ICreateUserFromPersonRequest, cancelToken?: CancelToken) : Promise<IGetUserResponse>
  {
    const route = `/api/v1/users/create-from-person`
    const res = await $post<IGetUserResponse>(route, request, {cancelToken});
    return res.data;
  }
  public async delete(userId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/users/${userId}`
    await $delete(route, {cancelToken});
  }
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {number[]} queries.userIds
  */
  public async bulkDelete(queries: {userIds: number[]}, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/users/bulk-delete`
    await $delete(route, {params: queries, cancelToken});
  }
  public async submitFeedback(body: ISubmitFeedbackRequestBody, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/users/submit-feedback`
    await $post(route, body, {cancelToken});
  }
  public async impersonateUser(userId: number, request: IImpersonationRequest, cancelToken?: CancelToken) : Promise<IOpResult>
  {
    const route = `/api/v1/users/${userId}/impersonate`
    const res = await $post<IOpResult>(route, request, {cancelToken});
    return res.data;
  }
  public async stopImpersonatingUser(cancelToken?: CancelToken) : Promise<IOpResult>
  {
    const route = `/api/v1/users/stop-impersonating`
    const res = await $post<IOpResult>(route, undefined, {cancelToken});
    return res.data;
  }
  public async getClaims(cancelToken?: CancelToken) : Promise<IGetClaimsResponse>
  {
    const route = `/api/v1/users/claims`
    const res = await $get<IGetClaimsResponse>(route, {cancelToken});
    return res.data;
  }
  public async grantAccessRbac(req: IGrantAccessRequestRbac, cancelToken?: CancelToken) : Promise<ICommandResult>
  {
    const route = `/api/v1/users/grant-access`
    const res = await $post<ICommandResult>(route, req, {cancelToken});
    return res.data;
  }
  public async invalidateCache(cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/users/invalidate-cache`
    await $post(route, undefined, {cancelToken});
  }
  public async bulkRemoveRolesFromPeople(request: IBulkUpdateRoleRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/users/remove-roles`
    await $post(route, request, {cancelToken});
  }
  public async bulkAssignRolesToPeople(request: IBulkUpdateRoleRequest, cancelToken?: CancelToken) : Promise<string>
  {
    const route = `/api/v1/users/add-roles`
    const res = await $post<string>(route, request, {cancelToken});
    return res.data;
  }
  public async updateExpiration(request: IUpdateExpirationRequest, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/users/update-expiration`
    await $post(route, request, {cancelToken});
  }
}
