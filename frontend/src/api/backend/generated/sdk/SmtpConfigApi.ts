//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { $get, $post, $put, $patch, $delete } from '@/api/backend/client';
import { CancelToken } from 'axios';
import { IGetSmtpConfigResponse } from '../responses';
import { ICreateSmtpRequest } from '../requests';
import { ISendTestEmailRequest } from '../requests';

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.SmtpConfigController */
export class SmtpConfigApi
{
  /**
  * @param {Object} queries - Query params passed to the controller
  * @param {string} queries.filters - Defaults to null in the controller
  * @param {string} queries.sorts - Defaults to null in the controller
  * @param {number} queries.page - Defaults to null in the controller
  * @param {number} queries.pageSize - Defaults to null in the controller
  */
  public async getAll(queries: {filters?: string, sorts?: string, page?: number, pageSize?: number} = {}, cancelToken?: CancelToken) : Promise<IGetSmtpConfigResponse[]>
  {
    const route = `/api/v1/smtp-configs`
    const res = await $get<IGetSmtpConfigResponse[]>(route, {params: queries, cancelToken});
    return res.data;
  }
  public async get(tenantId: number, cancelToken?: CancelToken) : Promise<IGetSmtpConfigResponse>
  {
    const route = `/api/v1/smtp-configs/${tenantId}`
    const res = await $get<IGetSmtpConfigResponse>(route, {cancelToken});
    return res.data;
  }
  public async create(request: ICreateSmtpRequest, cancelToken?: CancelToken) : Promise<IGetSmtpConfigResponse>
  {
    const route = `/api/v1/smtp-configs`
    const res = await $post<IGetSmtpConfigResponse>(route, request, {cancelToken});
    return res.data;
  }
  public async update(tenantId: number, request: ICreateSmtpRequest, cancelToken?: CancelToken) : Promise<IGetSmtpConfigResponse>
  {
    const route = `/api/v1/smtp-configs/${tenantId}`
    const res = await $post<IGetSmtpConfigResponse>(route, request, {cancelToken});
    return res.data;
  }
  public async delete(tenantId: number, cancelToken?: CancelToken) : Promise<void>
  {
    const route = `/api/v1/smtp-configs/${tenantId}`
    await $delete(route, {cancelToken});
  }
  public async sendTestEmail(request: ISendTestEmailRequest, cancelToken?: CancelToken) : Promise<string>
  {
    const route = `/api/v1/smtp-configs/send-test-email`
    const res = await $post<string>(route, request, {cancelToken});
    return res.data;
  }
}
