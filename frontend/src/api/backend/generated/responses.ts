//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { PackageType } from './enums';
import { IScriptDetailsBase } from './interfaces';
import { ApplicationLockEventTypes } from './enums';
import { ComputerOnboardingStatus } from './enums';
import { ChangeRequestObjectType } from './enums';
import { ChangeRequestState } from './enums';
import { AzTenantType } from './enums';
import { IComputerListViewModel } from './interfaces';
import { ITimelineEvent } from './interfaces';
import { DatabaseType } from './enums';
import { IntegrationTag } from './enums';
import { AgentIdentificationManualResolutionDecision } from './enums';
import { IProviderAgentDetails } from './interfaces';
import { SubscriptionStatus } from './enums';
import { ISubscriptionAddonDto } from './interfaces';
import { ReleaseChannel } from './enums';
import { IFeature } from './interfaces';
import { SessionStatus } from './enums';
import { IProviderTypeDto } from './interfaces';
import { SoftwareLicenseRequirement } from './enums';
import { SoftwareType } from './enums';
import { DetectionMethod } from './enums';
import { RepairActionType } from './enums';
import { SoftwareTableNameSearchMode } from './enums';
import { UpdateActionType } from './enums';
import { LicenseType } from './enums';
import { SoftwareVersionInstallerType } from './enums';
import { MaintenanceActionResult } from './enums';
import { MaintenanceActionType } from './enums';
import { MaintenanceActionReason } from './enums';
import { MaintenanceActionStatus } from './enums';
import { MaintenanceActionResultReason } from './enums';
import { DesiredSoftwareState } from './enums';
import { MaintenanceType } from './enums';
import { MaintenanceTaskMode } from './enums';
import { SoftwareProviderType } from './enums';
import { ScriptLanguage } from './enums';
import { SessionLogType } from './enums';
import { ScriptExecutionContext } from './enums';
import { ActionProgressPhaseName } from './enums';
import { SessionPhaseStatus } from './enums';
import { ISessionJobArgs } from './interfaces';
import { SessionStageType } from './enums';
import { TargetAssignmentApprovalStatus } from './enums';
import { TargetType } from './enums';
import { IApplicationPreferences } from './interfaces';
import { ITenantPreferences } from './interfaces';
import { IUserPreferences } from './interfaces';
import { AgentIdentificationLogType } from './enums';
import { IDeviceDetails } from './interfaces';
import { IAgentOnboardingOptions } from './interfaces';
import { IGetProviderLinkResponse } from './interfaces';
import { IReleaseDetails } from './interfaces';
import { RebootPreference } from './enums';
import { PromptTimeoutAction } from './enums';
import { ComputerOfflineMaintenanceSessionBehavior } from './enums';
import { TargetCategory } from './enums';
import { TargetGroupFilter } from './enums';
import { ScriptCategory } from './enums';
import { SubjectQualifier } from './enums';
import { Condition } from './enums';
import { ActionToPerform } from './enums';
import { ISourceContextLogLevel } from './interfaces';
import { IFeatureEnabledFromSubscription } from './interfaces';
import { ITenantTagAuthorization } from './interfaces';
import { TargetEnforcement } from './enums';
import { IAzureTenantConsentDetails } from './interfaces';
import { IAzureTenantInfo } from './interfaces';
import { IAzureSyncResult } from './interfaces';
import { IAzureTenantLinkDomainFilter } from './interfaces';
import { MediaCategory } from './enums';
import { IDeploymentParameterValue } from './interfaces';
import { IGetIpAddressesResponse } from './interfaces';
import { InventoryTaskFrequency } from './enums';
import { ITenantMedia } from './interfaces';
import { IOauthConsentData } from './interfaces';
import { ScriptOutputType } from './enums';
import { ITargetAssignmentResource } from './interfaces';
import { HealthStatus } from './enums';
import { NotificationType } from './notification-types';
import { Relationship } from './enums';
import { MaintenanceTaskCategory } from './enums';
import { ITenantMaintenanceTask } from './interfaces';
import { MaintenanceTaskParameterType } from './enums';

export interface ICircuitBreakerState2<T>
{
  policyName: string;
  circuitState: string;
  lastException?: unknown;
  lastResult?: T;
}
export interface IAnalyzeGlobalSoftwarePackageResponse
{
  fileName?: string;
  packageHash?: string;
  packageType?: PackageType;
  displayVersion?: string;
  extension?: string;
  description?: string;
  softwareTableName?: string;
  upgradeCode?: string;
  productCode?: string;
  isExe: boolean;
  isMsi: boolean;
  powerShellInstallScript?: IScriptDetailsBase;
  batchInstallScript?: IScriptDetailsBase;
  powerShellUninstallScript?: IScriptDetailsBase;
  detectionScript?: IScriptDetailsBase;
  defaultPowershellUninstallScript?: IScriptDetailsBase;
}
export interface IAnalyzeLocalSoftwarePackageResponse
{
  fileName?: string;
  packageHash?: string;
  packageType?: PackageType;
  displayVersion?: string;
  extension?: string;
  description?: string;
  softwareTableName?: string;
  upgradeCode?: string;
  productCode?: string;
  isExe: boolean;
  isMsi: boolean;
  powerShellInstallScript?: IScriptDetailsBase;
  batchInstallScript?: IScriptDetailsBase;
  powerShellUninstallScript?: IScriptDetailsBase;
  detectionScript?: IScriptDetailsBase;
  defaultPowershellUninstallScript?: IScriptDetailsBase;
}
export interface IApplicationLocksResponse
{
  key: string;
  lockHolder: IApplicationLockCallerInfo;
  lockWaiters: IApplicationLockCallerInfo[];
}
export interface IApplicationLockCallerInfo
{
  name: string;
  id: string;
  isLockHolder: boolean;
  since: string;
}
export interface IApplicationLockEvent
{
  key: string;
  eventType: ApplicationLockEventTypes;
  callerInfo: string;
  eventTimestamp: string;
  reason?: string;
}
export interface IBaseAnalyzePackageResponse
{
  fileName?: string;
  packageHash?: string;
  packageType?: PackageType;
  displayVersion?: string;
  extension?: string;
  description?: string;
  softwareTableName?: string;
  upgradeCode?: string;
  productCode?: string;
  isExe: boolean;
  isMsi: boolean;
  powerShellInstallScript?: IScriptDetailsBase;
  batchInstallScript?: IScriptDetailsBase;
  powerShellUninstallScript?: IScriptDetailsBase;
  detectionScript?: IScriptDetailsBase;
  defaultPowershellUninstallScript?: IScriptDetailsBase;
}
export interface IBulkDeleteComputersResponse
{
  numDeleted: number;
}
export interface IBulkDeleteResponse
{
  numDeleted: number;
}
export interface ICalculateTargetedComputerResponse
{
  id: number;
  cn: string;
  on: boolean;
  tn: string;
  ppi?: number;
  ppn: string;
  ti: number;
  os: string;
  obs: ComputerOnboardingStatus;
  sn: string;
  dr?: number;
  sb: boolean;
  ct: number[];
}
export interface IChangeRequestCommentResponse
{
  id: number;
  comment: string;
  commentedByUsername: string;
  changeRequestId: number;
  createdDateUtc: string;
}
export interface IChangeRequestResponse
{
  id: number;
  objectType: ChangeRequestObjectType;
  targetAssignmentId?: number;
  scriptId?: number;
  newValuesJson: any;
  state: ChangeRequestState;
  acknowledgedByUserId?: number;
  acknowledgedByUserName?: string;
  createdDateUtc: string;
  createdByUserName?: string;
  updatedDateUtc: string;
  comments: IChangeRequestCommentResponse[];
}
export interface ICheckTenantPartnerStatusResponseBody
{
  tenantPrincipalId: string;
  partnerPrincipalId?: string;
  azureTenantType?: AzTenantType;
}
export interface ICircuitBreakerState
{
  policyName: string;
  circuitState: string;
  lastException?: unknown;
}
export interface IComponentHealthCheckResponse
{
  component: string;
  status: string;
  description: string;
  data: { [key:string]: any };
}
export interface IComputerNameResponse
{
  id: number;
  name?: string;
}
export interface IComputerPageResponse
{
  count: number;
  results: IComputerListViewModel[];
}
export interface IComputerTimelineEventsPageResponse
{
  events: ITimelineEvent[];
}
export interface IDetectedComputerSoftwareResponse
{
  id: number;
  computerName?: string;
  computerId: number;
  softwareName?: string;
  softwareId?: number;
  tenantName?: string;
  tenantId: number;
  personName?: string;
  personEmail?: string;
  personId?: number;
  version?: string;
  detectedAt: string;
  installDate?: string;
  platform?: string;
  productCode?: string;
  upgradeCode?: string;
  quietUninstallString?: string;
  uninstallString?: string;
  systemComponent?: number;
  registryPath?: string;
  installLocation?: string;
}
export interface IDxComputer
{
  deviceId: string;
  id: number;
  computerName: string;
  primaryUserFirstName?: string;
  primaryUserLastName?: string;
  primaryUserEmail?: string;
  tenantName: string;
  tenantId: number;
  primaryPersonId?: number;
  serialNumber?: string;
  manufacturer?: string;
  model?: string;
  operatingSystem?: string;
  hasPendingReboot?: boolean;
  internalIpAddress?: string;
  externalIpAddress?: string;
  domain?: string;
  onboardingStatus: ComputerOnboardingStatus;
  isSandbox: boolean;
}
export interface IDxComputerInventoryScriptResult
{
  computerName?: string;
  computerId: number;
  tenantName: string;
  tenantId: number;
  timestampUtc: string;
  success: boolean;
  inventoryKey: string;
  latestSuccessResult?: any;
  latestSuccessResultJson?: string;
}
export interface IDynamicIntegrationTypeResponse
{
  id: number;
  databaseType: DatabaseType;
  integrationTypeId: string;
  name: string;
  enabled: boolean;
  scriptId: number;
  createdDateUtc: string;
  updatedDateUtc: string;
  creationErrorMessage?: string;
  docsUrl?: string;
  logoId: number;
  logo?: IGlobalMediaResponse;
  tag: IntegrationTag;
}
export interface IGetAgentIdentificationFailureResponse
{
  id: number;
  computerId?: number;
  existingAgentId?: number;
  pendingAgentId?: number;
  message?: string;
  createdDateUTC: string;
  resolved: boolean;
  requiresManualResolution: boolean;
  featureUsageExceeded: boolean;
  manualResolutionDecision?: AgentIdentificationManualResolutionDecision;
  existingComputerName?: string;
  existingPrimaryUserFirstName?: string;
  existingPrimaryUserLastName?: string;
  existingTenantName?: string;
  existingOperatingSystem?: string;
  existingSerialNumber?: string;
  existingManufacturer?: string;
  existingOSInstallDate?: string;
  existingAgent?: IProviderAgentDetails;
}
export interface IGetAuthResponse
{
  impersonating: boolean;
  userId: number;
  tenantId: number;
  personId?: number;
  displayName: string;
  firstName: string;
  lastName: string;
  email: string;
  tenantName: string;
  isAdmin: boolean;
  isMSP: boolean;
  isImmense: boolean;
  hasManagementAccess: boolean;
  isSupportTechnician: boolean;
  isImmySupportAccessEnabled: boolean;
  devInstanceDetails?: IDevInstanceDetails;
  localSoftwareEndpoint: string;
  globalSoftwareEndpoint: string;
  localPublicMediaContainerName: string;
  globalPublicMediaContainerName: string;
  backendRegAppId?: string;
  userLevelAuthSelected: boolean;
  status?: SubscriptionStatus;
  planId?: string;
  planPrice?: number;
  planQuantity?: number;
  addons: ISubscriptionAddonDto[];
  trialStartUtc?: string;
  trialEndUtc?: string;
  updateAvailable: boolean;
  currentReleaseVersion?: string;
  currentReleaseReleaseChannel?: ReleaseChannel;
  isInstanceUpdating: boolean;
  isInstanceRestarting: boolean;
  instanceUpdateHasFailed: boolean;
  instanceUpdateSource?: string;
  maxRunningSessionCount: number;
  openAccessRequestCount: number;
  maximumTrackableComputers?: number;
  canManageCrossTenantDeployments: boolean;
  immyProduct?: string;
  features: IFeature[];
  instanceReleaseChannel: ReleaseChannel;
  daysLeftInTrial?: number;
  claims?: IClaimResponse[];
}
export interface IDevInstanceDetails
{
  backendVersion: string;
  isHangfireServerRunning: boolean;
  postRoutes: { [key:string]: string };
}
export interface IGetAzureGroupResponse
{
  id: string;
  displayName: string;
}
export interface IGetBrandingResponse
{
  id: number;
  tenantId?: number;
  tenantName?: string;
  startDate?: string;
  endDate?: string;
  ignoreYear?: boolean;
  timeFormat?: string;
  fromAddress: string;
  mascotImgUri?: string;
  mascotName?: string;
  logoUri?: string;
  logoAltText?: string;
  backgroundColor?: string;
  foregroundColor?: string;
  tableHeaderColor?: string;
  tableHeaderTextColor?: string;
  textColor?: string;
  description: string;
  updatedDateUTC: string;
  createdDateUTC: string;
  updatedBy?: string;
}
export interface IGetClientResponse
{
  id: string;
  name: string;
}
export interface IGetComputerDeviceUpdateFormDataResponse
{
  devices: IDeviceUpdateFormDataResponse[];
}
export interface IDeviceUpdateFormDataResponse
{
  agentId: number;
  deviceUpdateFormData?: any;
}
export interface IGetComputerResponse
{
  id: number;
  deviceId: string;
  tenantId: number;
  computerName?: string;
  primaryPersonId?: number;
  onboardingStatus: ComputerOnboardingStatus;
  isOnline: boolean;
  isDomainController?: boolean;
  isPortable?: boolean;
  isServer?: boolean;
  isDesktop?: boolean;
  tenantName?: string;
  inventory?: any;
  isMissingSomeRequiredInventoryResults?: boolean;
  detectionOutdated: boolean;
  isSandbox: boolean;
  excludeFromMaintenance: boolean;
  devLabVmClaimExpirationDateUtc?: string;
  isDevLab: boolean;
  devLabVmUnclaimed: boolean;
  ephemeralAgentConnected: boolean;
  licensed: boolean;
  notes?: string;
  excludedFromUserAffinity: boolean;
  sessions: IGetMaintenanceSessionResponse[];
  additionalPersons: IGetPersonResponse[];
  primaryPerson?: IGetPersonResponse;
  tenant?: IGetTenantResponse;
  agents: IGetProviderAgentResponse[];
  computerTagIds: number[];
}
export interface IGetDxMaintenanceSessionResponse
{
  id: number;
  statusName: string;
  sessionStatus: SessionStatus;
  computerName: string;
  operatingSystem: string;
  manufacturer: string;
  model: string;
  serialNumber: string;
  domain: string;
  computerId?: number;
  tenantId?: number;
  personId?: number;
  createdBy?: string;
  createdDate: string;
  updatedDate: string;
  onboardingStageStatus?: SessionStatus;
  detectionStageStatus?: SessionStatus;
  executionStageStatus?: SessionStatus;
  agentUpdatesStageStatus?: SessionStatus;
  resolutionStageStatus?: SessionStatus;
  inventoryStageStatus?: SessionStatus;
  tenantName?: string;
  primaryPersonName?: string;
  primaryPersonEmail?: string;
  scheduleId?: number;
  fullMaintenance: boolean;
  scheduledExecutionDate?: string;
  personName?: string;
  personEmail?: string;
  duration?: string;
}
export interface IGetEphemeralAgentResponse
{
  id: string;
  isConnected: boolean;
  totalSentBytes: number;
  totalReceievedBytes: number;
  totalProcessedScriptCount: number;
  startupInfo?: any;
  lastActivity: string;
  createdAt: string;
}
export interface IGetExternalLinkInitializationInfoResponse
{
  providerLinkName: string;
  providerType: IProviderTypeDto;
  providerTypeFormData: any;
  inputsWithStoredPasswords: string[];
  supportsCrossProviderClientExternalLinking: boolean;
  crossProviderClientExternalLinkingDescription: string;
}
export interface IGetFailedPendingAgentResponse
{
  id: number;
  computerName?: string;
  operatingSystemName?: string;
  serial?: string;
  externalAgentId: string;
  deviceId: string;
  dateAdded: string;
  isOnline: boolean;
  providerLinkName: string;
  externalClientName: string;
  osInstallDate?: string;
  osInstallDateUtc?: string;
  identificationFailures: IGetAgentIdentificationFailureResponse[];
}
export interface IGetGlobalSoftwareResponse
{
  name: string;
  licenseRequirement: SoftwareLicenseRequirement;
  installOrder: number;
  hidden: boolean;
  softwareType: SoftwareType;
  identifier: string;
  id?: number;
  ownerTenantId?: number;
  detectionMethod: DetectionMethod;
  softwareTableName?: string;
  softwareVersions?: IGetGlobalSoftwareVersionResponse[];
  softwarePrerequisites?: IGetSoftwarePrerequisiteResponse[];
  detectionScriptId?: number;
  repairScriptId?: number;
  notes?: string;
  detectionScriptType: DatabaseType;
  maintenanceTaskType: DatabaseType;
  repairScriptType: DatabaseType;
  maintenanceTaskId?: number;
  rebootNeeded: boolean;
  updatedDateUTC: string;
  createdDateUTC: string;
  upgradeCode?: string;
  repairType: RepairActionType;
  softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  softwareIconMediaId?: number;
  recommended: boolean;
  chocoProviderSoftwareId?: string;
  niniteProviderSoftwareId?: string;
  softwareIcon?: IGlobalMediaResponse;
  installScriptId?: number;
  installScriptType: DatabaseType;
  testScriptId?: number;
  testScriptType: DatabaseType;
  testRequired: boolean;
  testFailedError?: string;
  upgradeScriptId?: number;
  upgradeScriptType: DatabaseType;
  upgradeStrategy: UpdateActionType;
  agentIntegrationTypeId?: string;
  uninstallScriptId?: number;
  uninstallScriptType: DatabaseType;
  postInstallScriptId?: number;
  postInstallScriptType: DatabaseType;
  postUninstallScriptId?: number;
  postUninstallScriptType: DatabaseType;
  licenseType: LicenseType;
  licenseDescription?: string;
  useDynamicVersions: boolean;
  dynamicVersionsScriptId?: number;
  dynamicVersionsScriptType: DatabaseType;
  downloadInstallerScriptId?: number;
  downloadInstallerScriptType: DatabaseType;
}
export interface IGetGlobalSoftwareVersionResponse
{
  deprecatedIdField?: number;
  softwareId: number;
  software?: IGetGlobalSoftwareResponse;
  installScriptType: DatabaseType;
  testScriptType: DatabaseType;
  upgradeScriptType: DatabaseType;
  uninstallScriptType: DatabaseType;
  postUninstallScriptType: DatabaseType;
  postInstallScriptType: DatabaseType;
  updatedDateUTC: string;
  createdDateUTC: string;
  displayName?: string;
  displayVersion?: string;
  semanticVersion: string;
  semanticVersionString: string;
  testRequired: boolean;
  uninstallIfNotInTarget: boolean;
  url?: string;
  relativeCacheSourcePath?: string;
  installerFile?: string;
  testFailedError?: string;
  packageHash?: string;
  installScriptId?: number;
  testScriptId?: number;
  upgradeScriptId?: number;
  uninstallScriptId?: number;
  postUninstallScriptId?: number;
  postInstallScriptId?: number;
  softwareIdentifier: string;
  numActionSuccesses: number;
  numActionFailures: number;
  blobName?: string;
  licenseType: LicenseType;
  upgradeStrategy: UpdateActionType;
  packageType: PackageType;
  installerType: SoftwareVersionInstallerType;
  lastResult?: MaintenanceActionResult;
  softwareType: SoftwareType;
  notes?: string;
  productCode?: string;
  dependsOnSemanticVersion?: string;
  hasOverrides: boolean;
}
export interface IGetLatestActionForComputer
{
  id: number;
  maintenanceSessionId: number;
  actionType: MaintenanceActionType;
  result: MaintenanceActionResult;
  reason: MaintenanceActionReason;
  status: MaintenanceActionStatus;
  resultReason?: MaintenanceActionResultReason;
  detectedVersion?: string;
  desiredVersion?: string;
  maintenanceDisplayName: string;
  desiredSoftwareState?: DesiredSoftwareState;
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  maintenanceTaskMode?: MaintenanceTaskMode;
  assignmentId?: number;
  assignmentType?: DatabaseType;
  startTime?: string;
  endTime?: string;
  createdDate?: string;
  softwareActionIdForConfigurationTask?: number;
  policyDescription?: string;
}
export interface IGetLicenseResponse
{
  id: number;
  name: string;
  licenseValue: string;
  softwareType: SoftwareType;
  softwareIdentifier: string;
  softwareName: string;
  semanticVersion?: string;
  restrictToMajorVersion: boolean;
  tenantId?: number;
  tenantName?: string;
  updatedBy?: string;
  updatedDate: string;
  createdDate: string;
}
export interface IGetLocalSoftwareResponse
{
  name: string;
  licenseRequirement: SoftwareLicenseRequirement;
  installOrder: number;
  hidden: boolean;
  softwareType: SoftwareType;
  identifier: string;
  id?: number;
  ownerTenantId?: number;
  detectionScriptId?: number;
  repairScriptId?: number;
  detectionMethod: DetectionMethod;
  softwareTableName?: string;
  notes?: string;
  detectionScriptType?: DatabaseType;
  repairScriptType?: DatabaseType;
  maintenanceTaskId?: number;
  maintenanceTaskType?: DatabaseType;
  tenantSoftware?: number[];
  softwareVersions?: IGetLocalSoftwareVersionResponse[];
  softwarePrerequisites?: IGetSoftwarePrerequisiteResponse[];
  updatedDate: string;
  rebootNeeded: boolean;
  updatedDateUTC: string;
  createdDateUTC: string;
  updatedBy?: string;
  upgradeCode?: string;
  repairType: RepairActionType;
  softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  softwareIconMediaId?: number;
  recommended: boolean;
  chocoProviderSoftwareId?: string;
  niniteProviderSoftwareId?: string;
  softwareIcon?: ILocalMediaResponse;
  installScriptId?: number;
  installScriptType?: DatabaseType;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  testRequired: boolean;
  testFailedError?: string;
  upgradeScriptId?: number;
  upgradeScriptType?: DatabaseType;
  upgradeStrategy: UpdateActionType;
  agentIntegrationTypeId?: string;
  uninstallScriptId?: number;
  uninstallScriptType?: DatabaseType;
  postInstallScriptId?: number;
  postInstallScriptType?: DatabaseType;
  postUninstallScriptId?: number;
  postUninstallScriptType?: DatabaseType;
  licenseType: LicenseType;
  licenseDescription?: string;
  useDynamicVersions: boolean;
  dynamicVersionsScriptId?: number;
  dynamicVersionsScriptType?: DatabaseType;
  downloadInstallerScriptId?: number;
  downloadInstallerScriptType?: DatabaseType;
}
export interface IGetLocalSoftwareVersionResponse
{
  deprecatedIdField?: number;
  softwareId: number;
  installScriptType?: DatabaseType;
  testScriptType?: DatabaseType;
  upgradeScriptType?: DatabaseType;
  uninstallScriptType?: DatabaseType;
  postUninstallScriptType?: DatabaseType;
  postInstallScriptType?: DatabaseType;
  software?: IGetLocalSoftwareResponse;
  updatedDateUTC: string;
  createdDateUTC: string;
  updatedBy?: string;
  displayName?: string;
  displayVersion?: string;
  semanticVersion: string;
  semanticVersionString: string;
  testRequired: boolean;
  uninstallIfNotInTarget: boolean;
  url?: string;
  relativeCacheSourcePath?: string;
  installerFile?: string;
  testFailedError?: string;
  packageHash?: string;
  installScriptId?: number;
  testScriptId?: number;
  upgradeScriptId?: number;
  uninstallScriptId?: number;
  postUninstallScriptId?: number;
  postInstallScriptId?: number;
  softwareIdentifier: string;
  numActionSuccesses: number;
  numActionFailures: number;
  blobName?: string;
  licenseType: LicenseType;
  upgradeStrategy: UpdateActionType;
  packageType: PackageType;
  installerType: SoftwareVersionInstallerType;
  lastResult?: MaintenanceActionResult;
  softwareType: SoftwareType;
  notes?: string;
  productCode?: string;
  dependsOnSemanticVersion?: string;
  hasOverrides: boolean;
}
export interface IGetMaintenanceActionResponse
{
  id: number;
  parentId?: number;
  maintenanceSessionId: number;
  tenantId?: number;
  computerId?: number;
  computerName?: string;
  isComputerOnline: boolean;
  startTime: string;
  endTime: string;
  actionType: MaintenanceActionType;
  actionTypeName: string;
  status: MaintenanceActionStatus;
  statusName: string;
  assignmentId?: number;
  assignmentType?: DatabaseType;
  tenantName?: string;
  maintenanceType: MaintenanceType;
  maintenanceIdentifier: string;
  desiredSoftwareState?: DesiredSoftwareState;
  description: string;
  postMaintenanceTest?: string;
  postMaintenanceTestType?: number;
  postMaintenanceTestResult?: boolean;
  postMaintenanceTestResultMessage?: string;
  maintenanceTypeName: string;
  maintenanceDisplayName?: string;
  detectedVersionString?: string;
  desiredVersionString?: string;
  reason: MaintenanceActionReason;
  result: MaintenanceActionResult;
  resultName: string;
  maintenanceTaskMode?: MaintenanceTaskMode;
  maintenanceTaskGetResult?: string;
  createdDateUTC: string;
  updatedDateUTC: string;
  updatedBy?: number;
  createdBy?: number;
  softwareType?: SoftwareType;
  maintenanceTaskType?: DatabaseType;
  resultReason?: MaintenanceActionResultReason;
  resultReasonMessage?: string;
  dependsOnNames: string[];
  dependentsNames: string[];
  softwareActionIdForConfigurationTask?: number;
  softwareProviderType: SoftwareProviderType;
  policyDescription?: string;
}
export interface IGetMaintenanceSessionLogResponse
{
  id: number;
  progressCorrelationId?: string;
  sessionPhaseId?: number;
  message?: string;
  maintenanceSessionId: number;
  maintenanceSessionStageId?: number;
  maintenanceActionId?: number;
  maintenanceActionStatus?: MaintenanceActionStatus;
  scriptId?: number;
  script?: string;
  scriptOutput?: string;
  scriptType?: DatabaseType;
  scriptLanguage?: ScriptLanguage;
  scriptParameters: { [key:string]: any };
  paramBlockParameters: { [key:string]: any };
  time: string;
  updatedTime?: string;
  sessionLogType: SessionLogType;
  scriptTimeout?: number;
  scriptExecutionContext?: ScriptExecutionContext;
  isPrimary: boolean;
  progressPercentComplete?: number;
  progressActivity?: string;
  progressStatus?: string;
  progressSecondsRemaining?: number;
  progressCurrentOperation?: string;
  progressCompleted: boolean;
}
export interface IGetMaintenanceSessionPhaseResponse
{
  id: number;
  phaseName: string;
  actionProgressPhaseName?: ActionProgressPhaseName;
  maintenanceSessionId: number;
  maintenanceSessionStageId: number;
  maintenanceActionId?: number;
  status: SessionPhaseStatus;
  progressPercentComplete?: number;
  progressStatus?: string;
  progressCompleted: boolean;
  dateStartedUtc?: string;
  dateCompletedUtc?: string;
}
export interface IGetMaintenanceSessionResponse
{
  id: number;
  jobId?: string;
  computerId?: number;
  tenantId?: number;
  personId?: number;
  scheduledId?: number;
  sessionStatus: SessionStatus;
  onboarding: boolean;
  createdDateUTC: string;
  updatedDateUTC: string;
  updatedById?: number;
  createdById?: number;
  createdBy?: string;
  fullMaintenance: boolean;
  computerName: string;
  tenantName: string;
  duration?: string;
  onboardingStageStatus?: SessionStatus;
  detectionStageStatus?: SessionStatus;
  executionStageStatus?: SessionStatus;
  agentUpdatesStageStatus?: SessionStatus;
  resolutionStageStatus?: SessionStatus;
  inventoryStageStatus?: SessionStatus;
  sessionJobArgs?: ISessionJobArgs;
  scheduledExecutionDate?: string;
  maintenanceActions?: IGetMaintenanceActionResponse[];
  stages?: IGetMaintenanceSessionStageResponse[];
  activities?: IMaintenanceActionActivityResponse[];
  logs?: IGetSessionLogResponse[];
  computer?: IGetComputerResponse;
  tenant?: IGetTenantResponse;
}
export interface IGetMaintenanceSessionStageResponse
{
  id: number;
  maintenanceSessionId: number;
  stageStatus: SessionStatus;
  type: SessionStageType;
  jobId?: string;
  updatedBy?: number;
  createdBy?: number;
  updatedDateUTC: string;
  createdDateUTC: string;
  maintenanceSession?: IGetMaintenanceSessionResponse;
}
export interface IGetOnboardingComputerResponse
{
  id: number;
  isOnline: boolean;
  onboardingStatus: ComputerOnboardingStatus;
  computerName?: string;
  tenantName?: string;
  onboardingFailed: boolean;
  onboardingSessionId?: number;
  serial?: string;
  updatedDate: string;
}
export interface IGetOptionalTargetAssignmentApprovalResponse
{
  id: number;
  targetAssignmentId: number;
  approved: TargetAssignmentApprovalStatus;
  targetType: TargetType;
  target?: string;
  updatedBy?: number;
  createdBy?: number;
  updatedDateUTC: string;
  createdDateUTC: string;
  maintenanceType: MaintenanceType;
}
export interface IGetPendingAgentResponse
{
  id: number;
  computerId?: number;
  providerLinkId: number;
  computerName?: string;
  isComputerDeleted: boolean;
  manufacturer?: string;
  operatingSystemName?: string;
  externalAgentId: string;
  dateAdded: string;
  isOnline: boolean;
  serial?: string;
  providerLinkName: string;
  externalClientName?: string;
  osInstallDateUtc?: string;
  requiresManualResolution: boolean;
  failed: boolean;
  identificationFailures: IGetAgentIdentificationFailureResponse[];
  requireManualIdentification: boolean;
}
export interface IGetPendingCountsResponse
{
  total: number;
  offline: number;
  failed: number;
  manual: number;
  recent: number;
  active: number;
}
export interface IGetPendingResponse
{
  count: number;
  results: IGetPendingAgentResponse[];
}
export interface IGetPersonResponse
{
  id: number;
  tenantId: number;
  azurePrincipalId?: string;
  firstName?: string;
  lastName?: string;
  emailAddress: string;
  displayName: string;
  updatedBy?: number;
  createdBy?: number;
  updatedDateUTC: string;
  createdDateUTC: string;
  primaryComputers?: IGetComputerResponse[];
  additionalComputers?: IGetComputerResponse[];
  user?: IGetUserResponse;
  userAffinities?: IGetUserAffinityResponse[];
}
export interface IGetPreferencesResponse
{
  applicationPreferences: IApplicationPreferences;
  tenantPreferences?: ITenantPreferences;
  userPreferences?: IUserPreferences;
}
export interface IGetProviderAgentIdentificationLogResponse
{
  id: number;
  providerAgentId: number;
  message: string;
  logType: AgentIdentificationLogType;
  timeUtc: string;
}
export interface IGetProviderAgentResponse
{
  agentVersion?: string;
  id: number;
  computerId?: number;
  providerLinkId: number;
  externalClientName: string;
  externalClientId: string;
  externalAgentId: string;
  runScriptPriority: number;
  providerTypeId: string;
  internalData?: any;
  isOnline: boolean;
  supportsRunningScripts: boolean;
  deviceUpdateFormData?: any;
  lastUpdatedUTC: string;
  deviceDetails: IDeviceDetails;
  onboardingOptions: IAgentOnboardingOptions;
  requireManualIdentification: boolean;
}
export interface IGetProviderClientResponse
{
  providerLinkId: number;
  externalClientId: string;
  linkedToTenantId?: number;
  externalClientName: string;
  internalData?: any;
  updatedBy?: number;
  updatedDateUTC: string;
  createdBy?: number;
  createdDateUTC: string;
  providerLink?: IGetProviderLinkResponse;
  status?: string;
  types?: string[];
}
export interface IGetRecommendedApprovalResponse
{
  globalTargetAssignmentId: number;
  approved: boolean;
  updatedBy?: string;
  createdBy?: string;
  updatedDateUTC: string;
  createdDateUTC: string;
}
export interface IGetReleasesResponse
{
  currentRelease?: IReleaseDetails;
  latestReleases: IReleaseDetails[];
}
export interface IGetScheduleResponse
{
  id: number;
  disabled: boolean;
  customCronExpression?: string;
  time?: string;
  day?: number;
  maintenanceTime?: string;
  sendDetectionEmail: boolean;
  sendDetectionEmailWhenAllActionsAreCompliant: boolean;
  sendFollowUpEmail: boolean;
  sendFollowUpOnlyIfActionNeeded: boolean;
  showRunNowButton: boolean;
  showPostponeButton: boolean;
  showMaintenanceActions: boolean;
  maintenanceType?: MaintenanceType;
  maintenanceIdentifier?: string;
  rebootPreference: RebootPreference;
  promptTimeoutAction: PromptTimeoutAction;
  autoConsentToReboots: boolean;
  promptTimeoutMinutes: number;
  applyWindowsUpdates: boolean;
  allowAccessToMSPResources: boolean;
  providerLink?: IGetProviderLinkResponse;
  updatedDateUTC: string;
  createdDateUTC: string;
  updatedBy?: number;
  createdBy?: number;
  updatedByName?: string;
  timeZoneInfoId?: string;
  offlineBehavior: ComputerOfflineMaintenanceSessionBehavior;
  suppressRebootsDuringBusinessHours: boolean;
  nextOccurenceDate?: string;
  useComputersTimezoneForExecution: boolean;
  scheduleExecutionAfterActiveHours: boolean;
  targetType: TargetType;
  target?: string;
  targetCategory: TargetCategory;
  targetGroupFilter: TargetGroupFilter;
  tenantId?: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  providerLinkId?: number;
  providerClientGroupType?: string;
  providerDeviceGroupType?: string;
  targetText?: string;
  targetTypeName: string;
  targetScopeName: string;
  targetMissing: boolean;
  tenantMissing: boolean;
  targetTypeMissing: boolean;
  targetTypeDescription: string;
  onboardingOnly: boolean;
  targetGroupFilterName: string;
}
export interface IGetScriptNameResponse
{
  id: number;
  name: string;
  updatedDateUtc: string;
  updatedBy?: string;
  action?: string;
  category: ScriptCategory;
}
export interface IGetScriptVariablesAndParametersResponse
{
  variables: { [key:string]: any };
  parameters: { [key:string]: any };
}
export interface IGetSessionLogResponse
{
  id: number;
  message?: string;
  maintenanceSessionId: number;
  maintenanceSessionStageId?: number;
  maintenanceActionId?: number;
  time: string;
  isPrimary: boolean;
  progressCorrelationId?: string;
  sessionPhaseId?: number;
  maintenanceActionStatus?: MaintenanceActionStatus;
  scriptId?: number;
  script?: string;
  scriptOutput?: string;
  scriptType?: DatabaseType;
  scriptLanguage?: ScriptLanguage;
  scriptParameters: { [key:string]: any };
  paramBlockParameters: { [key:string]: any };
  updatedTime?: string;
  sessionLogType: SessionLogType;
  progressPercentComplete?: number;
  progressActivity?: string;
  progressStatus?: string;
  progressSecondsRemaining?: number;
  progressCurrentOperation?: string;
  progressCompleted: boolean;
  maintenanceSession?: IGetMaintenanceSessionResponse;
  maintenanceSessionStage?: IGetMaintenanceSessionStageResponse;
  maintenanceAction?: IGetMaintenanceActionResponse;
}
export interface IGetSimplePersonResponse
{
  id: number;
  firstName?: string;
  lastName?: string;
  emailAddress: string;
  azurePrincipalId?: string;
  tenantName?: string;
  tenantId: number;
  fullName?: string;
  updatedDateUTC: string;
  createdDateUTC: string;
  updatedBy?: string;
  personTagIds: number[];
  onPremisesSecurityIdentifier?: string;
  roleIds: number[];
  userId?: number;
  expirationDateUTC?: string;
}
export interface IGetSmtpConfigResponse
{
  tenantId?: number;
  tenantName?: string;
  port: number;
  host: string;
  enableSSL: boolean;
  timeout: number;
  username?: string;
  enabled: boolean;
  useAuthentication: boolean;
}
export interface IGetSoftwarePrerequisiteResponse
{
  softwaresForCondition: IGetSpecifiedSoftwareResponse[];
  softwaresToPerformActionOn: IGetSpecifiedSoftwareResponse[];
  subjectQualifier: SubjectQualifier;
  condition: Condition;
  actionToPerform: ActionToPerform;
}
export interface IGetSoftwareVersionResponseBase
{
  displayName?: string;
  displayVersion?: string;
  semanticVersion: string;
  semanticVersionString: string;
  testRequired: boolean;
  uninstallIfNotInTarget: boolean;
  url?: string;
  relativeCacheSourcePath?: string;
  installerFile?: string;
  testFailedError?: string;
  packageHash?: string;
  installScriptId?: number;
  testScriptId?: number;
  upgradeScriptId?: number;
  uninstallScriptId?: number;
  postUninstallScriptId?: number;
  postInstallScriptId?: number;
  softwareIdentifier: string;
  numActionSuccesses: number;
  numActionFailures: number;
  blobName?: string;
  licenseType: LicenseType;
  upgradeStrategy: UpdateActionType;
  packageType: PackageType;
  installerType: SoftwareVersionInstallerType;
  lastResult?: MaintenanceActionResult;
  softwareType: SoftwareType;
  notes?: string;
  productCode?: string;
  dependsOnSemanticVersion?: string;
  hasOverrides: boolean;
}
export interface IGetSourceContextsResponse
{
  streamingEnabled: boolean;
  sourceContextLogLevels: ISourceContextLogLevel[];
}
export interface IGetSpecifiedSoftwareResponse
{
  softwareType: SoftwareType;
  softwareIdentifier: string;
}
export interface IGetSubscriptionDetailsResponse
{
  planId?: string;
  status?: SubscriptionStatus;
  trialStartUtc?: string;
  trialEndUtc?: string;
  subscriptionActivatedDateUtc?: string;
  featuresEnabledFromSubscription: IFeatureEnabledFromSubscription[];
}
export interface IGetSupportBrandingResponse
{
  providerId: string;
  supportSidebarTitle: string;
  headerAlertMessage: string;
  descriptionPlaceholderText: string;
  descriptionAlertMessage: string;
  footerMessage: string;
  showConfirmationCheckbox: boolean;
  sessionSupportButtonTitle: string;
  showSessionSupportConfirmCheckbox: boolean;
}
export interface IGetTagResponse
{
  id: number;
  name: string;
  description?: string;
  color?: string;
  updatedDateUTC: string;
  createdDateUTC: string;
  updatedBy?: string;
  owned: boolean;
  tenantTagAuthorizations: ITenantTagAuthorization[];
}
export interface IGetTargetAssignmentTypeResponse
{
  targetType: TargetType;
  tenantId?: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  target?: string;
  desiredSoftwareState?: DesiredSoftwareState;
  maintenanceTaskMode?: MaintenanceTaskMode;
  targetEnforcement: TargetEnforcement;
}
export interface IGetTenantPreferencesResponse
{
  id: number;
  tenantId: number;
  enableOnboarding: boolean;
  enableSessionEmails: boolean;
}
export interface IAzureTenantResponse
{
  principalId: string;
  consentDetails: IAzureTenantConsentDetails;
  azureTenantType: AzTenantType;
  partnerPrincipalId?: string;
  infoSyncedFromAzure?: IAzureTenantInfo;
  lastGetUsersSyncResult?: IAzureSyncResult;
  lastGetTenantInfoSyncResult?: IAzureSyncResult;
}
export interface IAzureTenantLinkResponse
{
  immyTenantId: number;
  azTenantId: string;
  azureTenant: IAzureTenantResponse;
  shouldLimitDomains: boolean;
  limitToDomains: IAzureTenantLinkDomainFilter[];
}
export interface IGetTenantResponse
{
  id: number;
  name: string;
  slug?: string;
  parentTenantId?: number;
  ownerTenantId?: number;
  active: boolean;
  azureTenantLink?: IAzureTenantLinkResponse;
  isMsp: boolean;
  updatedBy?: number;
  updatedDateUTC: string;
  createdBy?: number;
  createdDateUTC: string;
  tenantTagIds?: number[];
  tenantTagNames?: string[];
  markedForDeletionAtUtc?: string;
}
export interface IGetTenantSoftwareFromInventoryResponse
{
  azureUserObjectId?: string;
  computerId: number;
  computerName?: string;
  displayName: string;
  globalSoftwareId: number;
  globalSoftwareName: string;
  globalSoftwareVersion?: string;
  personId?: number;
  personName?: string;
  tenantId: number;
  dateDetectedUtc: string;
}
export interface IGetUserAffinityResponse
{
  id: number;
  computerId: number;
  personId: number;
  date: string;
  person?: IGetPersonResponse;
}
export interface IGetUserResponse
{
  id: number;
  email?: string;
  name?: string;
  tenantId: number;
  isAdmin: boolean;
  companyName?: string;
  type?: string;
  azurePrincipalId?: string;
  isExpired: boolean;
  expirationDateUTC?: string;
  personId?: number;
  canManageCrossTenantDeployments: boolean;
  hasManagementAccess: boolean;
  roles: string[];
}
export interface IGlobalMediaResponse
{
  id: number;
  name: string;
  fileName: string;
  mimeType?: string;
  packageHash?: string;
  blobReference: string;
  relativeCacheSourcePath?: string;
  updatedDateUTC: string;
  createdDateUTC: string;
  databaseType: DatabaseType;
  category: MediaCategory;
}
export interface IGlobalTargetAssignmentResource
{
  databaseType: DatabaseType;
  targetType: TargetType;
  target?: string;
  targetCategory: TargetCategory;
  targetGroupFilter: TargetGroupFilter;
  tenantId?: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  providerLinkId?: number;
  providerClientGroupType?: string;
  providerDeviceGroupType?: string;
  id: number;
  isCore: boolean;
  excluded: boolean;
  targetEnforcement: TargetEnforcement;
  notes?: string;
  notesUpdatedByUserName?: string;
  notesUpdatedUtc?: string;
  maintenanceType: MaintenanceType;
  maintenanceIdentifier: string;
  targetName?: string;
  softwareSemanticVersion?: string;
  softwareSemanticVersionString?: string;
  desiredSoftwareState?: DesiredSoftwareState;
  softwareProviderType?: SoftwareProviderType;
  licenseId?: number;
  licenseName?: string;
  updatedDateUTC: string;
  updatedBy?: string;
  createdDateUTC: string;
  maintenanceTaskMode?: MaintenanceTaskMode;
  taskParameterValues?: { [key:string]: IDeploymentParameterValue };
  sortOrder: number;
  integrationTypeId?: string;
  integrationPrompt?: string;
  providerLinkIdForMaintenanceItem?: number;
  targetText?: string;
  targetTypeName: string;
  targetScopeName: string;
  targetMissing: boolean;
  tenantMissing: boolean;
  targetTypeMissing: boolean;
  targetTypeDescription: string;
  onboardingOnly: boolean;
  targetGroupFilterName: string;
}
export interface IImmyIpAndHostnamesResponse
{
  ipInfo: IGetIpAddressesResponse;
  localBlobStorageEndpoint: string;
  globalBlobStorageEndpoint: string;
}
export interface IInventoryTaskResource
{
  id: number;
  inventoryTaskType: DatabaseType;
  name: string;
  frequency: InventoryTaskFrequency;
  specifiedNumMinutes?: number;
  scripts: IInventoryTaskScriptResource[];
  updatedDate: string;
  createdDate: string;
  fromProvider: boolean;
}
export interface IInventoryTaskScriptResource
{
  inventoryKey: string;
  scriptId: number;
  fromProvider: boolean;
}
export interface ILinkClientToNewTenantResponseBody
{
  tenantId?: number;
  tenantName?: string;
}
export interface ILocalMediaResponse
{
  owned: boolean;
  updatedBy?: string;
  tenants: ITenantMedia[];
  id: number;
  name: string;
  fileName: string;
  mimeType?: string;
  packageHash?: string;
  blobReference: string;
  relativeCacheSourcePath?: string;
  updatedDateUTC: string;
  createdDateUTC: string;
  databaseType: DatabaseType;
  category: MediaCategory;
}
export interface ILocalTargetAssignmentResource
{
  databaseType: DatabaseType;
  visibility?: ITargetAssignmentVisibilityResource;
  targetType: TargetType;
  target?: string;
  targetCategory: TargetCategory;
  targetGroupFilter: TargetGroupFilter;
  tenantId?: number;
  propagateToChildTenants: boolean;
  allowAccessToParentTenant: boolean;
  providerLinkId?: number;
  providerClientGroupType?: string;
  providerDeviceGroupType?: string;
  id: number;
  isCore: boolean;
  excluded: boolean;
  targetEnforcement: TargetEnforcement;
  notes?: string;
  notesUpdatedByUserName?: string;
  notesUpdatedUtc?: string;
  maintenanceType: MaintenanceType;
  maintenanceIdentifier: string;
  targetName?: string;
  softwareSemanticVersion?: string;
  softwareSemanticVersionString?: string;
  desiredSoftwareState?: DesiredSoftwareState;
  softwareProviderType?: SoftwareProviderType;
  licenseId?: number;
  licenseName?: string;
  updatedDateUTC: string;
  updatedBy?: string;
  createdDateUTC: string;
  maintenanceTaskMode?: MaintenanceTaskMode;
  taskParameterValues?: { [key:string]: IDeploymentParameterValue };
  sortOrder: number;
  integrationTypeId?: string;
  integrationPrompt?: string;
  providerLinkIdForMaintenanceItem?: number;
  targetText?: string;
  targetTypeName: string;
  targetScopeName: string;
  targetMissing: boolean;
  tenantMissing: boolean;
  targetTypeMissing: boolean;
  targetTypeDescription: string;
  onboardingOnly: boolean;
  targetGroupFilterName: string;
}
export interface IMaintenanceActionActivityResponse
{
  id: number;
  maintenanceSessionId: number;
  maintenanceActionId: number;
  activity: string;
  currentOperation?: string;
  activityId?: string;
  parentId?: string;
  percentComplete?: number;
  secondsRemaining?: number;
  sourceId?: string;
  status?: string;
  completed?: boolean;
  dateUtc: string;
  scriptName?: string;
}
export interface IMediaResponseBase
{
  id: number;
  name: string;
  fileName: string;
  mimeType?: string;
  packageHash?: string;
  blobReference: string;
  relativeCacheSourcePath?: string;
  updatedDateUTC: string;
  createdDateUTC: string;
  databaseType: DatabaseType;
  category: MediaCategory;
}
export interface IMediaSearchResponse
{
  global: IGlobalMediaResponse[];
  local: ILocalMediaResponse[];
}
export interface IMigrateToSupersedingAssignmentWhatIfResponse
{
  isNew: boolean;
  assignment: ILocalTargetAssignmentResource;
  policyDescription: string;
}
export interface IMyComputerResponse
{
  id: number;
  name?: string;
  isOnline: boolean;
}
export interface IOauth2AccessTokenWithTenantNameResponse
{
  tenantName?: string;
  createdDate: string;
  updatedDate: string;
  createdBy?: number;
  updatedBy?: number;
  id: number;
  consentData: IOauthConsentData;
  allowSilentRefresh: boolean;
  accessTokenId: string;
  tokenType: string;
  accessTokenExpiresAtUtc: string;
  tenantPrincipalId: string;
  refreshTokenId?: string;
  refreshTokenExpiresAtUtc?: string;
  identityTokenId?: string;
}
export interface IScriptActionAuditResult
{
  total: number;
  audit?: IScriptActionAudit;
}
export interface IScriptActionAudit
{
  newValue?: string;
  oldValue?: string;
  dateTimeUtc: string;
  userDisplayName?: string;
}
export interface IScriptAction
{
  action: string;
}
export interface IPackageAnalyzerScriptResponse
{
  id: number;
  action: string;
  name: string;
  scriptLanguage: ScriptLanguage;
  scriptExecutionContext: ScriptExecutionContext;
  scriptType: DatabaseType;
  scriptCategory: ScriptCategory;
  outputType: ScriptOutputType;
  timeout?: number;
}
export interface IProviderClientLinkToTenantByExactNameResponse
{
  externalClientId: string;
  linkedToTenantId?: number;
}
export interface IResolveOnboardingOverridableAssignmentsResponseBody
{
  overridableAssignments: ITargetAssignmentResource[];
  nextHighestPriorityNonOnboardingOnlyAssignments: ITargetAssignmentResource[];
}
export interface IRunImmyServiceResponseSession
{
  personId?: number;
  tenantId?: number;
  computerId?: number;
  sessionId: number;
}
export interface IRunImmyServiceResponseBody
{
  sessionsStarted: IRunImmyServiceResponseSession[];
  sessionsAlreadyInProgress: IRunImmyServiceResponseSession[];
}
export interface ISkipOnboardingResponse
{
  computerId: number;
  success: boolean;
  message: string;
}
export interface ISupportBlobUploadResponse
{
  fileName: string;
  blobName?: string;
  errorMessage?: string;
}
export interface ISystemHealthCheckResponse
{
  systemStatus: string;
  healthCheckDuration: string;
  components: IComponentHealthCheckResponse[];
}
export interface ITargetAssignmentDuplicateResponse
{
  duplicateIds: number[];
  count: number;
}
export interface ITargetAssignmentVisibilityResource
{
  selfService: boolean;
  technicianPod: boolean;
}
export interface ITenantComputerCountResponse
{
  tenantId: number;
  computerCount: number;
}
export interface ITenantConsentResponseBody
{
  tenantPrincipalId: string;
  isPartner: boolean;
}
export interface ITenantProviderLinksResponseItem
{
  id: number;
  providerTypeId: string;
  name: string;
  disabled: boolean;
  healthStatus: HealthStatus;
}
export interface ITimeZoneResource
{
  id: string;
  displayName: string;
}
export interface IGetClaimsResponse
{
  claims: IClaimResponse[];
}
export interface IClaimResponse
{
  type: string;
  value: string;
}
export interface IUserSilencedNotificationResponse
{
  id: number;
  userId: number;
  notificationType: NotificationType;
  notificationObjectId?: string;
  dateSilencedUtc: string;
}
export interface IDisabledPreflightScriptResponse
{
  id: number;
  scriptId: number;
  databaseType: DatabaseType;
}
export interface IGetGlobalScriptResponse
{
  scriptType: DatabaseType;
  name: string;
  id: number;
  action: string;
  scriptLanguage: ScriptLanguage;
  timeout?: number;
  scriptExecutionContext: ScriptExecutionContext;
  scriptCategory: ScriptCategory;
  outputType: ScriptOutputType;
  updatedDateUTC: string;
  createdDateUTC: string;
}
export interface IGetLocalScriptResponse
{
  owned: boolean;
  updatedBy: string;
  scriptType: DatabaseType;
  name: string;
  id: number;
  action: string;
  scriptLanguage: ScriptLanguage;
  timeout?: number;
  scriptExecutionContext: ScriptExecutionContext;
  scriptCategory: ScriptCategory;
  outputType: ScriptOutputType;
  updatedDateUTC: string;
  createdDateUTC: string;
}
export interface IGetLocalScriptResponseForMsp
{
  tenants: IGetTenantScriptResponse[];
  owned: boolean;
  updatedBy: string;
  scriptType: DatabaseType;
  name: string;
  id: number;
  action: string;
  scriptLanguage: ScriptLanguage;
  timeout?: number;
  scriptExecutionContext: ScriptExecutionContext;
  scriptCategory: ScriptCategory;
  outputType: ScriptOutputType;
  updatedDateUTC: string;
  createdDateUTC: string;
}
export interface IGetTenantScriptResponse
{
  tenantId: number;
  scriptId: number;
  relationship: Relationship;
}
export interface IScriptResource
{
  scriptType: DatabaseType;
  name: string;
  id: number;
  action: string;
  scriptLanguage: ScriptLanguage;
  timeout?: number;
  scriptExecutionContext: ScriptExecutionContext;
  scriptCategory: ScriptCategory;
  outputType: ScriptOutputType;
  updatedDateUTC: string;
  createdDateUTC: string;
}
export interface IScriptSearchResult
{
  id: number;
  scriptType: DatabaseType;
  scriptCategory: ScriptCategory;
  name: string;
  owned: boolean;
  action: string;
  scriptLanguage: ScriptLanguage;
  scriptExecutionContext: ScriptExecutionContext;
}
export interface IEventStream<T>
{
}
export interface IGetGlobalMaintenanceTaskResponse
{
  createdDateUTC: string;
  databaseType: DatabaseType;
  executeSerially: boolean;
  getEnabled: boolean;
  getScriptId?: number;
  getScriptType?: DatabaseType;
  icon?: IMediaResponseBase;
  iconMediaId?: number;
  id: number;
  ignoreDuringAutomaticOnboarding: boolean;
  isConfigurationTask: boolean;
  maintenanceTaskCategory: MaintenanceTaskCategory;
  name: string;
  notes?: string;
  onboardingOnly: boolean;
  parameters: IGetMaintenanceTaskParameterResponse[];
  recommended: boolean;
  setEnabled: boolean;
  setScriptId?: number;
  setScriptType?: DatabaseType;
  supersededByTaskId?: number;
  supersededByTaskMigrationScriptId?: number;
  supersededByTaskMigrationScriptType?: DatabaseType;
  supersededByTaskType?: DatabaseType;
  testEnabled: boolean;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  updatedDateUTC: string;
  useScriptParamBlock: boolean;
  integrationTypeId?: string;
}
export interface IGetLocalMaintenanceTaskResponse
{
  createdDateUTC: string;
  databaseType: DatabaseType;
  executeSerially: boolean;
  getEnabled: boolean;
  getScriptId?: number;
  getScriptType?: DatabaseType;
  icon?: IMediaResponseBase;
  iconMediaId?: number;
  id: number;
  ignoreDuringAutomaticOnboarding: boolean;
  isConfigurationTask: boolean;
  maintenanceTaskCategory: MaintenanceTaskCategory;
  name: string;
  notes?: string;
  onboardingOnly: boolean;
  owned: boolean;
  parameters: IGetMaintenanceTaskParameterResponse[];
  recommended: boolean;
  setEnabled: boolean;
  setScriptId?: number;
  setScriptType?: DatabaseType;
  supersededByTaskId?: number;
  supersededByTaskMigrationScriptId?: number;
  supersededByTaskMigrationScriptType?: DatabaseType;
  integrationTypeId?: string;
  supersededByTaskType?: DatabaseType;
  tenants: ITenantMaintenanceTask[];
  testEnabled: boolean;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  updatedBy?: string;
  updatedDateUTC: string;
  useScriptParamBlock: boolean;
}
export interface IGetMaintenanceTaskParameterResponse
{
  id: number;
  maintenanceTaskId: number;
  name: string;
  dataType: MaintenanceTaskParameterType;
  required: boolean;
  selectableValues: string[];
  notes?: string;
  defaultValue?: string;
  hidden: boolean;
  order: number;
  defaultMediaId?: number;
  defaultMediaDatabaseType?: DatabaseType;
  defaultMedia?: IMediaResponseBase;
}
export interface IMaintenanceTaskSearchResult
{
  id: number;
  databaseType: DatabaseType;
  name: string;
  owned: boolean;
  useScriptParamBlock: boolean;
}
