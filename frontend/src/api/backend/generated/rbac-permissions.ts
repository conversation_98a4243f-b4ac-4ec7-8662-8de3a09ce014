//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

export type RbacPermissions = 
  | "application_locks:manage"
  | "application_locks:view"
  | "application_preferences:manage"
  | "application_preferences:view"
  | "audit:view"
  | "azure_operations:sync_contracts"
  | "azure_operations:update_contract_links"
  | "azure_operations:update_permission_levels"
  | "azure_operations:view"
  | "billing:manage"
  | "billing:view"
  | "branding:global_manage"
  | "branding:manage"
  | "branding:send_test_email"
  | "branding:set_global_default"
  | "branding:view"
  | "computers:access_computer_terminal"
  | "computers:change_tenant"
  | "computers:export"
  | "computers:identify_agents"
  | "computers:manage"
  | "computers:manage_primary_person"
  | "computers:manage_registry"
  | "computers:onboard"
  | "computers:remote_access"
  | "computers:search_inventory"
  | "computers:user_affinity"
  | "computers:view"
  | "computers:view_agent_status_report"
  | "computers:view_inventory_report"
  | "computers:view_registry"
  | "deployments:deployment_approver"
  | "deployments:manage_cross_tenant"
  | "deployments:manage_cross_tenant_with_change_requests"
  | "deployments:manage_deployment_recommendations"
  | "deployments:manage_individual"
  | "deployments:manage_maintenance_item_ordering"
  | "deployments:manage_single_tenant"
  | "deployments:migrate_deployment_to_integration"
  | "deployments:override"
  | "deployments:run"
  | "deployments:supersede"
  | "deployments:view_cross_tenant"
  | "deployments:view_deployments_for_visibility"
  | "deployments:view_individual"
  | "deployments:view_single_tenant"
  | "dev_lab:manage"
  | "dynamic_integration_types:manage"
  | "dynamic_integration_types:view"
  | "getting_started:view"
  | "global:manage"
  | "integrations:link_clients"
  | "integrations:manage"
  | "integrations:sync_agents"
  | "integrations:view"
  | "integrations:view_psa_tickets"
  | "inventory_task:manage"
  | "inventory_task:view"
  | "licenses:download"
  | "licenses:manage"
  | "licenses:view"
  | "maintenance_sessions:manage"
  | "maintenance_sessions:rerun"
  | "maintenance_sessions:resume"
  | "maintenance_sessions:view"
  | "maintenance_tasks:manage"
  | "maintenance_tasks:view"
  | "manager:manage"
  | "media:manage"
  | "media:view"
  | "metrics:manage"
  | "metrics:view"
  | "no_authorization:no_authorization"
  | "notifications:view_admin_notifications"
  | "oauth:manage"
  | "oauth:view"
  | "persons:manage"
  | "persons:view"
  | "rbac:manage"
  | "rbac:view"
  | "schedules:manage"
  | "schedules:view"
  | "scripts:can_access_parent_tenant"
  | "scripts:manage"
  | "scripts:run"
  | "scripts:view"
  | "smtp_configurations:manage"
  | "smtp_configurations:view"
  | "software:manage"
  | "software:upload_versions"
  | "software:view"
  | "syncs:trigger_azure_user_sync"
  | "syncs:trigger_user_affinity_sync"
  | "system_operations:fetch_ip_addresses"
  | "system_operations:get_releases"
  | "system_operations:manage_support_access"
  | "system_operations:pull_updates"
  | "system_operations:restart_backend"
  | "system_operations:view"
  | "system_operations:view_open_script_editors"
  | "tags:manage"
  | "tags:view"
  | "tenant_preferences:manage"
  | "tenant_preferences:view"
  | "tenants:activation_control"
  | "tenants:manage"
  | "tenants:view"
  | "users:assign_cross_tenant_roles"
  | "users:impersonate"
  | "users:manage"
  | "users:manage_access_requests"
  | "users:view";
