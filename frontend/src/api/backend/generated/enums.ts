//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

export enum AzurePermissionLevel2 {
  DefaultAppRegistration = 1,
  CustomAppRegistration = 3
}
export enum TargetSpecifier {
  Software = 0,
  MaintenanceTask = 1
}
export enum ScriptExecutionContext {
  System = 0,
  CurrentUser = 1,
  Metascript = 2,
  CloudScript = 4
}
export enum Relationship {
  Owned = 0,
  Assigned = 1
}
export enum ScriptCategory {
  SoftwareDetection = 0,
  SoftwareVersionAction = 2,
  MaintenanceTaskSetter = 3,
  MetascriptDeploymentTarget = 4,
  FilterScriptDeploymentTarget = 5,
  DeviceInventory = 6,
  Function = 7,
  ImmySystem = 8,
  DynamicVersions = 9,
  DownloadInstaller = 10,
  Module = 11,
  Preflight = 12,
  Integration = 13
}
export enum SoftwareScriptCategory {
  Detection = 0
}
export enum SoftwareVersionScriptCategory {
  Install = 0,
  Uninstall = 1,
  Test = 2,
  Upgrade = 3,
  PostInstall = 4,
  PostUninstall = 5
}
export enum BitsJobState {
  Queued = 0,
  Connecting = 1,
  Transferring = 2,
  Suspended = 3,
  Error = 4,
  TransientError = 5,
  Transferred = 6,
  Acknowledged = 7,
  Cancelled = 8
}
export enum TargetScope {
  CrossTenant = 0,
  SingleTenant = 1,
  Individual = 2
}
export enum TargetCategory {
  Computer = 0,
  Tenant = 1,
  Person = 2
}
export enum TargetType {
  All = 1,
  Computer = 2,
  Metascript = 5,
  Person = 7,
  AzureGroup = 8,
  TenantMetascript = 20,
  AllForTenant = 21,
  ProviderDeviceGroup = 22,
  ProviderClientGroup = 23,
  TenantFilterScript = 24,
  FilterScript = 25,
  AllTenants = 28,
  SpecificTenant = 29,
  Tag = 30,
  TenantTag = 31,
  CloudTag = 40,
  CloudProviderClientGroup = 42
}
export enum TargetGroupFilter {
  All = 0,
  Servers = 1,
  Workstations = 2,
  PortableDevices = 3,
  WorkstationsAndPortableDevices = 4,
  DomainControllers = 5,
  PrimaryDomainControllers = 6
}
export enum DesiredSoftwareState {
  NewerOrEqualVersion = 0,
  NotPresent = 1,
  AnyVersion = 2,
  ThisVersion = 3,
  OlderOrEqualVersion = 4,
  LatestVersion = 5,
  NoAction = 6,
  UpdateIfFound = 7
}
export enum SoftwareProviderType {
  Ninite = 1,
  Chocolatey = 2,
  Inherent = 3
}
export enum TargetEnforcement {
  Required = 1,
  Optional = 2,
  Adhoc = 3,
  Onboarding = 4
}
export enum TargetVisibility {
  SelfService = 1,
  TechnicianTools = 2
}
export enum TargetAssignmentApprovalStatus {
  Pending = 1,
  Approved = 2,
  Denied = 3
}
export enum PackageType {
  None = 0,
  EntireFolder = 1,
  InstallerFile = 2
}
export enum InstallerType {
  Msi = 1,
  Exe = 2,
  Zip = 3
}
export enum Priority {
  Now = 0,
  Scheduled = 1
}
export enum UpdateActionType {
  None = 0,
  UninstallInstall = 1,
  InstallOver = 2,
  UpgradeScript = 3
}
export enum RepairActionType {
  None = 0,
  UninstallInstall = 1,
  InstallOver = 2,
  CustomScript = 3
}
export enum LoggingVerbosity {
  Debug = 0,
  Information = 1
}
export enum RebootPreference {
  Normal = 0,
  Suppress = 1,
  Prompt = 2,
  Force = -1
}
export enum PromptTimeoutAction {
  Reboot = 0,
  Suppress = 1,
  FailSession = 2
}
export enum Platform {
  Windows = 1,
  Mac = 2,
  Linux = 3
}
export enum LicenseType {
  None = 0,
  LicenseFile = 1,
  Key = 2
}
export enum SoftwareLicenseRequirement {
  None = 0,
  Required = 1,
  Optional = 2
}
export enum DownloadResult {
  Failed = 0,
  Success = 1,
  FileAlreadyExists = 2
}
export enum SessionType {
  Cloud = 1,
  Computer = 2,
  Person = 3
}
export enum SessionStatus {
  Passed = 0,
  Running = 1,
  Failed = 2,
  Created = 3,
  Cancelled = 4,
  Postponed = 5,
  Pending = 6,
  PartialPassed = 7,
  PendingConnectivity = 8,
  Missed = 9,
  MaxTrackedDevicesPerMonthExceeded = 10,
  PendingPreflight = 11,
  PendingEphemeralAgentConnection = 12,
  MaintenanceForTrackableDevicesNotAllowed = 13
}
export enum SoftwareType {
  GlobalSoftware = 0,
  LocalSoftware = 1,
  WindowsUpdate = 2,
  Chocolatey = 3,
  Ninite = 4
}
export enum SoftwareRegistryContext {
  Machine = 0,
  User = 1,
  System = 2
}
export enum SessionStageType {
  Onboarding = 0,
  Detection = 1,
  Execution = 2,
  AgentUpdates = 3,
  Resolution = 4,
  Inventory = 5
}
export enum PersonLinkType {
  AD = 0,
  O365 = 1
}
export enum SoftwareVersionInstallerType {
  None = 0,
  File = 1,
  Url = 2
}
export enum AzureContractActionType {
  Link = 0,
  Ignore = 1
}
export enum AzureResourceAlias {
  AzureAD = 0,
  MSGraph = 1,
  KeyVault = 2
}
export enum MaintenanceActionStatus {
  NotStarted = 0,
  Downloading = 1,
  PerformingAction = 2,
  DeterminingResult = 3,
  Complete = 4,
  WaitingForPrerequisite = 5,
  DeterminingAction = 6,
  Skipped = 7,
  PendingExecution = 8
}
export enum MaintenanceActionType {
  NoAction = 0,
  Install = 1,
  Update = 2,
  Uninstall = 3,
  Download = 4,
  Reinstall = 5,
  Downgrade = 6,
  Undetermined = 7,
  TaskEnforce = 8,
  TaskMonitor = 9,
  TaskAudit = 10
}
export enum MaintenanceActionReason {
  SoftwareMissing = 0,
  UpdateAvailable = 1,
  UpToDate = 2,
  NotAssigned = 3,
  TestFailed = 4,
  SoftwareDetected = 5,
  OlderVersionRequired = 6,
  NoApplicableSoftwareVersion = 7,
  NoAction = 8,
  Undetermined = 9,
  TaskEnforce = 10,
  TaskAudit = 11,
  TaskMonitor = 12,
  TaskNotCompliant = 13,
  TaskCompliant = 14,
  DeviceOffline = 15,
  RepairTriggered = 16
}
export enum MaintenanceActionResult {
  Pending = 0,
  Success = 1,
  Failed = 2,
  Cancelled = 3,
  Indeterminable = 4,
  Resolved = 5
}
export enum MaintenanceActionResultReason {
  NoLicenseAvailable = 0,
  SoftwareNotFound = 1,
  DetectionActionFailed = 2,
  TaskNotFound = 3,
  TaskNotCompliant = 4,
  TaskCompliant = 5,
  TaskSetIsMissing = 6,
  TaskTestIsMissing = 7,
  TaskGetIsMissing = 8,
  SoftwareConfigurationTaskMissing = 9,
  TaskModeIsMissing = 10,
  DeviceOffline = 11,
  RebootsSuppressed = 12
}
export enum ComputerOnboardingStatus {
  NeedsOnboarding = 0,
  Onboarding = 1,
  Onboarded = 2
}
export enum SubjectQualifier {
  AnyOf = 0,
  AllOf = 1
}
export enum Condition {
  Installed = 0,
  NotInstalled = 1
}
export enum ActionToPerform {
  Install = 0,
  Uninstall = 1,
  Fail = 2
}
export enum SessionLogType {
  Generic = 0,
  Error = 1,
  Command = 2,
  CommandResult = 3,
  Progress = 4
}
export enum SessionPhaseStatus {
  Unstarted = 0,
  Skipped = 1,
  Running = 2,
  Succeeded = 3,
  Failed = 4
}
export enum MaintenanceTaskCategory {
  Computer = 0,
  Tenant = 1,
  Person = 2
}
export enum DatabaseType {
  Global = 0,
  Local = 1
}
export enum MaintenanceType {
  GlobalSoftware = 0,
  LocalSoftware = 1,
  WindowsUpdate = 2,
  ChocolateySoftware = 3,
  NiniteSoftware = 4,
  LocalMaintenanceTask = 5,
  GlobalMaintenanceTask = 6,
  AgentUpdate = 7
}
export enum MaintenanceTaskParameterType {
  Number = 0,
  Text = 1,
  Boolean = 2,
  Select = 3,
  Password = 4,
  File = 5,
  Uri = 6,
  KeyValuePair = 7
}
export enum MaintenanceTaskMode {
  Enforce = 0,
  Audit = 1,
  Monitor = 2,
  NoAction = 3
}
export enum AgentIdentificationManualResolutionDecision {
  DeleteExisting = 0,
  GenerateNewDeviceId = 1,
  OverwriteExisting = 2
}
export enum InventoryTaskFrequency {
  SpecifiedNumMinutes = 0,
  EveryMinute = 1,
  Hourly = 2,
  Daily = 3,
  Weekly = 4
}
export enum ScriptOutputType {
  Object = 0,
  Table = 1
}
export enum DetectionMethod {
  SoftwareTable = 0,
  CustomDetectionScript = 1,
  UpgradeCode = 2,
  ProductCode = 3
}
export enum SoftwareTableNameSearchMode {
  Contains = 0,
  Regex = 1,
  Traditional = 2
}
export enum MediaCategory {
  None = 0,
  SoftwareIcon = 1
}
export enum PendingResolutionAction {
  CreateComputer = 0,
  ReplaceComputer = 1,
  AssignAgentToComputer = 2,
  ReplaceAgent = 3,
  Dedupe = 4,
  KeepBoth = 5
}
export enum MaintenanceItemOrderLocation {
  Beginning = 1,
  DontCare = 2,
  End = 3
}
export enum ExpirationTime {
  OneHour = 1,
  OneDay = 2,
  Indefinite = 3,
  ThreeDays = 4
}
export enum TimelineEventType {
  AgentConnected = 1,
  AgentDisconnected = 2,
  AgentCreated = 3,
  AgentDeleted = 4,
  MaintenanceActionStarted = 5,
  MaintenanceActionCompleted = 6,
  ComputerOnline = 7,
  ComputerOffline = 8,
  EphemeralAgentSessionCreated = 9,
  EphemeralAgentSessionConnected = 10,
  EphemeralAgentSessionConnectionTimeout = 11,
  EphemeralAgentSessionNotConnected = 12,
  EphemeralAgentSessionConnectionLost = 13,
  EphemeralAgentSessionIdleShutdown = 14,
  EphemeralAgentSessionZombieKill = 15,
  EphemeralAgentSessionCreationFailure = 16,
  EphemeralAgentSessionDisconnected = 17,
  RemoteSessionStarted = 18,
  RemoteSessionEnded = 19,
  EphemeralAgentSessionUpdateInstall = 20,
  AgentUpdated = 21,
  EphemeralAgentSessionAlreadyConnected = 22,
  EphemeralAgentSessionDisposed = 23
}
export enum TimelineObjectType {
  Computer = 1,
  ProviderAgent = 2,
  Tenant = 3
}
export enum ComputerCircuitBreakerPolicy {
  Unrestricted = 1,
  BypassEphemeral = 2
}
export enum ComputerOfflineMaintenanceSessionBehavior {
  Skip = 1,
  ApplyOnConnect = 2
}
export enum MaintenanceTaskScriptMethod {
  Test = 1,
  Get = 2,
  Set = 3
}
export enum TagType {
  Computer = 1,
  Tenant = 2,
  Person = 3
}
export enum SyncState {
  Running = 1,
  Success = 2,
  Failed = 3
}
export enum AgentIdentificationLogType {
  Verbose = 1,
  Error = 2
}
export enum TrustedManufacturer {
  Dell = 1,
  HP = 2,
  Lenovo = 3
}
export enum AppRegistrationType {
  Backend = 0,
  Custom = 1
}
export enum EnqueuedSessionType {
  HangfireAdhoc = 1,
  HangfireOnboarding = 2,
  HangfireScheduled = 3,
  HangfireConnectivityTriggered = 4,
  NotHangfire = 5
}
export enum SessionGroupEventType {
  SessionCreated = 1,
  SessionFailedToBeCreated = 2
}
export enum ScriptReferenceType {
  Software = 1,
  SoftwareVersion = 2,
  MaintenanceTask = 3
}
export enum ImmyBotRemoteControlFeatureStatus {
  Enabled = 1,
  Disabled = 2,
  NotSupported = 3
}
export enum FeatureEntitlementType {
  Switch = 1,
  Quantity = 2,
  Range = 3,
  Custom = 4
}
export enum ProviderTypeSource {
  BuiltIn = 1,
  Local = 2,
  Global = 3
}
export enum ProviderAgentFilter {
  All = 1,
  OnlyPendingIdentification = 2,
  OnlyIdentified = 3,
  RequiresManualDecision = 4,
  AssociatedToDeletedComputers = 5
}
export enum AzTenantType {
  Partner = 1,
  Customer = 2,
  Standalone = 3
}
export enum NotificationAcknowledgement {
  Unacknowledged = 1,
  Acknowledged = 2,
  Dismissed = 3
}
export enum NotificationSeverity {
  Critical = 1,
  Error = 2,
  Warning = 3,
  Info = 4
}
export enum AccessTokenSource {
  Obo = 1,
  DefaultAppRegistration = 2,
  CustomAppRegistration = 3,
  OboPartnerCenter = 4
}
export enum ChangeRequestObjectType {
  TargetAssignment = 1,
  Script = 2
}
export enum IntegrationTag {
  Developer = 1,
  Alpha = 2,
  Beta = 3,
  Production = 4
}
export enum ChangeRequestState {
  WaitingForApproval = 1,
  Denied = 2,
  ApprovedAndApplied = 3,
  NeedsAttention = 4
}
export enum TenantRestrictionMode {
  AllTenants = 0,
  Include = 1,
  Exclude = 2
}
export enum PersonType {
  All = 0,
  User = 1,
  Person = 2
}
export enum FeatureEnum {
  RecurringUserComputerAffinityJobFeature = 0,
  RecurringSyncAzureUsersJobFeature = 1,
  RecurringInventoryJobFeature = 2,
  SchedulesFeature = 3,
  DevLabFeature = 4,
  ImmyBotRemoteControlSessionsFeature = 5,
  TrackedDevicesFeature = 6,
  SelfServiceFeature = 7,
  GetImmyComputerUseParentTenantFeature = 8,
  OptionalDeploymentFeature = 9,
  PersonTasksFeature = 10,
  AgentStatusListFeature = 11,
  RBACFeature = 12,
  RBACCustomRolesFeature = 13
}
export enum ActionProgressPhaseName {
  DetectInstalledAgentVersion = 0,
  UpdateAgentVersion = 1,
  VerifyAgentVersionUpdated = 2,
  DetectInstalledVersion = 200,
  DetermineDesiredVersion = 201,
  TestSoftware = 202,
  CheckForSoftwareLicense = 203,
  AddConfigTask = 204,
  TestTask = 205,
  MonitorTask = 206,
  AuditTask = 207,
  FetchDesiredVersion = 301,
  EnforceTask = 302,
  ApplyWindowsPatch = 305,
  DownloadConfigTaskFiles = 306,
  FetchDependentVersion = 307,
  InstallDependentVersion = 308,
  InstallSoftware = 309,
  RunUpgradeSoftwareScript = 310,
  UninstallSoftware = 311,
  RepairSoftware = 313,
  DownloadInstaller = 314,
  PostUninstallSoftware = 315,
  PostInstallSoftware = 316,
  TestTaskBeforeExecution = 317,
  TestSoftwareAfterExecution = 318,
  VerifyTask = 319,
  VerifySoftwareIsInDesiredState = 320
}
export enum DeletionStatus {
  Pending = 0,
  InProgress = 1,
  Completed = 2,
  Errored = 3
}
export enum AzurePermissionLevel {
  Me = 0,
  MyOrganization = 1,
  MyCustomers = 2,
  DefaultAppRegistrationWithGdap = 3,
  CustomAppRegistrationWithGdap = 4
}
export enum SubscriptionStatus {
  InTrial = 0,
  Active = 1,
  Inactive = 2
}
export enum SubscriptionItemType {
  UnKnown = 0,
  Plan = 1,
  Addon = 2,
  Charge = 3
}
export enum SubscriptionFeatureStatus {
  UnKnown = 0,
  Active = 1,
  Archived = 2,
  Draft = 3
}
export enum SubscriptionFeatureType {
  UnKnown = 0,
  Switch = 1,
  Custom = 2,
  Quantity = 3,
  Range = 4
}
export enum ReleaseChannel {
  Developer = 1,
  Alpha = 2,
  Beta = 3,
  General = 4
}
export enum JoinedSessionGroupType {
  None = 0,
  Computer = 1,
  Tenant = 2,
  Session = 3
}
export enum HealthStatus {
  Unhealthy = 0,
  Degraded = 1,
  Healthy = 2
}
export enum ClaimCode {
  NewClaim = 1,
  ExistingClaim = 2,
  NoAvailableVmsToClaim = 3,
  Error = 4
}
export enum FeatureResetInterval {
  Daily = 0,
  Monthly = 1
}
export enum WindowsSessionType {
  Console = 1,
  RDP = 2
}
export enum ScriptLanguage {
  CommandLine = 1,
  PowerShell = 2
}
export enum LogLevel {
  Trace = 0,
  Debug = 1,
  Information = 2,
  Warning = 3,
  Error = 4,
  Critical = 5,
  None = 6
}
export enum RegistryHiveDto {
  ClassesRoot = -**********,
  CurrentUser = -**********,
  LocalMachine = -**********,
  Users = -**********,
  PerformanceData = -**********,
  CurrentConfig = -**********
}
export enum RegistryViewDto {
  Default = 0,
  Registry64 = 256,
  Registry32 = 512
}
export enum RegistryValueKindDto {
  Unknown = 0,
  String = 1,
  ExpandString = 2,
  Binary = 3,
  DWord = 4,
  MultiString = 7,
  QWord = 11,
  None = -1
}
export enum RegistrySearchTargets {
  None = 0,
  Key = 1,
  ValueName = 2,
  ValueData = 4,
  All = 7
}
export enum RegistrySearchResultType {
  Unknown = 0,
  Key = 1,
  ValueName = 2,
  ValueData = 3
}
export enum ApplicationLockEventTypes {
  CurrentlyHoldsEvent = 0,
  WaitingEvent = 1,
  HoldingEvent = 2,
  ReleasedEvent = 3,
  TokenAbortedWaitingEvent = 4,
  TimeoutAbortedWaitingEvent = 5,
  WatchdogAbortedHoldingEvent = 6,
  AbortedLockHolderDisposedHandleEvent = 7,
  InputTokenAbortedHoldingEvent = 8,
  TokenCancellationRequestedEvent = 9,
  WatchdogTimeoutCancellationRequestedEvent = 10,
  ExternalTokenCancellationRequestedEvent = 11,
  ExternalTokenAbortedHoldingEvent = 12
}
export enum PermissionCategory {
  View = 0,
  CoreCapability = 1,
  Advanced = 2
}
