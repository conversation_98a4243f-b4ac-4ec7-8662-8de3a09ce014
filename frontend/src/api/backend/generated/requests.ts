//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { IOauthConsentData } from './interfaces';
import { IOauth2AuthCodeErrorResponse } from './interfaces';
import { DatabaseType } from './enums';
import { ExpirationTime } from './enums';
import { TargetType } from './enums';
import { TargetGroupFilter } from './enums';
import { ISoftwarePrerequisite } from './interfaces';
import { SoftwareLicenseRequirement } from './enums';
import { DetectionMethod } from './enums';
import { SoftwareTableNameSearchMode } from './enums';
import { LicenseType } from './enums';
import { UpdateActionType } from './enums';
import { SoftwareType } from './enums';
import { PackageType } from './enums';
import { SoftwareVersionInstallerType } from './enums';
import { TargetCategory } from './enums';
import { MaintenanceType } from './enums';
import { RebootPreference } from './enums';
import { PromptTimeoutAction } from './enums';
import { ComputerOfflineMaintenanceSessionBehavior } from './enums';
import { ScriptExecutionContext } from './enums';
import { ScriptCategory } from './enums';
import { IParameterValue } from './interfaces';
import { InventoryTaskFrequency } from './enums';
import { DesiredSoftwareState } from './enums';
import { TargetVisibility } from './enums';
import { IDeploymentParameterValue } from './interfaces';
import { SoftwareProviderType } from './enums';
import { MaintenanceTaskMode } from './enums';
import { IIDomainScript } from './interfaces';
import { AppRegistrationType } from './enums';
import { LogLevel } from './enums';
import { ComputerOnboardingStatus } from './enums';
import { IUpdateRecommendedApprovalPayload } from './interfaces';
import { ScriptLanguage } from './enums';
import { ScriptOutputType } from './enums';
import { ITenantScript } from './interfaces';

export class AcknowledgeNotificationsRequest implements IAcknowledgeNotificationsRequest
{
  public notificationIds: string[];
  constructor (obj: IAcknowledgeNotificationsRequest)
  {
    this.notificationIds = obj.notificationIds;
  }
}
export interface IAcknowledgeNotificationsRequest
{
  notificationIds: string[];
}
export class AddChangeRequestCommentBody implements IAddChangeRequestCommentBody
{
  public comment: string;
  constructor (obj: IAddChangeRequestCommentBody)
  {
    this.comment = obj.comment;
  }
}
export interface IAddChangeRequestCommentBody
{
  comment: string;
}
export class AddTagsRequest implements IAddTagsRequest
{
  public entityIds: number[];
  public tagIds: number[];
  constructor (obj: IAddTagsRequest)
  {
    this.entityIds = obj.entityIds;
    this.tagIds = obj.tagIds;
  }
}
export interface IAddTagsRequest
{
  entityIds: number[];
  tagIds: number[];
}
export class RemoveTagsRequest implements IRemoveTagsRequest
{
  public entityIds: number[];
  public tagIds: number[];
  constructor (obj: IRemoveTagsRequest)
  {
    this.entityIds = obj.entityIds;
    this.tagIds = obj.tagIds;
  }
}
export interface IRemoveTagsRequest
{
  entityIds: number[];
  tagIds: number[];
}
export class AnalyzePackageParams implements IAnalyzePackageParams
{
  public blobName?: string;
  public fileName?: string;
  public url?: string;
  constructor (obj: IAnalyzePackageParams = {})
  {
    this.blobName = obj.blobName;
    this.fileName = obj.fileName;
    this.url = obj.url;
  }
}
export interface IAnalyzePackageParams
{
  blobName?: string;
  fileName?: string;
  url?: string;
}
export class BeginAuthCodeFlowRequest implements IBeginAuthCodeFlowRequest
{
  public allowSilentRefresh: boolean;
  public oauthConsentData: IOauthConsentData;
  constructor (obj: IBeginAuthCodeFlowRequest)
  {
    this.allowSilentRefresh = obj.allowSilentRefresh;
    this.oauthConsentData = obj.oauthConsentData;
  }
}
export interface IBeginAuthCodeFlowRequest
{
  allowSilentRefresh: boolean;
  oauthConsentData: IOauthConsentData;
}
export class BeginAuthCodeFlowResponse implements IBeginAuthCodeFlowResponse
{
  public oauthHookId: string;
  constructor (obj: IBeginAuthCodeFlowResponse)
  {
    this.oauthHookId = obj.oauthHookId;
  }
}
export interface IBeginAuthCodeFlowResponse
{
  oauthHookId: string;
}
export class FailAuthCodeFlowRequest implements IFailAuthCodeFlowRequest
{
  public oauthErrorResponse: IOauth2AuthCodeErrorResponse;
  public oauthHookId: string;
  constructor (obj: IFailAuthCodeFlowRequest)
  {
    this.oauthErrorResponse = obj.oauthErrorResponse;
    this.oauthHookId = obj.oauthHookId;
  }
}
export interface IFailAuthCodeFlowRequest
{
  oauthErrorResponse: IOauth2AuthCodeErrorResponse;
  oauthHookId: string;
}
export class BuildDynamicParametersRequest implements IBuildDynamicParametersRequest
{
  public databaseType: DatabaseType;
  public maintenanceTaskId: number;
  public tenantId?: number;
  constructor (obj: IBuildDynamicParametersRequest)
  {
    this.databaseType = obj.databaseType;
    this.maintenanceTaskId = obj.maintenanceTaskId;
    this.tenantId = obj.tenantId;
  }
}
export interface IBuildDynamicParametersRequest
{
  databaseType: DatabaseType;
  maintenanceTaskId: number;
  tenantId?: number;
}
export class BulkCreateTenantRequestBodyListItem implements IBulkCreateTenantRequestBodyListItem
{
  public name: string;
  public limitToDomains?: string[];
  public partnerPrincipalId?: string;
  public principalId?: string;
  public slug?: string;
  constructor (obj: IBulkCreateTenantRequestBodyListItem)
  {
    this.name = obj.name;
    this.limitToDomains = obj.limitToDomains;
    this.partnerPrincipalId = obj.partnerPrincipalId;
    this.principalId = obj.principalId;
    this.slug = obj.slug;
  }
}
export interface IBulkCreateTenantRequestBodyListItem
{
  name: string;
  limitToDomains?: string[];
  partnerPrincipalId?: string;
  principalId?: string;
  slug?: string;
}
export class BulkCreateTenantRequestBody implements IBulkCreateTenantRequestBody
{
  public ownerTenantId: number;
  public tenants: IBulkCreateTenantRequestBodyListItem[];
  constructor (obj: IBulkCreateTenantRequestBody)
  {
    this.ownerTenantId = obj.ownerTenantId;
    this.tenants = obj.tenants;
  }
}
export interface IBulkCreateTenantRequestBody
{
  ownerTenantId: number;
  tenants: IBulkCreateTenantRequestBodyListItem[];
}
export class BulkDeleteRequest implements IBulkDeleteRequest
{
  public ids: number[];
  public permanent: boolean;
  constructor (obj: IBulkDeleteRequest)
  {
    this.ids = obj.ids;
    this.permanent = obj.permanent;
  }
}
export interface IBulkDeleteRequest
{
  ids: number[];
  permanent: boolean;
}
export class BulkDeleteTenantsRequest implements IBulkDeleteTenantsRequest
{
  public tenantIds: number[];
  constructor (obj: IBulkDeleteTenantsRequest)
  {
    this.tenantIds = obj.tenantIds;
  }
}
export interface IBulkDeleteTenantsRequest
{
  tenantIds: number[];
}
export class BulkLinkClientsToTenantsRequestBody implements IBulkLinkClientsToTenantsRequestBody
{
  public clientIds: string[];
  constructor (obj: IBulkLinkClientsToTenantsRequestBody)
  {
    this.clientIds = obj.clientIds;
  }
}
export interface IBulkLinkClientsToTenantsRequestBody
{
  clientIds: string[];
}
export class BulkUpdateRoleRequest implements IBulkUpdateRoleRequest
{
  public personIds: number[];
  public roleIds: number[];
  constructor (obj: IBulkUpdateRoleRequest)
  {
    this.personIds = obj.personIds;
    this.roleIds = obj.roleIds;
  }
}
export interface IBulkUpdateRoleRequest
{
  personIds: number[];
  roleIds: number[];
}
export class UpdateExpirationRequest implements IUpdateExpirationRequest
{
  public expiresIn: ExpirationTime;
  public personIds: number[];
  constructor (obj: IUpdateExpirationRequest)
  {
    this.expiresIn = obj.expiresIn;
    this.personIds = obj.personIds;
  }
}
export interface IUpdateExpirationRequest
{
  expiresIn: ExpirationTime;
  personIds: number[];
}
export class CalculateTargetsRequest implements ICalculateTargetsRequest
{
  public allowAccessToParentTenant: boolean;
  public onboardingOnly: boolean;
  public propagateToChildTenants: boolean;
  public targetGroupFilter: TargetGroupFilter;
  public targetType: TargetType;
  public providerClientGroupType?: string;
  public providerDeviceGroupType?: string;
  public providerLinkId?: number;
  public target?: string;
  public tenantId?: number;
  constructor (obj: ICalculateTargetsRequest)
  {
    this.allowAccessToParentTenant = obj.allowAccessToParentTenant;
    this.onboardingOnly = obj.onboardingOnly;
    this.propagateToChildTenants = obj.propagateToChildTenants;
    this.targetGroupFilter = obj.targetGroupFilter;
    this.targetType = obj.targetType;
    this.providerClientGroupType = obj.providerClientGroupType;
    this.providerDeviceGroupType = obj.providerDeviceGroupType;
    this.providerLinkId = obj.providerLinkId;
    this.target = obj.target;
    this.tenantId = obj.tenantId;
  }
}
export interface ICalculateTargetsRequest
{
  allowAccessToParentTenant: boolean;
  onboardingOnly: boolean;
  propagateToChildTenants: boolean;
  targetGroupFilter: TargetGroupFilter;
  targetType: TargetType;
  providerClientGroupType?: string;
  providerDeviceGroupType?: string;
  providerLinkId?: number;
  target?: string;
  tenantId?: number;
}
export class CancelApplicationLockRequest implements ICancelApplicationLockRequest
{
  public key: string;
  public cancelReason?: string;
  constructor (obj: ICancelApplicationLockRequest)
  {
    this.key = obj.key;
    this.cancelReason = obj.cancelReason;
  }
}
export interface ICancelApplicationLockRequest
{
  key: string;
  cancelReason?: string;
}
export class CancelSessionsRequestBody implements ICancelSessionsRequestBody
{
  public sessionIds: number[];
  constructor (obj: ICancelSessionsRequestBody)
  {
    this.sessionIds = obj.sessionIds;
  }
}
export interface ICancelSessionsRequestBody
{
  sessionIds: number[];
}
export class ChunkMetadata implements IChunkMetadata
{
  public fileGuid: string;
  public fileName: string;
  public fileSize: number;
  public index: number;
  public totalCount: number;
  public fileType?: string;
  constructor (obj: IChunkMetadata)
  {
    this.fileGuid = obj.fileGuid;
    this.fileName = obj.fileName;
    this.fileSize = obj.fileSize;
    this.index = obj.index;
    this.totalCount = obj.totalCount;
    this.fileType = obj.fileType;
  }
}
export interface IChunkMetadata
{
  fileGuid: string;
  fileName: string;
  fileSize: number;
  index: number;
  totalCount: number;
  fileType?: string;
}
export class CreateBrandingRequestBody implements ICreateBrandingRequestBody
{
  public description: string;
  public backgroundColor?: string;
  public endDate?: string;
  public foregroundColor?: string;
  public fromAddress?: string;
  public ignoreYear?: boolean;
  public logoAltText?: string;
  public logoUri?: string;
  public mascotImgUri?: string;
  public mascotName?: string;
  public startDate?: string;
  public tableHeaderColor?: string;
  public tableHeaderTextColor?: string;
  public tenantId?: number;
  public textColor?: string;
  public timeFormat?: string;
  constructor (obj: ICreateBrandingRequestBody)
  {
    this.description = obj.description;
    this.backgroundColor = obj.backgroundColor;
    this.endDate = obj.endDate;
    this.foregroundColor = obj.foregroundColor;
    this.fromAddress = obj.fromAddress;
    this.ignoreYear = obj.ignoreYear;
    this.logoAltText = obj.logoAltText;
    this.logoUri = obj.logoUri;
    this.mascotImgUri = obj.mascotImgUri;
    this.mascotName = obj.mascotName;
    this.startDate = obj.startDate;
    this.tableHeaderColor = obj.tableHeaderColor;
    this.tableHeaderTextColor = obj.tableHeaderTextColor;
    this.tenantId = obj.tenantId;
    this.textColor = obj.textColor;
    this.timeFormat = obj.timeFormat;
  }
}
export interface ICreateBrandingRequestBody
{
  description: string;
  backgroundColor?: string;
  endDate?: string;
  foregroundColor?: string;
  fromAddress?: string;
  ignoreYear?: boolean;
  logoAltText?: string;
  logoUri?: string;
  mascotImgUri?: string;
  mascotName?: string;
  startDate?: string;
  tableHeaderColor?: string;
  tableHeaderTextColor?: string;
  tenantId?: number;
  textColor?: string;
  timeFormat?: string;
}
export class CreateGlobalSoftwareRequestBody implements ICreateGlobalSoftwareRequestBody
{
  public detectionMethod: DetectionMethod;
  public hidden: boolean;
  public installOrder: number;
  public licenseRequirement: SoftwareLicenseRequirement;
  public licenseType: LicenseType;
  public name: string;
  public rebootNeeded: boolean;
  public recommended: boolean;
  public softwarePrerequisites: ISoftwarePrerequisite[];
  public testRequired: boolean;
  public upgradeStrategy: UpdateActionType;
  public useDynamicVersions: boolean;
  public agentIntegrationTypeId?: string;
  public chocoProviderSoftwareId?: string;
  public detectionScriptId?: number;
  public downloadInstallerScriptId?: number;
  public dynamicVersionsScriptId?: number;
  public installScriptId?: number;
  public licenseDescription?: string;
  public maintenanceTaskId?: number;
  public niniteProviderSoftwareId?: string;
  public notes?: string;
  public postInstallScriptId?: number;
  public postUninstallScriptId?: number;
  public repairScriptId?: number;
  public repairType?: number;
  public softwareIconMediaId?: number;
  public softwareTableName?: string;
  public softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  public testFailedError?: string;
  public testScriptId?: number;
  public uninstallScriptId?: number;
  public upgradeCode?: string;
  public upgradeScriptId?: number;
  constructor (obj: ICreateGlobalSoftwareRequestBody)
  {
    this.detectionMethod = obj.detectionMethod;
    this.hidden = obj.hidden;
    this.installOrder = obj.installOrder;
    this.licenseRequirement = obj.licenseRequirement;
    this.licenseType = obj.licenseType;
    this.name = obj.name;
    this.rebootNeeded = obj.rebootNeeded;
    this.recommended = obj.recommended;
    this.softwarePrerequisites = obj.softwarePrerequisites;
    this.testRequired = obj.testRequired;
    this.upgradeStrategy = obj.upgradeStrategy;
    this.useDynamicVersions = obj.useDynamicVersions;
    this.agentIntegrationTypeId = obj.agentIntegrationTypeId;
    this.chocoProviderSoftwareId = obj.chocoProviderSoftwareId;
    this.detectionScriptId = obj.detectionScriptId;
    this.downloadInstallerScriptId = obj.downloadInstallerScriptId;
    this.dynamicVersionsScriptId = obj.dynamicVersionsScriptId;
    this.installScriptId = obj.installScriptId;
    this.licenseDescription = obj.licenseDescription;
    this.maintenanceTaskId = obj.maintenanceTaskId;
    this.niniteProviderSoftwareId = obj.niniteProviderSoftwareId;
    this.notes = obj.notes;
    this.postInstallScriptId = obj.postInstallScriptId;
    this.postUninstallScriptId = obj.postUninstallScriptId;
    this.repairScriptId = obj.repairScriptId;
    this.repairType = obj.repairType;
    this.softwareIconMediaId = obj.softwareIconMediaId;
    this.softwareTableName = obj.softwareTableName;
    this.softwareTableNameSearchMode = obj.softwareTableNameSearchMode;
    this.testFailedError = obj.testFailedError;
    this.testScriptId = obj.testScriptId;
    this.uninstallScriptId = obj.uninstallScriptId;
    this.upgradeCode = obj.upgradeCode;
    this.upgradeScriptId = obj.upgradeScriptId;
  }
}
export interface ICreateGlobalSoftwareRequestBody
{
  detectionMethod: DetectionMethod;
  hidden: boolean;
  installOrder: number;
  licenseRequirement: SoftwareLicenseRequirement;
  licenseType: LicenseType;
  name: string;
  rebootNeeded: boolean;
  recommended: boolean;
  softwarePrerequisites: ISoftwarePrerequisite[];
  testRequired: boolean;
  upgradeStrategy: UpdateActionType;
  useDynamicVersions: boolean;
  agentIntegrationTypeId?: string;
  chocoProviderSoftwareId?: string;
  detectionScriptId?: number;
  downloadInstallerScriptId?: number;
  dynamicVersionsScriptId?: number;
  installScriptId?: number;
  licenseDescription?: string;
  maintenanceTaskId?: number;
  niniteProviderSoftwareId?: string;
  notes?: string;
  postInstallScriptId?: number;
  postUninstallScriptId?: number;
  repairScriptId?: number;
  repairType?: number;
  softwareIconMediaId?: number;
  softwareTableName?: string;
  softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  testFailedError?: string;
  testScriptId?: number;
  uninstallScriptId?: number;
  upgradeCode?: string;
  upgradeScriptId?: number;
}
export class CreateGlobalSoftwareVersionRequestBody implements ICreateGlobalSoftwareVersionRequestBody
{
  public installerType: SoftwareVersionInstallerType;
  public licenseType: LicenseType;
  public packageType: PackageType;
  public semanticVersion: string;
  public softwareId: number;
  public softwareIdentifier: string;
  public softwareType: SoftwareType;
  public testRequired: boolean;
  public upgradeStrategy: UpdateActionType;
  public blobName?: string;
  public dependsOnSemanticVersion?: string;
  public displayName?: string;
  public displayVersion?: string;
  public installerFile?: string;
  public installScriptId?: number;
  public notes?: string;
  public packageHash?: string;
  public postInstallScriptId?: number;
  public postUninstallScriptId?: number;
  public productCode?: string;
  public relativeCacheSourcePath?: string;
  public testFailedError?: string;
  public testScriptId?: number;
  public uninstallScriptId?: number;
  public upgradeScriptId?: number;
  public url?: string;
  constructor (obj: ICreateGlobalSoftwareVersionRequestBody)
  {
    this.installerType = obj.installerType;
    this.licenseType = obj.licenseType;
    this.packageType = obj.packageType;
    this.semanticVersion = obj.semanticVersion;
    this.softwareId = obj.softwareId;
    this.softwareIdentifier = obj.softwareIdentifier;
    this.softwareType = obj.softwareType;
    this.testRequired = obj.testRequired;
    this.upgradeStrategy = obj.upgradeStrategy;
    this.blobName = obj.blobName;
    this.dependsOnSemanticVersion = obj.dependsOnSemanticVersion;
    this.displayName = obj.displayName;
    this.displayVersion = obj.displayVersion;
    this.installerFile = obj.installerFile;
    this.installScriptId = obj.installScriptId;
    this.notes = obj.notes;
    this.packageHash = obj.packageHash;
    this.postInstallScriptId = obj.postInstallScriptId;
    this.postUninstallScriptId = obj.postUninstallScriptId;
    this.productCode = obj.productCode;
    this.relativeCacheSourcePath = obj.relativeCacheSourcePath;
    this.testFailedError = obj.testFailedError;
    this.testScriptId = obj.testScriptId;
    this.uninstallScriptId = obj.uninstallScriptId;
    this.upgradeScriptId = obj.upgradeScriptId;
    this.url = obj.url;
  }
}
export interface ICreateGlobalSoftwareVersionRequestBody
{
  installerType: SoftwareVersionInstallerType;
  licenseType: LicenseType;
  packageType: PackageType;
  semanticVersion: string;
  softwareId: number;
  softwareIdentifier: string;
  softwareType: SoftwareType;
  testRequired: boolean;
  upgradeStrategy: UpdateActionType;
  blobName?: string;
  dependsOnSemanticVersion?: string;
  displayName?: string;
  displayVersion?: string;
  installerFile?: string;
  installScriptId?: number;
  notes?: string;
  packageHash?: string;
  postInstallScriptId?: number;
  postUninstallScriptId?: number;
  productCode?: string;
  relativeCacheSourcePath?: string;
  testFailedError?: string;
  testScriptId?: number;
  uninstallScriptId?: number;
  upgradeScriptId?: number;
  url?: string;
}
export class CreateLicenseRequestBody implements ICreateLicenseRequestBody
{
  public licenseValue: string;
  public name: string;
  public restrictToMajorVersion: boolean;
  public softwareIdentifier: string;
  public softwareName: string;
  public softwareType: SoftwareType;
  public semanticVersion?: string;
  public tenantId?: number;
  constructor (obj: ICreateLicenseRequestBody)
  {
    this.licenseValue = obj.licenseValue;
    this.name = obj.name;
    this.restrictToMajorVersion = obj.restrictToMajorVersion;
    this.softwareIdentifier = obj.softwareIdentifier;
    this.softwareName = obj.softwareName;
    this.softwareType = obj.softwareType;
    this.semanticVersion = obj.semanticVersion;
    this.tenantId = obj.tenantId;
  }
}
export interface ICreateLicenseRequestBody
{
  licenseValue: string;
  name: string;
  restrictToMajorVersion: boolean;
  softwareIdentifier: string;
  softwareName: string;
  softwareType: SoftwareType;
  semanticVersion?: string;
  tenantId?: number;
}
export class CreateLinkedProviderReferenceRequestBody implements ICreateLinkedProviderReferenceRequestBody
{
  public isCrossProviderClientExternalLinkingEnabled: boolean;
  public providerLinkId: number;
  constructor (obj: ICreateLinkedProviderReferenceRequestBody)
  {
    this.isCrossProviderClientExternalLinkingEnabled = obj.isCrossProviderClientExternalLinkingEnabled;
    this.providerLinkId = obj.providerLinkId;
  }
}
export interface ICreateLinkedProviderReferenceRequestBody
{
  isCrossProviderClientExternalLinkingEnabled: boolean;
  providerLinkId: number;
}
export class CreateLocalSoftwareRequestBody implements ICreateLocalSoftwareRequestBody
{
  public detectionMethod: DetectionMethod;
  public hidden: boolean;
  public installOrder: number;
  public licenseRequirement: SoftwareLicenseRequirement;
  public licenseType: LicenseType;
  public name: string;
  public rebootNeeded: boolean;
  public recommended: boolean;
  public softwarePrerequisites: ISoftwarePrerequisite[];
  public tenantSoftware: number[];
  public testRequired: boolean;
  public upgradeStrategy: UpdateActionType;
  public useDynamicVersions: boolean;
  public agentIntegrationTypeId?: string;
  public chocoProviderSoftwareId?: string;
  public detectionScriptId?: number;
  public detectionScriptType?: DatabaseType;
  public downloadInstallerScriptId?: number;
  public downloadInstallerScriptType?: DatabaseType;
  public dynamicVersionsScriptId?: number;
  public dynamicVersionsScriptType?: DatabaseType;
  public installScriptId?: number;
  public installScriptType?: DatabaseType;
  public licenseDescription?: string;
  public maintenanceTaskId?: number;
  public maintenanceTaskType?: DatabaseType;
  public niniteProviderSoftwareId?: string;
  public notes?: string;
  public ownerTenantId?: number;
  public postInstallScriptId?: number;
  public postInstallScriptType?: DatabaseType;
  public postUninstallScriptId?: number;
  public postUninstallScriptType?: DatabaseType;
  public repairScriptId?: number;
  public repairScriptType?: DatabaseType;
  public repairType?: number;
  public softwareIconMediaId?: number;
  public softwareTableName?: string;
  public softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  public testFailedError?: string;
  public testScriptId?: number;
  public testScriptType?: DatabaseType;
  public uninstallScriptId?: number;
  public uninstallScriptType?: DatabaseType;
  public upgradeCode?: string;
  public upgradeScriptId?: number;
  public upgradeScriptType?: DatabaseType;
  constructor (obj: ICreateLocalSoftwareRequestBody)
  {
    this.detectionMethod = obj.detectionMethod;
    this.hidden = obj.hidden;
    this.installOrder = obj.installOrder;
    this.licenseRequirement = obj.licenseRequirement;
    this.licenseType = obj.licenseType;
    this.name = obj.name;
    this.rebootNeeded = obj.rebootNeeded;
    this.recommended = obj.recommended;
    this.softwarePrerequisites = obj.softwarePrerequisites;
    this.tenantSoftware = obj.tenantSoftware;
    this.testRequired = obj.testRequired;
    this.upgradeStrategy = obj.upgradeStrategy;
    this.useDynamicVersions = obj.useDynamicVersions;
    this.agentIntegrationTypeId = obj.agentIntegrationTypeId;
    this.chocoProviderSoftwareId = obj.chocoProviderSoftwareId;
    this.detectionScriptId = obj.detectionScriptId;
    this.detectionScriptType = obj.detectionScriptType;
    this.downloadInstallerScriptId = obj.downloadInstallerScriptId;
    this.downloadInstallerScriptType = obj.downloadInstallerScriptType;
    this.dynamicVersionsScriptId = obj.dynamicVersionsScriptId;
    this.dynamicVersionsScriptType = obj.dynamicVersionsScriptType;
    this.installScriptId = obj.installScriptId;
    this.installScriptType = obj.installScriptType;
    this.licenseDescription = obj.licenseDescription;
    this.maintenanceTaskId = obj.maintenanceTaskId;
    this.maintenanceTaskType = obj.maintenanceTaskType;
    this.niniteProviderSoftwareId = obj.niniteProviderSoftwareId;
    this.notes = obj.notes;
    this.ownerTenantId = obj.ownerTenantId;
    this.postInstallScriptId = obj.postInstallScriptId;
    this.postInstallScriptType = obj.postInstallScriptType;
    this.postUninstallScriptId = obj.postUninstallScriptId;
    this.postUninstallScriptType = obj.postUninstallScriptType;
    this.repairScriptId = obj.repairScriptId;
    this.repairScriptType = obj.repairScriptType;
    this.repairType = obj.repairType;
    this.softwareIconMediaId = obj.softwareIconMediaId;
    this.softwareTableName = obj.softwareTableName;
    this.softwareTableNameSearchMode = obj.softwareTableNameSearchMode;
    this.testFailedError = obj.testFailedError;
    this.testScriptId = obj.testScriptId;
    this.testScriptType = obj.testScriptType;
    this.uninstallScriptId = obj.uninstallScriptId;
    this.uninstallScriptType = obj.uninstallScriptType;
    this.upgradeCode = obj.upgradeCode;
    this.upgradeScriptId = obj.upgradeScriptId;
    this.upgradeScriptType = obj.upgradeScriptType;
  }
}
export interface ICreateLocalSoftwareRequestBody
{
  detectionMethod: DetectionMethod;
  hidden: boolean;
  installOrder: number;
  licenseRequirement: SoftwareLicenseRequirement;
  licenseType: LicenseType;
  name: string;
  rebootNeeded: boolean;
  recommended: boolean;
  softwarePrerequisites: ISoftwarePrerequisite[];
  tenantSoftware: number[];
  testRequired: boolean;
  upgradeStrategy: UpdateActionType;
  useDynamicVersions: boolean;
  agentIntegrationTypeId?: string;
  chocoProviderSoftwareId?: string;
  detectionScriptId?: number;
  detectionScriptType?: DatabaseType;
  downloadInstallerScriptId?: number;
  downloadInstallerScriptType?: DatabaseType;
  dynamicVersionsScriptId?: number;
  dynamicVersionsScriptType?: DatabaseType;
  installScriptId?: number;
  installScriptType?: DatabaseType;
  licenseDescription?: string;
  maintenanceTaskId?: number;
  maintenanceTaskType?: DatabaseType;
  niniteProviderSoftwareId?: string;
  notes?: string;
  ownerTenantId?: number;
  postInstallScriptId?: number;
  postInstallScriptType?: DatabaseType;
  postUninstallScriptId?: number;
  postUninstallScriptType?: DatabaseType;
  repairScriptId?: number;
  repairScriptType?: DatabaseType;
  repairType?: number;
  softwareIconMediaId?: number;
  softwareTableName?: string;
  softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  testFailedError?: string;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  uninstallScriptId?: number;
  uninstallScriptType?: DatabaseType;
  upgradeCode?: string;
  upgradeScriptId?: number;
  upgradeScriptType?: DatabaseType;
}
export class CreateLocalSoftwareVersionRequestBody implements ICreateLocalSoftwareVersionRequestBody
{
  public installerType: SoftwareVersionInstallerType;
  public licenseType: LicenseType;
  public packageType: PackageType;
  public semanticVersion: string;
  public softwareId: number;
  public softwareIdentifier: string;
  public softwareType: SoftwareType;
  public testRequired: boolean;
  public upgradeStrategy: UpdateActionType;
  public blobName?: string;
  public dependsOnSemanticVersion?: string;
  public displayName?: string;
  public displayVersion?: string;
  public installerFile?: string;
  public installScriptId?: number;
  public installScriptType?: DatabaseType;
  public notes?: string;
  public packageHash?: string;
  public postInstallScriptId?: number;
  public postInstallScriptType?: DatabaseType;
  public postUninstallScriptId?: number;
  public postUninstallScriptType?: DatabaseType;
  public productCode?: string;
  public relativeCacheSourcePath?: string;
  public testFailedError?: string;
  public testScriptId?: number;
  public testScriptType?: DatabaseType;
  public uninstallScriptId?: number;
  public uninstallScriptType?: DatabaseType;
  public upgradeScriptId?: number;
  public upgradeScriptType?: DatabaseType;
  public url?: string;
  constructor (obj: ICreateLocalSoftwareVersionRequestBody)
  {
    this.installerType = obj.installerType;
    this.licenseType = obj.licenseType;
    this.packageType = obj.packageType;
    this.semanticVersion = obj.semanticVersion;
    this.softwareId = obj.softwareId;
    this.softwareIdentifier = obj.softwareIdentifier;
    this.softwareType = obj.softwareType;
    this.testRequired = obj.testRequired;
    this.upgradeStrategy = obj.upgradeStrategy;
    this.blobName = obj.blobName;
    this.dependsOnSemanticVersion = obj.dependsOnSemanticVersion;
    this.displayName = obj.displayName;
    this.displayVersion = obj.displayVersion;
    this.installerFile = obj.installerFile;
    this.installScriptId = obj.installScriptId;
    this.installScriptType = obj.installScriptType;
    this.notes = obj.notes;
    this.packageHash = obj.packageHash;
    this.postInstallScriptId = obj.postInstallScriptId;
    this.postInstallScriptType = obj.postInstallScriptType;
    this.postUninstallScriptId = obj.postUninstallScriptId;
    this.postUninstallScriptType = obj.postUninstallScriptType;
    this.productCode = obj.productCode;
    this.relativeCacheSourcePath = obj.relativeCacheSourcePath;
    this.testFailedError = obj.testFailedError;
    this.testScriptId = obj.testScriptId;
    this.testScriptType = obj.testScriptType;
    this.uninstallScriptId = obj.uninstallScriptId;
    this.uninstallScriptType = obj.uninstallScriptType;
    this.upgradeScriptId = obj.upgradeScriptId;
    this.upgradeScriptType = obj.upgradeScriptType;
    this.url = obj.url;
  }
}
export interface ICreateLocalSoftwareVersionRequestBody
{
  installerType: SoftwareVersionInstallerType;
  licenseType: LicenseType;
  packageType: PackageType;
  semanticVersion: string;
  softwareId: number;
  softwareIdentifier: string;
  softwareType: SoftwareType;
  testRequired: boolean;
  upgradeStrategy: UpdateActionType;
  blobName?: string;
  dependsOnSemanticVersion?: string;
  displayName?: string;
  displayVersion?: string;
  installerFile?: string;
  installScriptId?: number;
  installScriptType?: DatabaseType;
  notes?: string;
  packageHash?: string;
  postInstallScriptId?: number;
  postInstallScriptType?: DatabaseType;
  postUninstallScriptId?: number;
  postUninstallScriptType?: DatabaseType;
  productCode?: string;
  relativeCacheSourcePath?: string;
  testFailedError?: string;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  uninstallScriptId?: number;
  uninstallScriptType?: DatabaseType;
  upgradeScriptId?: number;
  upgradeScriptType?: DatabaseType;
  url?: string;
}
export class CreateProviderLinkRequestBody implements ICreateProviderLinkRequestBody
{
  public name: string;
  public providerTypeId: string;
  public providerTypeFormData?: any;
  constructor (obj: ICreateProviderLinkRequestBody)
  {
    this.name = obj.name;
    this.providerTypeId = obj.providerTypeId;
    this.providerTypeFormData = obj.providerTypeFormData;
  }
}
export interface ICreateProviderLinkRequestBody
{
  name: string;
  providerTypeId: string;
  providerTypeFormData?: any;
}
export class CreateProviderLinkWithExternalProviderReferenceRequestBody implements ICreateProviderLinkWithExternalProviderReferenceRequestBody
{
  public providerLink: any;
  public providerLinkExternalReferenceData?: IProviderLinkExternalReferenceDataBody;
  constructor (obj: ICreateProviderLinkWithExternalProviderReferenceRequestBody)
  {
    this.providerLink = obj.providerLink;
    this.providerLinkExternalReferenceData = obj.providerLinkExternalReferenceData;
  }
}
export interface ICreateProviderLinkWithExternalProviderReferenceRequestBody
{
  providerLink: any;
  providerLinkExternalReferenceData?: IProviderLinkExternalReferenceDataBody;
}
export class ProviderLinkExternalReferenceDataBody implements IProviderLinkExternalReferenceDataBody
{
  public enableClientExternalLinking: boolean;
  public providerLinkId: number;
  public providerTypeFormData: any;
  constructor (obj: IProviderLinkExternalReferenceDataBody)
  {
    this.enableClientExternalLinking = obj.enableClientExternalLinking;
    this.providerLinkId = obj.providerLinkId;
    this.providerTypeFormData = obj.providerTypeFormData;
  }
}
export interface IProviderLinkExternalReferenceDataBody
{
  enableClientExternalLinking: boolean;
  providerLinkId: number;
  providerTypeFormData: any;
}
export class CreateScheduleRequest implements ICreateScheduleRequest
{
  public allowAccessToMSPResources: boolean;
  public allowAccessToParentTenant: boolean;
  public applyWindowsUpdates: boolean;
  public autoConsentToReboots: boolean;
  public disabled: boolean;
  public offlineBehavior: ComputerOfflineMaintenanceSessionBehavior;
  public promptTimeoutAction: PromptTimeoutAction;
  public promptTimeoutMinutes: number;
  public propagateToChildTenants: boolean;
  public rebootPreference: RebootPreference;
  public scheduleExecutionAfterActiveHours: boolean;
  public sendDetectionEmail: boolean;
  public sendDetectionEmailWhenAllActionsAreCompliant: boolean;
  public sendFollowUpEmail: boolean;
  public sendFollowUpOnlyIfActionNeeded: boolean;
  public showMaintenanceActions: boolean;
  public showPostponeButton: boolean;
  public showRunNowButton: boolean;
  public suppressRebootsDuringBusinessHours: boolean;
  public targetCategory: TargetCategory;
  public targetGroupFilter: TargetGroupFilter;
  public targetType: TargetType;
  public useComputersTimezoneForExecution: boolean;
  public customCronExpression?: string;
  public day?: number;
  public maintenanceIdentifier?: string;
  public maintenanceTime?: string;
  public maintenanceType?: MaintenanceType;
  public providerClientGroupType?: string;
  public providerDeviceGroupType?: string;
  public providerLinkId?: number;
  public target?: string;
  public tenantId?: number;
  public time?: string;
  public timeZoneInfoId?: string;
  constructor (obj: ICreateScheduleRequest)
  {
    this.allowAccessToMSPResources = obj.allowAccessToMSPResources;
    this.allowAccessToParentTenant = obj.allowAccessToParentTenant;
    this.applyWindowsUpdates = obj.applyWindowsUpdates;
    this.autoConsentToReboots = obj.autoConsentToReboots;
    this.disabled = obj.disabled;
    this.offlineBehavior = obj.offlineBehavior;
    this.promptTimeoutAction = obj.promptTimeoutAction;
    this.promptTimeoutMinutes = obj.promptTimeoutMinutes;
    this.propagateToChildTenants = obj.propagateToChildTenants;
    this.rebootPreference = obj.rebootPreference;
    this.scheduleExecutionAfterActiveHours = obj.scheduleExecutionAfterActiveHours;
    this.sendDetectionEmail = obj.sendDetectionEmail;
    this.sendDetectionEmailWhenAllActionsAreCompliant = obj.sendDetectionEmailWhenAllActionsAreCompliant;
    this.sendFollowUpEmail = obj.sendFollowUpEmail;
    this.sendFollowUpOnlyIfActionNeeded = obj.sendFollowUpOnlyIfActionNeeded;
    this.showMaintenanceActions = obj.showMaintenanceActions;
    this.showPostponeButton = obj.showPostponeButton;
    this.showRunNowButton = obj.showRunNowButton;
    this.suppressRebootsDuringBusinessHours = obj.suppressRebootsDuringBusinessHours;
    this.targetCategory = obj.targetCategory;
    this.targetGroupFilter = obj.targetGroupFilter;
    this.targetType = obj.targetType;
    this.useComputersTimezoneForExecution = obj.useComputersTimezoneForExecution;
    this.customCronExpression = obj.customCronExpression;
    this.day = obj.day;
    this.maintenanceIdentifier = obj.maintenanceIdentifier;
    this.maintenanceTime = obj.maintenanceTime;
    this.maintenanceType = obj.maintenanceType;
    this.providerClientGroupType = obj.providerClientGroupType;
    this.providerDeviceGroupType = obj.providerDeviceGroupType;
    this.providerLinkId = obj.providerLinkId;
    this.target = obj.target;
    this.tenantId = obj.tenantId;
    this.time = obj.time;
    this.timeZoneInfoId = obj.timeZoneInfoId;
  }
}
export interface ICreateScheduleRequest
{
  allowAccessToMSPResources: boolean;
  allowAccessToParentTenant: boolean;
  applyWindowsUpdates: boolean;
  autoConsentToReboots: boolean;
  disabled: boolean;
  offlineBehavior: ComputerOfflineMaintenanceSessionBehavior;
  promptTimeoutAction: PromptTimeoutAction;
  promptTimeoutMinutes: number;
  propagateToChildTenants: boolean;
  rebootPreference: RebootPreference;
  scheduleExecutionAfterActiveHours: boolean;
  sendDetectionEmail: boolean;
  sendDetectionEmailWhenAllActionsAreCompliant: boolean;
  sendFollowUpEmail: boolean;
  sendFollowUpOnlyIfActionNeeded: boolean;
  showMaintenanceActions: boolean;
  showPostponeButton: boolean;
  showRunNowButton: boolean;
  suppressRebootsDuringBusinessHours: boolean;
  targetCategory: TargetCategory;
  targetGroupFilter: TargetGroupFilter;
  targetType: TargetType;
  useComputersTimezoneForExecution: boolean;
  customCronExpression?: string;
  day?: number;
  maintenanceIdentifier?: string;
  maintenanceTime?: string;
  maintenanceType?: MaintenanceType;
  providerClientGroupType?: string;
  providerDeviceGroupType?: string;
  providerLinkId?: number;
  target?: string;
  tenantId?: number;
  time?: string;
  timeZoneInfoId?: string;
}
export class CreateSmtpRequest implements ICreateSmtpRequest
{
  public enabled: boolean;
  public enableSSL: boolean;
  public host: string;
  public port: number;
  public timeout: number;
  public useAuthentication: boolean;
  public password?: string;
  public tenantId?: number;
  public username?: string;
  constructor (obj: ICreateSmtpRequest)
  {
    this.enabled = obj.enabled;
    this.enableSSL = obj.enableSSL;
    this.host = obj.host;
    this.port = obj.port;
    this.timeout = obj.timeout;
    this.useAuthentication = obj.useAuthentication;
    this.password = obj.password;
    this.tenantId = obj.tenantId;
    this.username = obj.username;
  }
}
export interface ICreateSmtpRequest
{
  enabled: boolean;
  enableSSL: boolean;
  host: string;
  port: number;
  timeout: number;
  useAuthentication: boolean;
  password?: string;
  tenantId?: number;
  username?: string;
}
export class CreateTenantRequestBody implements ICreateTenantRequestBody
{
  public isMsp: boolean;
  public name: string;
  public ownerTenantId: number;
  public limitToDomains?: string[];
  public parentTenantId?: number;
  public partnerPrincipalId?: string;
  public principalId?: string;
  public slug?: string;
  constructor (obj: ICreateTenantRequestBody)
  {
    this.isMsp = obj.isMsp;
    this.name = obj.name;
    this.ownerTenantId = obj.ownerTenantId;
    this.limitToDomains = obj.limitToDomains;
    this.parentTenantId = obj.parentTenantId;
    this.partnerPrincipalId = obj.partnerPrincipalId;
    this.principalId = obj.principalId;
    this.slug = obj.slug;
  }
}
export interface ICreateTenantRequestBody
{
  isMsp: boolean;
  name: string;
  ownerTenantId: number;
  limitToDomains?: string[];
  parentTenantId?: number;
  partnerPrincipalId?: string;
  principalId?: string;
  slug?: string;
}
export class CreateUserFromPersonRequest implements ICreateUserFromPersonRequest
{
  public hasManagementAccess: boolean;
  public personId: number;
  constructor (obj: ICreateUserFromPersonRequest)
  {
    this.hasManagementAccess = obj.hasManagementAccess;
    this.personId = obj.personId;
  }
}
export interface ICreateUserFromPersonRequest
{
  hasManagementAccess: boolean;
  personId: number;
}
export class DisambiguateAzureTenantTypeRequestBody implements IDisambiguateAzureTenantTypeRequestBody
{
  public tenantPrincipalId: string;
  constructor (obj: IDisambiguateAzureTenantTypeRequestBody)
  {
    this.tenantPrincipalId = obj.tenantPrincipalId;
  }
}
export interface IDisambiguateAzureTenantTypeRequestBody
{
  tenantPrincipalId: string;
}
export class DoesScriptHaveParamBlockRequest implements IDoesScriptHaveParamBlockRequest
{
  public script: string;
  constructor (obj: IDoesScriptHaveParamBlockRequest)
  {
    this.script = obj.script;
  }
}
export interface IDoesScriptHaveParamBlockRequest
{
  script: string;
}
export class DuplicateAssignmentRequestBody implements IDuplicateAssignmentRequestBody
{
  public databaseType: DatabaseType;
  public id: number;
  constructor (obj: IDuplicateAssignmentRequestBody)
  {
    this.databaseType = obj.databaseType;
    this.id = obj.id;
  }
}
export interface IDuplicateAssignmentRequestBody
{
  databaseType: DatabaseType;
  id: number;
}
export class ExcludeFromMaintenanceRequest implements IExcludeFromMaintenanceRequest
{
  public exclude: boolean;
  constructor (obj: IExcludeFromMaintenanceRequest)
  {
    this.exclude = obj.exclude;
  }
}
export interface IExcludeFromMaintenanceRequest
{
  exclude: boolean;
}
export class FastCreateGlobalVersionRequestBody implements IFastCreateGlobalVersionRequestBody
{
  public software?: ICreateGlobalSoftwareRequestBody;
  public softwareId?: number;
  public softwareVersion?: ICreateGlobalSoftwareVersionRequestBody;
  constructor (obj: IFastCreateGlobalVersionRequestBody = {})
  {
    this.software = obj.software;
    this.softwareId = obj.softwareId;
    this.softwareVersion = obj.softwareVersion;
  }
}
export interface IFastCreateGlobalVersionRequestBody
{
  software?: ICreateGlobalSoftwareRequestBody;
  softwareId?: number;
  softwareVersion?: ICreateGlobalSoftwareVersionRequestBody;
}
export class FastCreateLocalVersionRequestBody implements IFastCreateLocalVersionRequestBody
{
  public software?: ICreateLocalSoftwareRequestBody;
  public softwareId?: number;
  public softwareVersion?: ICreateLocalSoftwareVersionRequestBody;
  constructor (obj: IFastCreateLocalVersionRequestBody = {})
  {
    this.software = obj.software;
    this.softwareId = obj.softwareId;
    this.softwareVersion = obj.softwareVersion;
  }
}
export interface IFastCreateLocalVersionRequestBody
{
  software?: ICreateLocalSoftwareRequestBody;
  softwareId?: number;
  softwareVersion?: ICreateLocalSoftwareVersionRequestBody;
}
export class FinishAuthCodeFlowRequest implements IFinishAuthCodeFlowRequest
{
  public authCode: string;
  public oauthHookId: string;
  public redirectUriUsedInAuthLeg: string;
  constructor (obj: IFinishAuthCodeFlowRequest)
  {
    this.authCode = obj.authCode;
    this.oauthHookId = obj.oauthHookId;
    this.redirectUriUsedInAuthLeg = obj.redirectUriUsedInAuthLeg;
  }
}
export interface IFinishAuthCodeFlowRequest
{
  authCode: string;
  oauthHookId: string;
  redirectUriUsedInAuthLeg: string;
}
export class GenerateSessionLogsDownloadUrlRequestBody implements IGenerateSessionLogsDownloadUrlRequestBody
{
  public overwriteIfAlreadyExists: boolean;
  public sessionId: number;
  constructor (obj: IGenerateSessionLogsDownloadUrlRequestBody)
  {
    this.overwriteIfAlreadyExists = obj.overwriteIfAlreadyExists;
    this.sessionId = obj.sessionId;
  }
}
export interface IGenerateSessionLogsDownloadUrlRequestBody
{
  overwriteIfAlreadyExists: boolean;
  sessionId: number;
}
export class GetScriptVariablesAndParametersRequest implements IGetScriptVariablesAndParametersRequest
{
  public scriptCategory: ScriptCategory;
  public scriptExecutionContext: ScriptExecutionContext;
  public computerId?: number;
  public softwareId?: number;
  public softwareType?: SoftwareType;
  public taskId?: number;
  public taskType?: DatabaseType;
  public tenantId?: number;
  constructor (obj: IGetScriptVariablesAndParametersRequest)
  {
    this.scriptCategory = obj.scriptCategory;
    this.scriptExecutionContext = obj.scriptExecutionContext;
    this.computerId = obj.computerId;
    this.softwareId = obj.softwareId;
    this.softwareType = obj.softwareType;
    this.taskId = obj.taskId;
    this.taskType = obj.taskType;
    this.tenantId = obj.tenantId;
  }
}
export interface IGetScriptVariablesAndParametersRequest
{
  scriptCategory: ScriptCategory;
  scriptExecutionContext: ScriptExecutionContext;
  computerId?: number;
  softwareId?: number;
  softwareType?: SoftwareType;
  taskId?: number;
  taskType?: DatabaseType;
  tenantId?: number;
}
export class GetFileDownloadUrlRequest implements IGetFileDownloadUrlRequest
{
  public blobName: string;
  public databaseType: DatabaseType;
  constructor (obj: IGetFileDownloadUrlRequest)
  {
    this.blobName = obj.blobName;
    this.databaseType = obj.databaseType;
  }
}
export interface IGetFileDownloadUrlRequest
{
  blobName: string;
  databaseType: DatabaseType;
}
export class GetFunctionSyntaxRequest implements IGetFunctionSyntaxRequest
{
  public functionName: string;
  constructor (obj: IGetFunctionSyntaxRequest)
  {
    this.functionName = obj.functionName;
  }
}
export interface IGetFunctionSyntaxRequest
{
  functionName: string;
}
export class GetLatestActionForComputersRequestBody implements IGetLatestActionForComputersRequestBody
{
  public computerIds: number[];
  public maintenanceIdentifier: string;
  public maintenanceType: MaintenanceType;
  public dateUtc?: string;
  constructor (obj: IGetLatestActionForComputersRequestBody)
  {
    this.computerIds = obj.computerIds;
    this.maintenanceIdentifier = obj.maintenanceIdentifier;
    this.maintenanceType = obj.maintenanceType;
    this.dateUtc = obj.dateUtc;
  }
}
export interface IGetLatestActionForComputersRequestBody
{
  computerIds: number[];
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  dateUtc?: string;
}
export class GetLatestActionForTenantsRequestBody implements IGetLatestActionForTenantsRequestBody
{
  public maintenanceIdentifier: string;
  public maintenanceType: MaintenanceType;
  public tenantIds: number[];
  public dateUtc?: string;
  constructor (obj: IGetLatestActionForTenantsRequestBody)
  {
    this.maintenanceIdentifier = obj.maintenanceIdentifier;
    this.maintenanceType = obj.maintenanceType;
    this.tenantIds = obj.tenantIds;
    this.dateUtc = obj.dateUtc;
  }
}
export interface IGetLatestActionForTenantsRequestBody
{
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  tenantIds: number[];
  dateUtc?: string;
}
export class GetPartnerCenterOrganizationDetailsRequest implements IGetPartnerCenterOrganizationDetailsRequest
{
  public oauth2AccessTokenId: number;
  constructor (obj: IGetPartnerCenterOrganizationDetailsRequest)
  {
    this.oauth2AccessTokenId = obj.oauth2AccessTokenId;
  }
}
export interface IGetPartnerCenterOrganizationDetailsRequest
{
  oauth2AccessTokenId: number;
}
export class GrantAccessRequest implements IGrantAccessRequest
{
  public expirationTime: ExpirationTime;
  public hasManagementAccess: boolean;
  public isAdmin: boolean;
  constructor (obj: IGrantAccessRequest)
  {
    this.expirationTime = obj.expirationTime;
    this.hasManagementAccess = obj.hasManagementAccess;
    this.isAdmin = obj.isAdmin;
  }
}
export interface IGrantAccessRequest
{
  expirationTime: ExpirationTime;
  hasManagementAccess: boolean;
  isAdmin: boolean;
}
export class GrantAccessRequestRbac implements IGrantAccessRequestRbac
{
  public personIds: number[];
  public roleIds: number[];
  public expiresIn?: ExpirationTime;
  constructor (obj: IGrantAccessRequestRbac)
  {
    this.personIds = obj.personIds;
    this.roleIds = obj.roleIds;
    this.expiresIn = obj.expiresIn;
  }
}
export interface IGrantAccessRequestRbac
{
  personIds: number[];
  roleIds: number[];
  expiresIn?: ExpirationTime;
}
export class IdentifyAgentRequest implements IIdentifyAgentRequest
{
  public agentIds: number[];
  public tenantId: number;
  constructor (obj: IIdentifyAgentRequest)
  {
    this.agentIds = obj.agentIds;
    this.tenantId = obj.tenantId;
  }
}
export interface IIdentifyAgentRequest
{
  agentIds: number[];
  tenantId: number;
}
export class ImpersonationRequest implements IImpersonationRequest
{
  public expiresAtUtc: string;
  constructor (obj: IImpersonationRequest)
  {
    this.expiresAtUtc = obj.expiresAtUtc;
  }
}
export interface IImpersonationRequest
{
  expiresAtUtc: string;
}
export class IntegrationBindParametersRequest implements IIntegrationBindParametersRequest
{
  public parameterValues: { [key:string]: IParameterValue };
  public providerLinkId?: number;
  constructor (obj: IIntegrationBindParametersRequest)
  {
    this.parameterValues = obj.parameterValues;
    this.providerLinkId = obj.providerLinkId;
  }
}
export interface IIntegrationBindParametersRequest
{
  parameterValues: { [key:string]: IParameterValue };
  providerLinkId?: number;
}
export class InventoryTaskPayload implements IInventoryTaskPayload
{
  public frequency: InventoryTaskFrequency;
  public name: string;
  constructor (obj: IInventoryTaskPayload)
  {
    this.frequency = obj.frequency;
    this.name = obj.name;
  }
}
export interface IInventoryTaskPayload
{
  frequency: InventoryTaskFrequency;
  name: string;
}
export class InventoryTaskScriptPayload implements IInventoryTaskScriptPayload
{
  public inventoryKey: string;
  public inventoryTaskId: number;
  public scriptId: number;
  constructor (obj: IInventoryTaskScriptPayload)
  {
    this.inventoryKey = obj.inventoryKey;
    this.inventoryTaskId = obj.inventoryTaskId;
    this.scriptId = obj.scriptId;
  }
}
export interface IInventoryTaskScriptPayload
{
  inventoryKey: string;
  inventoryTaskId: number;
  scriptId: number;
}
export class LinkClientsToTenantRequestBody implements ILinkClientsToTenantRequestBody
{
  public clientIds: string[];
  public tenantId: number;
  constructor (obj: ILinkClientsToTenantRequestBody)
  {
    this.clientIds = obj.clientIds;
    this.tenantId = obj.tenantId;
  }
}
export interface ILinkClientsToTenantRequestBody
{
  clientIds: string[];
  tenantId: number;
}
export class UnlinkClientsRequestBody implements IUnlinkClientsRequestBody
{
  public clientIds: string[];
  constructor (obj: IUnlinkClientsRequestBody)
  {
    this.clientIds = obj.clientIds;
  }
}
export interface IUnlinkClientsRequestBody
{
  clientIds: string[];
}
export class LinkClientToNewTenantRequestBody implements ILinkClientToNewTenantRequestBody
{
  public externalClientId: string;
  public tenantName?: string;
  constructor (obj: ILinkClientToNewTenantRequestBody)
  {
    this.externalClientId = obj.externalClientId;
    this.tenantName = obj.tenantName;
  }
}
export interface ILinkClientToNewTenantRequestBody
{
  externalClientId: string;
  tenantName?: string;
}
export class OverrideTargetAssignmentRequest implements IOverrideTargetAssignmentRequest
{
  public desiredSoftwareState: DesiredSoftwareState;
  public target: string;
  public targetType: TargetType;
  constructor (obj: IOverrideTargetAssignmentRequest)
  {
    this.desiredSoftwareState = obj.desiredSoftwareState;
    this.target = obj.target;
    this.targetType = obj.targetType;
  }
}
export interface IOverrideTargetAssignmentRequest
{
  desiredSoftwareState: DesiredSoftwareState;
  target: string;
  targetType: TargetType;
}
export class PendoTrackEventBody implements IPendoTrackEventBody
{
  public eventName: string;
  public customerEmail?: string;
  public eventProperties?: { [key:string]: any };
  constructor (obj: IPendoTrackEventBody)
  {
    this.eventName = obj.eventName;
    this.customerEmail = obj.customerEmail;
    this.eventProperties = obj.eventProperties;
  }
}
export interface IPendoTrackEventBody
{
  eventName: string;
  customerEmail?: string;
  eventProperties?: { [key:string]: any };
}
export class PreconsentCustomerTenantsRequest implements IPreconsentCustomerTenantsRequest
{
  public customerPrincipalIds: string[];
  public partnerPrincipalId: string;
  constructor (obj: IPreconsentCustomerTenantsRequest)
  {
    this.customerPrincipalIds = obj.customerPrincipalIds;
    this.partnerPrincipalId = obj.partnerPrincipalId;
  }
}
export interface IPreconsentCustomerTenantsRequest
{
  customerPrincipalIds: string[];
  partnerPrincipalId: string;
}
export class PrioritizedProviderLink implements IPrioritizedProviderLink
{
  public id: number;
  public priority: number;
  constructor (obj: IPrioritizedProviderLink)
  {
    this.id = obj.id;
    this.priority = obj.priority;
  }
}
export interface IPrioritizedProviderLink
{
  id: number;
  priority: number;
}
export class RegistryPayload implements IRegistryPayload
{
  public keyPath: string;
  constructor (obj: IRegistryPayload)
  {
    this.keyPath = obj.keyPath;
  }
}
export interface IRegistryPayload
{
  keyPath: string;
}
export class RequestFormSupportBody implements IRequestFormSupportBody
{
  public notes: string;
  public requesterEmail: string;
  public subject: string;
  public allowTechnicianAccess?: boolean;
  public blobNames?: string[];
  constructor (obj: IRequestFormSupportBody)
  {
    this.notes = obj.notes;
    this.requesterEmail = obj.requesterEmail;
    this.subject = obj.subject;
    this.allowTechnicianAccess = obj.allowTechnicianAccess;
    this.blobNames = obj.blobNames;
  }
}
export interface IRequestFormSupportBody
{
  notes: string;
  requesterEmail: string;
  subject: string;
  allowTechnicianAccess?: boolean;
  blobNames?: string[];
}
export class RequestSessionSupportRequestBody implements IRequestSessionSupportRequestBody
{
  public includeSessionData: boolean;
  public sessionId: number;
  public ticketNotes: string;
  public ticketSubject: string;
  public allowTechnicianAccess?: boolean;
  constructor (obj: IRequestSessionSupportRequestBody)
  {
    this.includeSessionData = obj.includeSessionData;
    this.sessionId = obj.sessionId;
    this.ticketNotes = obj.ticketNotes;
    this.ticketSubject = obj.ticketSubject;
    this.allowTechnicianAccess = obj.allowTechnicianAccess;
  }
}
export interface IRequestSessionSupportRequestBody
{
  includeSessionData: boolean;
  sessionId: number;
  ticketNotes: string;
  ticketSubject: string;
  allowTechnicianAccess?: boolean;
}
export class RerunSessionsRequestBody implements IRerunSessionsRequestBody
{
  public sessionIds: number[];
  constructor (obj: IRerunSessionsRequestBody)
  {
    this.sessionIds = obj.sessionIds;
  }
}
export interface IRerunSessionsRequestBody
{
  sessionIds: number[];
}
export class ResolveAssignmentsForMaintenanceItemRequest implements IResolveAssignmentsForMaintenanceItemRequest
{
  public maintenanceIdentifier: string;
  public maintenanceType: MaintenanceType;
  public tenantId: number;
  constructor (obj: IResolveAssignmentsForMaintenanceItemRequest)
  {
    this.maintenanceIdentifier = obj.maintenanceIdentifier;
    this.maintenanceType = obj.maintenanceType;
    this.tenantId = obj.tenantId;
  }
}
export interface IResolveAssignmentsForMaintenanceItemRequest
{
  maintenanceIdentifier: string;
  maintenanceType: MaintenanceType;
  tenantId: number;
}
export class ResolveFailuresRequestBody implements IResolveFailuresRequestBody
{
  public agentIds: number[];
  public allAgents: boolean;
  constructor (obj: IResolveFailuresRequestBody)
  {
    this.agentIds = obj.agentIds;
    this.allAgents = obj.allAgents;
  }
}
export interface IResolveFailuresRequestBody
{
  agentIds: number[];
  allAgents: boolean;
}
export class ResolveVisibilityTargetAssignmentsRequest implements IResolveVisibilityTargetAssignmentsRequest
{
  public visibility: TargetVisibility;
  public computerId?: number;
  public personId?: number;
  public tenantId?: number;
  constructor (obj: IResolveVisibilityTargetAssignmentsRequest)
  {
    this.visibility = obj.visibility;
    this.computerId = obj.computerId;
    this.personId = obj.personId;
    this.tenantId = obj.tenantId;
  }
}
export interface IResolveVisibilityTargetAssignmentsRequest
{
  visibility: TargetVisibility;
  computerId?: number;
  personId?: number;
  tenantId?: number;
}
export class RunImmyServiceRequestBody implements IRunImmyServiceRequestBody
{
  public allowAccessToParentTenant: boolean;
  public autoConsentToReboots: boolean;
  public cacheOnly: boolean;
  public computers: IRunImmybotComputerRequestBody[];
  public detectionOnly: boolean;
  public fullMaintenance: boolean;
  public inventoryOnly: boolean;
  public offlineBehavior: ComputerOfflineMaintenanceSessionBehavior;
  public persons: IRunImmybotPersonRequestBody[];
  public promptTimeoutAction: PromptTimeoutAction;
  public promptTimeoutMinutes: number;
  public propagateToChildTenants: boolean;
  public rebootPreference: RebootPreference;
  public resolutionOnly: boolean;
  public runInventoryInDetection: boolean;
  public scheduleExecutionAfterActiveHours: boolean;
  public sendDetectionEmail: boolean;
  public sendDetectionEmailWhenAllActionsAreCompliant: boolean;
  public sendFollowUpEmail: boolean;
  public sendFollowUpOnlyIfActionNeeded: boolean;
  public showMaintenanceActions: boolean;
  public showPostponeButton: boolean;
  public showRunNowButton: boolean;
  public skipBackgroundJob: boolean;
  public suppressRebootsDuringBusinessHours: boolean;
  public tenants: IRunImmybotTenantRequestBody[];
  public useComputersTimezoneForExecution: boolean;
  public useWinningDeployment: boolean;
  public deploymentId?: number;
  public deploymentType?: DatabaseType;
  public licenseId?: number;
  public maintenanceParams?: IRunImmybotMaintenanceRequestBody;
  public providerLinkIdForAgentUpdates?: number;
  public sessionGroupId?: string;
  public timeZoneInfoId?: string;
  public updateTime?: string;
  constructor (obj: IRunImmyServiceRequestBody)
  {
    this.allowAccessToParentTenant = obj.allowAccessToParentTenant;
    this.autoConsentToReboots = obj.autoConsentToReboots;
    this.cacheOnly = obj.cacheOnly;
    this.computers = obj.computers;
    this.detectionOnly = obj.detectionOnly;
    this.fullMaintenance = obj.fullMaintenance;
    this.inventoryOnly = obj.inventoryOnly;
    this.offlineBehavior = obj.offlineBehavior;
    this.persons = obj.persons;
    this.promptTimeoutAction = obj.promptTimeoutAction;
    this.promptTimeoutMinutes = obj.promptTimeoutMinutes;
    this.propagateToChildTenants = obj.propagateToChildTenants;
    this.rebootPreference = obj.rebootPreference;
    this.resolutionOnly = obj.resolutionOnly;
    this.runInventoryInDetection = obj.runInventoryInDetection;
    this.scheduleExecutionAfterActiveHours = obj.scheduleExecutionAfterActiveHours;
    this.sendDetectionEmail = obj.sendDetectionEmail;
    this.sendDetectionEmailWhenAllActionsAreCompliant = obj.sendDetectionEmailWhenAllActionsAreCompliant;
    this.sendFollowUpEmail = obj.sendFollowUpEmail;
    this.sendFollowUpOnlyIfActionNeeded = obj.sendFollowUpOnlyIfActionNeeded;
    this.showMaintenanceActions = obj.showMaintenanceActions;
    this.showPostponeButton = obj.showPostponeButton;
    this.showRunNowButton = obj.showRunNowButton;
    this.skipBackgroundJob = obj.skipBackgroundJob;
    this.suppressRebootsDuringBusinessHours = obj.suppressRebootsDuringBusinessHours;
    this.tenants = obj.tenants;
    this.useComputersTimezoneForExecution = obj.useComputersTimezoneForExecution;
    this.useWinningDeployment = obj.useWinningDeployment;
    this.deploymentId = obj.deploymentId;
    this.deploymentType = obj.deploymentType;
    this.licenseId = obj.licenseId;
    this.maintenanceParams = obj.maintenanceParams;
    this.providerLinkIdForAgentUpdates = obj.providerLinkIdForAgentUpdates;
    this.sessionGroupId = obj.sessionGroupId;
    this.timeZoneInfoId = obj.timeZoneInfoId;
    this.updateTime = obj.updateTime;
  }
}
export interface IRunImmyServiceRequestBody
{
  allowAccessToParentTenant: boolean;
  autoConsentToReboots: boolean;
  cacheOnly: boolean;
  computers: IRunImmybotComputerRequestBody[];
  detectionOnly: boolean;
  fullMaintenance: boolean;
  inventoryOnly: boolean;
  offlineBehavior: ComputerOfflineMaintenanceSessionBehavior;
  persons: IRunImmybotPersonRequestBody[];
  promptTimeoutAction: PromptTimeoutAction;
  promptTimeoutMinutes: number;
  propagateToChildTenants: boolean;
  rebootPreference: RebootPreference;
  resolutionOnly: boolean;
  runInventoryInDetection: boolean;
  scheduleExecutionAfterActiveHours: boolean;
  sendDetectionEmail: boolean;
  sendDetectionEmailWhenAllActionsAreCompliant: boolean;
  sendFollowUpEmail: boolean;
  sendFollowUpOnlyIfActionNeeded: boolean;
  showMaintenanceActions: boolean;
  showPostponeButton: boolean;
  showRunNowButton: boolean;
  skipBackgroundJob: boolean;
  suppressRebootsDuringBusinessHours: boolean;
  tenants: IRunImmybotTenantRequestBody[];
  useComputersTimezoneForExecution: boolean;
  useWinningDeployment: boolean;
  deploymentId?: number;
  deploymentType?: DatabaseType;
  licenseId?: number;
  maintenanceParams?: IRunImmybotMaintenanceRequestBody;
  providerLinkIdForAgentUpdates?: number;
  sessionGroupId?: string;
  timeZoneInfoId?: string;
  updateTime?: string;
}
export class RunImmybotComputerRequestBody implements IRunImmybotComputerRequestBody
{
  public computerId: number;
  public maintenanceTaskParameterValueOverrides?: { [key:string]: { [key:string]: IDeploymentParameterValue } };
  constructor (obj: IRunImmybotComputerRequestBody)
  {
    this.computerId = obj.computerId;
    this.maintenanceTaskParameterValueOverrides = obj.maintenanceTaskParameterValueOverrides;
  }
}
export interface IRunImmybotComputerRequestBody
{
  computerId: number;
  maintenanceTaskParameterValueOverrides?: { [key:string]: { [key:string]: IDeploymentParameterValue } };
}
export class RunImmybotTenantRequestBody implements IRunImmybotTenantRequestBody
{
  public tenantId: number;
  public maintenanceTaskParameterValueOverrides?: { [key:string]: { [key:string]: IDeploymentParameterValue } };
  constructor (obj: IRunImmybotTenantRequestBody)
  {
    this.tenantId = obj.tenantId;
    this.maintenanceTaskParameterValueOverrides = obj.maintenanceTaskParameterValueOverrides;
  }
}
export interface IRunImmybotTenantRequestBody
{
  tenantId: number;
  maintenanceTaskParameterValueOverrides?: { [key:string]: { [key:string]: IDeploymentParameterValue } };
}
export class RunImmybotPersonRequestBody implements IRunImmybotPersonRequestBody
{
  public personId: number;
  public maintenanceTaskParameterValueOverrides?: { [key:string]: { [key:string]: IDeploymentParameterValue } };
  constructor (obj: IRunImmybotPersonRequestBody)
  {
    this.personId = obj.personId;
    this.maintenanceTaskParameterValueOverrides = obj.maintenanceTaskParameterValueOverrides;
  }
}
export interface IRunImmybotPersonRequestBody
{
  personId: number;
  maintenanceTaskParameterValueOverrides?: { [key:string]: { [key:string]: IDeploymentParameterValue } };
}
export class RunImmybotMaintenanceRequestBody implements IRunImmybotMaintenanceRequestBody
{
  public repair: boolean;
  public desiredSoftwareState?: DesiredSoftwareState;
  public maintenanceIdentifier?: string;
  public maintenanceTaskMode?: MaintenanceTaskMode;
  public maintenanceType?: MaintenanceType;
  public providerLinkIdForMaintenanceItem?: number;
  public semanticVersion?: string;
  public semanticVersionNormalized?: string;
  public softwareProviderType?: SoftwareProviderType;
  public softwareType?: SoftwareType;
  public taskParameterValues?: { [key:string]: IDeploymentParameterValue };
  constructor (obj: IRunImmybotMaintenanceRequestBody)
  {
    this.repair = obj.repair;
    this.desiredSoftwareState = obj.desiredSoftwareState;
    this.maintenanceIdentifier = obj.maintenanceIdentifier;
    this.maintenanceTaskMode = obj.maintenanceTaskMode;
    this.maintenanceType = obj.maintenanceType;
    this.providerLinkIdForMaintenanceItem = obj.providerLinkIdForMaintenanceItem;
    this.semanticVersion = obj.semanticVersion;
    this.semanticVersionNormalized = obj.semanticVersionNormalized;
    this.softwareProviderType = obj.softwareProviderType;
    this.softwareType = obj.softwareType;
    this.taskParameterValues = obj.taskParameterValues;
  }
}
export interface IRunImmybotMaintenanceRequestBody
{
  repair: boolean;
  desiredSoftwareState?: DesiredSoftwareState;
  maintenanceIdentifier?: string;
  maintenanceTaskMode?: MaintenanceTaskMode;
  maintenanceType?: MaintenanceType;
  providerLinkIdForMaintenanceItem?: number;
  semanticVersion?: string;
  semanticVersionNormalized?: string;
  softwareProviderType?: SoftwareProviderType;
  softwareType?: SoftwareType;
  taskParameterValues?: { [key:string]: IDeploymentParameterValue };
}
export class RunScriptRequestBody implements IRunScriptRequestBody
{
  public cancellationId: string;
  public script: IIDomainScript;
  public computerId?: number;
  public maintenanceActionId?: number;
  public maintenanceSessionId?: number;
  public maintenanceTaskId?: number;
  public maintenanceTaskType?: DatabaseType;
  public sessionLogId?: number;
  public tenantId?: number;
  public terminalId?: string;
  constructor (obj: IRunScriptRequestBody)
  {
    this.cancellationId = obj.cancellationId;
    this.script = obj.script;
    this.computerId = obj.computerId;
    this.maintenanceActionId = obj.maintenanceActionId;
    this.maintenanceSessionId = obj.maintenanceSessionId;
    this.maintenanceTaskId = obj.maintenanceTaskId;
    this.maintenanceTaskType = obj.maintenanceTaskType;
    this.sessionLogId = obj.sessionLogId;
    this.tenantId = obj.tenantId;
    this.terminalId = obj.terminalId;
  }
}
export interface IRunScriptRequestBody
{
  cancellationId: string;
  script: IIDomainScript;
  computerId?: number;
  maintenanceActionId?: number;
  maintenanceSessionId?: number;
  maintenanceTaskId?: number;
  maintenanceTaskType?: DatabaseType;
  sessionLogId?: number;
  tenantId?: number;
  terminalId?: string;
}
export class ScheduleSyncRequestBody implements IScheduleSyncRequestBody
{
  public enable: boolean;
  constructor (obj: IScheduleSyncRequestBody)
  {
    this.enable = obj.enable;
  }
}
export interface IScheduleSyncRequestBody
{
  enable: boolean;
}
export class SendTestBrandingEmailRequest implements ISendTestBrandingEmailRequest
{
  public branding: ICreateBrandingRequestBody;
  public to: string;
  constructor (obj: ISendTestBrandingEmailRequest)
  {
    this.branding = obj.branding;
    this.to = obj.to;
  }
}
export interface ISendTestBrandingEmailRequest
{
  branding: ICreateBrandingRequestBody;
  to: string;
}
export class SendTestEmailRequest implements ISendTestEmailRequest
{
  public enabled: boolean;
  public enableSSL: boolean;
  public from: string;
  public host: string;
  public port: number;
  public timeout: number;
  public to: string;
  public useAuthentication: boolean;
  public password?: string;
  public tenantId?: number;
  public username?: string;
  constructor (obj: ISendTestEmailRequest)
  {
    this.enabled = obj.enabled;
    this.enableSSL = obj.enableSSL;
    this.from = obj.from;
    this.host = obj.host;
    this.port = obj.port;
    this.timeout = obj.timeout;
    this.to = obj.to;
    this.useAuthentication = obj.useAuthentication;
    this.password = obj.password;
    this.tenantId = obj.tenantId;
    this.username = obj.username;
  }
}
export interface ISendTestEmailRequest
{
  enabled: boolean;
  enableSSL: boolean;
  from: string;
  host: string;
  port: number;
  timeout: number;
  to: string;
  useAuthentication: boolean;
  password?: string;
  tenantId?: number;
  username?: string;
}
export class SetExcludedFromUserAffinityRequestBody implements ISetExcludedFromUserAffinityRequestBody
{
  public isExcluded: boolean;
  constructor (obj: ISetExcludedFromUserAffinityRequestBody)
  {
    this.isExcluded = obj.isExcluded;
  }
}
export interface ISetExcludedFromUserAffinityRequestBody
{
  isExcluded: boolean;
}
export class BatchSetExcludedFromUserAffinityRequestBody implements IBatchSetExcludedFromUserAffinityRequestBody
{
  public computerIds: number[];
  public isExcluded: boolean;
  constructor (obj: IBatchSetExcludedFromUserAffinityRequestBody)
  {
    this.computerIds = obj.computerIds;
    this.isExcluded = obj.isExcluded;
  }
}
export interface IBatchSetExcludedFromUserAffinityRequestBody
{
  computerIds: number[];
  isExcluded: boolean;
}
export class SetParentTenantRequest implements ISetParentTenantRequest
{
  public parentTenantId: number;
  public tenantIds: number[];
  constructor (obj: ISetParentTenantRequest)
  {
    this.parentTenantId = obj.parentTenantId;
    this.tenantIds = obj.tenantIds;
  }
}
export interface ISetParentTenantRequest
{
  parentTenantId: number;
  tenantIds: number[];
}
export class RemoveParentTenantRequest implements IRemoveParentTenantRequest
{
  public parentTenantId: number;
  public tenantIds: number[];
  constructor (obj: IRemoveParentTenantRequest)
  {
    this.parentTenantId = obj.parentTenantId;
    this.tenantIds = obj.tenantIds;
  }
}
export interface IRemoveParentTenantRequest
{
  parentTenantId: number;
  tenantIds: number[];
}
export class SetupTestIntegrationRequest implements ISetupTestIntegrationRequest
{
  public script: string;
  constructor (obj: ISetupTestIntegrationRequest)
  {
    this.script = obj.script;
  }
}
export interface ISetupTestIntegrationRequest
{
  script: string;
}
export class TestIntegrationBindConfigurationFormRequest implements ITestIntegrationBindConfigurationFormRequest
{
  public parameterValues: { [key:string]: IParameterValue };
  constructor (obj: ITestIntegrationBindConfigurationFormRequest)
  {
    this.parameterValues = obj.parameterValues;
  }
}
export interface ITestIntegrationBindConfigurationFormRequest
{
  parameterValues: { [key:string]: IParameterValue };
}
export class TestIntegrationMethodRequest implements ITestIntegrationMethodRequest
{
  public providerTypeFormData: any;
  constructor (obj: ITestIntegrationMethodRequest)
  {
    this.providerTypeFormData = obj.providerTypeFormData;
  }
}
export interface ITestIntegrationMethodRequest
{
  providerTypeFormData: any;
}
export class SilenceNotificationByObjectIdRequest implements ISilenceNotificationByObjectIdRequest
{
  public objectId: string;
  constructor (obj: ISilenceNotificationByObjectIdRequest)
  {
    this.objectId = obj.objectId;
  }
}
export interface ISilenceNotificationByObjectIdRequest
{
  objectId: string;
}
export class SkipOnboardingRequest implements ISkipOnboardingRequest
{
  public computerId: number;
  constructor (obj: ISkipOnboardingRequest)
  {
    this.computerId = obj.computerId;
  }
}
export interface ISkipOnboardingRequest
{
  computerId: number;
}
export class SoftwareRequestBodyBase implements ISoftwareRequestBodyBase
{
  public detectionMethod: DetectionMethod;
  public hidden: boolean;
  public installOrder: number;
  public licenseRequirement: SoftwareLicenseRequirement;
  public licenseType: LicenseType;
  public name: string;
  public rebootNeeded: boolean;
  public recommended: boolean;
  public testRequired: boolean;
  public upgradeStrategy: UpdateActionType;
  public useDynamicVersions: boolean;
  public agentIntegrationTypeId?: string;
  public chocoProviderSoftwareId?: string;
  public detectionScriptId?: number;
  public downloadInstallerScriptId?: number;
  public dynamicVersionsScriptId?: number;
  public installScriptId?: number;
  public licenseDescription?: string;
  public maintenanceTaskId?: number;
  public niniteProviderSoftwareId?: string;
  public notes?: string;
  public postInstallScriptId?: number;
  public postUninstallScriptId?: number;
  public repairScriptId?: number;
  public repairType?: number;
  public softwareIconMediaId?: number;
  public softwareTableName?: string;
  public softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  public testFailedError?: string;
  public testScriptId?: number;
  public uninstallScriptId?: number;
  public upgradeCode?: string;
  public upgradeScriptId?: number;
  constructor (obj: ISoftwareRequestBodyBase)
  {
    this.detectionMethod = obj.detectionMethod;
    this.hidden = obj.hidden;
    this.installOrder = obj.installOrder;
    this.licenseRequirement = obj.licenseRequirement;
    this.licenseType = obj.licenseType;
    this.name = obj.name;
    this.rebootNeeded = obj.rebootNeeded;
    this.recommended = obj.recommended;
    this.testRequired = obj.testRequired;
    this.upgradeStrategy = obj.upgradeStrategy;
    this.useDynamicVersions = obj.useDynamicVersions;
    this.agentIntegrationTypeId = obj.agentIntegrationTypeId;
    this.chocoProviderSoftwareId = obj.chocoProviderSoftwareId;
    this.detectionScriptId = obj.detectionScriptId;
    this.downloadInstallerScriptId = obj.downloadInstallerScriptId;
    this.dynamicVersionsScriptId = obj.dynamicVersionsScriptId;
    this.installScriptId = obj.installScriptId;
    this.licenseDescription = obj.licenseDescription;
    this.maintenanceTaskId = obj.maintenanceTaskId;
    this.niniteProviderSoftwareId = obj.niniteProviderSoftwareId;
    this.notes = obj.notes;
    this.postInstallScriptId = obj.postInstallScriptId;
    this.postUninstallScriptId = obj.postUninstallScriptId;
    this.repairScriptId = obj.repairScriptId;
    this.repairType = obj.repairType;
    this.softwareIconMediaId = obj.softwareIconMediaId;
    this.softwareTableName = obj.softwareTableName;
    this.softwareTableNameSearchMode = obj.softwareTableNameSearchMode;
    this.testFailedError = obj.testFailedError;
    this.testScriptId = obj.testScriptId;
    this.uninstallScriptId = obj.uninstallScriptId;
    this.upgradeCode = obj.upgradeCode;
    this.upgradeScriptId = obj.upgradeScriptId;
  }
}
export interface ISoftwareRequestBodyBase
{
  detectionMethod: DetectionMethod;
  hidden: boolean;
  installOrder: number;
  licenseRequirement: SoftwareLicenseRequirement;
  licenseType: LicenseType;
  name: string;
  rebootNeeded: boolean;
  recommended: boolean;
  testRequired: boolean;
  upgradeStrategy: UpdateActionType;
  useDynamicVersions: boolean;
  agentIntegrationTypeId?: string;
  chocoProviderSoftwareId?: string;
  detectionScriptId?: number;
  downloadInstallerScriptId?: number;
  dynamicVersionsScriptId?: number;
  installScriptId?: number;
  licenseDescription?: string;
  maintenanceTaskId?: number;
  niniteProviderSoftwareId?: string;
  notes?: string;
  postInstallScriptId?: number;
  postUninstallScriptId?: number;
  repairScriptId?: number;
  repairType?: number;
  softwareIconMediaId?: number;
  softwareTableName?: string;
  softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  testFailedError?: string;
  testScriptId?: number;
  uninstallScriptId?: number;
  upgradeCode?: string;
  upgradeScriptId?: number;
}
export class SoftwareVersionRequestBodyBase implements ISoftwareVersionRequestBodyBase
{
  public displayVersion: string;
  public installerType: SoftwareVersionInstallerType;
  public licenseType: LicenseType;
  public packageType: PackageType;
  public semanticVersion: string;
  public softwareIdentifier: string;
  public softwareType: SoftwareType;
  public testRequired: boolean;
  public upgradeStrategy: UpdateActionType;
  public blobName?: string;
  public dependsOnSemanticVersion?: string;
  public displayName?: string;
  public installerFile?: string;
  public installScriptId?: number;
  public notes?: string;
  public packageHash?: string;
  public postInstallScriptId?: number;
  public postUninstallScriptId?: number;
  public productCode?: string;
  public relativeCacheSourcePath?: string;
  public testFailedError?: string;
  public testScriptId?: number;
  public uninstallScriptId?: number;
  public upgradeScriptId?: number;
  public url?: string;
  constructor (obj: ISoftwareVersionRequestBodyBase)
  {
    this.displayVersion = obj.displayVersion;
    this.installerType = obj.installerType;
    this.licenseType = obj.licenseType;
    this.packageType = obj.packageType;
    this.semanticVersion = obj.semanticVersion;
    this.softwareIdentifier = obj.softwareIdentifier;
    this.softwareType = obj.softwareType;
    this.testRequired = obj.testRequired;
    this.upgradeStrategy = obj.upgradeStrategy;
    this.blobName = obj.blobName;
    this.dependsOnSemanticVersion = obj.dependsOnSemanticVersion;
    this.displayName = obj.displayName;
    this.installerFile = obj.installerFile;
    this.installScriptId = obj.installScriptId;
    this.notes = obj.notes;
    this.packageHash = obj.packageHash;
    this.postInstallScriptId = obj.postInstallScriptId;
    this.postUninstallScriptId = obj.postUninstallScriptId;
    this.productCode = obj.productCode;
    this.relativeCacheSourcePath = obj.relativeCacheSourcePath;
    this.testFailedError = obj.testFailedError;
    this.testScriptId = obj.testScriptId;
    this.uninstallScriptId = obj.uninstallScriptId;
    this.upgradeScriptId = obj.upgradeScriptId;
    this.url = obj.url;
  }
}
export interface ISoftwareVersionRequestBodyBase
{
  displayVersion: string;
  installerType: SoftwareVersionInstallerType;
  licenseType: LicenseType;
  packageType: PackageType;
  semanticVersion: string;
  softwareIdentifier: string;
  softwareType: SoftwareType;
  testRequired: boolean;
  upgradeStrategy: UpdateActionType;
  blobName?: string;
  dependsOnSemanticVersion?: string;
  displayName?: string;
  installerFile?: string;
  installScriptId?: number;
  notes?: string;
  packageHash?: string;
  postInstallScriptId?: number;
  postUninstallScriptId?: number;
  productCode?: string;
  relativeCacheSourcePath?: string;
  testFailedError?: string;
  testScriptId?: number;
  uninstallScriptId?: number;
  upgradeScriptId?: number;
  url?: string;
}
export class SubmitFeedbackRequestBody implements ISubmitFeedbackRequestBody
{
  public details: string;
  public rating: string;
  constructor (obj: ISubmitFeedbackRequestBody)
  {
    this.details = obj.details;
    this.rating = obj.rating;
  }
}
export interface ISubmitFeedbackRequestBody
{
  details: string;
  rating: string;
}
export class SyncAgentsForClientsRequest implements ISyncAgentsForClientsRequest
{
  public clientIds: string[];
  constructor (obj: ISyncAgentsForClientsRequest)
  {
    this.clientIds = obj.clientIds;
  }
}
export interface ISyncAgentsForClientsRequest
{
  clientIds: string[];
}
export class SyncAzureDataForTenantsRequest implements ISyncAzureDataForTenantsRequest
{
  public allPartnerTenants: boolean;
  public tenantPrincipalIds: string[];
  constructor (obj: ISyncAzureDataForTenantsRequest)
  {
    this.allPartnerTenants = obj.allPartnerTenants;
    this.tenantPrincipalIds = obj.tenantPrincipalIds;
  }
}
export interface ISyncAzureDataForTenantsRequest
{
  allPartnerTenants: boolean;
  tenantPrincipalIds: string[];
}
export class TenantConsentRequestBody implements ITenantConsentRequestBody
{
  public appType: AppRegistrationType;
  public tenantPrincipalId: string;
  constructor (obj: ITenantConsentRequestBody)
  {
    this.appType = obj.appType;
    this.tenantPrincipalId = obj.tenantPrincipalId;
  }
}
export interface ITenantConsentRequestBody
{
  appType: AppRegistrationType;
  tenantPrincipalId: string;
}
export class UpdateApplicationLogSourceContextRequest implements IUpdateApplicationLogSourceContextRequest
{
  public minimumLevel: LogLevel;
  public sourceContext: string;
  constructor (obj: IUpdateApplicationLogSourceContextRequest)
  {
    this.minimumLevel = obj.minimumLevel;
    this.sourceContext = obj.sourceContext;
  }
}
export interface IUpdateApplicationLogSourceContextRequest
{
  minimumLevel: LogLevel;
  sourceContext: string;
}
export class ClearApplicationLogSourceContextRequest implements IClearApplicationLogSourceContextRequest
{
  public sourceContext: string;
  constructor (obj: IClearApplicationLogSourceContextRequest)
  {
    this.sourceContext = obj.sourceContext;
  }
}
export interface IClearApplicationLogSourceContextRequest
{
  sourceContext: string;
}
export class ToggleApplicationLogStreamingRequest implements IToggleApplicationLogStreamingRequest
{
  public enabled: boolean;
  constructor (obj: IToggleApplicationLogStreamingRequest)
  {
    this.enabled = obj.enabled;
  }
}
export interface IToggleApplicationLogStreamingRequest
{
  enabled: boolean;
}
export class UpdateAzureTenantLinkRequest implements IUpdateAzureTenantLinkRequest
{
  public removeCustomersSyncedUsers: boolean;
  public removeSyncedUsers: boolean;
  public tenantId: number;
  public unlinkCustomers: boolean;
  public limitToDomains?: string[];
  public partnerPrincipalId?: string;
  public principalId?: string;
  constructor (obj: IUpdateAzureTenantLinkRequest)
  {
    this.removeCustomersSyncedUsers = obj.removeCustomersSyncedUsers;
    this.removeSyncedUsers = obj.removeSyncedUsers;
    this.tenantId = obj.tenantId;
    this.unlinkCustomers = obj.unlinkCustomers;
    this.limitToDomains = obj.limitToDomains;
    this.partnerPrincipalId = obj.partnerPrincipalId;
    this.principalId = obj.principalId;
  }
}
export interface IUpdateAzureTenantLinkRequest
{
  removeCustomersSyncedUsers: boolean;
  removeSyncedUsers: boolean;
  tenantId: number;
  unlinkCustomers: boolean;
  limitToDomains?: string[];
  partnerPrincipalId?: string;
  principalId?: string;
}
export class UpdateBrandingRequestBody implements IUpdateBrandingRequestBody
{
  public description: string;
  public id: number;
  public backgroundColor?: string;
  public endDate?: string;
  public foregroundColor?: string;
  public fromAddress?: string;
  public ignoreYear?: boolean;
  public logoAltText?: string;
  public logoUri?: string;
  public mascotImgUri?: string;
  public mascotName?: string;
  public startDate?: string;
  public tableHeaderColor?: string;
  public tableHeaderTextColor?: string;
  public tenantId?: number;
  public textColor?: string;
  public timeFormat?: string;
  constructor (obj: IUpdateBrandingRequestBody)
  {
    this.description = obj.description;
    this.id = obj.id;
    this.backgroundColor = obj.backgroundColor;
    this.endDate = obj.endDate;
    this.foregroundColor = obj.foregroundColor;
    this.fromAddress = obj.fromAddress;
    this.ignoreYear = obj.ignoreYear;
    this.logoAltText = obj.logoAltText;
    this.logoUri = obj.logoUri;
    this.mascotImgUri = obj.mascotImgUri;
    this.mascotName = obj.mascotName;
    this.startDate = obj.startDate;
    this.tableHeaderColor = obj.tableHeaderColor;
    this.tableHeaderTextColor = obj.tableHeaderTextColor;
    this.tenantId = obj.tenantId;
    this.textColor = obj.textColor;
    this.timeFormat = obj.timeFormat;
  }
}
export interface IUpdateBrandingRequestBody
{
  description: string;
  id: number;
  backgroundColor?: string;
  endDate?: string;
  foregroundColor?: string;
  fromAddress?: string;
  ignoreYear?: boolean;
  logoAltText?: string;
  logoUri?: string;
  mascotImgUri?: string;
  mascotName?: string;
  startDate?: string;
  tableHeaderColor?: string;
  tableHeaderTextColor?: string;
  tenantId?: number;
  textColor?: string;
  timeFormat?: string;
}
export class UpdateComputerAdditionalPersonsRequestBody implements IUpdateComputerAdditionalPersonsRequestBody
{
  public additionalPersonIds: number[];
  constructor (obj: IUpdateComputerAdditionalPersonsRequestBody)
  {
    this.additionalPersonIds = obj.additionalPersonIds;
  }
}
export interface IUpdateComputerAdditionalPersonsRequestBody
{
  additionalPersonIds: number[];
}
export class UpdateComputerPrimaryPersonRequestBody implements IUpdateComputerPrimaryPersonRequestBody
{
  public primaryPersonId?: number;
  constructor (obj: IUpdateComputerPrimaryPersonRequestBody = {})
  {
    this.primaryPersonId = obj.primaryPersonId;
  }
}
export interface IUpdateComputerPrimaryPersonRequestBody
{
  primaryPersonId?: number;
}
export class UpdateComputerRequestBody implements IUpdateComputerRequestBody
{
  public additionalPersonIds: number[];
  public onboardingStatus: ComputerOnboardingStatus;
  public providerLinkUpdates: IProviderLinkUpdate[];
  public tenantId: number;
  public notes?: string;
  public primaryPersonId?: number;
  constructor (obj: IUpdateComputerRequestBody)
  {
    this.additionalPersonIds = obj.additionalPersonIds;
    this.onboardingStatus = obj.onboardingStatus;
    this.providerLinkUpdates = obj.providerLinkUpdates;
    this.tenantId = obj.tenantId;
    this.notes = obj.notes;
    this.primaryPersonId = obj.primaryPersonId;
  }
}
export interface IUpdateComputerRequestBody
{
  additionalPersonIds: number[];
  onboardingStatus: ComputerOnboardingStatus;
  providerLinkUpdates: IProviderLinkUpdate[];
  tenantId: number;
  notes?: string;
  primaryPersonId?: number;
}
export class ProviderLinkUpdate implements IProviderLinkUpdate
{
  public providerLinkId: number;
  public clientId?: string;
  public deviceUpdateFormData?: any;
  constructor (obj: IProviderLinkUpdate)
  {
    this.providerLinkId = obj.providerLinkId;
    this.clientId = obj.clientId;
    this.deviceUpdateFormData = obj.deviceUpdateFormData;
  }
}
export interface IProviderLinkUpdate
{
  providerLinkId: number;
  clientId?: string;
  deviceUpdateFormData?: any;
}
export class UpdateGlobalSoftwareRequestBody implements IUpdateGlobalSoftwareRequestBody
{
  public detectionMethod: DetectionMethod;
  public hidden: boolean;
  public installOrder: number;
  public licenseRequirement: SoftwareLicenseRequirement;
  public licenseType: LicenseType;
  public name: string;
  public rebootNeeded: boolean;
  public recommended: boolean;
  public softwareId: number;
  public softwarePrerequisites: ISoftwarePrerequisite[];
  public testRequired: boolean;
  public upgradeStrategy: UpdateActionType;
  public useDynamicVersions: boolean;
  public agentIntegrationTypeId?: string;
  public chocoProviderSoftwareId?: string;
  public detectionScriptId?: number;
  public downloadInstallerScriptId?: number;
  public dynamicVersionsScriptId?: number;
  public installScriptId?: number;
  public licenseDescription?: string;
  public maintenanceTaskId?: number;
  public niniteProviderSoftwareId?: string;
  public notes?: string;
  public postInstallScriptId?: number;
  public postUninstallScriptId?: number;
  public repairScriptId?: number;
  public repairType?: number;
  public softwareIconMediaId?: number;
  public softwareTableName?: string;
  public softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  public testFailedError?: string;
  public testScriptId?: number;
  public uninstallScriptId?: number;
  public upgradeCode?: string;
  public upgradeScriptId?: number;
  constructor (obj: IUpdateGlobalSoftwareRequestBody)
  {
    this.detectionMethod = obj.detectionMethod;
    this.hidden = obj.hidden;
    this.installOrder = obj.installOrder;
    this.licenseRequirement = obj.licenseRequirement;
    this.licenseType = obj.licenseType;
    this.name = obj.name;
    this.rebootNeeded = obj.rebootNeeded;
    this.recommended = obj.recommended;
    this.softwareId = obj.softwareId;
    this.softwarePrerequisites = obj.softwarePrerequisites;
    this.testRequired = obj.testRequired;
    this.upgradeStrategy = obj.upgradeStrategy;
    this.useDynamicVersions = obj.useDynamicVersions;
    this.agentIntegrationTypeId = obj.agentIntegrationTypeId;
    this.chocoProviderSoftwareId = obj.chocoProviderSoftwareId;
    this.detectionScriptId = obj.detectionScriptId;
    this.downloadInstallerScriptId = obj.downloadInstallerScriptId;
    this.dynamicVersionsScriptId = obj.dynamicVersionsScriptId;
    this.installScriptId = obj.installScriptId;
    this.licenseDescription = obj.licenseDescription;
    this.maintenanceTaskId = obj.maintenanceTaskId;
    this.niniteProviderSoftwareId = obj.niniteProviderSoftwareId;
    this.notes = obj.notes;
    this.postInstallScriptId = obj.postInstallScriptId;
    this.postUninstallScriptId = obj.postUninstallScriptId;
    this.repairScriptId = obj.repairScriptId;
    this.repairType = obj.repairType;
    this.softwareIconMediaId = obj.softwareIconMediaId;
    this.softwareTableName = obj.softwareTableName;
    this.softwareTableNameSearchMode = obj.softwareTableNameSearchMode;
    this.testFailedError = obj.testFailedError;
    this.testScriptId = obj.testScriptId;
    this.uninstallScriptId = obj.uninstallScriptId;
    this.upgradeCode = obj.upgradeCode;
    this.upgradeScriptId = obj.upgradeScriptId;
  }
}
export interface IUpdateGlobalSoftwareRequestBody
{
  detectionMethod: DetectionMethod;
  hidden: boolean;
  installOrder: number;
  licenseRequirement: SoftwareLicenseRequirement;
  licenseType: LicenseType;
  name: string;
  rebootNeeded: boolean;
  recommended: boolean;
  softwareId: number;
  softwarePrerequisites: ISoftwarePrerequisite[];
  testRequired: boolean;
  upgradeStrategy: UpdateActionType;
  useDynamicVersions: boolean;
  agentIntegrationTypeId?: string;
  chocoProviderSoftwareId?: string;
  detectionScriptId?: number;
  downloadInstallerScriptId?: number;
  dynamicVersionsScriptId?: number;
  installScriptId?: number;
  licenseDescription?: string;
  maintenanceTaskId?: number;
  niniteProviderSoftwareId?: string;
  notes?: string;
  postInstallScriptId?: number;
  postUninstallScriptId?: number;
  repairScriptId?: number;
  repairType?: number;
  softwareIconMediaId?: number;
  softwareTableName?: string;
  softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  testFailedError?: string;
  testScriptId?: number;
  uninstallScriptId?: number;
  upgradeCode?: string;
  upgradeScriptId?: number;
}
export class UpdateGlobalSoftwareVersionRequestBody implements IUpdateGlobalSoftwareVersionRequestBody
{
  public installerType: SoftwareVersionInstallerType;
  public licenseType: LicenseType;
  public packageType: PackageType;
  public semanticVersion: string;
  public softwareId: number;
  public softwareIdentifier: string;
  public softwareType: SoftwareType;
  public testRequired: boolean;
  public upgradeStrategy: UpdateActionType;
  public blobName?: string;
  public currentSemanticVersion?: string;
  public dependsOnSemanticVersion?: string;
  public displayName?: string;
  public displayVersion?: string;
  public installerFile?: string;
  public installScriptId?: number;
  public notes?: string;
  public packageHash?: string;
  public postInstallScriptId?: number;
  public postUninstallScriptId?: number;
  public productCode?: string;
  public relativeCacheSourcePath?: string;
  public testFailedError?: string;
  public testScriptId?: number;
  public uninstallScriptId?: number;
  public upgradeScriptId?: number;
  public url?: string;
  constructor (obj: IUpdateGlobalSoftwareVersionRequestBody)
  {
    this.installerType = obj.installerType;
    this.licenseType = obj.licenseType;
    this.packageType = obj.packageType;
    this.semanticVersion = obj.semanticVersion;
    this.softwareId = obj.softwareId;
    this.softwareIdentifier = obj.softwareIdentifier;
    this.softwareType = obj.softwareType;
    this.testRequired = obj.testRequired;
    this.upgradeStrategy = obj.upgradeStrategy;
    this.blobName = obj.blobName;
    this.currentSemanticVersion = obj.currentSemanticVersion;
    this.dependsOnSemanticVersion = obj.dependsOnSemanticVersion;
    this.displayName = obj.displayName;
    this.displayVersion = obj.displayVersion;
    this.installerFile = obj.installerFile;
    this.installScriptId = obj.installScriptId;
    this.notes = obj.notes;
    this.packageHash = obj.packageHash;
    this.postInstallScriptId = obj.postInstallScriptId;
    this.postUninstallScriptId = obj.postUninstallScriptId;
    this.productCode = obj.productCode;
    this.relativeCacheSourcePath = obj.relativeCacheSourcePath;
    this.testFailedError = obj.testFailedError;
    this.testScriptId = obj.testScriptId;
    this.uninstallScriptId = obj.uninstallScriptId;
    this.upgradeScriptId = obj.upgradeScriptId;
    this.url = obj.url;
  }
}
export interface IUpdateGlobalSoftwareVersionRequestBody
{
  installerType: SoftwareVersionInstallerType;
  licenseType: LicenseType;
  packageType: PackageType;
  semanticVersion: string;
  softwareId: number;
  softwareIdentifier: string;
  softwareType: SoftwareType;
  testRequired: boolean;
  upgradeStrategy: UpdateActionType;
  blobName?: string;
  currentSemanticVersion?: string;
  dependsOnSemanticVersion?: string;
  displayName?: string;
  displayVersion?: string;
  installerFile?: string;
  installScriptId?: number;
  notes?: string;
  packageHash?: string;
  postInstallScriptId?: number;
  postUninstallScriptId?: number;
  productCode?: string;
  relativeCacheSourcePath?: string;
  testFailedError?: string;
  testScriptId?: number;
  uninstallScriptId?: number;
  upgradeScriptId?: number;
  url?: string;
}
export class UpdateLicenseRequestBody implements IUpdateLicenseRequestBody
{
  public id: number;
  public licenseValue: string;
  public name: string;
  public restrictToMajorVersion: boolean;
  public softwareIdentifier: string;
  public softwareName: string;
  public softwareType: SoftwareType;
  public semanticVersion?: string;
  public tenantId?: number;
  constructor (obj: IUpdateLicenseRequestBody)
  {
    this.id = obj.id;
    this.licenseValue = obj.licenseValue;
    this.name = obj.name;
    this.restrictToMajorVersion = obj.restrictToMajorVersion;
    this.softwareIdentifier = obj.softwareIdentifier;
    this.softwareName = obj.softwareName;
    this.softwareType = obj.softwareType;
    this.semanticVersion = obj.semanticVersion;
    this.tenantId = obj.tenantId;
  }
}
export interface IUpdateLicenseRequestBody
{
  id: number;
  licenseValue: string;
  name: string;
  restrictToMajorVersion: boolean;
  softwareIdentifier: string;
  softwareName: string;
  softwareType: SoftwareType;
  semanticVersion?: string;
  tenantId?: number;
}
export class UpdateLocalSoftwareRequestBody implements IUpdateLocalSoftwareRequestBody
{
  public detectionMethod: DetectionMethod;
  public hidden: boolean;
  public installOrder: number;
  public licenseRequirement: SoftwareLicenseRequirement;
  public licenseType: LicenseType;
  public name: string;
  public rebootNeeded: boolean;
  public recommended: boolean;
  public softwareId: number;
  public tenantSoftware: number[];
  public testRequired: boolean;
  public upgradeStrategy: UpdateActionType;
  public useDynamicVersions: boolean;
  public agentIntegrationTypeId?: string;
  public chocoProviderSoftwareId?: string;
  public detectionScriptId?: number;
  public detectionScriptType?: DatabaseType;
  public downloadInstallerScriptId?: number;
  public downloadInstallerScriptType?: DatabaseType;
  public dynamicVersionsScriptId?: number;
  public dynamicVersionsScriptType?: DatabaseType;
  public installScriptId?: number;
  public installScriptType?: DatabaseType;
  public licenseDescription?: string;
  public maintenanceTaskId?: number;
  public maintenanceTaskType?: DatabaseType;
  public niniteProviderSoftwareId?: string;
  public notes?: string;
  public ownerTenantId?: number;
  public postInstallScriptId?: number;
  public postInstallScriptType?: DatabaseType;
  public postUninstallScriptId?: number;
  public postUninstallScriptType?: DatabaseType;
  public repairScriptId?: number;
  public repairScriptType?: DatabaseType;
  public repairType?: number;
  public softwareIconMediaId?: number;
  public softwarePrerequisites?: ISoftwarePrerequisite[];
  public softwareTableName?: string;
  public softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  public testFailedError?: string;
  public testScriptId?: number;
  public testScriptType?: DatabaseType;
  public uninstallScriptId?: number;
  public uninstallScriptType?: DatabaseType;
  public upgradeCode?: string;
  public upgradeScriptId?: number;
  public upgradeScriptType?: DatabaseType;
  constructor (obj: IUpdateLocalSoftwareRequestBody)
  {
    this.detectionMethod = obj.detectionMethod;
    this.hidden = obj.hidden;
    this.installOrder = obj.installOrder;
    this.licenseRequirement = obj.licenseRequirement;
    this.licenseType = obj.licenseType;
    this.name = obj.name;
    this.rebootNeeded = obj.rebootNeeded;
    this.recommended = obj.recommended;
    this.softwareId = obj.softwareId;
    this.tenantSoftware = obj.tenantSoftware;
    this.testRequired = obj.testRequired;
    this.upgradeStrategy = obj.upgradeStrategy;
    this.useDynamicVersions = obj.useDynamicVersions;
    this.agentIntegrationTypeId = obj.agentIntegrationTypeId;
    this.chocoProviderSoftwareId = obj.chocoProviderSoftwareId;
    this.detectionScriptId = obj.detectionScriptId;
    this.detectionScriptType = obj.detectionScriptType;
    this.downloadInstallerScriptId = obj.downloadInstallerScriptId;
    this.downloadInstallerScriptType = obj.downloadInstallerScriptType;
    this.dynamicVersionsScriptId = obj.dynamicVersionsScriptId;
    this.dynamicVersionsScriptType = obj.dynamicVersionsScriptType;
    this.installScriptId = obj.installScriptId;
    this.installScriptType = obj.installScriptType;
    this.licenseDescription = obj.licenseDescription;
    this.maintenanceTaskId = obj.maintenanceTaskId;
    this.maintenanceTaskType = obj.maintenanceTaskType;
    this.niniteProviderSoftwareId = obj.niniteProviderSoftwareId;
    this.notes = obj.notes;
    this.ownerTenantId = obj.ownerTenantId;
    this.postInstallScriptId = obj.postInstallScriptId;
    this.postInstallScriptType = obj.postInstallScriptType;
    this.postUninstallScriptId = obj.postUninstallScriptId;
    this.postUninstallScriptType = obj.postUninstallScriptType;
    this.repairScriptId = obj.repairScriptId;
    this.repairScriptType = obj.repairScriptType;
    this.repairType = obj.repairType;
    this.softwareIconMediaId = obj.softwareIconMediaId;
    this.softwarePrerequisites = obj.softwarePrerequisites;
    this.softwareTableName = obj.softwareTableName;
    this.softwareTableNameSearchMode = obj.softwareTableNameSearchMode;
    this.testFailedError = obj.testFailedError;
    this.testScriptId = obj.testScriptId;
    this.testScriptType = obj.testScriptType;
    this.uninstallScriptId = obj.uninstallScriptId;
    this.uninstallScriptType = obj.uninstallScriptType;
    this.upgradeCode = obj.upgradeCode;
    this.upgradeScriptId = obj.upgradeScriptId;
    this.upgradeScriptType = obj.upgradeScriptType;
  }
}
export interface IUpdateLocalSoftwareRequestBody
{
  detectionMethod: DetectionMethod;
  hidden: boolean;
  installOrder: number;
  licenseRequirement: SoftwareLicenseRequirement;
  licenseType: LicenseType;
  name: string;
  rebootNeeded: boolean;
  recommended: boolean;
  softwareId: number;
  tenantSoftware: number[];
  testRequired: boolean;
  upgradeStrategy: UpdateActionType;
  useDynamicVersions: boolean;
  agentIntegrationTypeId?: string;
  chocoProviderSoftwareId?: string;
  detectionScriptId?: number;
  detectionScriptType?: DatabaseType;
  downloadInstallerScriptId?: number;
  downloadInstallerScriptType?: DatabaseType;
  dynamicVersionsScriptId?: number;
  dynamicVersionsScriptType?: DatabaseType;
  installScriptId?: number;
  installScriptType?: DatabaseType;
  licenseDescription?: string;
  maintenanceTaskId?: number;
  maintenanceTaskType?: DatabaseType;
  niniteProviderSoftwareId?: string;
  notes?: string;
  ownerTenantId?: number;
  postInstallScriptId?: number;
  postInstallScriptType?: DatabaseType;
  postUninstallScriptId?: number;
  postUninstallScriptType?: DatabaseType;
  repairScriptId?: number;
  repairScriptType?: DatabaseType;
  repairType?: number;
  softwareIconMediaId?: number;
  softwarePrerequisites?: ISoftwarePrerequisite[];
  softwareTableName?: string;
  softwareTableNameSearchMode?: SoftwareTableNameSearchMode;
  testFailedError?: string;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  uninstallScriptId?: number;
  uninstallScriptType?: DatabaseType;
  upgradeCode?: string;
  upgradeScriptId?: number;
  upgradeScriptType?: DatabaseType;
}
export class UpdateLocalSoftwareVersionRequestBody implements IUpdateLocalSoftwareVersionRequestBody
{
  public installerType: SoftwareVersionInstallerType;
  public licenseType: LicenseType;
  public packageType: PackageType;
  public semanticVersion: string;
  public softwareId: number;
  public softwareIdentifier: string;
  public softwareType: SoftwareType;
  public testRequired: boolean;
  public upgradeStrategy: UpdateActionType;
  public blobName?: string;
  public currentSemanticVersion?: string;
  public dependsOnSemanticVersion?: string;
  public displayName?: string;
  public displayVersion?: string;
  public installerFile?: string;
  public installScriptId?: number;
  public installScriptType?: DatabaseType;
  public notes?: string;
  public packageHash?: string;
  public postInstallScriptId?: number;
  public postInstallScriptType?: DatabaseType;
  public postUninstallScriptId?: number;
  public postUninstallScriptType?: DatabaseType;
  public productCode?: string;
  public relativeCacheSourcePath?: string;
  public testFailedError?: string;
  public testScriptId?: number;
  public testScriptType?: DatabaseType;
  public uninstallScriptId?: number;
  public uninstallScriptType?: DatabaseType;
  public upgradeScriptId?: number;
  public upgradeScriptType?: DatabaseType;
  public url?: string;
  constructor (obj: IUpdateLocalSoftwareVersionRequestBody)
  {
    this.installerType = obj.installerType;
    this.licenseType = obj.licenseType;
    this.packageType = obj.packageType;
    this.semanticVersion = obj.semanticVersion;
    this.softwareId = obj.softwareId;
    this.softwareIdentifier = obj.softwareIdentifier;
    this.softwareType = obj.softwareType;
    this.testRequired = obj.testRequired;
    this.upgradeStrategy = obj.upgradeStrategy;
    this.blobName = obj.blobName;
    this.currentSemanticVersion = obj.currentSemanticVersion;
    this.dependsOnSemanticVersion = obj.dependsOnSemanticVersion;
    this.displayName = obj.displayName;
    this.displayVersion = obj.displayVersion;
    this.installerFile = obj.installerFile;
    this.installScriptId = obj.installScriptId;
    this.installScriptType = obj.installScriptType;
    this.notes = obj.notes;
    this.packageHash = obj.packageHash;
    this.postInstallScriptId = obj.postInstallScriptId;
    this.postInstallScriptType = obj.postInstallScriptType;
    this.postUninstallScriptId = obj.postUninstallScriptId;
    this.postUninstallScriptType = obj.postUninstallScriptType;
    this.productCode = obj.productCode;
    this.relativeCacheSourcePath = obj.relativeCacheSourcePath;
    this.testFailedError = obj.testFailedError;
    this.testScriptId = obj.testScriptId;
    this.testScriptType = obj.testScriptType;
    this.uninstallScriptId = obj.uninstallScriptId;
    this.uninstallScriptType = obj.uninstallScriptType;
    this.upgradeScriptId = obj.upgradeScriptId;
    this.upgradeScriptType = obj.upgradeScriptType;
    this.url = obj.url;
  }
}
export interface IUpdateLocalSoftwareVersionRequestBody
{
  installerType: SoftwareVersionInstallerType;
  licenseType: LicenseType;
  packageType: PackageType;
  semanticVersion: string;
  softwareId: number;
  softwareIdentifier: string;
  softwareType: SoftwareType;
  testRequired: boolean;
  upgradeStrategy: UpdateActionType;
  blobName?: string;
  currentSemanticVersion?: string;
  dependsOnSemanticVersion?: string;
  displayName?: string;
  displayVersion?: string;
  installerFile?: string;
  installScriptId?: number;
  installScriptType?: DatabaseType;
  notes?: string;
  packageHash?: string;
  postInstallScriptId?: number;
  postInstallScriptType?: DatabaseType;
  postUninstallScriptId?: number;
  postUninstallScriptType?: DatabaseType;
  productCode?: string;
  relativeCacheSourcePath?: string;
  testFailedError?: string;
  testScriptId?: number;
  testScriptType?: DatabaseType;
  uninstallScriptId?: number;
  uninstallScriptType?: DatabaseType;
  upgradeScriptId?: number;
  upgradeScriptType?: DatabaseType;
  url?: string;
}
export class UpdateNotesPayload implements IUpdateNotesPayload
{
  public notes: string;
  constructor (obj: IUpdateNotesPayload)
  {
    this.notes = obj.notes;
  }
}
export interface IUpdateNotesPayload
{
  notes: string;
}
export class UpdateProviderLinkRequestBody implements IUpdateProviderLinkRequestBody
{
  public disabled: boolean;
  public excludedCapabilities: string[];
  public id: number;
  public name: string;
  public providerTypeFormData: any;
  public updatedBy?: number;
  constructor (obj: IUpdateProviderLinkRequestBody)
  {
    this.disabled = obj.disabled;
    this.excludedCapabilities = obj.excludedCapabilities;
    this.id = obj.id;
    this.name = obj.name;
    this.providerTypeFormData = obj.providerTypeFormData;
    this.updatedBy = obj.updatedBy;
  }
}
export interface IUpdateProviderLinkRequestBody
{
  disabled: boolean;
  excludedCapabilities: string[];
  id: number;
  name: string;
  providerTypeFormData: any;
  updatedBy?: number;
}
export class UpdateRecommendedApprovalsRequestBody implements IUpdateRecommendedApprovalsRequestBody
{
  public approvals: IUpdateRecommendedApprovalPayload[];
  constructor (obj: IUpdateRecommendedApprovalsRequestBody)
  {
    this.approvals = obj.approvals;
  }
}
export interface IUpdateRecommendedApprovalsRequestBody
{
  approvals: IUpdateRecommendedApprovalPayload[];
}
export class UpdateScheduleRequest implements IUpdateScheduleRequest
{
  public allowAccessToMSPResources: boolean;
  public allowAccessToParentTenant: boolean;
  public applyWindowsUpdates: boolean;
  public autoConsentToReboots: boolean;
  public disabled: boolean;
  public id: number;
  public offlineBehavior: ComputerOfflineMaintenanceSessionBehavior;
  public promptTimeoutAction: PromptTimeoutAction;
  public promptTimeoutMinutes: number;
  public propagateToChildTenants: boolean;
  public rebootPreference: RebootPreference;
  public scheduleExecutionAfterActiveHours: boolean;
  public sendDetectionEmail: boolean;
  public sendDetectionEmailWhenAllActionsAreCompliant: boolean;
  public sendFollowUpEmail: boolean;
  public sendFollowUpOnlyIfActionNeeded: boolean;
  public showMaintenanceActions: boolean;
  public showPostponeButton: boolean;
  public showRunNowButton: boolean;
  public suppressRebootsDuringBusinessHours: boolean;
  public targetCategory: TargetCategory;
  public targetGroupFilter: TargetGroupFilter;
  public targetType: TargetType;
  public useComputersTimezoneForExecution: boolean;
  public customCronExpression?: string;
  public day?: number;
  public maintenanceIdentifier?: string;
  public maintenanceTime?: string;
  public maintenanceType?: MaintenanceType;
  public providerClientGroupType?: string;
  public providerDeviceGroupType?: string;
  public providerLinkId?: number;
  public target?: string;
  public tenantId?: number;
  public time?: string;
  public timeZoneInfoId?: string;
  constructor (obj: IUpdateScheduleRequest)
  {
    this.allowAccessToMSPResources = obj.allowAccessToMSPResources;
    this.allowAccessToParentTenant = obj.allowAccessToParentTenant;
    this.applyWindowsUpdates = obj.applyWindowsUpdates;
    this.autoConsentToReboots = obj.autoConsentToReboots;
    this.disabled = obj.disabled;
    this.id = obj.id;
    this.offlineBehavior = obj.offlineBehavior;
    this.promptTimeoutAction = obj.promptTimeoutAction;
    this.promptTimeoutMinutes = obj.promptTimeoutMinutes;
    this.propagateToChildTenants = obj.propagateToChildTenants;
    this.rebootPreference = obj.rebootPreference;
    this.scheduleExecutionAfterActiveHours = obj.scheduleExecutionAfterActiveHours;
    this.sendDetectionEmail = obj.sendDetectionEmail;
    this.sendDetectionEmailWhenAllActionsAreCompliant = obj.sendDetectionEmailWhenAllActionsAreCompliant;
    this.sendFollowUpEmail = obj.sendFollowUpEmail;
    this.sendFollowUpOnlyIfActionNeeded = obj.sendFollowUpOnlyIfActionNeeded;
    this.showMaintenanceActions = obj.showMaintenanceActions;
    this.showPostponeButton = obj.showPostponeButton;
    this.showRunNowButton = obj.showRunNowButton;
    this.suppressRebootsDuringBusinessHours = obj.suppressRebootsDuringBusinessHours;
    this.targetCategory = obj.targetCategory;
    this.targetGroupFilter = obj.targetGroupFilter;
    this.targetType = obj.targetType;
    this.useComputersTimezoneForExecution = obj.useComputersTimezoneForExecution;
    this.customCronExpression = obj.customCronExpression;
    this.day = obj.day;
    this.maintenanceIdentifier = obj.maintenanceIdentifier;
    this.maintenanceTime = obj.maintenanceTime;
    this.maintenanceType = obj.maintenanceType;
    this.providerClientGroupType = obj.providerClientGroupType;
    this.providerDeviceGroupType = obj.providerDeviceGroupType;
    this.providerLinkId = obj.providerLinkId;
    this.target = obj.target;
    this.tenantId = obj.tenantId;
    this.time = obj.time;
    this.timeZoneInfoId = obj.timeZoneInfoId;
  }
}
export interface IUpdateScheduleRequest
{
  allowAccessToMSPResources: boolean;
  allowAccessToParentTenant: boolean;
  applyWindowsUpdates: boolean;
  autoConsentToReboots: boolean;
  disabled: boolean;
  id: number;
  offlineBehavior: ComputerOfflineMaintenanceSessionBehavior;
  promptTimeoutAction: PromptTimeoutAction;
  promptTimeoutMinutes: number;
  propagateToChildTenants: boolean;
  rebootPreference: RebootPreference;
  scheduleExecutionAfterActiveHours: boolean;
  sendDetectionEmail: boolean;
  sendDetectionEmailWhenAllActionsAreCompliant: boolean;
  sendFollowUpEmail: boolean;
  sendFollowUpOnlyIfActionNeeded: boolean;
  showMaintenanceActions: boolean;
  showPostponeButton: boolean;
  showRunNowButton: boolean;
  suppressRebootsDuringBusinessHours: boolean;
  targetCategory: TargetCategory;
  targetGroupFilter: TargetGroupFilter;
  targetType: TargetType;
  useComputersTimezoneForExecution: boolean;
  customCronExpression?: string;
  day?: number;
  maintenanceIdentifier?: string;
  maintenanceTime?: string;
  maintenanceType?: MaintenanceType;
  providerClientGroupType?: string;
  providerDeviceGroupType?: string;
  providerLinkId?: number;
  target?: string;
  tenantId?: number;
  time?: string;
  timeZoneInfoId?: string;
}
export class ValidateParamBlockParametersFromScriptRequest implements IValidateParamBlockParametersFromScriptRequest
{
  public databaseType: DatabaseType;
  public forceRebind: boolean;
  public script: string;
  public validateRequiresOverrideForOnboarding: boolean;
  public computerId?: number;
  public maintenanceSessionId?: number;
  public parameterValues?: { [key:string]: IParameterValue };
  public tenantId?: number;
  public terminalId?: string;
  constructor (obj: IValidateParamBlockParametersFromScriptRequest)
  {
    this.databaseType = obj.databaseType;
    this.forceRebind = obj.forceRebind;
    this.script = obj.script;
    this.validateRequiresOverrideForOnboarding = obj.validateRequiresOverrideForOnboarding;
    this.computerId = obj.computerId;
    this.maintenanceSessionId = obj.maintenanceSessionId;
    this.parameterValues = obj.parameterValues;
    this.tenantId = obj.tenantId;
    this.terminalId = obj.terminalId;
  }
}
export interface IValidateParamBlockParametersFromScriptRequest
{
  databaseType: DatabaseType;
  forceRebind: boolean;
  script: string;
  validateRequiresOverrideForOnboarding: boolean;
  computerId?: number;
  maintenanceSessionId?: number;
  parameterValues?: { [key:string]: IParameterValue };
  tenantId?: number;
  terminalId?: string;
}
export class ValidateParamBlockParametersFromTaskRequest implements IValidateParamBlockParametersFromTaskRequest
{
  public databaseType: DatabaseType;
  public forceRebind: boolean;
  public maintenanceTaskId: number;
  public computerId?: number;
  public deploymentDatabaseType?: DatabaseType;
  public deploymentId?: number;
  public maintenanceSessionId?: number;
  public parameterValues?: { [key:string]: IParameterValue };
  public personId?: number;
  public tenantId?: number;
  public terminalId?: string;
  constructor (obj: IValidateParamBlockParametersFromTaskRequest)
  {
    this.databaseType = obj.databaseType;
    this.forceRebind = obj.forceRebind;
    this.maintenanceTaskId = obj.maintenanceTaskId;
    this.computerId = obj.computerId;
    this.deploymentDatabaseType = obj.deploymentDatabaseType;
    this.deploymentId = obj.deploymentId;
    this.maintenanceSessionId = obj.maintenanceSessionId;
    this.parameterValues = obj.parameterValues;
    this.personId = obj.personId;
    this.tenantId = obj.tenantId;
    this.terminalId = obj.terminalId;
  }
}
export interface IValidateParamBlockParametersFromTaskRequest
{
  databaseType: DatabaseType;
  forceRebind: boolean;
  maintenanceTaskId: number;
  computerId?: number;
  deploymentDatabaseType?: DatabaseType;
  deploymentId?: number;
  maintenanceSessionId?: number;
  parameterValues?: { [key:string]: IParameterValue };
  personId?: number;
  tenantId?: number;
  terminalId?: string;
}
export class VerifyProviderLinkRequestBody implements IVerifyProviderLinkRequestBody
{
  public id: number;
  public name: string;
  public ownerTenantId: number;
  public providerTypeId: string;
  public providerTypeFormData?: any;
  constructor (obj: IVerifyProviderLinkRequestBody)
  {
    this.id = obj.id;
    this.name = obj.name;
    this.ownerTenantId = obj.ownerTenantId;
    this.providerTypeId = obj.providerTypeId;
    this.providerTypeFormData = obj.providerTypeFormData;
  }
}
export interface IVerifyProviderLinkRequestBody
{
  id: number;
  name: string;
  ownerTenantId: number;
  providerTypeId: string;
  providerTypeFormData?: any;
}
export class CreateGlobalScriptRequestBody implements ICreateGlobalScriptRequestBody
{
  public action: string;
  public name: string;
  public outputType: ScriptOutputType;
  public scriptCategory: ScriptCategory;
  public scriptExecutionContext: ScriptExecutionContext;
  public scriptLanguage: ScriptLanguage;
  public scriptType: DatabaseType;
  public timeout?: number;
  constructor (obj: ICreateGlobalScriptRequestBody)
  {
    this.action = obj.action;
    this.name = obj.name;
    this.outputType = obj.outputType;
    this.scriptCategory = obj.scriptCategory;
    this.scriptExecutionContext = obj.scriptExecutionContext;
    this.scriptLanguage = obj.scriptLanguage;
    this.scriptType = obj.scriptType;
    this.timeout = obj.timeout;
  }
}
export interface ICreateGlobalScriptRequestBody
{
  action: string;
  name: string;
  outputType: ScriptOutputType;
  scriptCategory: ScriptCategory;
  scriptExecutionContext: ScriptExecutionContext;
  scriptLanguage: ScriptLanguage;
  scriptType: DatabaseType;
  timeout?: number;
}
export class CreateLocalScriptRequestBody implements ICreateLocalScriptRequestBody
{
  public action: string;
  public name: string;
  public outputType: ScriptOutputType;
  public scriptCategory: ScriptCategory;
  public scriptExecutionContext: ScriptExecutionContext;
  public scriptLanguage: ScriptLanguage;
  public scriptType: DatabaseType;
  public tenants: ITenantScript[];
  public timeout?: number;
  constructor (obj: ICreateLocalScriptRequestBody)
  {
    this.action = obj.action;
    this.name = obj.name;
    this.outputType = obj.outputType;
    this.scriptCategory = obj.scriptCategory;
    this.scriptExecutionContext = obj.scriptExecutionContext;
    this.scriptLanguage = obj.scriptLanguage;
    this.scriptType = obj.scriptType;
    this.tenants = obj.tenants;
    this.timeout = obj.timeout;
  }
}
export interface ICreateLocalScriptRequestBody
{
  action: string;
  name: string;
  outputType: ScriptOutputType;
  scriptCategory: ScriptCategory;
  scriptExecutionContext: ScriptExecutionContext;
  scriptLanguage: ScriptLanguage;
  scriptType: DatabaseType;
  tenants: ITenantScript[];
  timeout?: number;
}
export class DuplicateScriptRequestBody implements IDuplicateScriptRequestBody
{
  public id: number;
  public scriptType: DatabaseType;
  constructor (obj: IDuplicateScriptRequestBody)
  {
    this.id = obj.id;
    this.scriptType = obj.scriptType;
  }
}
export interface IDuplicateScriptRequestBody
{
  id: number;
  scriptType: DatabaseType;
}
export class ScriptSyntaxCheckRequestBody implements IScriptSyntaxCheckRequestBody
{
  public script: string;
  public scriptLanguage: ScriptLanguage;
  constructor (obj: IScriptSyntaxCheckRequestBody)
  {
    this.script = obj.script;
    this.scriptLanguage = obj.scriptLanguage;
  }
}
export interface IScriptSyntaxCheckRequestBody
{
  script: string;
  scriptLanguage: ScriptLanguage;
}
export class SetPreflightScriptEnablementRequest implements ISetPreflightScriptEnablementRequest
{
  public databaseType: DatabaseType;
  public isEnabled: boolean;
  public scriptId: number;
  constructor (obj: ISetPreflightScriptEnablementRequest)
  {
    this.databaseType = obj.databaseType;
    this.isEnabled = obj.isEnabled;
    this.scriptId = obj.scriptId;
  }
}
export interface ISetPreflightScriptEnablementRequest
{
  databaseType: DatabaseType;
  isEnabled: boolean;
  scriptId: number;
}
export class UpdateGlobalScriptRequestBody implements IUpdateGlobalScriptRequestBody
{
  public action: string;
  public name: string;
  public outputType: ScriptOutputType;
  public scriptCategory: ScriptCategory;
  public scriptExecutionContext: ScriptExecutionContext;
  public scriptLanguage: ScriptLanguage;
  public scriptType: DatabaseType;
  public timeout?: number;
  constructor (obj: IUpdateGlobalScriptRequestBody)
  {
    this.action = obj.action;
    this.name = obj.name;
    this.outputType = obj.outputType;
    this.scriptCategory = obj.scriptCategory;
    this.scriptExecutionContext = obj.scriptExecutionContext;
    this.scriptLanguage = obj.scriptLanguage;
    this.scriptType = obj.scriptType;
    this.timeout = obj.timeout;
  }
}
export interface IUpdateGlobalScriptRequestBody
{
  action: string;
  name: string;
  outputType: ScriptOutputType;
  scriptCategory: ScriptCategory;
  scriptExecutionContext: ScriptExecutionContext;
  scriptLanguage: ScriptLanguage;
  scriptType: DatabaseType;
  timeout?: number;
}
export class UpdateLocalScriptRequestBody implements IUpdateLocalScriptRequestBody
{
  public action: string;
  public name: string;
  public outputType: ScriptOutputType;
  public scriptCategory: ScriptCategory;
  public scriptExecutionContext: ScriptExecutionContext;
  public scriptLanguage: ScriptLanguage;
  public scriptType: DatabaseType;
  public tenants: ITenantScript[];
  public scriptCacheName?: string;
  public timeout?: number;
  constructor (obj: IUpdateLocalScriptRequestBody)
  {
    this.action = obj.action;
    this.name = obj.name;
    this.outputType = obj.outputType;
    this.scriptCategory = obj.scriptCategory;
    this.scriptExecutionContext = obj.scriptExecutionContext;
    this.scriptLanguage = obj.scriptLanguage;
    this.scriptType = obj.scriptType;
    this.tenants = obj.tenants;
    this.scriptCacheName = obj.scriptCacheName;
    this.timeout = obj.timeout;
  }
}
export interface IUpdateLocalScriptRequestBody
{
  action: string;
  name: string;
  outputType: ScriptOutputType;
  scriptCategory: ScriptCategory;
  scriptExecutionContext: ScriptExecutionContext;
  scriptLanguage: ScriptLanguage;
  scriptType: DatabaseType;
  tenants: ITenantScript[];
  scriptCacheName?: string;
  timeout?: number;
}
export class DuplicateMaintenanceTaskRequestbody implements IDuplicateMaintenanceTaskRequestbody
{
  public maintenanceTaskId: number;
  public maintenanceTaskType: DatabaseType;
  constructor (obj: IDuplicateMaintenanceTaskRequestbody)
  {
    this.maintenanceTaskId = obj.maintenanceTaskId;
    this.maintenanceTaskType = obj.maintenanceTaskType;
  }
}
export interface IDuplicateMaintenanceTaskRequestbody
{
  maintenanceTaskId: number;
  maintenanceTaskType: DatabaseType;
}
export class MaintenanceTaskReferenceCountRequestBody implements IMaintenanceTaskReferenceCountRequestBody
{
  public maintenanceTaskId: number;
  public maintenanceTaskType: DatabaseType;
  constructor (obj: IMaintenanceTaskReferenceCountRequestBody)
  {
    this.maintenanceTaskId = obj.maintenanceTaskId;
    this.maintenanceTaskType = obj.maintenanceTaskType;
  }
}
export interface IMaintenanceTaskReferenceCountRequestBody
{
  maintenanceTaskId: number;
  maintenanceTaskType: DatabaseType;
}
