//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

export * from './requests';
export * from './responses';
export * from './routes';
export * from './enums';
export * from './interfaces';
export * from './signalr';

export abstract class MaxLengthConstants
{
  public static EmailAddress: number = 254;
  public static Url: number = 2083;
  public static SmallText: number = 150;
  public static MediumText: number = 1000;
  public static LargeText: number = 5000;
  public static SemanticVersion: number = 64;
  public static Guid: number = 36;
  public static HexColor: number = 7;
  public static BlobName: number = 1024;
}
export abstract class Constants
{
  public static ImmyProviderTypeIdGuid: string;
  public static CwClientId: string = `ce73126e-0d5a-4c24-beb5-87d563d6c499`;
  public static CwAutomateProviderId: string = `37c21337-9eb0-421f-a254-1dce46e5667a`;
  public static CwManageProviderTypeId: string = `0ad20371-100e-412a-a002-ef081ed39703`;
  public static CwControlProviderTypeId: string = `bb06ed6e-8c9e-4471-8ac0-06ba64d885ed`;
  public static ImmyProviderTypeId: string = `FCDE1B0B-38A2-41B3-86D6-E8BD0AD52171`;
  public static NCentralProviderTypeId: string = `0b76d22e-ff17-4edd-94d1-d2825d787e32`;
  public static HaloPsaProviderTypeId: string = `c4fc3e84-3a67-4b2f-a0f2-a5dad48ec39c`;
}
export abstract class InventoryKeys
{
  public static SystemRequiredInventoryKeys: string[];
  public static WindowsSystemInfo: string = `WindowsSystemInfo`;
  public static WindowsNetworkAdaptersInventoryKey: string = `NetworkAdapters`;
  public static ExternalIpInventoryKey: string = `ExternalIp`;
  public static InternalIpInventoryKey: string = `InternalIp`;
  public static ExternalHostnamesInventoryKey: string = `ExternalHostnames`;
  public static WindowsPhysicalDisksInventoryKey: string = `PhysicalDisks`;
  public static WindowsLogicalDisksInventoryKey: string = `LogicalDisks`;
  public static WindowsPartitionsInventoryKey: string = `Partitions`;
  public static WindowsAntivirusInventoryKey: string = `Antivirus`;
  public static WindowsPhysicalMemoryInventoryKey: string = `PhysicalMemory`;
  public static WindowsProcessesInventoryKey: string = `Processes`;
  public static WindowsRebootPendingInventoryKey: string = `RebootPending`;
  public static WindowsSoftwareInventoryKey: string = `Software`;
  public static WindowsLoggedOnUserKey: string = `LoggedOnUser`;
  public static WindowsLoggedOnUserSidKey: string = `LoggedOnUserSID`;
  public static InventoryTaskMetaKey: string = `__InventoryTasks`;
  public static OutputStreamKey: string = `Output`;
  public static ConsoleTextKey: string = `ConsoleText`;
  public static WindowsSystemInfoArchitectureKey: string = `Architecture`;
  public static WindowsSystemInfoProcessorArchitectureKey: string = `ProcessorArchitecture`;
  public static WindowsSystemInfoTimeZoneKey: string = `TimeZone`;
  public static WindowsSystemInfoActiveHoursEnd: string = `ActiveHoursEnd`;
}
export abstract class ComputerInventoryConstants
{
  public static PortableChassisTypes: number[];
  public static WindowsDomainControllerRoles: number[];
  public static PrimaryDomainControllerRole: number = 5;
  public static SecondaryDomainControllerRole: number = 4;
}
