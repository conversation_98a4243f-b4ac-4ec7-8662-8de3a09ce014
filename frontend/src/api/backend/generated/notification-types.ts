//     This code was generated by a Reinforced.Typings tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.

import { INotification } from './interfaces';
import { IIntegrationUnhealthyNotificationInput } from './interfaces';
import { IAccessRequestedNotificationInput } from './interfaces';
import { ILargeScriptOutputNotificationInput } from './interfaces';
import { IUnacknowledgedDeploymentNotificationInput } from './interfaces';
import { IUpdateInstanceNotificationInput } from './interfaces';
import { IProviderAgentsRequiringManualDecisionNotificationInput } from './interfaces';
import { IAzureTenantProblemsNotificationInput } from './interfaces';
import { IAzureCustomerPreconsentFailedNotificationInput } from './interfaces';
import { IAzureMultiCustomerPreconsentFailedNotificationInput } from './interfaces';
import { IChangeRequestCreatedOrUpdatedNotificationInput } from './interfaces';
import { IChangeRequestAcknowledgedNotificationInput } from './interfaces';
import { IIntegrationRecommendationNotificationInput } from './interfaces';
import { IFeatureUsageExceededNotificationInput } from './interfaces';
import { ITenantDeletionNotificationInput } from './interfaces';
import { IScheduleFailedNotificationInput } from './interfaces';

export enum NotificationType {
  ProviderHealth = 1,
  AccessRequested = 2,
  LargeScriptOutput = 3,
  UnacknowledgedDeployments = 4,
  UpdateInstance = 5,
  ProviderAgentsRequiringManualDecision = 6,
  AzureTenantProblems = 7,
  AzureCustomerPreconsentFailed = 8,
  AzureMultiCustomerPreconsentFailed = 9,
  ChangeRequestCreatedOrUpdated = 10,
  ChangeRequestAcknowledged = 11,
  ProviderRecommendation = 12,
  FeatureUsageExceeded = 13,
  TenantDeletion = 14,
  ScheduleFailed = 15
}

export type NotificationInputTypes = 
  | { type: NotificationType.ProviderHealth, inputData: IIntegrationUnhealthyNotificationInput }
  | { type: NotificationType.AccessRequested, inputData: IAccessRequestedNotificationInput }
  | { type: NotificationType.LargeScriptOutput, inputData: ILargeScriptOutputNotificationInput }
  | { type: NotificationType.UnacknowledgedDeployments, inputData: IUnacknowledgedDeploymentNotificationInput }
  | { type: NotificationType.UpdateInstance, inputData: IUpdateInstanceNotificationInput }
  | { type: NotificationType.ProviderAgentsRequiringManualDecision, inputData: IProviderAgentsRequiringManualDecisionNotificationInput }
  | { type: NotificationType.AzureTenantProblems, inputData: IAzureTenantProblemsNotificationInput }
  | { type: NotificationType.AzureCustomerPreconsentFailed, inputData: IAzureCustomerPreconsentFailedNotificationInput }
  | { type: NotificationType.AzureMultiCustomerPreconsentFailed, inputData: IAzureMultiCustomerPreconsentFailedNotificationInput }
  | { type: NotificationType.ChangeRequestCreatedOrUpdated, inputData: IChangeRequestCreatedOrUpdatedNotificationInput }
  | { type: NotificationType.ChangeRequestAcknowledged, inputData: IChangeRequestAcknowledgedNotificationInput }
  | { type: NotificationType.ProviderRecommendation, inputData: IIntegrationRecommendationNotificationInput }
  | { type: NotificationType.FeatureUsageExceeded, inputData: IFeatureUsageExceededNotificationInput }
  | { type: NotificationType.TenantDeletion, inputData: ITenantDeletionNotificationInput }
  | { type: NotificationType.ScheduleFailed, inputData: IScheduleFailedNotificationInput };

export type NotificationTyped = Omit<INotification, "type" | "inputData"> & NotificationInputTypes;
