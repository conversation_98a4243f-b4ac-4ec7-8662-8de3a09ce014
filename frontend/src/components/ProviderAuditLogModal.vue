<template>
  <ImmyModal id="provider-audit-log-modal" v-model="show" size="lg" ok-title="Ok" title="Audit Log">
    <div class="audit-log-details">
      <ProviderAuditLogModalLineItem>
        <template #label>
          Time UTC
        </template>
        <template #value>
          {{ props.log.timeUtc }}
        </template>
      </ProviderAuditLogModalLineItem>
      <ProviderAuditLogModalLineItem>
        <template #label>
          Method
        </template>
        <template #value>
          {{ props.log.methodName }}
        </template>
      </ProviderAuditLogModalLineItem>
      <ProviderAuditLogModalLineItem>
        <template #label>
          Log ID
        </template>
        <template #value>
          {{ props.log.id }}
        </template>
      </ProviderAuditLogModalLineItem>
      <ProviderAuditLogModalLineItem v-if="props.log.correlationId">
        <template #label>
          Correlation ID
        </template>
        <template #value>
          {{ props.log.correlationId }}
        </template>
      </ProviderAuditLogModalLineItem>
      <ProviderAuditLogModalLineItem>
        <template #label>
          Parameters
        </template>
        <template #value>
          <pre>{{ props.log.input }}</pre>
        </template>
      </ProviderAuditLogModalLineItem>
      <ProviderAuditLogModalLineItem v-if="props.log.errorMessage">
        <template #label>
          Error
        </template>
        <template #value>
          <span class="text-danger">{{ props.log.errorMessage }}</span>
        </template>
      </ProviderAuditLogModalLineItem>
      <ProviderAuditLogModalLineItem>
        <template #label>
          Console Output
        </template>
        <template #value>
          <div v-if="consoleText" class="console-text-wrapper">
            <XtermVue
              :ref="setXtermRef"
              class="xterm-wrapper"
              :font-size="12"
              background-color="#0e1010"
              font-family="Roboto Mono"
              @initialized="onXtermInitialized"
            />
          </div>
        </template>
      </ProviderAuditLogModalLineItem>
    </div>
  </ImmyModal>
</template>

<script setup lang="ts">
import { computed, Ref, ref } from "vue";
import { IProviderAuditLog } from "@/api/backend/generated/interfaces";
import XtermVue from "@/components/XtermVue.vue";

const props = defineProps<{
  log: IProviderAuditLog;
}>();

const show = ref(true);

const xtermRef: Ref<XtermVue | null> = ref(null);

function setXtermRef(ref: any) {
  xtermRef.value = ref;
}

function onXtermInitialized() {
  xtermRef.value?.write(props.log.output);
}

const consoleText = computed(() => {
  return props.log.output;
});
</script>

<style scoped lang="scss">
.audit-log-details {
  max-height: 50vh;
  @extend .immy-scrollbar;
}

#provider-audit-log-modal___BV_modal_outer_ {
  z-index: 1052 !important;
}
.xterm-wrapper {
  border: 1px solid var(--divider);
  margin-top: 0.25rem;
  padding-left: 0.5rem;
  padding-top: 0.25rem;
}

.console-text-wrapper {
  padding: 0 1rem 0 0;
}

.audit-log-modal-line-item-value pre {
  color: var(--text-primary);
}
</style>
