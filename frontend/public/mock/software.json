[{"softwareID": 1, "name": "Foxit PhantomPDF Business", "licensed": true, "installOrder": 12, "iV_Item_RecID": 1046, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Foxit PhantomPDF*", "autoUpdate": true, "autoUpdateActionTypeID": 2, "autoUpdateAction": "Import-Module Carbon\\nImport-Module LabTech -Force\\nConnect-LTDatabase\\n\\n$baseurl = \\\"http://cdn01.foxitsoftware.com/pub/foxit/phantomPDF/desktop/win/8.x/\\\"\\n$Content = Invoke-WebRequest $baseurl\\n$MinorVersion = $Content.Links.innerText | ?{$_ -match \\\"[0-9]\\\\.[0-9]*\\\"} | sort -desc | select -first 1\\n$ReleaseUrl = ($baseurl + $MinorVersion + \\\"/en_us/\\\")\\n$MinorVersions = Invoke-WebRequest $ReleaseUrl\\n$LatestFileName = $MinorVersions.Links.innerText | ?{$_ -like \\\"*Setup.msi\\\"} | select -last 1\\n$LatestImmyFileName = gci \\\"\\\\\\\\IN-LT\\\\LTShare\\\\Transfer\\\\ManagedSoftwareInstallers\\\\FoxitPhantomPDFBusiness-*\\\\*.msi\\\" -Recurse | select -ExpandProperty Name | sort | select -last 1\\n\\nif($LatestFileName -ne $LatestImmyFileName)\\n{\\n  $DownloadPath = $ReleaseUrl + $LatestFileName\\n  $Destination = (\\\"C:\\\\windows\\\\temp\\\\\\\" + $LatestFileName)\\n  (\\\"Downloading\\\" + $DownloadPath + \\\" to \\\" + $Destination)\\n  wget $DownloadPath -OutFile $Destination\\n  #$Destination = \\\"D:\\\\LTShare\\\\Transfer\\\\ManagedSoftwareInstallers\\\\FoxitPhantomPDFBusiness-8.3.1.21155\\\\FoxitPhantomPDF831_enu_Setup.msi\\\"\\n  $Version = get-msi $Destination | Select -ExpandProperty ProductVersion\\n  if($Version -ne $null)\\n  {\\n\\t  $RelativeCacheSourcePath = \\\"FoxitPhantomPDFBusiness-$Version\\\"\\n\\t  $TargetDirectory = \\\"\\\\\\\\IN-LT\\\\LTShare\\\\Transfer\\\\ManagedSoftwareInstallers\\\\$RelativeCacheSourcePath\\\"\\n\\t  mkdir $TargetDirectory -Force\\n\\t  Copy-Item -Path $Destination -Destination $TargetDirectory\\n\\t  $ZipPackagePath = ((Split-Path -Path $TargetDirectory -Parent) + \\\"\\\\$RelativeCacheSourcePath.zip\\\")\\n\\t  Compress-Archive $TargetDirectory -DestinationPath $ZipPackagePath -Force\\n\\t  $Hash = Get-FileHash $ZipPackagePath -Algorithm MD5\\n\\t  if($Hash)\\n\\t  {\\n\\t\\t  $SoftwareVersion = Get-LabtechData \\\"select * from plugin_indeploy_softwareversions where SoftwareID=1 order by sortorder desc limit 1\\\"\\n\\t\\t  $SoftwareVersion.SoftwareVersionID = $null\\n\\t\\t  $SoftwareVersion.DisplayVersion = $Version\\n\\t\\t  $SoftwareVersion.InstallerFile = $LatestFileName\\n\\t\\t  $SoftwareVersion.RelativeCacheSourcePath = $RelativeCacheSourcePath\\n\\t\\t  $SoftwareVersion.SortOrder++;\\n\\t\\t  $SoftwareVersion.PackageHash = $Hash.Hash\\n\\t\\t  $SoftwareVersionID = Import-PSObjectIntoLabtech -TableName plugin_indeploy_softwareversions -Object $SoftwareVersion | select -ExpandProperty LAST_INSERT_ID\\n\\t\\t  if($SoftwareVersionID -ne $null -and $SoftwareVersionID -gt 0)\\n\\t\\t  {\\n\\t\\t\\t  Assert-LabtechQuery \\\"update plugin_indeploy_targetassignments set SoftwareVersionID=$SoftwareVersionID where AutoUpdate=1 and SoftwareVersionID in (select SoftwareVersionID from plugin_indeploy_softwareversions where SoftwareID=1)\\\"\\n\\t\\t  }\\n\\t\\t  Remove-Item $Destination\\n\\t  }\\n  }\\n}", "softwareVersions": null}, {"softwareID": 2, "name": "Mimecast for Outlook", "licensed": false, "installOrder": 11, "iV_Item_RecID": 947, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Mimecast for Outlook*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 3, "name": "inDrop for Outlook", "licensed": false, "installOrder": 10, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "inDrop for Outlook*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 4, "name": "Umbrella Roaming Client", "licensed": false, "installOrder": 8, "iV_Item_RecID": 833, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Umbrella Roaming Clien*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 5, "name": "Microsoft Office 365 Pro Plus", "licensed": true, "installOrder": 6, "iV_Item_RecID": 870, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "Get-Package -ErrorAction SilentlyContinue | ?{$_.Metadata[\\'UninstallString\\'] -like \\\"*O365ProPlusRetail*\\\"} | sort @{E={[System.Version]$_.Version}} -Descending | select -First 1 -ExpandProperty Version ", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 6, "name": "Immense Direct", "licensed": false, "installOrder": 9, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": " $ProgramFilesName = \\\"Immense Direct Installer\\\"\\r\\n$paths = @(\\\"HKLM:\\\\software\\\\WOW6432Node\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\run\\\\\\\",\\\"HKLM:\\\\software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\run\\\\\\\")\\r\\n$paths | %{\\r\\n$path = $_\\r\\nif(Test-Path $path)\\r\\n{\\r\\ntry\\r\\n{\\r\\n$pathwithparams = Get-ItemPropertyValue -Path $path -Name \\\"deskDirectorPortalMachineInstaller\\\"\\r\\n$exepath = $pathwithparams.Split(\\\"--\\\")[0].Trim()\\r\\nif($exepath -like \\\"*$ProgramFilesName*\\\")\\r\\n{\\r\\nif($path -like \\\"*WOW6432Node*\\\")\\r\\n{\\r\\n$exepath = $exepath.Replace($env:programfiles, ${env:ProgramFiles(x86)})\\r\\n}\\r\\nif(Test-Path $exepath)\\r\\n{\\r\\n[System.Diagnostics.FileVersionInfo]::GetVersionInfo($exepath).FileVersion\\r\\n}\\r\\n}\\r\\n}\\r\\ncatch {}\\r\\n}\\r\\n} | select -first 1 \\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 8, "name": "FortiClient", "licensed": false, "installOrder": 5, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "FortiClien*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 9, "name": "Snagit", "licensed": true, "installOrder": 13, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "$Path = ${env:ProgramFiles} \\r\\n$Snagitexes = dir @(\\\"$Path\\\\TechSmith\\\\Snagit*\\\\Snagit32.exe\\\",\\\"${env:ProgramFiles(x86)}\\\\TechSmith\\\\Snagit*\\\\Snagit32.exe\\\") -ErrorAction SilentlyContinue | select -ExpandProperty FullName\\r\\n$Versions = $Snagitexes | % { get-item -Path $_ -ErrorAction SilentlyContinue | select -ExpandProperty VersionInfo | select -ExpandProperty FileVersion }\\r\\n$Versions | sort @{E={[System.Version]$_}} -Descending | Select -First 1\\r\\n \\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 10, "name": "Immense Package Manager", "licensed": false, "installOrder": 0, "iV_Item_RecID": 0, "hidden": true, "detectionActionTypeID": 2, "detectionAction": "@choco upgrade chocolatey --noop -r", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 11, "name": "Powershell 5.0", "licensed": false, "installOrder": 1, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "$psversiontable.psversion.ToString()", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 12, "name": "3CX Phone Client", "licensed": false, "installOrder": 14, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "3CXPhone for Windows", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 13, "name": "3CX TAPI Driver", "licensed": false, "installOrder": 15, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "3CX Plugin*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 14, "name": "DDB Ticket Portal", "licensed": false, "installOrder": 16, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "$ProgramFilesName = \\\"DDB Ticket Portal Installer\\\"\\r\\n $paths = @(\\\"HKLM:\\\\software\\\\WOW6432Node\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\run\\\\\\\",\\\"HKLM:\\\\software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\run\\\\\\\")\\r\\n  $paths | %{\\r\\n      $path = $_\\r\\n      if(Test-Path $path)\\r\\n      {\\r\\n          try\\r\\n          {\\r\\n              $pathwithparams = Get-ItemPropertyValue -Path $path -Name \\\"deskDirectorPortalMachineInstaller\\\" \\r\\n              $exepath = $pathwithparams.Split(\\\"--\\\")[0].Trim()\\r\\n              if($exepath -like \\\"*$ProgramFilesName*\\\")\\r\\n              {\\r\\n                  if($path -like \\\"*WOW6432Node*\\\")\\r\\n                  {\\r\\n                      $exepath = $exepath.Replace($env:programfiles, ${env:ProgramFiles(x86)})\\r\\n                  }\\r\\n                  if(Test-Path $exepath)\\r\\n                  {\\r\\n                      [System.Diagnostics.FileVersionInfo]::GetVersionInfo($exepath).FileVersion \\r\\n                  }\\r\\n              }\\r\\n          }\\r\\n          catch {}\\r\\n      }\\r\\n  } | select -first 1", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 15, "name": "<PERSON><PERSON>", "licensed": false, "installOrder": 18, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "$content = get-content \\\"c:\\\\needles\\\\needles.ini\\\"\\n$Version = ($content | ?{$_ -like \\\"Installed=*\\\"}).Split(\\\"=\\\")[1]\\n$Version \\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 16, "name": "Microsoft Office 365 Business", "licensed": true, "installOrder": 7, "iV_Item_RecID": 822, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "Get-Package * | ?{$_.Metadata[\\\"UninstallString\\\"] -like \\\"*O365BusinessRetail*\\\"} | sort @{E={[System.Version]$_.Version}} -Descending | select -First 1 -ExpandProperty Version", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 17, "name": "Visual C++ Redistributables", "licensed": false, "installOrder": 3, "iV_Item_RecID": 0, "hidden": true, "detectionActionTypeID": 2, "detectionAction": "$MajorVersions = @(8,9,10,11,12,14)\\r\\n$InstalledMajorVersions = Get-Package | ?{$_.Name -like \\\"*c++*redist*\\\"} | %{[version]$_.Version} | %{$_.Major} | Group | %{$_.Name}\\r\\nif($InstalledMajorVersions -eq $null)\\r\\n{\\r\\n    \\\"\\\"\\r\\n    return\\r\\n}\\r\\n$result = Compare-Object $MajorVersions $InstalledMajorVersions\\r\\nif($result -eq $null)\\r\\n{\\r\\n    \\\"1.0\\\"\\r\\n}\\r\\nelse\\r\\n{\\r\\n    \\\"\\\"\\r\\n}", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 18, "name": "ManicTime", "licensed": true, "installOrder": 19, "iV_Item_RecID": 0, "hidden": true, "detectionActionTypeID": 2, "detectionAction": "get-item \\\"${env:ProgramFiles(x86)}\\\\Manictime\\\\ManicTime.exe\\\" -ErrorAction SilentlyContinue | select -ExpandProperty VersionInfo | %{$_.FileVersion.Replace(\\\", \\\",\\\".\\\")}", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 19, "name": "Veeam Windows Agent", "licensed": true, "installOrder": 20, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Veeam Agent for Microsoft Windo*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 21, "name": "Sybase SQL Anywhere", "licensed": true, "installOrder": 17, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "Get-Package * | ?{$_.Meta.Attributes[\\\"ProductCode\\\"] -eq \\\"{1DFA77E6-91B2-4DCC-B8BE-98EA70705D39}\\\"} | sort @{E={[System.Version]$_.Version}} -Descending | select -First 1 -ExpandProperty Version", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 22, "name": "Thycotic Password Reset Server Client", "licensed": false, "installOrder": 22, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "Get-Package * | ?{$_.Meta.Attributes[\\\"ProductCode\\\"] -eq \\\"{568F8040-1EA6-4175-BF90-6F23E3E505F4}\\\"} | sort @{E={[System.Version]$_.Version}} -Descending | select -First 1 -ExpandProperty Version", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 23, "name": "Palo Alto GlobalProtect", "licensed": false, "installOrder": 23, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "GlobalProte*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 24, "name": "ISC Bomgar Jump Client", "licensed": false, "installOrder": 24, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Bomgar Jump Client*[support.iscgrp.com]*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 25, "name": "Microsoft Office Professional Plus 2016", "licensed": true, "installOrder": 25, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Microsoft Office Professional Plus 2016", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 26, "name": "Proofpoint Email Encryption Outlook Plugin", "licensed": false, "installOrder": 26, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Proofpoint Encryption Plug-in for Microsoft Outlook", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 27, "name": "Proofpoint Email Feedback Outlook Plugin", "licensed": false, "installOrder": 27, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "Get-Package * | ?{$_.Meta.Attributes[\\\"ProductCode\\\"] -eq \\\"{53CC8ADF-1BA1-41BE-88D5-531F8C70377B}\\\"} | sort @{E={[System.Version]$_.Version}} -Descending | select -First 1 -ExpandProperty Version", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 28, "name": "Proofpoint Secure Share Outlook Plugin", "licensed": false, "installOrder": 28, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "Get-Package * | ?{$_.Meta.Attributes[\\\"ProductCode\\\"] -eq \\\"{73B03BFA-C471-4611-A7DD-33227EBCE118}\\\"} | sort @{E={[System.Version]$_.Version}} -Descending | select -First 1 -ExpandProperty Version", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 29, "name": "McAfee Agent", "licensed": false, "installOrder": 30, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "@(\\\"$($env:ProgramFiles)\\\\McAfee\\\\Agent\\\\masvc.exe\\\",\\\"${env:ProgramFiles}\\\\McAfee\\\\Common Framework\\\\masvc.exe\\\",\\\"C:\\\\Program Files (x86)\\\\McAfee\\\\Common Framework\\\") | get-item -ErrorAction SilentlyContinue | select -ExpandProperty VersionInfo -ErrorAction SilentlyContinue| %{$_.FileVersion.Replace(\\\", \\\",\\\".\\\")} | sort @{E={[system.Version]$_}} | select -first 1\\r\\n \\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 30, "name": "McAfee Endpoint Security Adaptive Threat Protection", "licensed": false, "installOrder": 32, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "McAfee Endpoint Security Adaptive Threat Protection", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 31, "name": "McAfee Endpoint Security Platform", "licensed": false, "installOrder": 31, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "Get-Package * | ?{$_.Meta.Attributes[\\\"ProductCode\\\"] -eq \\\"{6D20F37F-05CB-401E-83A3-DEB93B29196E}\\\"} | sort @{E={[System.Version]$_.Version}} -Descending | select -First 1 -ExpandProperty Version", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 32, "name": "McAfee Endpoint Security Threat Prevention", "licensed": false, "installOrder": 33, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "Get-Package * | ?{$_.Meta.Attributes[\\\"ProductCode\\\"] -eq \\\"{4F574B83-3AE0-419F-8A3B-985C389334B4}\\\"} | sort @{E={[System.Version]$_.Version}} -Descending | select -First 1 -ExpandProperty Version", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 33, "name": "McAfee DLP Endpoint", "licensed": false, "installOrder": 34, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "McAfee DLP Endpoint*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 34, "name": "Orca", "licensed": false, "installOrder": 35, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "orca", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 35, "name": "Crowdstrike Windows Sensor", "licensed": true, "installOrder": 39, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "CrowdStrike Windows Sensor", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 36, "name": ".NET 4.5.1", "licensed": false, "installOrder": 4, "iV_Item_RecID": 0, "hidden": true, "detectionActionTypeID": 2, "detectionAction": "Get-ChildItem \\'HKLM:\\\\SOFTWARE\\\\Microsoft\\\\NET Framework Setup\\\\NDP\\' -recurse |\\r\\nGet-ItemProperty -name Version,Release -EA 0 |\\r\\nWhere { $_.PSChildName -match \\'^(?!S)\\\\p{L}\\'} | ?{$_.Pschildname -like \\\"Full\\\"} | Select -ExpandProperty Version\\r\\n \\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 37, "name": "McAfee File and Removable Media Protection", "licensed": false, "installOrder": 36, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "get-item \\\"$($env:ProgramFiles)\\\\McAfee\\\\Endpoint Encryption for Files and Folders\\\\MfeFfCore.exe\\\" -ErrorAction SilentlyContinue | select -ExpandProperty VersionInfo | %{$_.FileVersion.Replace(\\\", \\\",\\\".\\\")}", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 38, "name": "McAfee Drive Encryption Agent", "licensed": false, "installOrder": 37, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "McAfee Drive Encryption Agent", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 39, "name": "McAfee Drive Encryption", "licensed": false, "installOrder": 38, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "McAfee Drive Encry*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 40, "name": "Veeam Backup & Replication", "licensed": false, "installOrder": 40, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "$corePath = Get-ItemProperty -Path \\\"HKLM:\\\\Software\\\\Veeam\\\\Veeam Backup and Replication\\\\\\\" -Name \\\"CorePath\\\"\\r\\n$depDLLPath = Join-Path -Path $corePath.CorePath -ChildPath \\\"Packages\\\\VeeamDeploymentDll.dll\\\" -Resolve\\r\\n$file = Get-Item -Path $depDLLPath\\r\\n$version = $file.VersionInfo.ProductVersion\\r\\n$version", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 41, "name": "eClinicalWorks", "licensed": false, "installOrder": 41, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if((Test-Path \\\"C:\\\\Program Files (x86)\\\"))\\n{\\n    $FileName = \\\"C:\\\\Program Files (x86)\\\\eClinicalWorks\\\\eClinicalWorks.exe\\\"\\n}\\nelse\\n{\\n    $FileName = \\\"C:\\\\Program Files\\\\eClinicalWorks\\\\eClinicalWorks.exe\\\"\\n}\\n$Version = get-item $FileName -ErrorAction SilentlyContinue | select -ExpandProperty VersionInfo | %{([system.Version]$_.FileVersion.Replace(\\\", \\\",\\\".\\\")).ToString()}\\n\\n$Version\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 42, "name": "Bit Titan DeploymentPro", "licensed": false, "installOrder": 42, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if ($env:PROCESSOR_ARCHITECTURE -eq \\\"amd64\\\") \\r\\n{ $Path = ${env:ProgramFiles(x86)}}\\r\\nelse {$Path = ${env:ProgramFiles} }\\r\\nget-item \\\"$Path\\\\BitTitan\\\\DeviceManagementAgent\\\\BitTitanDMAService.exe\\\" -ErrorAction SilentlyContinue | select -ExpandProperty VersionInfo | %{$_.FileVersion.Replace(\\\", \\\",\\\".\\\")}", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 43, "name": "Microsoft Team", "licensed": false, "installOrder": 43, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Microsoft Teams", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 44, "name": "Quickbooks", "licensed": true, "installOrder": 44, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Quickbooks", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 45, "name": "FileMaker Pro 15", "licensed": true, "installOrder": 45, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "FileMaker Pro 15", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 46, "name": "Job Runner Extensions", "licensed": false, "installOrder": 46, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if($env:PROCESSOR_ARCHITECTURE -eq \\\"AMD64\\\")\\n{\\n    $BasePath = ${env:ProgramFiles(x86)}\\n}\\nelse\\n{\\n    $BasePath = $env:ProgramFiles\\n}\\n\\n$ExtensionsPath = $BasePath + \\\"\\\\FileMaker\\\\FileMaker Pro 15\\\\Extensions\\\\\\\"\\n$Extensions = @(\\\"360Works_Email.fmx\\\",\\\"FTPit_Pro_WIN.fmx\\\",\\\"360Works_Email.fmx\\\",\\\"Troi_File_Plugin.fmx\\\")\\nforeach($Extension in $Extensions){\\n    if((Test-Path ($ExtensionsPath + $Extension)) -eq $false)\\n    {\\n        return \\\"\\\"\\n    }\\n}\\nreturn \\\"*******\\\";", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 47, "name": "Windows 10 x64", "licensed": true, "installOrder": 51, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if((Get-WmiObject -Class Win32_ComputerSystem).SystemType -like \\\"*x64*\\\")\\r\\n{\\r\\n    [System.Environment]::OSVersion.Version.ToString()\\r\\n}\\r\\n \\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 48, "name": "Samanage", "licensed": false, "installOrder": 47, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Samanage Agent", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 49, "name": "IQMS", "licensed": true, "installOrder": 48, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "EnterpriseIQ Client", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 50, "name": "Speco Multiclient", "licensed": true, "installOrder": 49, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "SpecoTech Multi Tech", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 51, "name": "Electronic Service Control", "licensed": false, "installOrder": 50, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "Get-Item \\\"${env:ProgramFiles(x86)}\\\\dESCO\\\\ESC\\\\ESC.exe\\\" -ErrorAction SilentlyContinue | select -ExpandProperty VersionInfo | select -ExpandProperty ProductVersion", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 52, "name": "Windows 10 x86", "licensed": false, "installOrder": 52, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if((Get-WmiObject -Class Win32_ComputerSystem).SystemType -like \\\"*x86*\\\")\\r\\n{\\r\\n    [System.Environment]::OSVersion.Version.ToString()\\r\\n}\\r\\n \\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 53, "name": "PhishMe Reporter", "licensed": false, "installOrder": 53, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "PhishMe Reporter", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 54, "name": "2020 Software", "licensed": false, "installOrder": 54, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Design", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 55, "name": "Remote Desktop Manager", "licensed": false, "installOrder": 55, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Remote Desktop Manager", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 56, "name": "USSC Web Components", "licensed": false, "installOrder": 56, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "$ErrorActionPreference = \\'SilentlyContinue\\';\\r\\n[System.Diagnostics.FileVersionInfo]::GetVersionInfo(\\\"C:\\\\Program Files (x86)\\\\USSC Web Components\\\\npUSSCWebVideoPlugin.dll\\\").FileVersionRaw.toString();\\r\\n \\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 57, "name": "pVault Imaging System", "licensed": true, "installOrder": 57, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if([environment]::Is64BitOperatingSystem)\\r\\n{\\r\\n\\t$ProgramFiles32 = ${env:ProgramFiles(x86)}\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\t$ProgramFiles32 = $env:ProgramFiles\\r\\n}\\r\\n$ErrorActionPreference = \\'SilentlyContinue\\';\\r\\n[System.Diagnostics.FileVersionInfo]::GetVersionInfo(\\\"$ProgramFiles32\\\\Paperless Environments\\\\pVault Imaging System\\\\pVault\\\\pVault.exe\\\").FileVersionRaw.toString();", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 58, "name": "AirTame", "licensed": false, "installOrder": 58, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "AirTame", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 59, "name": "Quick Bid", "licensed": false, "installOrder": 59, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Quick Bid", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 60, "name": "Bluebeam Revu", "licensed": true, "installOrder": 62, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if($psversiontable.psVersion.major -eq 5)\\r\\n{\\r\\n    if(Get-Package \\\"Bluebeam Revu*\\\" -errorAction SilentlyContinue |Select Version)\\r\\n\\t{\\r\\n\\t\\tWrite-Output \\\"1.0\\\"\\r\\n\\t}\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $yo = gci \\\"HKLM:\\\\Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\*\\\\\\\",\\\"HKLM:\\\\Software\\\\WOW6432Node\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\*\\\\\\\" | Get-Item | Get-ItemProperty |Where-Object -FilterScript {$_.DisplayName -ne $null}|Select @{N=\\'Name\\'; E={$_.DisplayName}}, @{N=\\'Version\\'; E={$_.DisplayVersion}} | Where-Object -FilterScript {$_.Name -like \\\"Bluebeam Revu*\\\"}\\r\\n\\r\\nif($Yo.Version -ne $null)\\r\\n{\\r\\n\\t\\tWrite-Output \\\"1.0\\\"\\r\\n}\\r\\n\\r\\n}\\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 62, "name": "Dell SupportAssist", "licensed": false, "installOrder": 63, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if((get-package \\\"Dell SupportAssist*\\\" | measure).count -gt 0)\\r\\n{\\r\\n    \\\"*******\\\"\\r\\n}\\r\\n \\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 63, "name": "Sage 300 Construction and Real Estate Client", "licensed": false, "installOrder": 64, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Sage 300 Construction and Real Estate Client", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 64, "name": "On-Screen Takeoff", "licensed": false, "installOrder": 61, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "On-Screen Takeoff", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 65, "name": "Windows 10 Update Assistant", "licensed": false, "installOrder": 65, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Windows 10 Update Assistant", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 66, "name": "Cisco AnyConnect (Remove Only)", "licensed": false, "installOrder": 66, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "*AnyConnect*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 67, "name": "Microsoft ReportViewer 2010 Redistributable", "licensed": false, "installOrder": 67, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Microsoft ReportViewer 2010 Redistributable", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 68, "name": "Microsoft System CLR Types for SQL Server 2012", "licensed": false, "installOrder": 68, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Microsoft System CLR Types for SQL Server 2012", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 69, "name": "Microsoft System CLR Types for SQL Server 2014", "licensed": false, "installOrder": 69, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Microsoft System CLR Types for SQL Server 2014", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 70, "name": "Microsoft Report Viewer 2012 Runtime", "licensed": false, "installOrder": 70, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Microsoft Report Viewer 2012 Runtime", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 71, "name": "Microsoft Report Viewer 2015 Runtime", "licensed": false, "installOrder": 71, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Microsoft Report Viewer 2015 Runtime", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 72, "name": "Autodesk AutoCAD LT 2018", "licensed": true, "installOrder": 72, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "AutoCAD LT 2018 - English", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 73, "name": "Hamachi", "licensed": false, "installOrder": 73, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "*Hamachi*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 74, "name": "Autodesk AutoCAD LT 2019", "licensed": true, "installOrder": 74, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "AutoCAD LT 2019 - English", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 75, "name": "Autodesk AutoCAD 2018", "licensed": false, "installOrder": 75, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "AutoCAD 2018 - English", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 76, "name": "Sage 300 CRE Estimating SQL", "licensed": false, "installOrder": 76, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Sage Estimating*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 77, "name": "Autodesk AutoCAD 2019", "licensed": false, "installOrder": 77, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "AutoCAD 2019 - English", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 78, "name": "RJ Outlook CRM Plugin", "licensed": false, "installOrder": 78, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "CRM Outlook Add-In", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 79, "name": "Microsoft Visual Studio Code", "licensed": false, "installOrder": 79, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Microsoft Visual Studio Code", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 80, "name": "Autodesk AutoCAD Mechanical 2019", "licensed": false, "installOrder": 80, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Autocad Mechanical 2019 - English", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 81, "name": "Autodesk AutoCAD Mechanical 2018", "licensed": false, "installOrder": 81, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Autocad Mechanical 2018 - English", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 82, "name": "Any PDF to DWG", "licensed": false, "installOrder": 82, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Any PDF to DWG Converter 2010", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 83, "name": "Crosby Sling Calculator", "licensed": false, "installOrder": 83, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Crosby Sling Calculator*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 84, "name": "FloorRight Commercial 3D", "licensed": false, "installOrder": 84, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "FloorRight Commercial 3D*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 85, "name": "LICCON Work Planner", "licensed": false, "installOrder": 85, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if(Test-Path \\\"C:\\\\ProgramData\\\\LIEBHERR\\\\LIKAPLAN\\\\tab\\\\1025_81.DAT\\\")\\r\\n{\\r\\nWrite-Output \\\"6.6\\\"\\r\\n}\\r\\nelseif(test-path \\\"C:\\\\Program Files (x86)\\\\Likaplan\\\\starteplocal.vbs\\\")\\r\\n{\\r\\n    Write-Output \\\"6.0.0\\\"\\r\\n}\\r\\nelseif(test-path \\\"C:\\\\Program Files (x86)\\\\LIEBHERR\\\\Likaplan\\\\starteplocal.vbs\\\")\\r\\n{\\r\\n    Write-Output \\\"6.0.0\\\"\\r\\n}\\r\\n\\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 86, "name": "FloorRight Viewer 3D", "licensed": false, "installOrder": 86, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "FloorRight Viewer 3D*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 87, "name": "Vista Viewpoint", "licensed": false, "installOrder": 87, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Vista*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 88, "name": "Riverbed Steelhead Mobile Client", "licensed": false, "installOrder": 88, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Riverbed Steelhead Mobile", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 89, "name": "Mitel Connect", "licensed": false, "installOrder": 90, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Mitel C*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 90, "name": "Crane Manager 2014", "licensed": false, "installOrder": 95, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Crane Manager* \\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 91, "name": "Microsoft Online Services Sign-in Assistant", "licensed": false, "installOrder": 96, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Microsoft Online Services Sign-in Assistant", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 96, "name": "Microsoft BitLocker Encryption", "licensed": false, "installOrder": 97, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "MDOP MBAM", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 99, "name": "DOSBox", "licensed": false, "installOrder": 98, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if($env:PROCESSOR_ARCHITECTURE -like \\'AMD64\\')\\r\\n{\\r\\n    $ProgramFilesx86 = ${env:ProgramFiles(x86)}\\r\\n}\\r\\nelse\\r\\n{\\r\\n    $ProgramFilesx86 = $env:ProgramFiles\\r\\n}\\r\\n$exepath = gci \\\"$ProgramFilesx86\\\\DosBox*\\\\Dosbox.exe\\\" | select -first 1\\r\\n\\r\\nif($exepath -ne $null -and (Test-Path $exepath -ErrorAction SilentlyContinue))\\r\\n{\\r\\n    $Version = [System.Version]([System.Diagnostics.FileVersionInfo]::GetVersionInfo($exepath).FileVersion).ToString().Replace(\\\",\\\",\\\".\\\")\\r\\n    $Version.ToString()\\r\\n}", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 100, "name": "Event 1 Office Connector", "licensed": false, "installOrder": 99, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Event 1 Core Services", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 101, "name": "Event 1 Core Services", "licensed": false, "installOrder": 100, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 0, "detectionAction": null, "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 102, "name": "Event 1 Function Selector", "licensed": false, "installOrder": 101, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 0, "detectionAction": null, "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 103, "name": "Intel ProSet Wireless Enterprise Software", "licensed": false, "installOrder": 102, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if(Get-Package \\\"*PROSet*Wireless Enterprise Software\\\" -Erroraction SilentlyContinue)\\r\\n{\\r\\n\\tWrite-Output \\\"1.0.0\\\"\\r\\n}\\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 104, "name": "Intel ProSet Wireless WiFi Software", "licensed": false, "installOrder": 103, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "if(Get-Package \\\"*PROSet/Wireless WiFi Software\\\" -Erroraction SilentlyContinue)\\r\\n{\\r\\n\\tWrite-Output \\\"1.0.0\\\"\\r\\n}\\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 105, "name": "FaxFinder Client", "licensed": true, "installOrder": 104, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "FaxFinder*Client*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 106, "name": "EclipseSE IPro", "licensed": true, "installOrder": 105, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "EclipseSE*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 107, "name": "McAfee Agent(Uninstall Only)", "licensed": false, "installOrder": 106, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "McAfee Agent", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 108, "name": "TimeClick", "licensed": true, "installOrder": 107, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "TimeClick*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 109, "name": "VMware vCenter Converter Standalone Agent", "licensed": false, "installOrder": 108, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "VMware vCenter Converter Standalone Agent", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 110, "name": "UPNTest", "licensed": false, "installOrder": 109, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "AzureAD", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 111, "name": "Access Database Agent x86", "licensed": false, "installOrder": 89, "iV_Item_RecID": 0, "hidden": true, "detectionActionTypeID": 4, "detectionAction": "Microsoft Access database*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 112, "name": "Visual C++ Redistributable 2013", "licensed": false, "installOrder": 2, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "$InstalledMajorVersions = Get-Package \\\"*c++ 2013*redist*\\\" -ErrorAction SIlentlyContinue\\r\\nif($InstalledMajorVersions -eq $null)\\r\\n{\\r\\n    \\\"\\\"\\r\\n    return\\r\\n}\\r\\nif($InstalledMajorVersions -ne $null)\\r\\n{\\r\\n    \\\"1.0\\\"\\r\\n}\\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 113, "name": "Microsoft OneDrive", "licensed": false, "installOrder": 29, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 2, "detectionAction": "$User = gwmi win32_computersystem -Property Username\\r\\n$UserName = $User.UserName\\r\\n$UserSplit = $User.UserName.Split(\\u201c\\\\\\u201d)\\r\\n$OneDrive = \\u201c$env:SystemDrive\\\\users\\\\\\u201d + $UserSplit[1] +\\u201c\\\\appdata\\\\local\\\\microsoft\\\\onedrive\\\\OneDrive.exe\\u201d\\r\\nIf(Test-Path $OneDrive)\\r\\n{\\r\\n\\t$OneDriveFile = Get-Item $OneDrive\\r\\n\\tReturn (Get-Item $OneDrive).VersionInfo.FileVersion.ToString()\\r\\n}\\r\\nElse\\r\\n{\\r\\n    Return False;\\r\\n} \\r\\n", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 114, "name": "iSpring Suite 9", "licensed": true, "installOrder": 110, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "iSpring*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 115, "name": "Team<PERSON><PERSON>", "licensed": false, "installOrder": 111, "iV_Item_RecID": 0, "hidden": true, "detectionActionTypeID": 2, "detectionAction": "if(Get-Package \\\"Teamviewer*\\\")\\r\\n{\\r\\nWrite-Output \\\"1.0\\\";\\r\\n}", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 116, "name": "GoToMyPC Uninstall", "licensed": false, "installOrder": 112, "iV_Item_RecID": 0, "hidden": true, "detectionActionTypeID": 2, "detectionAction": "if(Get-Package \\\"GoToMyPC\\\")\\r\\n{\\r\\n\\tWrite-Output \\\"1.0\\\";\\r\\n}", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 117, "name": "LogMeIn Uninstall", "licensed": false, "installOrder": 113, "iV_Item_RecID": 0, "hidden": true, "detectionActionTypeID": 2, "detectionAction": "if(Get-Package \\\"LogMeIn*\\\")\\r\\n{\\r\\n\\tWrite-Output \\\"1.0\\\"\\r\\n}", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 118, "name": "<PERSON><PERSON><PERSON>", "licensed": false, "installOrder": 114, "iV_Item_RecID": 0, "hidden": true, "detectionActionTypeID": 2, "detectionAction": "if(Test-Path -Path \\\"C:\\\\ProgramData\\\\{4CEC2908-5CE4-48F0-A717-8FC833D8017A}\\\\0.1.197\\\")\\r\\n{\\r\\n    Write-Output \\\"0.1.197\\\"\\r\\n}", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 119, "name": "SmartPSS Camera Viewer", "licensed": false, "installOrder": 115, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "SmartPSS*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 120, "name": "NVMS7000", "licensed": true, "installOrder": 116, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "NVMS7000", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 121, "name": "Citrix Receiver", "licensed": false, "installOrder": 117, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Citrix Receiver*", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}, {"softwareID": 122, "name": "Autodesk Navisworks 2018", "licensed": false, "installOrder": 118, "iV_Item_RecID": 0, "hidden": false, "detectionActionTypeID": 4, "detectionAction": "Autodesk Navisworks Freedom 2018", "autoUpdate": false, "autoUpdateActionTypeID": 0, "autoUpdateAction": null, "softwareVersions": null}]