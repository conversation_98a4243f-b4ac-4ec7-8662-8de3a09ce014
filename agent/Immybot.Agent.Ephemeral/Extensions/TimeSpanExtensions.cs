namespace Immybot.Agent.Ephemeral.Extensions;

public static class TimeSpanExtensions
{
  /// <summary>
  /// Returns a random <see cref="TimeSpan"/> within a uniform range centered on the original value,
  /// where the range is determined by the specified variance. This helps distribute requests over time to avoid spikes.
  /// </summary>
  /// <param name="timespan"> The base <see cref="TimeSpan"/> value. </param>
  /// <param name="variance"> The maximum fractional deviation from the base value (e.g., 0.1 means ±10%). </param>
  /// <returns> A random <see cref="TimeSpan"/> within the specified range. </returns>
  /// <example>
  /// For a base <paramref name="timespan"/> of 10 seconds and a <paramref name="variance"/> of 0.1,
  /// the result will be a random value in the range [9.0, 11.0) seconds.
  /// </example>
  public static TimeSpan WithJitter(this TimeSpan timespan, double variance = 0.1)
  {
    return timespan.Multiply(1 - variance + (2 * variance * Random.Shared.NextDouble()));
  }
}
