using Immybot.Agent.Ephemeral.Services.PowerShellExecutionHost;
using Immybot.Shared.DataContracts.Agent.EphemeralRpc;

namespace Immybot.Agent.Ephemeral.Extensions;

public static class DtoExtensions
{
  public static PowershellExecutionHostDto ToDto(this IPowerShellExecutionHost host)
  {
    return new()
    {
      IsRunningPowershell = host.IsRunningPowershell,
      PipeHostPID = host.PipeHostPID,
      CreatedAt = host.CreatedAt,
      LastUsedAt = host.LastUsedAt,
      LastOutputAt = host.LastOutputAt,
    };
  }
}
