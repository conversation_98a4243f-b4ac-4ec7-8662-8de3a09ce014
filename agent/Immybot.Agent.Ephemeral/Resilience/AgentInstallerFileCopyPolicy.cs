using Polly;

namespace Immybot.Agent.Ephemeral.Resilience;

public static class AgentInstallerFileCopyPolicy
{
  public const string Key = nameof(AgentInstallerFileCopyPolicy);

  public static TimeSpan Delay { get; } = TimeSpan.FromSeconds(0.5);
  public static int MaxRetryAttempts { get; } = 10;

  public static void BuildPolicy(ResiliencePipelineBuilder builder)
  {
    builder.AddRetry(new Polly.Retry.RetryStrategyOptions()
    {
      BackoffType = DelayBackoffType.Linear,
      UseJitter = true,
      Delay = Delay,
      MaxRetryAttempts = MaxRetryAttempts,
    });
  }
}
