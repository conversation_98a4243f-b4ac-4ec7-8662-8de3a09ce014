using Polly;

namespace Immybot.Agent.Ephemeral.Resilience;

public static class AgentPowershellExecutionHostInitPolicy
{
  public const string Key = nameof(AgentPowershellExecutionHostInitPolicy);

  public static TimeSpan Delay { get; } = TimeSpan.FromSeconds(0.5);
  public static int MaxRetryAttempts { get; } = 5;

  public static void BuildPolicy(ResiliencePipelineBuilder builder)
  {
    builder.AddRetry(new Polly.Retry.RetryStrategyOptions()
    {
      BackoffType = DelayBackoffType.Constant,
      Delay = Delay,
      MaxRetryAttempts = MaxRetryAttempts,
    });
  }
}
