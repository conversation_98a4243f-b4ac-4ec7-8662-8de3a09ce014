using System.Diagnostics.CodeAnalysis;
using System.Diagnostics.Metrics;
using System.Net.WebSockets;
using System.Runtime.Versioning;
using Immybot.Agent.Ephemeral.Services;
using Immybot.Shared.DataContracts.Agent.EphemeralRpc;
using Immybot.Shared.DataContracts.Signalr;
using Immybot.Shared.DataContracts.Signalr.AgentDtos;
using Immybot.Shared.DataContracts.WindowsRegistry;
using Immybot.Shared.Primitives;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.Threading;
using NuGet.Versioning;
using OpenTelemetry.Exporter;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using StreamJsonRpc;

[assembly: SupportedOSPlatform("windows")]

var builder = Host.CreateApplicationBuilder(args);

builder.Services
  .Configure<AgentSimulatorOptions>(options =>
  {
    if (args.ElementAtOrDefault(0) is { } serverUrl) options.ServerUrl = new Uri(serverUrl);
    if (args.ElementAtOrDefault(1) is { } clientCount) options.ClientCount = int.Parse(clientCount);
  })
  .AddLogging()
  .AddSingleton<AgentSimulatorResourceDetector>()
  .AddTransient<IHubConnectionBuilder, HubConnectionBuilder>()
  .AddTransient<AgentSimulatorHubConnection>()
  .AddSingleton(TimeProvider.System)
  .AddHostedService<AgentSimulatorService>()
  ;

builder.Services.AddOpenTelemetry()
  .ConfigureResource(it => it
    .AddService(
      serviceName: "immybot.agent.simulator",
      serviceVersion: typeof(Program).Assembly.GetName().Version?.ToString(),
      serviceInstanceId: Environment.MachineName
    )
    .AddContainerDetector()
    .AddHostDetector()
    .AddOperatingSystemDetector()
    .AddProcessDetector()
    .AddDetector(sp => sp.GetRequiredService<AgentSimulatorResourceDetector>())
  )
  .WithLogging()
  .WithTracing(tracerBuilder => tracerBuilder.AddSource(builder.Environment.ApplicationName))
  .WithMetrics(meterBuilder => meterBuilder
    .AddMeter(builder.Environment.ApplicationName)
    .AddOtlpExporter((exporterOptions, metricReaderOptions) =>
    {
      metricReaderOptions.PeriodicExportingMetricReaderOptions.ExportIntervalMilliseconds = 2000;
      exporterOptions.Endpoint = new Uri("http://localhost:4317");
      exporterOptions.Protocol = OtlpExportProtocol.Grpc;
    })
  );

var app = builder.Build();
await app.RunAsync();

internal record AgentSimulatorOptions
{
  public int ClientCount { get; set; } = 1;
  public Uri ServerUrl { get; set; } = new("http://localhost:5000");
  public int MinWaitMilliseconds { get; set; } = 1_000;
  public int MaxWaitMilliseconds { get; set; } = 10_000;
  public int MaxConcurrentConnects { get; set; } = 50;
  public string AgentVersion { get; set; } = "0.58.3.25209";
}

internal class AgentSimulatorResourceDetector(IOptions<AgentSimulatorOptions> options) : IResourceDetector
{
  public Resource Detect()
  {
    var resources = new Dictionary<string, object>
    {
      { "options.client_count", options.Value.ClientCount },
      { "options.server_url", options.Value.ServerUrl.AbsoluteUri },
      { "options.min_wait_milliseconds", options.Value.MinWaitMilliseconds },
      { "options.max_wait_milliseconds", options.Value.MaxWaitMilliseconds },
      { "options.max_concurrent_connects", options.Value.MaxConcurrentConnects },
      { "options.agent_version", options.Value.AgentVersion },
    };
    return new Resource(resources);
  }
}

internal class AgentSimulatorService(
  IHostEnvironment environment,
  IHostApplicationLifetime hostLifetime,
  IServiceProvider serviceProvider,
  ILogger<AgentSimulatorService> logger,
  IOptionsMonitor<AgentSimulatorOptions> optionsMonitor)
  : IHostedService
{
  public async Task StartAsync(CancellationToken cancellationToken)
  {
    var options = optionsMonitor.CurrentValue;

    if (environment.IsDevelopment())
      logger.LogWarning("Running in development mode. This is not recommended for large tests.");

    logger.LogInformation("Options: {options}", options);
    var maxConcurrentConnects = options.MaxConcurrentConnects;
    using var connectSemaphore = new SemaphoreSlim(maxConcurrentConnects, maxConcurrentConnects);

    var meter = new Meter(environment.ApplicationName);
    var runningCounter = meter.CreateUpDownCounter<long>("running");
    var waitingCounter = meter.CreateUpDownCounter<long>("waiting");
    var connectingCounter = meter.CreateUpDownCounter<long>("connecting");
    var connectedCounter = meter.CreateUpDownCounter<long>("connected");
    var errorsCounter = meter.CreateCounter<long>("errors");

    [SuppressMessage("ReSharper", "AccessToDisposedClosure")]
    async Task<IDisposable> ObtainConnectLockAsync()
    {
      using (waitingCounter.IncrementAndDecrement())
        await connectSemaphore.WaitAsync(hostLifetime.ApplicationStopping);

      connectingCounter.Add(1);

      return Disposable.Create(() =>
      {
        connectingCounter.Add(-1);
        connectSemaphore.Release();
      });
    }

    // var activitySource = new ActivitySource(environment.ApplicationName);

    await Task.WhenAll(
      Enumerable.Range(0, options.ClientCount).Select(async clientIndex =>
        {
          var deviceId = options.ClientCount > 1
            ? new Guid(clientIndex, 0, 0x4000, new byte[8])
            : Guid.Parse("5ceab5cb-3bb2-4076-a841-679e18c37fdf");

          using (runningCounter.IncrementAndDecrement())
            while (!hostLifetime.ApplicationStopping.IsCancellationRequested)
            {
              using (waitingCounter.IncrementAndDecrement())
                await Task.Delay(
                    Random.Shared.Next(options.MinWaitMilliseconds, options.MaxWaitMilliseconds),
                    hostLifetime.ApplicationStopping)
                  .ConfigureAwait(ConfigureAwaitOptions.SuppressThrowing);

              var agentHubConnection = serviceProvider.GetRequiredService<AgentSimulatorHubConnection>();

              try
              {
                HubConnection connection;

                using (await ObtainConnectLockAsync())
                {
                  using (var timeoutTokenSource = new CancellationTokenSource(100_000))
                  using (var combinedToken = hostLifetime.ApplicationStopping.CombineWith(timeoutTokenSource.Token))
                  {
                    // await webSocket.ConnectAsync(new Uri(serverUrl, $"agent/{instanceId}-{clientIndex}"), cancellationTokenSource.Token);
                    connection = await agentHubConnection.Connect(combinedToken.Token);
                  }
                }

                Console.WriteLine($"Connected client {clientIndex} successfully.");

                using (connectedCounter.IncrementAndDecrement())
                {
                  var result = await agentHubConnection.CreateAgentSessionOnServer(
                    connection,
                    deviceId,
                    hostLifetime.ApplicationStopping);

                  Console.WriteLine($"Created agent session {clientIndex} result: {result}");

                  var ephemeralSessionResult = await agentHubConnection.CreateEphemeralSession(connection, deviceId);
                  Console.WriteLine($"Created ephemeral session {clientIndex} result: {ephemeralSessionResult}");

                  if (ephemeralSessionResult.IsSuccessStatus)
                  {
                    using var socket = new ClientWebSocket();
                    var rpcHandler = new AgentSimulatorEphemeralAgentRPC();
                    var agentInstanceId = Ulid.NewUlid().ToString();
                    var backendUri = new UriBuilder(options.ServerUrl)
                    {
                      Path = $"api/v1/ephemeral-session/{ephemeralSessionResult.SessionId}/{agentInstanceId}/{ephemeralSessionResult.ProviderAgentId}",
                      Scheme = options.ServerUrl.Scheme switch
                      {
                        "http" => "ws",
                        "https" => "wss",
                        _ => options.ServerUrl.Scheme,
                      },
                    }.Uri;

                    await socket.ConnectAsync(backendUri, cancellationToken);
                    logger.LogInformation("Connected to web socket. Establishing JSON-RPC protocol...");

                    using var jsonRpc = new JsonRpc(new AgentMessageHandler(socket), rpcHandler);
                    jsonRpc.ExceptionStrategy = ExceptionProcessing.ISerializable;
                    jsonRpc.CancelLocallyInvokedMethodsWhenConnectionIsClosed = true;
                    var backendRpc = jsonRpc.Attach<IBackendRPC>();
                    jsonRpc.StartListening();
                    logger.LogInformation("JSON-RPC listener started and ready.");

                    var requestedInitParams = await backendRpc.SubmitAgentStartupInfo(new AgentStartupInfo
                    {
                      PID = (uint)Environment.ProcessId,
                      InstalledAgentState = new AgentInstallationDetails(
                        true,
                        true,
                        true,
                        options.AgentVersion,
                        options.AgentVersion),
                    });
                    logger.LogInformation("Submitted Agent-Start information to backend server. Requested params: {RequestedInitParams}",
                      requestedInitParams);

                    await jsonRpc.Completion.WaitAsync(hostLifetime.ApplicationStopping);
                  }

                  await agentHubConnection.WaitForDisconnection(hostLifetime.ApplicationStopping);
                  Console.WriteLine($"Disconnected client {clientIndex}.");
                }
              }
              catch (Exception ex)
              {
                if (ex is not TaskCanceledException)
                  await Console.Error.WriteLineAsync(ex.ToString());

                errorsCounter.Add(1);
              }
            }
        }
      )
    );
  }

  public async Task StopAsync(CancellationToken cancellationToken) { }
}

internal class AgentSimulatorHubConnection(
  IHubConnectionBuilder hubConnectionBuilder,
  TimeProvider timeProvider,
  IOptions<AgentSimulatorOptions> options,
  ILogger<AgentSimulatorHubConnection> logger)
{
  private readonly TaskCompletionSource<Exception?> _disconnectionTcs = new();

  public async Task<HubConnection> Connect(CancellationToken cancellationToken)
  {
    var hubEndpoint = new Uri(options.Value.ServerUrl, "/hubs/v1/agent-hub");
    logger.LogInformation("Connecting to SignalR hub ({AgentHubUrl}).", hubEndpoint);

    var connection = hubConnectionBuilder.WithUrl(hubEndpoint).Build();
    connection.Closed += async error => _disconnectionTcs.TrySetResult(error);

    await connection.StartAsync(cancellationToken);
    connection.On<RunScriptRequest, string>(nameof(RunScript), RunScript);

    logger.LogInformation("Connected to hub ({AgentHubUrl}).", hubEndpoint);
    return connection;
  }

  public Task<Exception?> WaitForDisconnection(CancellationToken cancellationToken)
  {
#pragma warning disable VSTHRD003
    return _disconnectionTcs.Task.WaitAsync(cancellationToken);
#pragma warning restore VSTHRD003
  }

  public async Task<AddAgentSessionResult> CreateAgentSessionOnServer(
    HubConnection connection,
    Guid deviceId,
    CancellationToken cancellationToken)
  {
    logger.LogInformation("Sending device info.");

    var deviceDetails = new AgentDeviceDetails
    {
      AgentDeviceId = deviceId,
      OperatingSystemName = "Windows something probably",
      Manufacturer = string.Empty,
      DeviceName = Environment.MachineName,
      LastUpdated = timeProvider.GetUtcNow(),
      IsOnline = true,
      HardwareIdentifier = "IMMYHWID==CPU:cpu;BIOS:bios;MOBO:mobo;GPU:gpu;TPM:NO_TPM;",
      SerialNumber = string.Empty,
      AgentVersion = NuGetVersion.Parse(options.Value.AgentVersion),
    };

    var result = await connection.InvokeAsync<AddAgentSessionResult>(
      nameof(IAgentHub.AddAgentSession2),
      deviceDetails,
      cancellationToken: cancellationToken);

    return result;
  }

  public async Task<string> RunScript(RunScriptRequest runScriptRequest)
  {
    await Task.Delay(1000); // Simulate some work.
    return $"Script {runScriptRequest.ScriptPath} executed successfully:\n {runScriptRequest.ScriptCode}.";
  }

  public async Task<CreateEphemeralSessionResult> CreateEphemeralSession(
    HubConnection connection,
    Guid deviceId)
  {
    logger.LogInformation("Requesting ephemeral session from server.");
    return await connection.InvokeAsync<CreateEphemeralSessionResult>(nameof(IAgentHub.CreateEphemeralSession),
      new CreateEphemeralSessionRequest(deviceId));
  }
}

internal class AgentSimulatorEphemeralAgentRPC : IEphemeralAgentRPC
{
  public Task Ping(CancellationToken token) => Task.CompletedTask;
  public Task<RegistryKeyDto[]> GetRegistryBaseKeys(CancellationToken token) => throw new NotImplementedException();
  public Task<RegistryKeyDto[]> GetRegistrySubKeys(string parentKeyPath, CancellationToken token) => throw new NotImplementedException();
  public Task<RegistryValueDto[]> GetRegistryValues(string parentKeyPath, CancellationToken token) => throw new NotImplementedException();
  public IAsyncEnumerable<RegistrySearchResultDto> SearchRegistry(RegistrySearchRequestDto request, CancellationToken token) => throw new NotImplementedException();
  public Task<Ulid> StartInvokeScript(bool isUserContext, string cliXmlParams, string script, CancellationToken token) => throw new NotImplementedException();
  public Task<IReadOnlyList<IPowershellHostOutput>> ReadScriptOutputBatch(Ulid executionId, int maxBatchSize, uint? lastReceivedOutputIndex, CancellationToken token) => throw new NotImplementedException();
  public Task<OpResult> AcknowledgeCompletedScriptExecution(Ulid executionId, CancellationToken token) => throw new NotImplementedException();
  public Task<OpResult> CancelScriptExecution(Ulid executionId, CancellationToken token) => throw new NotImplementedException();
}

public static class Extensions
{
  public static IDisposable IncrementAndDecrement(this UpDownCounter<long> counter)
  {
    counter.Add(1);
    return new CounterDecrementer(counter);
  }

  private class CounterDecrementer(UpDownCounter<long> counter) : IDisposable
  {
    public void Dispose() => counter.Add(-1);
  }
}
