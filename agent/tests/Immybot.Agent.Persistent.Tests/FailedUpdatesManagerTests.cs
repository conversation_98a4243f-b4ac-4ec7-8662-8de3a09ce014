using System.Diagnostics;
using System.IO.Abstractions.TestingHelpers;
using System.Security.Cryptography;
using System.Text.Json;
using Immybot.Agent.Persistent.Resilience;
using Immybot.Agent.Persistent.Services;
using Immybot.Agent.Persistent.Updates;
using Immybot.Shared.Tests.TestingUtilities;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Time.Testing;
using Moq;

#pragma warning disable S2930 // "IDisposables" should be disposed

namespace Immybot.Agent.Persistent.Tests;
public class FailedUpdatesManagerTests
{
  private readonly FakeTimeProvider _timeProvider;
  private readonly MockFileSystem _fileSystem;
  private readonly Mock<ILogger<FailedUpdatesManager>> _logger;
  private readonly CancellationTokenSource _cts;
  private readonly byte[] _newAgentBinaryData;
  private readonly string _newAgentBinaryHash;
  private readonly FailedUpdatesManager _failedUpdates;
  private readonly ResiliencePipelineProviderMock<string> _resilienceProvider;

  public FailedUpdatesManagerTests()
  {
    _timeProvider = new FakeTimeProvider(DateTimeOffset.Now);
    _resilienceProvider = new ResiliencePipelineProviderMock<string>();
    _resilienceProvider.AddPipeline(
      PersistentAgentInstallerIoPolicy.Key,
      builder =>
      {
        PersistentAgentInstallerIoPolicy.BuildPolicy(builder);
        builder.TimeProvider = _timeProvider;
      });
    _fileSystem = new MockFileSystem();
    _logger = new Mock<ILogger<FailedUpdatesManager>>();

    var timeout = Debugger.IsAttached ? TimeSpan.FromMinutes(5) : TimeSpan.FromSeconds(5);
    _cts = new CancellationTokenSource(timeout);

    _newAgentBinaryData = new byte[47_754_500];
    RandomNumberGenerator.Fill(_newAgentBinaryData);
    _newAgentBinaryHash = Convert.ToHexString(SHA256.HashData(_newAgentBinaryData));

    _failedUpdates = new FailedUpdatesManager(
      _timeProvider,
      _resilienceProvider,
      _fileSystem,
      _logger.Object);
  }

  [Fact]
  public async Task ShouldInstallUpdate_NoFailedUpdatesFile_ReturnsTrue()
  {
    var result = await _failedUpdates.ShouldInstallUpdate("hash", _cts.Token);
    Assert.True(result);
  }

  [Fact]
  public async Task ShouldInstallUpdate_UpdateFailedRecently_ReturnsFalse()
  {
    var list = new Dictionary<string, FailedUpdateEntry>
    {
      [_newAgentBinaryHash] = new FailedUpdateEntry(_newAgentBinaryHash, FailedAt: _timeProvider.GetLocalNow().AddHours(-1))
    };

    var mockFile = new MockFileData(JsonSerializer.Serialize(list));
    _fileSystem.AddFile(PathHelpers.FailedUpdatesFilePath, mockFile);

    var result = await _failedUpdates.ShouldInstallUpdate(_newAgentBinaryHash, _cts.Token);
    Assert.False(result);
  }

  [Fact]
  public async Task ShouldInstallUpdate_RetryIntervalHasElapsedOnPreviousFailure_ReturnsTrue()
  {
    var list = new Dictionary<string, FailedUpdateEntry>
    {
      [_newAgentBinaryHash] = new FailedUpdateEntry(_newAgentBinaryHash, FailedAt: _timeProvider.GetLocalNow().AddDays(-4))
    };

    var mockFile = new MockFileData(JsonSerializer.Serialize(list));
    _fileSystem.AddFile(PathHelpers.FailedUpdatesFilePath, mockFile);

    var result = await _failedUpdates.ShouldInstallUpdate(_newAgentBinaryHash, _cts.Token);
    Assert.True(result);
  }

  [Fact]
  public async Task MarkUpdateAsFailed_WritesToFile()
  {
    await _failedUpdates.MarkUpdateAsFailed(_newAgentBinaryHash, _cts.Token);
    var fileContent = await _fileSystem.File.ReadAllTextAsync(PathHelpers.FailedUpdatesFilePath);
    var list = JsonSerializer.Deserialize<Dictionary<string, FailedUpdateEntry>>(fileContent);

    Assert.NotNull(list);

    var exists = list.TryGetValue(_newAgentBinaryHash, out var entry);
    Assert.True(exists);
    Assert.NotNull(entry);

    Assert.Equal(_newAgentBinaryHash, entry.Sha256BinaryHash);
  }
}
