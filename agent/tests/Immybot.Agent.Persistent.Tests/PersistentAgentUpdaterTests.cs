using System.Diagnostics;
using System.IO.Abstractions.TestingHelpers;
using System.Net;
using System.Security.Cryptography;
using Immybot.Agent.Configuration.Options;
using Immybot.Agent.Http;
using Immybot.Agent.Persistent.Services;
using Immybot.Agent.Tests.TestingUtilities;
using Immybot.Shared.Abstractions.Device.Processes;
using Immybot.Shared.Abstractions.Device.Windows.Threading;
using Immybot.Shared.DataContracts.Agent;
using Immybot.Shared.Tests.TestingUtilities;
using Immybot.Shared.Tests.TestingUtilities.Http;
using Microsoft.Extensions.Logging;
using Moq;

#pragma warning disable S2930 // "IDisposables" should be disposed

namespace Immybot.Agent.Persistent.Tests;
public class PersistentAgentUpdaterTests
{
  private readonly MockHttpClientFactory _httpClientFactory;
  private readonly Mock<IBackendApi> _backendApi;
  private readonly MockSystemEnvironment _systemEnvironment;
  private readonly MockFileSystem _fileSystem;
  private readonly Mock<IProcessManager> _processManager;
  private readonly OptionsMonitorWrapper<DeveloperOptions> _devOptionsMonitor;
  private readonly GlobalSemaphoreProvider _globalSemaphoreProvider;
  private readonly Mock<IFailedUpdatesManager> _failedUpdatesManager;
  private readonly Mock<ILogger<PersistentAgentUpdater>> _logger;
  private readonly PersistentAgentUpdater _updater;
  private readonly CancellationTokenSource _cts;

  public PersistentAgentUpdaterTests()
  {
    // When debugging, we don't want to break on Debug.Assert calls.
    Trace.Listeners.Clear();

    _httpClientFactory = new MockHttpClientFactory();
    _backendApi = new Mock<IBackendApi>();
    _fileSystem = new MockFileSystem();
    _systemEnvironment = new MockSystemEnvironment(_fileSystem);
    _processManager = new Mock<IProcessManager>();
    _devOptionsMonitor = new OptionsMonitorWrapper<DeveloperOptions>(new DeveloperOptions());
    _globalSemaphoreProvider = new GlobalSemaphoreProvider();
    _failedUpdatesManager = new Mock<IFailedUpdatesManager>();
    _logger = new Mock<ILogger<PersistentAgentUpdater>>();
    _cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
    _updater = new PersistentAgentUpdater(
      _httpClientFactory,
      _backendApi.Object,
      _systemEnvironment,
      _fileSystem,
      _processManager.Object,
      _globalSemaphoreProvider,
      _failedUpdatesManager.Object,
      _devOptionsMonitor,
      _logger.Object);
  }

  [Fact]
  public async Task EnsureLatestVersion_WhenAgentUpdatesAreDisabled_ReturnsEarly()
  {
    // Arrange
    var devOptions = new DeveloperOptions()
    {
      DisableAgentUpdates = true
    };
    _devOptionsMonitor.Update(devOptions, null);

    // Act
    await _updater.EnsureLatestVersion(_cts.Token);

    // Assert
    _backendApi.VerifyNoOtherCalls();
    _processManager.VerifyNoOtherCalls();
  }

  [Fact]
  public async Task EnsureLatestVersion_WhenDriveHasInsufficientSpace_Aborts()
  {
    // Arrange
    _fileSystem.AddDrive(
      "C:\\",
      new MockDriveData()
      {
        AvailableFreeSpace = 100_000_000
      });

    AddImmybotInstalledFiles();

    // Act
    await _updater.EnsureLatestVersion(_cts.Token);

    // Assert
    _backendApi.VerifyNoOtherCalls();
    _processManager.VerifyNoOtherCalls();
  }

  [Fact]
  public async Task EnsureLatestVersion_WhenNewAgentHashIsOnFailedUpdatesList_Aborts()
  {
    // Arrange
    var remoteBinaryData = new byte[47_734_000];
    RandomNumberGenerator.Fill(remoteBinaryData);
    var remoteHash = SHA256.HashData(remoteBinaryData);
    var remoteHexHash = Convert.ToHexString(remoteHash);
    var exeDownloadUri = new Uri("https://example.com/download/Immybot.Agent.exe");
    var remoteHashResponse = new GetAgentSha256HashResponse(exeDownloadUri, remoteHexHash);
    var exeDownloadClient = TestableHttpClient.Create();
    var exeDownloadResponse = new HttpResponseMessage(HttpStatusCode.OK)
    {
      Content = new ByteArrayContent(remoteBinaryData)
    };

    // This client will be retrieved in PersistentAgentUpdater.DownloadLatestVersion.
    exeDownloadClient.AddResponse(
      HttpMethod.Get,
      exeDownloadUri,
      () => exeDownloadResponse);

    _fileSystem.AddDrive(
      "C:\\",
      new MockDriveData()
      {
        AvailableFreeSpace = 100_000_000_000
      });

    AddImmybotInstalledFiles();

    _failedUpdatesManager
      .Setup(x => x.ShouldInstallUpdate(remoteHexHash, It.IsAny<CancellationToken>()))
      .ReturnsAsync(false);

    _backendApi
      .Setup(x => x.GetAgentSha256Hash(It.IsAny<CancellationToken>()))
      .ReturnsAsync(remoteHashResponse);

    _processManager
      .Setup(x => x.Start(PathHelpers.AgentExeTempPath, "service install"))
      .Returns(new MockProcess());

    _httpClientFactory.EnqueueClient(exeDownloadClient);

    // Act
    await _updater.EnsureLatestVersion(_cts.Token);

    // Assert

    // The remote binary should not have been downloaded.
    var callVerified = exeDownloadClient.VerifyCallCount(
      method: HttpMethod.Get,
      uri: exeDownloadUri,
      expectedCallCount: 0);

    Assert.True(callVerified);


    Assert.False(_fileSystem.FileExists(PathHelpers.AgentExeTempPath));

    // The installer should not have been launched.
    _processManager.VerifyNoOtherCalls();
  }

  [Fact]
  public async Task EnsureLatestVersion_WhenGlobalUpdateLockIsHeld_Aborts()
  {
    // Arrange
    var remoteBinaryData = new byte[47_734_000];
    RandomNumberGenerator.Fill(remoteBinaryData);
    var remoteHash = SHA256.HashData(remoteBinaryData);
    var remoteHexHash = Convert.ToHexString(remoteHash);
    var exeDownloadUri = new Uri("https://example.com/download/Immybot.Agent.exe");
    var remoteHashResponse = new GetAgentSha256HashResponse(exeDownloadUri, remoteHexHash);
    var exeDownloadClient = TestableHttpClient.Create();
    var exeDownloadResponse = new HttpResponseMessage(HttpStatusCode.OK)
    {
      Content = new ByteArrayContent(remoteBinaryData)
    };

    using var updateLock = _globalSemaphoreProvider.GetSecuredSemaphore(
      name: WellKnownSemaphoreNames.PersistentAgentUpdate,
      initialCount: 1,
      maximumCount: 1,
      createdNew: out _);

    Assert.True(updateLock.WaitOne(0));

    // This client will be retrieved in PersistentAgentUpdater.DownloadLatestVersion.
    exeDownloadClient.AddResponse(
      HttpMethod.Get,
      exeDownloadUri,
      () => exeDownloadResponse);

    _fileSystem.AddDrive(
      "C:\\",
      new MockDriveData()
      {
        AvailableFreeSpace = 100_000_000_000
      });

    AddImmybotInstalledFiles();

    _backendApi
      .Setup(x => x.GetAgentSha256Hash(It.IsAny<CancellationToken>()))
      .ReturnsAsync(remoteHashResponse);

    _processManager
      .Setup(x => x.Start(PathHelpers.AgentExeTempPath, "service install"))
      .Returns(new MockProcess());

    _httpClientFactory.EnqueueClient(exeDownloadClient);

    // Act
    await _updater.EnsureLatestVersion(_cts.Token);

    // Assert

    // The remote binary should not have been downloaded.
    var callVerified = exeDownloadClient.VerifyCallCount(
      method: HttpMethod.Get,
      uri: exeDownloadUri,
      expectedCallCount: 0);

    Assert.True(callVerified);


    Assert.False(_fileSystem.FileExists(PathHelpers.AgentExeTempPath));

    // The installer should not have been launched.
    _processManager.VerifyNoOtherCalls();

    updateLock.Release();
  }

  [Fact]
  public async Task EnsureLatestVersion_WhenRemoteHashIsTheSame_DoesNotUpdate()
  {
    // Arrange
    var remoteBinaryData = new byte[47_734_000];
    RandomNumberGenerator.Fill(remoteBinaryData);
    var remoteHash = SHA256.HashData(remoteBinaryData);
    var remoteHexHash = Convert.ToHexString(remoteHash);
    var exeDownloadUri = new Uri("https://example.com/download/Immybot.Agent.exe");
    var remoteHashResponse = new GetAgentSha256HashResponse(exeDownloadUri, remoteHexHash);

    _fileSystem.AddDrive(
      "C:\\",
      new MockDriveData()
      {
        AvailableFreeSpace = 100_000_000_000
      });

    AddImmybotInstalledFiles(remoteBinaryData);

    _backendApi
      .Setup(x => x.GetAgentSha256Hash(It.IsAny<CancellationToken>()))
      .ReturnsAsync(remoteHashResponse);

    // Act
    await _updater.EnsureLatestVersion(_cts.Token);

    // Assert

    // The backend API should have been called once.
    _backendApi.Verify(x => x.GetAgentSha256Hash(It.IsAny<CancellationToken>()), Times.Once);
    _backendApi.VerifyNoOtherCalls();

    // No download should have occurred.
    Assert.False(_fileSystem.File.Exists(PathHelpers.AgentExeTempPath));

    // The installer should not have been launched.
    _processManager.VerifyNoOtherCalls();
  }

  [Fact]
  public async Task EnsureLatestVersion_WhenAllIsWell_DownloadsAndLaunchesInstaller()
  {
    // Arrange
    var remoteBinaryData = new byte[47_734_000];
    RandomNumberGenerator.Fill(remoteBinaryData);
    var remoteHash = SHA256.HashData(remoteBinaryData);
    var remoteHexHash = Convert.ToHexString(remoteHash);
    var exeDownloadUri = new Uri("https://example.com/download/Immybot.Agent.exe");
    var remoteHashResponse = new GetAgentSha256HashResponse(exeDownloadUri, remoteHexHash);
    var exeDownloadClient = TestableHttpClient.Create();
    var exeDownloadResponse = new HttpResponseMessage(HttpStatusCode.OK)
    {
      Content = new ByteArrayContent(remoteBinaryData)
    };

    // This client will be retrieved in PersistentAgentUpdater.DownloadLatestVersion.
    exeDownloadClient.AddResponse(
      HttpMethod.Get,
      exeDownloadUri,
      () => exeDownloadResponse);

    _fileSystem.AddDrive(
      "C:\\",
      new MockDriveData()
      {
        AvailableFreeSpace = 100_000_000_000
      });

    AddImmybotInstalledFiles();

    _failedUpdatesManager
      .Setup(x => x.ShouldInstallUpdate(remoteHexHash, It.IsAny<CancellationToken>()))
      .ReturnsAsync(true);

    _backendApi
      .Setup(x => x.GetAgentSha256Hash(It.IsAny<CancellationToken>()))
      .ReturnsAsync(remoteHashResponse);

    _processManager
      .Setup(x => x.Start(PathHelpers.AgentExeTempPath, "service install"))
      .Returns(new MockProcess());

    _httpClientFactory.EnqueueClient(exeDownloadClient);

    // Act
    await _updater.EnsureLatestVersion(_cts.Token);

    // Assert

    // The remote binary should have been downloaded to the temp path.
    var callVerified = exeDownloadClient.VerifyCallCount(
      method: HttpMethod.Get,
      uri: exeDownloadUri,
      expectedCallCount: 1);

    Assert.True(callVerified);

    var fileBytes = await _fileSystem.File.ReadAllBytesAsync(PathHelpers.AgentExeTempPath);
    Assert.Equal(remoteBinaryData, fileBytes);

    // The installer should have been launched.
    _processManager.Verify(x => x.Start(PathHelpers.AgentExeTempPath, "service install"), Times.Once);
    _processManager.VerifyNoOtherCalls();
  }

  private void AddImmybotInstalledFiles(byte[]? agentBinaryData = null)
  {
    _fileSystem.AddDirectory(PathHelpers.ProgramCADPath);
    _fileSystem.AddDirectory("C:\\Program Files (x86)\\Immybot");
    _fileSystem.AddDirectory(Path.GetDirectoryName(PathHelpers.AgentExeTempPath));

    if (agentBinaryData is null)
    {
      agentBinaryData = new byte[47_734_000];
      RandomNumberGenerator.Fill(agentBinaryData);
    }

    _fileSystem.AddFile(
      PathHelpers.AgentExeInstallPath,
      new MockFileData(agentBinaryData));
  }
}
