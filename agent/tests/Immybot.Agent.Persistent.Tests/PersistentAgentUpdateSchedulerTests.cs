using System.Diagnostics;
using CommunityToolkit.Mvvm.Messaging;
using Immybot.Agent.Persistent.Messages;
using Immybot.Agent.Persistent.Services;
using Immybot.Shared.Primitives.Helpers;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Logging;
using Moq;

#pragma warning disable S2930 // "IDisposables" should be disposed

namespace Immybot.Agent.Persistent.Tests;

public class PersistentAgentUpdateSchedulerTests
{
  private readonly StrongReferenceMessenger _messenger;
  private readonly Mock<IPersistentAgentUpdater> _agentUpdater;
  private readonly Mock<ILogger<PersistentAgentUpdateScheduler>> _logger;
  private readonly CancellationTokenSource _appCts;
  private readonly PersistentAgentUpdateScheduler _scheduler;

  public PersistentAgentUpdateSchedulerTests()
  {
    _messenger = new StrongReferenceMessenger();
    _agentUpdater = new Mock<IPersistentAgentUpdater>();
    _logger = new Mock<ILogger<PersistentAgentUpdateScheduler>>();

    // All tests should complete nearly instantly.
    var timeout = Debugger.IsAttached ? TimeSpan.FromMinutes(5) : TimeSpan.FromSeconds(5);
    _appCts = new CancellationTokenSource(timeout);
    _scheduler = new PersistentAgentUpdateScheduler(_agentUpdater.Object, _messenger, _logger.Object);
  }

  [Fact]
  public async Task StartAsync_WhenHubReconnects_CallsUpdate()
  {
    await _scheduler.StartAsync(_appCts.Token);

    // The background service waits until the hub connection state changes to connected before checking for updates.
    _agentUpdater.Verify(x => x.EnsureLatestVersion(It.IsAny<CancellationToken>()), Times.Never);
    _agentUpdater.VerifyNoOtherCalls();
    _agentUpdater.Invocations.Clear();

    _messenger.Send(new HubConnectionStateChangedMessage(HubConnectionState.Connected));

    var changeTask = WaitHelper.WaitForAsync(
      () => _agentUpdater.Invocations.Count > 0,
      TimeSpan.FromSeconds(3));


    // Wait for the agent updater to be called as a result of the hub state change.
    Assert.True(await changeTask.WaitAsync(_appCts.Token));
    await _appCts.CancelAsync();
    // Loop should break, and ExecuteTask should finish.
    await _scheduler.ExecuteTask!.WaitAsync(TimeSpan.FromSeconds(3));
  }
}
