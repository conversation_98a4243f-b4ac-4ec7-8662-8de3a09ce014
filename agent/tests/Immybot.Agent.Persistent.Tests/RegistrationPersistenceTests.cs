using System.IO.Abstractions.TestingHelpers;
using System.Text.Json;
using Immybot.Agent.Configuration.Options;
using Immybot.Agent.Persistent;
using Immybot.Agent.Persistent.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;

namespace Immybot.Agent.Ephemeral.Tests;

public class RegistrationPersistenceTests
{
  private readonly MockFileSystem _fileSystem;
  private readonly Mock<IOptionsMonitor<DeveloperOptions>> _devOptions;
  private readonly Mock<ILogger<RegistrationPersistence>> _logger;
  private readonly RegistrationPersistence _regPersistence;

  public RegistrationPersistenceTests()
  {
    _fileSystem = new MockFileSystem();
    _devOptions = new Mock<IOptionsMonitor<DeveloperOptions>>();
    _logger = new Mock<ILogger<RegistrationPersistence>>();

    _regPersistence = new RegistrationPersistence(
      _fileSystem,
      _devOptions.Object,
      _logger.Object);
  }

  [Fact]
  public async Task GetRegistrationParameters_GivenMissingFile_Fails()
  {
    var result = await _regPersistence.GetRegistrationParameters();
    Assert.False(result.IsSuccess);
    Assert.NotNull(result.Exception);
    Assert.Equal("Registration file doesn't exist.", result.Exception.Message);
  }

  [Fact]
  public async Task GetRegistrationParameters_GivenIoException_Fails()
  {
    var regFile = new MockFileData("")
    {
      AllowedFileShare = FileShare.None
    };
    _fileSystem.AddFile(PathHelpers.RegistrationFilePath, regFile);

    var result = await _regPersistence.GetRegistrationParameters();
    Assert.False(result.IsSuccess);
    Assert.IsType<IOException>(result.Exception);
  }

  [Fact]
  public async Task GetRegistrationParameters_GivenInvalidJson_Fails()
  {
    var regFile = new MockFileData("[}");
    _fileSystem.AddFile(PathHelpers.RegistrationFilePath, regFile);
    var result = await _regPersistence.GetRegistrationParameters();
    Assert.False(result.IsSuccess);
    Assert.IsType<JsonException>(result.Exception);
  }

  [Fact]
  public async Task GetRegistrationParameters_GivenHappyPath_Ok()
  {
    var regFile = new MockFileData("{\"agentInstallerID\":\"k\"}");
    _fileSystem.AddFile(PathHelpers.RegistrationFilePath, regFile);
    var result = await _regPersistence.GetRegistrationParameters();
    Assert.True(result.IsSuccess);
    Assert.Equal("k", result.Value.AgentInstallerID);
  }


  [Fact]
  [Obsolete("This method is obsolete. Please use IOptionsMonitor<ConfigOptions> instead.")]
  public async Task GetConfigurationParameters_GivenMissingFile_Fails()
  {
    var result = await _regPersistence.GetConfigurationParameters();
    Assert.False(result.IsSuccess);
    Assert.NotNull(result.Exception);
    Assert.Equal("Configuration file doesn't exist.", result.Exception!.Message);
  }

  [Fact]
  [Obsolete("This method is obsolete. Please use IOptionsMonitor<ConfigOptions> instead.")]
  public async Task GetConfigurationParameters_GivenIoException_Fails()
  {
    var configFile = new MockFileData("")
    {
      AllowedFileShare = FileShare.None
    };

    _fileSystem.AddFile(PathHelpers.ConfigFilePath, configFile);
    var result = await _regPersistence.GetConfigurationParameters();
    Assert.False(result.IsSuccess);
    Assert.IsType<IOException>(result.Exception);
  }

  [Fact]
  [Obsolete("This method is obsolete. Please use IOptionsMonitor<ConfigOptions> instead.")]
  public async Task GetConfigurationParameters_GivenInvalidJson_Fails()
  {
    var configFile = new MockFileData("[}");
    _fileSystem.AddFile(PathHelpers.ConfigFilePath, configFile);
    var result = await _regPersistence.GetConfigurationParameters();
    Assert.False(result.IsSuccess);
    Assert.IsType<JsonException>(result.Exception);
  }

  [Fact]
  [Obsolete("This method is obsolete. Please use IOptionsMonitor<ConfigOptions> instead.")]
  public async Task GetConfigurationParameters_GivenHappyPath_Ok()
  {
    var configFile = new MockFileData("{\"AgentHubEndpoint\":\"https://example.com\"}");
    _fileSystem.AddFile(PathHelpers.ConfigFilePath, configFile);
    var result = await _regPersistence.GetConfigurationParameters();
    Assert.True(result.IsSuccess);
    Assert.Equal("https://example.com/", result.Value.AgentHubEndpoint!.ToString());
  }
}
