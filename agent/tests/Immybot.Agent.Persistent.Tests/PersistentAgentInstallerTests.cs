using System.IO.Abstractions.TestingHelpers;
using System.Security.Cryptography;
using System.ServiceProcess;
using Immybot.Agent.Persistent.Resilience;
using Immybot.Agent.Persistent.Services;
using Immybot.Agent.Tests.TestingUtilities;
using Immybot.Shared.Abstractions.Device.FileSystem;
using Immybot.Shared.Abstractions.Device.Processes;
using Immybot.Shared.Abstractions.Device.Windows;
using Immybot.Shared.Abstractions.Device.Windows.Threading;
using Immybot.Shared.Primitives;
using Immybot.Shared.Tests.Mocks.Windows.Registry;
using Immybot.Shared.Tests.Mocks.Windows.Services;
using Immybot.Shared.Tests.TestingUtilities;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Time.Testing;
using Moq;

#pragma warning disable S2930 // "IDisposables" should be disposed

namespace Immybot.Agent.Persistent.Tests;
public class PersistentAgentInstallerTests
{
  private readonly FakeTimeProvider _timeProvider;
  private readonly ResiliencePipelineProviderMock<string> _resilienceProvider;
  private readonly MockFileSystem _fileSystem;
  private readonly MockSystemEnvironment _systemEnvironment;
  private readonly Mock<IElevationChecker> _elevationChecker;
  private readonly MockServiceController _serviceController;
  private readonly MockService _immyAgentService;
  private readonly Mock<IProcessManager> _processManager;
  private readonly GlobalEventWaitProvider _globalWaitProvider;
  private readonly GlobalSemaphoreProvider _globalSemaphoreProvider;
  private readonly MockRegistryAccessor _registryAccessor;
  private readonly Mock<IFileVersionProvider> _fileVersionInfo;
  private readonly Mock<IFailedUpdatesManager> _failedUpdatesManager;
  private readonly Mock<ILogger<PersistentAgentInstaller>> _logger;
  private readonly PersistentAgentInstaller _installer;
  private readonly CancellationTokenSource _cts;
  private readonly byte[] _currentAgentBinaryData;
  private readonly string _currentVersion = "********";
  private readonly byte[] _newAgentBinaryData;
  private readonly string _newVersion = "********";
  private readonly string _newAgentBinaryHash;

  public PersistentAgentInstallerTests()
  {
    _timeProvider = new FakeTimeProvider(DateTimeOffset.Now);
    _resilienceProvider = new ResiliencePipelineProviderMock<string>();
    _resilienceProvider.AddPipeline(
      PersistentAgentInstallerIoPolicy.Key,
      builder =>
      {
        PersistentAgentInstallerIoPolicy.BuildPolicy(builder);
        builder.TimeProvider = _timeProvider;
      });
    _fileSystem = new MockFileSystem();
    _systemEnvironment = new MockSystemEnvironment(_fileSystem);
    _elevationChecker = new Mock<IElevationChecker>();
    _serviceController = new MockServiceController();
    _immyAgentService = new MockService(PathHelpers.AgentServiceName);
    _serviceController.Services.Add(_immyAgentService);
    _processManager = new Mock<IProcessManager>();
    _globalWaitProvider = new GlobalEventWaitProvider();
    _globalSemaphoreProvider = new GlobalSemaphoreProvider();
    _registryAccessor = new MockRegistryAccessor();
    _fileVersionInfo = new Mock<IFileVersionProvider>();
    _failedUpdatesManager = new Mock<IFailedUpdatesManager>();
    _logger = new Mock<ILogger<PersistentAgentInstaller>>();
    _cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));

    _currentAgentBinaryData = new byte[47_734_000];

    _newAgentBinaryData = new byte[47_754_500];
    RandomNumberGenerator.Fill(_newAgentBinaryData);
    _newAgentBinaryHash = Convert.ToHexString(SHA256.HashData(_newAgentBinaryData));

    _fileVersionInfo
      .Setup(x => x.GetFileProductVersion(It.IsAny<string>()))
      .Returns((string path) =>
      {
        var currentBytes = _fileSystem.File.ReadAllBytes(path);
        if (currentBytes.SequenceEqual(_currentAgentBinaryData))
        {
          return _currentVersion;
        }

        if (currentBytes.SequenceEqual(_newAgentBinaryData))
        {
          return _newVersion;
        }

        throw new InvalidOperationException("Unexpected file data.");
      });

    _installer = new PersistentAgentInstaller(
      _resilienceProvider,
      _fileSystem,
      _systemEnvironment,
      _elevationChecker.Object,
      _serviceController,
      _processManager.Object,
      _globalWaitProvider,
      _globalSemaphoreProvider,
      _registryAccessor,
      _fileVersionInfo.Object,
      _failedUpdatesManager.Object,
      _logger.Object);
  }

  [Fact]
  public async Task InstallAgent_WhenNoInstalledExeIsPresent_Aborts()
  {
    // Act
    await _installer.Install(_cts.Token);

    // Assert
    using var semaphore = _globalSemaphoreProvider.GetSecuredSemaphore(
       name: WellKnownSemaphoreNames.PersistentAgentInstallation,
       initialCount: 1,
       maximumCount: 1,
       out var createdNew);

    // The semaphore should not have even been created.
    Assert.True(createdNew);
    Assert.Equal(0, _immyAgentService.StartCallCount);
    Assert.Equal(0, _immyAgentService.StopCallCount);
    _processManager.VerifyNoOtherCalls();
  }

  [Fact]
  public async Task InstallAgent_WhenInstallationLockIsHeld_Aborts()
  {
    // Arrange
    AddImmybotInstalledFiles(PathHelpers.AgentExeInstallPath);

    using var semaphore = _globalSemaphoreProvider.GetSecuredSemaphore(
       name: WellKnownSemaphoreNames.PersistentAgentInstallation,
       initialCount: 1,
       maximumCount: 1,
       out _);

    Assert.True(semaphore.WaitOne(0));
    using var callback = Disposable.Create(() => semaphore.Release());

    // Act
    await _installer.Install(_cts.Token);

    // Assert
    _elevationChecker.VerifyNoOtherCalls();
    _processManager.VerifyNoOtherCalls();
    Assert.Equal(0, _immyAgentService.StartCallCount);
    Assert.Equal(0, _immyAgentService.StopCallCount);
  }


  [Fact]
  public async Task InstallAgent_WhenRunFromNonElevatedProcess_Aborts()
  {
    // Arrange
    AddImmybotInstalledFiles(PathHelpers.AgentExeInstallPath);
    _elevationChecker.Setup(x => x.IsAdministrator).Returns(false);

    // Act
    await _installer.Install(_cts.Token);

    // Assert
    _processManager.VerifyNoOtherCalls();
    Assert.Equal(0, _immyAgentService.StartCallCount);
    Assert.Equal(0, _immyAgentService.StopCallCount);
  }

  [Fact]
  public async Task InstallAgent_WhenConfigFileIsMissing_Aborts()
  {
    // Arrange
    AddImmybotInstalledFiles(PathHelpers.AgentExeInstallPath);
    _fileSystem.RemoveFile(PathHelpers.ConfigFilePath);
    _elevationChecker.Setup(x => x.IsAdministrator).Returns(true);

    // Act
    await _installer.Install(_cts.Token);

    // Assert
    _processManager.VerifyNoOtherCalls();
    Assert.Equal(0, _immyAgentService.StartCallCount);
    Assert.Equal(0, _immyAgentService.StopCallCount);
  }

  [Fact]
  public async Task InstallAgent_WhenIsExecutingFromInstalledLocation_Aborts()
  {
    // Arrange
    AddImmybotInstalledFiles(PathHelpers.AgentExeInstallPath);
    _elevationChecker.Setup(x => x.IsAdministrator).Returns(true);
    _systemEnvironment.StartupExe = PathHelpers.AgentExeInstallPath;

    // Act
    await _installer.Install(_cts.Token);

    // Assert
    _processManager.VerifyNoOtherCalls();
    Assert.Equal(0, _immyAgentService.StartCallCount);
    Assert.Equal(0, _immyAgentService.StopCallCount);
  }


  [Fact]
  public async Task InstallAgent_WhenHashesAreTheSame_Aborts()
  {
    // Arrange
    AddImmybotInstalledFiles(PathHelpers.AgentExeInstallPath);

    _elevationChecker.Setup(x => x.IsAdministrator).Returns(true);
    _systemEnvironment.StartupExe = PathHelpers.AgentExeTempPath;
    _fileSystem.AddFile(PathHelpers.AgentExeTempPath, new MockFileData(_currentAgentBinaryData));

    // Act
    await _installer.Install(_cts.Token);

    // Assert
    _processManager.VerifyNoOtherCalls();
    Assert.Equal(0, _immyAgentService.StartCallCount);
    Assert.Equal(0, _immyAgentService.StopCallCount);
  }

  [Theory]
  [CombinatorialData]
  public async Task InstallAgent_WhenStopServiceThrowsTimeout_Aborts(bool isServiceStoppedAfterFailure)
  {
    // Arrange
    AddImmybotInstalledFiles(PathHelpers.AgentExeInstallPath);

    _elevationChecker.Setup(x => x.IsAdministrator).Returns(true);
    _systemEnvironment.StartupExe = PathHelpers.AgentExeTempPath;
    _immyAgentService.Status = ServiceControllerStatus.Running;
    _immyAgentService.StopCallback = () =>
    {
      if (isServiceStoppedAfterFailure)
      {
        _immyAgentService.Status = ServiceControllerStatus.Stopped;
      }
      throw new System.ServiceProcess.TimeoutException();
    };

    // Act
    await _installer.Install(_cts.Token);

    // Assert
    Assert.Equal(1, _immyAgentService.StopCallCount);
    // If Immy Agent service is stopped when this fails, it will try
    // to start it again before exiting.
    var expectedCount = isServiceStoppedAfterFailure ? 1 : 0;
    Assert.Equal(expectedCount, _immyAgentService.StartCallCount);
    _processManager.VerifyNoOtherCalls();
  }

  [Theory]
  [CombinatorialData]
  public async Task InstallAgent_WhenStopServiceThrowsOtherException_RunsRecovery(
    bool isServiceStoppedAfterFailure,
    bool useNewAgentFilePath)
  {
    // Arrange
    var effectiveAgentFilePath = useNewAgentFilePath
      ? PathHelpers.AgentExeInstallPath
      : PathHelpers.AgentExeInstallPathOld;

    AddImmybotInstalledFiles(effectiveAgentFilePath);
    AddImmyProcesses();

    _elevationChecker.Setup(x => x.IsAdministrator).Returns(true);
    _systemEnvironment.StartupExe = PathHelpers.AgentExeTempPath;
    _immyAgentService.Status = ServiceControllerStatus.Running;
    _immyAgentService.StopCallback = () =>
    {
      if (isServiceStoppedAfterFailure)
      {
        _immyAgentService.Status = ServiceControllerStatus.Stopped;
      }
      throw new InvalidOperationException();
    };
    _immyAgentService.StartCallback = () => _immyAgentService.Status = ServiceControllerStatus.Running;

    // Act
    await _installer.Install(_cts.Token);

    // Assert
    Assert.Equal(1, _immyAgentService.StopCallCount);

    // The new binary hash should be in the failed updates file.
    _failedUpdatesManager.Verify(
      x => x.MarkUpdateAsFailed(_newAgentBinaryHash, It.IsAny<CancellationToken>()),
      Times.Once);

    // The agent binary should be the same as the original file.
    var postInstallBinaryData = await _fileSystem.File.ReadAllBytesAsync(effectiveAgentFilePath);
    Assert.Equal(_currentAgentBinaryData, postInstallBinaryData);

    // If Immy Agent service is stopped when this fails, it will try
    // to start it again before exiting.
    var expectedCount = isServiceStoppedAfterFailure ? 1 : 0;
    Assert.Equal(expectedCount, _immyAgentService.StartCallCount);
    _processManager.VerifyNoOtherCalls();
  }

  [Theory]
  [CombinatorialData]
  public async Task InstallAgent_WhenInstallThrows_RunsRecovery(bool useNewAgentFilePath)
  {
    // Arrange
    var effectiveAgentFilePath = useNewAgentFilePath
      ? PathHelpers.AgentExeInstallPath
      : PathHelpers.AgentExeInstallPathOld;

    AddImmybotInstalledFiles(effectiveAgentFilePath);
    AddImmyProcesses();
    AddRegistryKeys();

    _elevationChecker.Setup(x => x.IsAdministrator).Returns(true);
    _systemEnvironment.StartupExe = PathHelpers.AgentExeTempPath;
    _immyAgentService.Status = ServiceControllerStatus.Running;
    _immyAgentService.StopCallback = () => _immyAgentService.Status = ServiceControllerStatus.Stopped;
    _immyAgentService.StartCallback = () =>
    {
      if (_immyAgentService.StartCallCount == 1)
      {
        throw new InvalidOperationException();
      }
      _immyAgentService.Status = ServiceControllerStatus.Running;
    };


    // Act
    await _installer.Install(_cts.Token);

    // Assert
    Assert.Equal(1, _immyAgentService.StopCallCount);

    // The new binary hash should have been marked as failed.
    _failedUpdatesManager.Verify(
      x => x.MarkUpdateAsFailed(_newAgentBinaryHash, It.IsAny<CancellationToken>()),
      Times.Once);

    // The agent binary should be the same as the original file.
    var postInstallBinaryData = await _fileSystem.File.ReadAllBytesAsync(effectiveAgentFilePath);
    Assert.Equal(_currentAgentBinaryData, postInstallBinaryData);

    // First call is from the initial install, second is from the recovery.
    Assert.Equal(2, _immyAgentService.StartCallCount);

    _processManager.Verify(x => x.GetCurrentProcess(), Times.Once);
    _processManager.Verify(x => x.GetProcessesByName("Immybot.Agent"), Times.Once);
    _processManager.VerifyNoOtherCalls();
  }


  [Theory]
  [CombinatorialData]
  public async Task InstallAgent_WhenAllIsWell_InstallsNewAgent(bool useNewAgentFilePath)
  {
    // Arrange
    var effectiveAgentFilePath = useNewAgentFilePath
      ? PathHelpers.AgentExeInstallPath
      : PathHelpers.AgentExeInstallPathOld;

    AddImmybotInstalledFiles(effectiveAgentFilePath);
    AddImmyProcesses();
    AddRegistryKeys();

    _elevationChecker.Setup(x => x.IsAdministrator).Returns(true);
    _systemEnvironment.StartupExe = PathHelpers.AgentExeTempPath;
    _immyAgentService.Status = ServiceControllerStatus.Running;
    _immyAgentService.StopCallback = () => _immyAgentService.Status = ServiceControllerStatus.Stopped;
    _immyAgentService.StartCallback = () => _immyAgentService.Status = ServiceControllerStatus.Running;

    using var waitHandle = _globalWaitProvider.GetSecuredWaitHandle(
        initialState: false,
        mode: EventResetMode.ManualReset,
        name: WellKnownEventNames.PersistentAgentConnected,
        createdNew: out _);

    waitHandle.Set();

    // Act
    await _installer.Install(_cts.Token);

    // Assert
    Assert.Equal(1, _immyAgentService.StopCallCount);

    // No failure file should have been created.
    Assert.False(_fileSystem.FileExists(PathHelpers.FailedUpdatesFilePath));

    // The agent binary should be the new one.
    var postInstallBinaryData = await _fileSystem.File.ReadAllBytesAsync(effectiveAgentFilePath);
    Assert.Equal(_newAgentBinaryData, postInstallBinaryData);

    // Service should be restarted after install.
    Assert.Equal(1, _immyAgentService.StartCallCount);

    _processManager.Verify(x => x.GetCurrentProcess(), Times.Once);
    _processManager.Verify(x => x.GetProcessesByName("Immybot.Agent"), Times.Once);
    _processManager.VerifyNoOtherCalls();
  }

  private void AddImmybotInstalledFiles(string agentInstalledExePath)
  {
    _fileSystem.AddDirectory(PathHelpers.ProgramCADPath);
    _fileSystem.AddDirectory("C:\\Program Files (x86)\\Immybot");
    _fileSystem.AddDirectory(Path.GetDirectoryName(PathHelpers.AgentExeTempPath));
    _fileSystem.AddEmptyFile(PathHelpers.ConfigFilePath);

    _fileSystem.AddFile(
      agentInstalledExePath,
      new MockFileData(_currentAgentBinaryData));

    _fileSystem.AddFile(
      PathHelpers.AgentExeTempPath,
      new MockFileData(_newAgentBinaryData));
  }

  private void AddImmyProcesses()
  {
    var processName = _fileSystem.Path.GetFileNameWithoutExtension(PathHelpers.AgentExeTempPath);

    var currentProcess = new MockProcess()
    {
      Id = 42,
      ProcessName = processName
    };

    var processes = new[]
    {
      currentProcess,
      new MockProcess()
      {
        Id = 43,
        ProcessName = processName
      }
    };

    _processManager
      .Setup(x => x.GetCurrentProcess())
      .Returns(currentProcess);

    _processManager
      .Setup(x => x.GetProcessesByName(processName))
      .Returns(processes);
  }

  private void AddRegistryKeys()
  {
    using var uninstallKey = _registryAccessor.LocalMachine.CreateSubKey(PathHelpers.UninstallRegistryKeyPath, true);
    using var immyKey = uninstallKey.CreateSubKey("Immybot", true);
    immyKey.SetValue("DisplayName", PathHelpers.AgentRegistryDisplayName);
    immyKey.SetValue("DisplayVersion", _currentVersion);
  }
}
