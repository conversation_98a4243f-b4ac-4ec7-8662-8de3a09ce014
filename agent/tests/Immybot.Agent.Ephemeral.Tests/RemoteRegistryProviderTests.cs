using System.Runtime.Versioning;
using Immybot.Shared.Abstractions.Device.Windows;
using Immybot.Shared.DataContracts.WindowsRegistry;
using Immybot.Shared.DataContracts.WindowsRegistry.Exceptions;
using Immybot.Shared.Tests.Mocks.Windows.Registry;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using Moq;

namespace Immybot.Agent.Ephemeral.Tests;

[SupportedOSPlatform("windows")]
public class RemoteRegistryProviderTests
{
  private readonly Mock<ILogger<RemoteRegistryProvider>> _logger;
  private readonly MockRegistryAccessor _registryAccessor;
  private readonly RemoteRegistryProvider _provider;
  private readonly IRegistryKey _hklm;
  private readonly IRegistryKey _softwareKey;
  private readonly IRegistryKey _dellKey;
  private readonly IRegistryKey _optimizerKey;

  public RemoteRegistryProviderTests()
  {
    _logger = new Mock<ILogger<RemoteRegistryProvider>>();
    _registryAccessor = new MockRegistryAccessor();
    _provider = new RemoteRegistryProvider(_registryAccessor, _logger.Object);

    _hklm = _registryAccessor.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64);
    _softwareKey = _hklm.CreateSubKey("SOFTWARE", true);
    _dellKey = _softwareKey.CreateSubKey("Dell", true);
    _optimizerKey = _dellKey.CreateSubKey("DellOptimizer", true);
    _optimizerKey.SetValue("String", "one two three", RegistryValueKind.String);
    _optimizerKey.SetValue("DWord", 123, RegistryValueKind.DWord);
    _optimizerKey.SetValue("QWord", (long)123, RegistryValueKind.QWord);
    _optimizerKey.SetValue("Binary", new byte[] { 1, 2, 3 }, RegistryValueKind.Binary);
    _optimizerKey.SetValue("MultiString", new string[] { "one", "two", "three" }, RegistryValueKind.MultiString);
    _optimizerKey.SetValue("ExpandString", "C:\\Program Files\\Dell", RegistryValueKind.ExpandString);
    using (_dellKey.CreateSubKey("UpdateService", false)) { }
  }

  [Fact]
  public void GetBaseKeys_WhenDifferentViews_ReturnsCorrectAmount()
  {
    var reg64Count = _provider.GetBaseKeys(RegistryViewDto.Registry64).ToArray().Length;
    Assert.Equal(6, reg64Count);

    var reg32Count = _provider.GetBaseKeys(RegistryViewDto.Registry32).ToArray().Length;
    Assert.Equal(6, reg32Count);
  }

  [Fact]
  public void GetSubKeys_WhenPathExists_ReturnsSubKeys()
  {
    var subkeys = _provider.GetSubKeys(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell");
    Assert.Equal(2, subkeys.Count());
    Assert.Contains(subkeys, k => k.DisplayName == "DellOptimizer");
    Assert.Contains(subkeys, k => k.FullPath == "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer");
    Assert.Contains(subkeys, k => k.DisplayName == "UpdateService");
    Assert.Contains(subkeys, k => k.FullPath == "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\UpdateService");
  }

  [Fact]
  public void GetSubKeys_WhenPathExistsWithNoSubKeys_ReturnsEmptyArray()
  {
    var subkeys = _provider.GetSubKeys(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer");
    Assert.Empty(subkeys);
  }

  [Fact]
  public void GetSubKeys_WhenHiveIsInvalid_Throws()
  {
    Assert.Throws<ArgumentException>(() =>
    {
      _ = _provider.GetSubKeys(RegistryViewDto.Registry64, "HKLM\\SOFTWARE\\Dell\\DellOptimizer");
    });
  }

  [Fact]
  public void GetSubKeys_WhenKeyDoesNotExist_Throws()
  {
    Assert.Throws<InvalidOperationException>(() =>
    {
      _ = _provider.GetSubKeys(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer\\DoesNotExist");
    });
  }

  [Fact]
  public void GetValues_WhenPathExists_ReturnsValues()
  {
    var values = _provider.GetValues(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer");
    Assert.Equal(6, values.Count());

    Assert.Contains(values, x =>
      x.Kind == RegistryValueKindDto.DWord &&
      x.DWordValue == 123);

    Assert.Contains(values, x =>
      x.Kind == RegistryValueKindDto.QWord &&
      x.QWordValue == 123);

    Assert.Contains(values, x =>
      x.Kind == RegistryValueKindDto.String &&
      x.StringValue == "one two three");

    Assert.Contains(values, x =>
      x.Kind == RegistryValueKindDto.ExpandString &&
      x.StringValue == "C:\\Program Files\\Dell");

    Assert.Contains(values, x =>
      x.Kind == RegistryValueKindDto.Binary &&
      Convert.FromBase64String(x.BinaryValue!) is byte[] bytes &&
      bytes.SequenceEqual(new byte[] { 1, 2, 3 }));

    Assert.Contains(values, x =>
      x.Kind == RegistryValueKindDto.MultiString &&
      x.MultiStringValue!.SequenceEqual(new string[] { "one", "two", "three" }));
  }

  [Fact]
  public void GetValues_WhenPathExistsWithNoValues_ReturnsEmptyArray()
  {
    var values = _provider.GetValues(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\UpdateService");
    Assert.Empty(values);
  }

  [Fact]
  public void GetValues_WhenHiveIsInvalid_Throws()
  {
    Assert.Throws<ArgumentException>(() =>
    {
      _ = _provider.GetValues(RegistryViewDto.Registry64, "HKLM\\SOFTWARE\\Dell\\DellOptimizer");
    });
  }

  [Fact]
  public void GetValues_WhenKeyDoesNotExist_Throws()
  {
    Assert.Throws<InvalidOperationException>(() =>
    {
      _ = _provider.GetValues(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer\\DoesNotExist");
    });
  }

  [Fact]
  public void DeleteSubKey_WhenKeyExists_DeletesKey()
  {
    var subkeys = _provider.GetSubKeys(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell");
    Assert.Contains(subkeys, k => k.DisplayName == "UpdateService");

    _provider.DeleteKey
      (RegistryViewDto.Registry64,
      "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\UpdateService",
      throwIfSubKeyMissing: true);

    subkeys = _provider.GetSubKeys(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell");
    Assert.DoesNotContain(subkeys, k => k.DisplayName == "UpdateService");
  }

  [Fact]
  public void DeleteSubKey_WhenKeyExistsAndHasSubKeysAndRecurseNotSpecified_Throws()
  {
    Assert.Throws<InvalidOperationException>(() =>
    {
      _provider.DeleteKey
        (RegistryViewDto.Registry64,
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell",
        throwIfSubKeyMissing: true,
        recursive: false);
    });
  }

  [Fact]
  public void DeleteSubKey_WhenKeyExistsAndHasSubKeysAndRecurseIsSpecified_DeletesTree()
  {
      _provider.DeleteKey
        (RegistryViewDto.Registry64,
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell",
        throwIfSubKeyMissing: true,
        recursive: true);

    var subkeys = _provider.GetSubKeys(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE");
    Assert.DoesNotContain(subkeys, k => k.DisplayName == "Dell");
  }

  [Fact]
  public void DeleteSubKey_WhenKeyDoesNotExistAndThrowIfMissingIsTrue_Throws()
  {
    Assert.Throws<ArgumentException>(() =>
    {
      _provider.DeleteKey
        (RegistryViewDto.Registry64,
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell2",
        throwIfSubKeyMissing: true,
        recursive: true);
    });
  }

  [Fact]
  public void DeleteSubKey_WhenParentKeyDoesNotExistAndThrowIfMissingIsFalse_Throws()
  {
    Assert.Throws<InvalidOperationException>(() =>
    {
      _provider.DeleteKey
        (RegistryViewDto.Registry64,
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell2\\Test",
        throwIfSubKeyMissing: false,
        recursive: true);
    });
  }

  [Fact]
  public void CreateSubKey_WhenKeyDoesNotExist_CreatesKey()
  {
    var subkeys = _provider.GetSubKeys(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell");
    Assert.DoesNotContain(subkeys, k => k.DisplayName == "Test");

    _provider.CreateSubKey(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell", "Test");

    subkeys = _provider.GetSubKeys(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell");
    Assert.Contains(subkeys, k => k.DisplayName == "Test");
  }

  [Fact]
  public void CreateSubKey_WhenKeyExists_ReturnsExistingKey()
  {
    var subkeys = _provider.GetSubKeys(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell");
    Assert.Contains(subkeys, k => k.DisplayName == "DellOptimizer");

    var key = _provider.CreateSubKey(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell", "DellOptimizer");
    Assert.Equal("HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer", key.FullPath);
  }

  [Fact]
  public void CreateSubKey_WhenParentKeyDoesNotExist_Throws()
  {
    Assert.Throws<InvalidOperationException>(() =>
    {
      _provider.CreateSubKey(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell2", "Test");
    });
  }

  [Fact]
  public void SetValue_WhenValueDoesNotExist_CreatesValue()
  {
    var values = _provider.GetValues(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer");
    Assert.DoesNotContain(values, v => v.ValueName == "Test");

    _provider.SetValue(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer", "Test", "TestValue", RegistryValueKindDto.String);

    values = _provider.GetValues(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer");
    Assert.Contains(values, v => v.ValueName == "Test");
  }

  [Theory]
  [InlineData(RegistryValueKindDto.DWord, "123")]
  [InlineData(RegistryValueKindDto.QWord, "123")]
  [InlineData(RegistryValueKindDto.Binary, "123")]
  [InlineData(RegistryValueKindDto.MultiString, "123")]
  [InlineData(RegistryValueKindDto.ExpandString, 123)]
  [InlineData(RegistryValueKindDto.String, 123)]
  public void SetValue_WhenValueKindIsIncorrect_Throws(RegistryValueKindDto valueKind, object valueData)
  {
    Assert.Throws<RegistryValueDataTypeException>(() =>
    {
      _provider.SetValue(
        RegistryViewDto.Registry64,
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer",
        "Test",
        valueData,
        valueKind);
    });
  }

  [Fact]
  public void SetValue_WhenValueExists_UpdatesValue()
  {
    var values = _provider.GetValues(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer");
    Assert.Contains(values, v => v.ValueName == "String" && v.StringValue == "one two three");

    _provider.SetValue(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer", "String", "NewValue", RegistryValueKindDto.String);

    values = _provider.GetValues(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer");
    Assert.Contains(values, v => v.ValueName == "String" && v.StringValue == "NewValue");
  }

  [Fact]
  public void SetValue_WhenParentKeyDoesNotExist_Throws()
  {
    Assert.Throws<InvalidOperationException>(() =>
    {
      _provider.SetValue(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell2\\DellOptimizer", "String", "NewValue", RegistryValueKindDto.String);
    });
  }

  [Fact]
  public void RenameKey_WhenKeyExists_RenamesKey()
  {
    _provider.RenameKey(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\", "SOFTWARE2");

    using var hklm = _registryAccessor.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64);
    using var oldKey = hklm.OpenSubKey("SOFTWARE");
    Assert.Null(oldKey);

    using var newKey = hklm.OpenSubKey("SOFTWARE2");
    Assert.NotNull(newKey);

    using var dellKey = newKey.OpenSubKey("Dell");
    Assert.NotNull(dellKey);

    using var optimizerKey = dellKey.OpenSubKey("DellOptimizer");
    Assert.NotNull(optimizerKey);

    var values = optimizerKey.GetValueNames();
    Assert.Contains("String", values);
    Assert.Contains("DWord", values);
    Assert.Contains("QWord", values);
    Assert.Contains("Binary", values);
    Assert.Contains("MultiString", values);
    Assert.Contains("ExpandString", values);

    var stringValue = optimizerKey.GetValue("String");
    Assert.Equal("one two three", stringValue);
    var expandStringValue = optimizerKey.GetValue("ExpandString");
    Assert.Equal("C:\\Program Files\\Dell", expandStringValue);
    var dwordValue = optimizerKey.GetValue("DWord");
    Assert.Equal(123, dwordValue);
    var qwordValue = optimizerKey.GetValue("QWord");
    Assert.Equal((long)123, qwordValue);
    var binaryValue = optimizerKey.GetValue("Binary");
    Assert.True(binaryValue is byte[] bytes && bytes.SequenceEqual(new byte[] { 1, 2, 3 }));
    var multiStringValue = optimizerKey.GetValue("MultiString");
    Assert.True(multiStringValue is string[] multiStrings && multiStrings.SequenceEqual(new string[] { "one", "two", "three" }));
  }

  [Fact]
  public void RenameKey_WhenKeyDoesNotExist_Throws()
  {
    Assert.Throws<InvalidOperationException>(() =>
    {
      _provider.RenameKey(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell2", "Dell3");
    });
  }

  [Fact]
  public void RenameKey_WhenSubKeyThrows_OriginalKeyRemains()
  {
    var hklmFake = (MockRegistryKey)_registryAccessor.LocalMachine;
    var software = hklmFake.SubKeys["SOFTWARE"];

    var throwingKey = new ThrowingKey() { Name = "ThrowingKey", View = RegistryView.Registry64, Writable = false };
    software.SubKeys["ThrowingKey"] = throwingKey;

    Assert.Throws<IOException>(() =>
    {
      _provider.RenameKey(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\", "SOFTWARE2");
    });

    using var hklm = _registryAccessor.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64);
    using var oldKey = hklm.OpenSubKey("SOFTWARE");
    Assert.NotNull(oldKey);

    using var newKey = hklm.OpenSubKey("SOFTWARE2");
    Assert.Null(newKey);

    using var dellKey = oldKey.OpenSubKey("Dell");
    Assert.NotNull(dellKey);

    using var optimizerKey = dellKey.OpenSubKey("DellOptimizer");
    Assert.NotNull(optimizerKey);
  }

  [Fact]
  public void RenameValue_WhenValueExists_RenamesValue()
  {
    _provider.RenameValue(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer", "String", "NewString");

    var values = _provider.GetValues(RegistryViewDto.Registry64, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer");
    Assert.Contains(values, v => v.ValueName == "NewString" && v.StringValue == "one two three");
    Assert.DoesNotContain(values, v => v.ValueName == "String");
  }

  // We're not using CombinatorialData here because it doesn't properly test
  // all Flags combinations.  I want at least one test that uses all of them.
  [Theory]
  [InlineData(RegistrySearchTargets.Key, false)]
  [InlineData(RegistrySearchTargets.Key | RegistrySearchTargets.ValueName, false)]
  [InlineData(RegistrySearchTargets.Key | RegistrySearchTargets.ValueData, false)]
  [InlineData(RegistrySearchTargets.ValueName, false)]
  [InlineData(RegistrySearchTargets.ValueData, false)]
  [InlineData(RegistrySearchTargets.ValueData | RegistrySearchTargets.ValueName, false)]
  [InlineData(RegistrySearchTargets.All, false)]
  [InlineData(RegistrySearchTargets.Key, true)]
  [InlineData(RegistrySearchTargets.Key | RegistrySearchTargets.ValueName, true)]
  [InlineData(RegistrySearchTargets.Key | RegistrySearchTargets.ValueData, true)]
  [InlineData(RegistrySearchTargets.ValueName, true)]
  [InlineData(RegistrySearchTargets.ValueData, true)]
  [InlineData(RegistrySearchTargets.ValueData | RegistrySearchTargets.ValueName, true)]
  [InlineData(RegistrySearchTargets.All, true)]
  public async Task SearchRegistry_WhenKeyExists_ReturnsKeyResult(RegistrySearchTargets targets, bool matchWholeWord)
  {
    var results = new List<RegistrySearchResultDto>();
    using var cts = new CancellationTokenSource();
    
    var request = new RegistrySearchRequestDto(
      ComputerId: 1,
      SearchPattern: "Optimizer",
      StartKeyPath: "HKEY_LOCAL_MACHINE",
      Targets: targets,
      MatchWholeWord: matchWholeWord,
      UserHubConnectionId: "");
    await foreach (var result in _provider.SearchRegistry(request, cts.Token))
    {
      results.Add(result);
    }

    if (targets.HasFlag(RegistrySearchTargets.Key) && !matchWholeWord)
    {
      Assert.Single(results);
      Assert.Equal(RegistrySearchResultType.Key, results[0].Type);
      Assert.Equal("HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer", results[0].FullKeyPath);
    }
    else
    {
      Assert.Empty(results);
    }
  }


  [Theory]
  [CombinatorialData]
  public async Task SearchRegistry_WhenValueNameExists_ReturnsValueNameResult(RegistrySearchTargets targets, bool matchWholeWord)
  {
    var results = new List<RegistrySearchResultDto>();
    using var cts = new CancellationTokenSource();
    var request = new RegistrySearchRequestDto(
      ComputerId: 1,
      SearchPattern: "MultiStr",
      StartKeyPath: "HKEY_LOCAL_MACHINE",
      Targets: targets,
      MatchWholeWord: matchWholeWord,
      UserHubConnectionId: "");
    await foreach (var result in _provider.SearchRegistry(request, cts.Token))
    {
      results.Add(result);
    }

    if (targets.HasFlag(RegistrySearchTargets.ValueName) && !matchWholeWord)
    {
      Assert.Single(results);
      Assert.Equal(RegistrySearchResultType.ValueName, results[0].Type);
      Assert.Equal("HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer", results[0].FullKeyPath);
      Assert.Equal("MultiString", results[0].ValueName);
    }
    else
    {
      Assert.Empty(results);
    }
  }

  [Fact]
  public async Task SearchRegistry_WhenValueNameExistsAndMatchWholeWord_ReturnsValueNameResult()
  {
    var results = new List<RegistrySearchResultDto>();
    using var cts = new CancellationTokenSource();
    var request = new RegistrySearchRequestDto(
      ComputerId: 1,
      SearchPattern: "MultiString",
      StartKeyPath: "HKEY_LOCAL_MACHINE",
      Targets: RegistrySearchTargets.ValueName,
      MatchWholeWord: true,
      UserHubConnectionId: "");

    await foreach (var result in _provider.SearchRegistry(request, cts.Token))
    {
      results.Add(result);
    }

    Assert.Single(results);
    Assert.Equal(RegistrySearchResultType.ValueName, results[0].Type);
    Assert.Equal("HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer", results[0].FullKeyPath);
    Assert.Equal("MultiString", results[0].ValueName);
  }

  [Theory]
  [CombinatorialData]
  public async Task SearchRegistry_WhenValueDataExists_ReturnsValueNameResult(RegistrySearchTargets targets, bool matchWholeWord)
  {
    var results = new List<RegistrySearchResultDto>();
    using var cts = new CancellationTokenSource();
    var request = new RegistrySearchRequestDto(
      ComputerId: 1,
      SearchPattern: "three",
      StartKeyPath: "HKEY_LOCAL_MACHINE",
      Targets: targets,
      MatchWholeWord: matchWholeWord,
      UserHubConnectionId: "");
    await foreach (var result in _provider.SearchRegistry(request, cts.Token))
    {
      results.Add(result);
    }

    if (targets.HasFlag(RegistrySearchTargets.ValueData) && !matchWholeWord)
    {
      Assert.Equal(2, results.Count);
      Assert.Equal(RegistrySearchResultType.ValueData, results[0].Type);
      Assert.Equal(RegistrySearchResultType.ValueData, results[1].Type);
      Assert.Equal("HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer", results[0].FullKeyPath);
      Assert.Equal("HKEY_LOCAL_MACHINE\\SOFTWARE\\Dell\\DellOptimizer", results[1].FullKeyPath);
      Assert.Single(results, x => x.ValueName == "String");
      Assert.Single(results, x => x.ValueName == "MultiString");
    }
    else
    {
      Assert.Empty(results);
    }
  }


  [Fact]
  public async Task SearchRegistry_WhenStartPathIsInvalid_Throws()
  {
    var results = new List<RegistrySearchResultDto>();
    using var cts = new CancellationTokenSource();
    var request = new RegistrySearchRequestDto(
      ComputerId: 1,
      SearchPattern: "Optimizer",
      StartKeyPath: "HKLM",
      Targets: RegistrySearchTargets.All,
      MatchWholeWord: false,
      UserHubConnectionId: "");
    await Assert.ThrowsAsync<ArgumentException>(async () =>
    {
      await foreach (var result in _provider.SearchRegistry(request, cts.Token))
      {
        results.Add(result);
      }
    });

    request = new RegistrySearchRequestDto(
      ComputerId: 1,
      SearchPattern: "Optimizer",
      StartKeyPath: string.Empty,
      Targets: RegistrySearchTargets.All,
      MatchWholeWord: false,
      UserHubConnectionId: "");

    await Assert.ThrowsAsync<ArgumentNullException>(async () =>
    {
      await foreach (var result in _provider.SearchRegistry(request, cts.Token))
      {
        results.Add(result);
      }
    });
  }


  private class ThrowingKey : MockRegistryKey
  {
    public override string[] GetValueNames() => throw new IOException("A test error.");
  }
}
