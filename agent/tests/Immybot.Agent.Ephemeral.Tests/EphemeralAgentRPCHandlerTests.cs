using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Immybot.Agent.Ephemeral.Services;
using Immybot.Agent.Ephemeral.Services.PowerShellExecutionHost;
using Immybot.Agent.Tests.TestingUtilities;
using Immybot.Shared.Abstractions.Device.Processes;
using Immybot.Shared.Abstractions.Device.Windows;
using Immybot.Shared.DataContracts.Agent.EphemeralRpc;
using Immybot.Shared.DataContracts.WindowsRegistry;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Xunit.Abstractions;

namespace Immybot.Agent.Ephemeral.Tests;

public class RealChannelScriptExecutor : IPowerShellScriptExecutor
{
  private readonly PowerShellOutputChannel _outputChannel;
  private Func<Task>? _outputDrainedAction;

  public Ulid ExecutionId { get; }
  public bool IsCancelled { get; private set; }
  public bool IsAcknowledged { get; private set; }

  public RealChannelScriptExecutor()
  {
    ExecutionId = Ulid.NewUlid();
    _outputChannel = new PowerShellOutputChannel();
  }

  // Processes script content and outputs results through the real channel
  public async Task ProcessScriptAsync(string script, CancellationToken token)
  {
    // Simulate script execution with actual PowerShellOutputChannel
    uint outputIndex = 0;

    if (script.Contains("Write-Output"))
    {
      // Extract the content inside the single quotes
      var outputContent = script.Split('\'')[1];
      await _outputChannel.Writer.WriteAsync(new PowershellHostOutputContent(outputIndex++, outputContent), token);
    }
    else if (script.Contains("Test-BatchOutputOf"))
    {
      var outputContentCount = int.Parse(script.Split(' ')[1]);
      // Add multiple outputs for batch testing
      for (int i = 0; i < outputContentCount; i++)
      {
        await _outputChannel.Writer.WriteAsync(new PowershellHostOutputContent(outputIndex++, $"Batch Output {i}"), token);
      }
    }

    // Add end marker
    await _outputChannel.Writer.WriteAsync(new PowershellHostOutputEnd(outputIndex), token);

    // Complete the channel
    _outputChannel.Writer.Complete();
  }

  public async Task StartScriptExecutionAsync(
      PowershellContext targetContext,
      string cliXmlParams,
      string script,
      Func<Task> onScriptOutputDrainedAction,
      CancellationToken scriptSubmissionCancellationToken)
  {
    _outputDrainedAction = onScriptOutputDrainedAction;

    // Process the script in the background
    await ProcessScriptAsync(script, scriptSubmissionCancellationToken);
  }

  public Task<IReadOnlyList<IPowershellHostOutput>> ReadBatchAsync(
      int maxBatchSize,
      uint? lastReceivedOutputIndex,
      CancellationToken token)
  {
    // This uses the real PowerShellOutputChannel implementation
    return _outputChannel.ReadBatchAsync(maxBatchSize, lastReceivedOutputIndex, token);
  }

  public Task AcknowledgeCompletedScriptExecution()
  {
    IsAcknowledged = true;

    if (_outputDrainedAction != null)
    {
      return _outputDrainedAction();
    }

    return Task.CompletedTask;
  }

  public Task CancelScriptExecutionAsync()
  {
    IsCancelled = true;
    _outputChannel.Dispose();
    return Task.CompletedTask;
  }

  public ValueTask DisposeAsync()
  {
    _outputChannel.Dispose();
    return ValueTask.CompletedTask;
  }
}

public class EphemeralAgentRPCHandlerTests
{
  private readonly ILoggerFactory _loggerFactory;
  private readonly ILogger<EphemeralAgentRPCHandler> _logger;
  private readonly IHostApplicationLifetime _appLifetime;
  private readonly Mock<IRemoteRegistryProvider> _mockRegistryProvider;
  private readonly PowerShellExecutionService _powerShellExecutionService;
  private readonly EphemeralAgentRPCHandler _handler;
  private readonly CancellationTokenSource _testCancellationTokenSource;
  private readonly IServiceProvider _serviceProvider;

  public EphemeralAgentRPCHandlerTests(ITestOutputHelper helper)
  {
    _loggerFactory = new XunitLoggerFactory(helper);
    _logger = _loggerFactory.CreateLogger<EphemeralAgentRPCHandler>();
    _testCancellationTokenSource = new CancellationTokenSource();

    // Create mock app lifetime
    var mockAppLifetime = new Mock<IHostApplicationLifetime>();
    _appLifetime = mockAppLifetime.Object;

    // Create the registry provider mock
    _mockRegistryProvider = new Mock<IRemoteRegistryProvider>();

    // Setup service provider for PowerShellExecutionService
    var services = new ServiceCollection();
    services.AddSingleton(_loggerFactory);
    services.AddSingleton(_loggerFactory.CreateLogger<PowerShellExecutionService>());
    services.AddTransient<IPowerShellScriptExecutor, RealChannelScriptExecutor>();
    _serviceProvider = services.BuildServiceProvider();

    // Create PowerShellExecutionService
    _powerShellExecutionService = new PowerShellExecutionService(
        _serviceProvider,
        _loggerFactory.CreateLogger<PowerShellExecutionService>());

    // Create the handler with our components
    _handler = new EphemeralAgentRPCHandler(
        _appLifetime,
        _logger,
        _powerShellExecutionService,
        _mockRegistryProvider.Object);
  }

  /// <summary>
  /// Helper method to check if an executor exists by attempting to read from it
  /// </summary>
  private async Task<bool> ExecutorExists(Ulid executionId)
  {
    try
    {
      // Try to read from the executor - will throw if it doesn't exist
      await _powerShellExecutionService.ReadOutputBatchAsync(
          executionId,
          maxBatchSize: 1,
          lastReceivedOutputIndex: null,
          CancellationToken.None);

      return true;
    }
    catch (InvalidOperationException)
    {
      return false;
    }
  }

  [Fact]
  public async Task ReadScriptOutputBatch_ReturnsSuccessWhenDataAvailable()
  {
    // Arrange
    var script = "Write-Output 'Test output'";
    var executionId = await _powerShellExecutionService.StartScriptInvocationAsync(
        PowershellContext.SYSTEM_CONTEXT,
        "<Objs Version=\"*******\"><Nil /></Objs>",
        script,
        _testCancellationTokenSource.Token);

    // Act
    var result = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 10,
        lastReceivedOutputIndex: null,
        CancellationToken.None);

    // Assert
    Assert.NotNull(result);
    Assert.Equal(2, result.Count);

    // First item should be the output content
    Assert.IsType<PowershellHostOutputContent>(result[0]);
    Assert.Equal("Test output", ((PowershellHostOutputContent)result[0]).Output);
    Assert.Equal(0u, result[0].Index);

    // Second item should be the end marker
    Assert.IsType<PowershellHostOutputEnd>(result[1]);
    Assert.Equal(1u, result[1].Index);
  }

  [Fact]
  public async Task ReadScriptOutputBatch_ReturnsEndMarkerWhenNoDataAvailable()
  {
    // Arrange
    var script = "Test-Empty";
    var executionId = await _powerShellExecutionService.StartScriptInvocationAsync(
        PowershellContext.SYSTEM_CONTEXT,
        "<Objs Version=\"*******\"><Nil /></Objs>",
        script,
        _testCancellationTokenSource.Token);

    // Act
    var result = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 10,
        lastReceivedOutputIndex: null,
        CancellationToken.None);

    // Assert
    Assert.NotNull(result);
    // For an empty script, we should just get an end marker
    Assert.Single(result);
    Assert.IsType<PowershellHostOutputEnd>(result[0]);
    Assert.Equal(0u, result[0].Index);
  }

  [Fact]
  public async Task ReadScriptOutputBatch_ChunksDataCorrectlyInOrder()
  {
    // Arrange
    var script = "Test-BatchOutputOf 5";
    var executionId = await _powerShellExecutionService.StartScriptInvocationAsync(
        PowershellContext.SYSTEM_CONTEXT,
        "<Objs Version=\"*******\"><Nil /></Objs>",
        script,
        _testCancellationTokenSource.Token);

    // Act - First request with small batch size to get just the first chunk
    var result1 = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 2, // Small batch size to test chunking
        lastReceivedOutputIndex: null,
        CancellationToken.None);

    // Act - Second request with last index from first batch
    var result2 = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 2,
        lastReceivedOutputIndex: result1[result1.Count - 1].Index, // Use the last index from first batch
        CancellationToken.None);

    // Assert - First batch
    Assert.Equal(2, result1.Count);
    Assert.Equal("Batch Output 0", ((PowershellHostOutputContent)result1[0]).Output);
    Assert.Equal("Batch Output 1", ((PowershellHostOutputContent)result1[1]).Output);

    // Assert - Second batch
    Assert.Equal(2, result2.Count);
    Assert.Equal("Batch Output 2", ((PowershellHostOutputContent)result2[0]).Output);
    Assert.Equal("Batch Output 3", ((PowershellHostOutputContent)result2[1]).Output);
  }

  [Fact]
  public async Task ReadScriptOutputBatch_CanRecoverFromMissingChunk()
  {
    // Arrange
    var script = "Test-BatchOutputOf 5";
    var executionId = await _powerShellExecutionService.StartScriptInvocationAsync(
        PowershellContext.SYSTEM_CONTEXT,
        "<Objs Version=\"*******\"><Nil /></Objs>",
        script,
        _testCancellationTokenSource.Token);

    // Act - Get the first batch of output
    var result1 = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 2, // Small batch size to test chunking
        lastReceivedOutputIndex: null,
        CancellationToken.None);

    // Simulate missing the second batch by not reading it and jumping ahead
    // This tests the channel's ability to handle gaps in acknowledgements

    // Act - Request the "missing" chunk by asking for everything after the first batch
    var result2 = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 4, // Larger batch size to get remaining outputs
        lastReceivedOutputIndex: result1[result1.Count - 1].Index, // Use the last index from first batch
        CancellationToken.None);

    // Assert - First batch is valid
    Assert.Equal(2, result1.Count);
    // Check the outputs in the first batch
    Assert.Equal("Batch Output 0", ((PowershellHostOutputContent)result1[0]).Output);
    Assert.Equal("Batch Output 1", ((PowershellHostOutputContent)result1[1]).Output);

    // Assert - Second request contains remaining outputs
    Assert.Equal(4, result2.Count); // Should contain remaining outputs plus end marker
    // Second batch should contain outputs 2, 3, 4 and the end marker
    Assert.Equal("Batch Output 2", ((PowershellHostOutputContent)result2[0]).Output);
    Assert.Equal("Batch Output 3", ((PowershellHostOutputContent)result2[1]).Output);
    Assert.Equal("Batch Output 4", ((PowershellHostOutputContent)result2[2]).Output);
    Assert.IsType<PowershellHostOutputEnd>(result2[3]);
  }

  [Fact]
  public async Task ReadScriptOutputBatch_WhenHostNotFound_ReturnsFailure()
  {
    // Arrange - Use a non-existent execution ID
    var executionId = Ulid.NewUlid();

    // Act & Assert
    await Assert.ThrowsAsync<InvalidOperationException>(() =>
        _handler.ReadScriptOutputBatch(
            executionId,
            maxBatchSize: 10,
            lastReceivedOutputIndex: null,
            CancellationToken.None));
  }

  [Fact]
  public async Task CancelScriptExecution_ReturnsSuccess()
  {
    // Arrange
    var script = "Write-Output 'Cancel Test'";
    var executionId = await _powerShellExecutionService.StartScriptInvocationAsync(
        PowershellContext.SYSTEM_CONTEXT,
        "<Objs Version=\"*******\"><Nil /></Objs>",
        script,
        _testCancellationTokenSource.Token);

    // Verify executor exists before cancellation
    Assert.True(await ExecutorExists(executionId));

    // Act
    var result = await _handler.CancelScriptExecution(executionId, CancellationToken.None);

    // Assert
    Assert.True(result.IsSuccess);
  }

  [Fact]
  public async Task AcknowledgeCompletedScriptExecution_ReturnsSuccess()
  {
    // Arrange
    var script = "Write-Output 'Acknowledge Test'";
    var executionId = await _powerShellExecutionService.StartScriptInvocationAsync(
        PowershellContext.SYSTEM_CONTEXT,
        "<Objs Version=\"*******\"><Nil /></Objs>",
        script,
        _testCancellationTokenSource.Token);

    // First read all output from the channel, including the end marker
    var outputBatch = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 10, // Large enough to get all output
        lastReceivedOutputIndex: null,
        CancellationToken.None);

    // Verify we got expected output before acknowledging
    Assert.NotEmpty(outputBatch);
    Assert.Equal("Acknowledge Test", ((PowershellHostOutputContent)outputBatch[0]).Output);

    // Verify executor exists before acknowledgment
    Assert.True(await ExecutorExists(executionId));

    // Act - Now acknowledge completion after reading all output
    var result = await _handler.AcknowledgeCompletedScriptExecution(executionId, CancellationToken.None);

    // Assert
    Assert.True(result.IsSuccess);

    // Verify executor no longer exists after acknowledgment
    Assert.False(await ExecutorExists(executionId));
  }

  [Fact]
  public async Task ReadScriptOutputBatch_ThrowsWhenRequestingOlderIndex()
  {
    // Arrange
    var script = "Test-BatchOutputOf 5";
    var executionId = await _powerShellExecutionService.StartScriptInvocationAsync(
        PowershellContext.SYSTEM_CONTEXT,
        "<Objs Version=\"*******\"><Nil /></Objs>",
        script,
        _testCancellationTokenSource.Token);

    // First read with no previous index
    var firstBatch = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 2,
        lastReceivedOutputIndex: null,
        CancellationToken.None);

    // Second read with index from first batch
    await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 2,
        lastReceivedOutputIndex: firstBatch[firstBatch.Count - 1].Index,
        CancellationToken.None);

    // Act & Assert - Trying to read with lower index should throw
    await Assert.ThrowsAsync<ArgumentOutOfRangeException>(() =>
        _handler.ReadScriptOutputBatch(
            executionId,
            maxBatchSize: 2,
            lastReceivedOutputIndex: 0u, // This is lower than our previous read with index 1
            CancellationToken.None));
  }

  [Fact]
  public async Task ReadScriptOutputBatch_ThrowsWhenUsingNullAfterAcknowledgedIndex()
  {
    // Arrange
    var script = "Test-BatchOutputOf 5";
    var executionId = await _powerShellExecutionService.StartScriptInvocationAsync(
        PowershellContext.SYSTEM_CONTEXT,
        "<Objs Version=\"*******\"><Nil /></Objs>",
        script,
        _testCancellationTokenSource.Token);

    // First read with null index (initial read)
    var firstBatch = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 2,
        lastReceivedOutputIndex: null,
        CancellationToken.None);

    // Get the last index from the first batch
    var lastIndexFromFirstBatch = firstBatch[firstBatch.Count - 1].Index;

    // Second read with the last index from first batch (this will set _latestAcknowledgedOutputIndex)
    await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 2,
        lastReceivedOutputIndex: lastIndexFromFirstBatch,
        CancellationToken.None);

    // Act & Assert - Trying to read with null again should throw
    var exception = await Assert.ThrowsAsync<ArgumentOutOfRangeException>(() =>
        _handler.ReadScriptOutputBatch(
            executionId,
            maxBatchSize: 2,
            lastReceivedOutputIndex: null, // Using null after acknowledging an index
            CancellationToken.None));

    // Verify the exception message matches the expected one from PowerShellOutputChannel
    Assert.Contains("Output has been previously acknowledged", exception.Message);
  }

  [Fact]
  public async Task ReadScriptOutputBatch_ResendsFullChunkWhenRequestingWithSameIndex()
  {
    // Arrange
    var script = "Test-BatchOutputOf 5";
    var executionId = await _powerShellExecutionService.StartScriptInvocationAsync(
        PowershellContext.SYSTEM_CONTEXT,
        "<Objs Version=\"*******\"><Nil /></Objs>",
        script,
        _testCancellationTokenSource.Token);

    // Act - First request to get initial batch
    var result1 = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 3, // Get first 3 items
        lastReceivedOutputIndex: null,
        CancellationToken.None);

    // Get the last index from the first batch
    var lastIndexFromFirstBatch = result1[result1.Count - 1].Index;

    // Act - Now request second batch with the index from first batch
    var result2 = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 3, // Same batch size as before
        lastReceivedOutputIndex: lastIndexFromFirstBatch,
        CancellationToken.None);

    // Get some content from result2 to verify it's working
    Assert.NotEmpty(result2);

    // Act - Request with same index again - this should resend the same batch
    var result3 = await _handler.ReadScriptOutputBatch(
        executionId,
        maxBatchSize: 3, // Same batch size
        lastReceivedOutputIndex: lastIndexFromFirstBatch, // Same index as previous request
        CancellationToken.None);

    // Assert - The "lost chunk" (result2) and the "resent chunk" (result3) should be identical
    Assert.Equal(result2.Count, result3.Count);

    for (int i = 0; i < result2.Count; i++)
    {
      Assert.Equal(result2[i].Index, result3[i].Index);

      if (result2[i] is PowershellHostOutputContent content2 &&
          result3[i] is PowershellHostOutputContent content3)
      {
        Assert.Equal(content2.Output, content3.Output);
      }
      else if (result2[i] is PowershellHostOutputEnd && result3[i] is PowershellHostOutputEnd)
      {
        // Both are end markers, which is expected
        Assert.IsType<PowershellHostOutputEnd>(result2[i]);
        Assert.IsType<PowershellHostOutputEnd>(result3[i]);
      }
      else
      {
        // This should not happen - the types should match
        Assert.Fail("Output types don't match between batches");
      }
    }
  }
}
