using System.Text.Json;
using Immybot.Shared.DataContracts.DeviceRegistration;

namespace Immybot.Agent.Ephemeral.Tests;

public class ChallengeResponseSerializationTests
{
  private readonly string _challengeResponseJson = @"
  {
          ""TargetDeviceID"": ""5411-5450-0933-6182-6397-0425-00"",
          ""SignedChallengeResponse"": ""RhDxCILNuv1UA8UsOHxJ/xsfZ+Qr2EtEeJerceb/10aAGVd6CtkpObLWGyXEx46gkrDFD/DwPa0Jh+bPyWSNAw=="",
          ""AgentDeviceDetails"": {
            ""AgentDeviceId"": ""00000000-0000-0000-0000-000000000000"",
            ""AgentVersion"": ""0.0.1.21908"",
            ""DeviceName"": ""JGOODWIN-VM3"",
            ""HardwareIdentifier"": ""IMMYHWID==CPU:FEA9B285A798DF3A023E98CDF32FFE25452AA15C;BIOS:5424F5BD8D85EB491B17610D4EC4084D6B8F2388;MOBO:E48967F00BFF7E1462EB9D5EB756AB9D29ACE93E;GPU:33AEEA40EFC9EB3B700FA7CE24D62DBE889A1CF8;TPM:9EA0F5FAE3A4F8AA13C788B84FE1726E3169FC60;"",
            ""IsOnline"": true,
            ""LastUpdated"": ""2023-06-06T15:04:40.691866-07:00"",
            ""Manufacturer"": ""Microsoft Corporation"",
            ""OperatingSystemName"": ""Microsoft Windows 11 Pro 64-bit"",
            ""RemoveFromIotHub"": false,
            ""SerialNumber"": ""5411-5450-0933-6182-6397-0425-00""
          },
          ""OriginalChallenge"": {
            ""AgentID"": ""0d687a85-fcd4-4e95-a7b5-66bdaf7f38c8"",
            ""Exp"": ""2023-06-06T22:04:51.0995074Z"",
            ""Challenge"": ""rdmTnXhmnui/rWU/lBChXLUQCFCr8oJtns7ZUSVG/sSBk2Pe0V7EBGDWLT9IpRjybS9VAMNcJSQZNoy4xhGegg=="",
            ""ChallengeSignature"": ""HifacIhh94pN1J8yOSbpzcW4bXBEZjZiQNR+WINubhM=""
          }
  }";

  [Fact]
  public void Serialize_UsingRestSharpSerializer_Ok()
  {
    var stjWebOptions = new JsonSerializerOptions(JsonSerializerDefaults.Web);
    var challengeResp = JsonSerializer.Deserialize<ChallengeResponse>(_challengeResponseJson);
    var restSerializer = new RestSharp.Serializers.Json.SystemTextJsonSerializer();

    var serialized = restSerializer.Serialize(challengeResp);

    // These will simulate a backend deserializing the request.
    var deserialized1 = JsonSerializer.Deserialize<ChallengeResponse>(serialized!, stjWebOptions);
    Assert.Equal("0.0.1.21908", deserialized1!.AgentDeviceDetails!.AgentVersion!.ToNormalizedString());

    var deserialized2 = Newtonsoft.Json.JsonConvert.DeserializeObject<ChallengeResponse>(serialized!);
    Assert.Equal("0.0.1.21908", deserialized2!.AgentDeviceDetails!.AgentVersion!.ToNormalizedString());
  }
}
