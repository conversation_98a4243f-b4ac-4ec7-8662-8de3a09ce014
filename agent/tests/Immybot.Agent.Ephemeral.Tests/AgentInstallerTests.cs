using System.IO.Abstractions.TestingHelpers;
using System.ServiceProcess;
using Immybot.Agent.Configuration.Options;
using Immybot.Agent.Ephemeral.Resilience;
using Immybot.Agent.Ephemeral.Services;
using Immybot.Shared.Abstractions.Device.FileSystem;
using Immybot.Shared.Abstractions.Device.Processes;
using Immybot.Shared.Abstractions.Device.Windows;
using Immybot.Shared.Tests.Mocks.Windows.Registry;
using Immybot.Shared.Tests.TestingUtilities;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using TimeoutException = System.ServiceProcess.TimeoutException;

namespace Immybot.Agent.Ephemeral.Tests;

public class AgentInstallerTests
{
  private const string _agentUninstallKeyName = "{D5F96787-FBEF-4E4A-BF33-134E38538424}";
  private const string _immyPath = "C:\\Program Files (x86)\\ImmyBot";
  private const string _installationName = "ImmyBot Agent";
  private const string _processName = "ImmyBot.Agent";
  private const string _uninstallKeyPath = "Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall";
  private static readonly string _installPath =
    Path.Combine(
      Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86),
      "ImmyBot", "ImmyBot.Agent.exe");

  private static readonly string _oldInstallPath =
    Path.Combine(
      Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86),
      "ImmyBot", "ImmyBot.Agent.Service.exe");

  private readonly Uri _baseUrl = new Uri("https://foo.bar");
  private readonly Mock<IElevationChecker> _elevationChecker;
  private readonly Guid _ephemeralAgentGuid = Guid.Parse("d3e3e3e3-e3e3-e3e3-e3e3-e3e3e3e3e3e3");
  private readonly string _ephemeralAgentPath = Path.Combine(_immyPath, "ImmyBot.Agent.Ephemeral.exe");
  private readonly MockFileSystem _fileSystem;
  private readonly Mock<IFileVersionProvider> _fileVersionProvider;
  private readonly Mock<ILogger<AgentInstaller>> _logger;
  private readonly MockRegistryAccessor _registryAccessor;
  private readonly AgentInstaller _service;
  private readonly Mock<IServiceController> _serviceController;
  private readonly Mock<IHostApplicationLifetime> _appLifetime;
  private readonly CancellationTokenSource _appCts;
  private readonly Mock<IProcessManager> _processManager;
  private readonly ResiliencePipelineProviderMock<string> _resilienceProvider;

  public AgentInstallerTests()
  {
    _resilienceProvider = new ResiliencePipelineProviderMock<string>();
    _resilienceProvider.AddPipeline(AgentInstallerFileCopyPolicy.Key, AgentInstallerFileCopyPolicy.BuildPolicy);
    _fileSystem = new MockFileSystem();
    _appCts = new CancellationTokenSource();
    _appLifetime = new Mock<IHostApplicationLifetime>();
    _appLifetime
      .Setup(x => x.ApplicationStopping)
      .Returns(_appCts.Token);

    var serviceProcess = new MockProcess() {
      Id = 12,
      ProcessName = _processName,
      HasExited = false,
      OnKill =  (process, _) =>
      {
        process.HasExited = true;
      }};
    _processManager = new Mock<IProcessManager>();
    _processManager
      .Setup(x => x.GetCurrentProcess())
      .Returns(serviceProcess);

    _fileVersionProvider = new Mock<IFileVersionProvider>();
    _elevationChecker = new Mock<IElevationChecker>();
    _serviceController = new Mock<IServiceController>();
    _registryAccessor = new MockRegistryAccessor();
    _logger = new Mock<ILogger<AgentInstaller>>();
    _service = new AgentInstaller(
      _resilienceProvider,
      _fileSystem,
      _fileVersionProvider.Object,
      _elevationChecker.Object,
      MakeEphemeralAgentArgs(),
      _serviceController.Object,
      _registryAccessor,
      _processManager.Object,
      _appLifetime.Object,
      _logger.Object);
  }

  [Fact]
  public void GetInstallationState_MustBeRunAsAdministrator()
  {
    // arrange
    _elevationChecker.Setup(x => x.IsAdministrator).Returns(false);
    // act
    var result = _service.GetInstallationState();
    // assert
    Assert.False(result.IsSuccess);
    Assert.Contains("must be run as an administrator", result.Reason);
  }

  [Fact]
  public void GetInstallationState_ShouldReturnCorrectAgentInstallationDetails()
  {
    // arrange
    var service = new Mock<IService>();

    service.Setup(a => a.ServiceName).Returns(_installationName);

    var status = ServiceControllerStatus.Running;

    service.Setup(a => a.Status).Returns(status);
    service.Setup(a => a.Stop()).Callback(() => service.Setup(a => a.Status).Returns(ServiceControllerStatus.Stopped));
    _serviceController.Setup(a => a.GetServices()).Returns([service.Object]);
    _elevationChecker.Setup(x => x.IsAdministrator).Returns(true);
    _fileVersionProvider.Setup(a => a.GetFileProductVersion(It.IsAny<string>())).Returns("1.0.0");

    SetupAgentSoftwareRegistry(omitImmyAgentFromEntries: false);
    // act
    var result = _service.GetInstallationState();
    // assert
    Assert.True(result.IsSuccess);
    Assert.True(result.Value.IsApplicationRegistryEntryPresent);
    Assert.True(result.Value.IsServicePresent);
    Assert.True(result.Value.IsServiceRunning);
    Assert.Equal("1.0.0", result.Value.DetectedInstalledVersion);
    Assert.Equal("1.0.0", result.Value.RunningBinaryVersion);
  }

  public IOptions<EphemeralAgentOptions> MakeEphemeralAgentArgs()
  {
    var options = new EphemeralAgentOptions()
    {
      BackendAddress = new Uri(_baseUrl, $"api/v1/ephemeral-session/{_ephemeralAgentGuid}"),
      SessionId = _ephemeralAgentGuid,
      ImmyScriptPath = _immyPath,
      AgentAssemblyVersion = new Version(1, 0, 0),
      AgentInstanceId = Guid.NewGuid().ToString(), // Fix for CS9035: Set required member 'AgentInstanceId'
      ProviderAgentId = 1 // Fix for CS9035: Set required member 'ProviderAgentId'
    };
    return new OptionsWrapper<EphemeralAgentOptions>(options);
  }
  [Fact]
  public async Task Update_ShouldCopyTheNewBinary()
  {
    // arrange
    _elevationChecker.Setup(a => a.IsAdministrator).Returns(true);

    var service = new Mock<IService>();
    service.Setup(a => a.ServiceName).Returns(_installationName);
    service.Setup(a => a.Status).Returns(ServiceControllerStatus.Running);
    service.Setup(a => a.Stop()).Callback(() => service.Setup(a => a.Status).Returns(ServiceControllerStatus.Stopped));
    _serviceController.Setup(a => a.GetServices()).Returns([service.Object]);
    _fileVersionProvider.Setup(a => a.GetFileProductVersion(It.IsAny<string>())).Returns("1.1.0");

    SetupAgentSoftwareRegistry(omitImmyAgentFromEntries: false);

    _fileSystem.AddEmptyFile(_installPath);
    _fileSystem.AddEmptyFile(_ephemeralAgentPath);

    // act
    _ = await _service.Update();

    // assert
    var backupPath = $"{_installPath}.backup";

    Assert.True(_fileSystem.File.Exists(backupPath));
  }

  [Theory]
  [CombinatorialData]
  public async Task Update_ShouldDoNothing_WhenVersionIsUpToDate(bool isVersionUpToDate)
  {
    // arrange
    _elevationChecker.Setup(a => a.IsAdministrator).Returns(true);
    SetupAgentSoftwareRegistry(false);
    var service = new Mock<IService>();
    service.Setup(a => a.ServiceName).Returns(_installationName);
    service.Setup(a => a.Status).Returns(ServiceControllerStatus.Running);
    service.Setup(a => a.Stop()).Callback(() => service.Setup(a => a.Status).Returns(ServiceControllerStatus.Stopped));
    _serviceController.Setup(a => a.GetServices()).Returns([service.Object]);
    var version = isVersionUpToDate ? "1.0.0" : "2.0.0";
    _fileVersionProvider.Setup(a => a.GetFileProductVersion(It.IsAny<string>())).Returns(version);

    // act

    var res = await _service.Update();

    // assert

    if (isVersionUpToDate)
    {
      Assert.True(res.IsSuccess);
    }
    else
    {
      Assert.False(res.IsSuccess);
    }
  }

  [Theory]
  [CombinatorialData]
  public async Task Update_ShouldFail_WhenAgentBinaryIsNotPresent(bool isOldBinaryPresent, bool isNewBinaryPresent)
  {
    // arrange
    _elevationChecker.Setup(a => a.IsAdministrator).Returns(true);

    var service = new Mock<IService>();
    service.Setup(a => a.ServiceName).Returns(_installationName);
    service.Setup(a => a.Status).Returns(ServiceControllerStatus.Running);
    service.Setup(a => a.Stop()).Callback(() => service.Setup(a => a.Status).Returns(ServiceControllerStatus.Stopped));
    _serviceController.Setup(a => a.GetServices()).Returns([service.Object]);
    _fileVersionProvider.Setup(a => a.GetFileProductVersion(It.IsAny<string>())).Returns("1.0.0");

    _fileSystem.AddEmptyFile(_ephemeralAgentPath);

    if (isNewBinaryPresent)
    {
      _fileSystem.AddEmptyFile(_installPath);
    }

    if (isOldBinaryPresent)
    {
      _fileSystem.AddEmptyFile(_oldInstallPath);
    }

    // act
    var res = await _service.Update();

    // assert
    var reason = "No Agent binary was found in expected installation directory.";
    if (isOldBinaryPresent || isNewBinaryPresent)
    {
      Assert.NotEqual(reason, res.Reason);
    }
    else
    {
      Assert.False(res.IsSuccess);
      Assert.Equal(reason, res.Reason);
    }
  }

  [Theory]
  [CombinatorialData]
  public async Task Update_ShouldFail_WhenServiceIsNotFound(bool isServiceFound)
  {
    // arrange
    _elevationChecker.Setup(a => a.IsAdministrator).Returns(true);
    var service = new Mock<IService>();
    service.Setup(a => a.ServiceName).Returns(_installationName);
    _serviceController.Setup(a => a.GetServices()).Returns(isServiceFound ? [service.Object] : []);
    _fileVersionProvider.Setup(a => a.GetFileProductVersion(It.IsAny<string>())).Returns("1.0.0");

    _fileSystem.AddEmptyFile(_ephemeralAgentPath);
    _fileSystem.AddEmptyFile(_installPath);

    // act
    var res = await _service.Update();

    // assert
    var reason = $"Agent Service '{_installationName}' was not found.";
    if (isServiceFound)
    {
      Assert.NotEqual(reason, res.Reason);
    }
    else
    {
      Assert.False(res.IsSuccess);
      Assert.Equal(reason, res.Reason);
    }
  }

  [Fact]
  public async Task Update_ShouldRevert_WhenFailureOccurs()
  {
    // arrange
    _elevationChecker.Setup(a => a.IsAdministrator).Returns(true);

    var service = new Mock<IService>();
    service.Setup(a => a.ServiceName).Returns(_installationName);
    service.Setup(a => a.Status).Returns(ServiceControllerStatus.Running);
    service.Setup(a => a.Stop()).Callback(() => service.Setup(a => a.Status).Returns(ServiceControllerStatus.Stopped));
    _serviceController.Setup(a => a.GetServices()).Returns([service.Object]);
    _fileVersionProvider.Setup(a => a.GetFileProductVersion(It.IsAny<string>())).Returns("1.0.0");

    _fileSystem.AddEmptyFile(_ephemeralAgentPath);
    _fileSystem.AddEmptyFile(_installPath);

    // act
    var res = await _service.Update();

    // assert
    var backupPath = $"{_installPath}.backup";

    // should revert the file
    Assert.True(_fileSystem.File.Exists(_installPath));
    Assert.False(_fileSystem.File.Exists(backupPath));

    // should start the service
    service.Verify(a => a.Start(), Times.Once);

    Assert.False(res.IsSuccess);
  }

  [Fact]
  public async Task Update_ShouldSetTheNewBinaryVersionInTheRegistryAsync()
  {
    var service = new Mock<IService>();

    service.Setup(a => a.ServiceName).Returns(_installationName);

    var status = ServiceControllerStatus.Running;

    service.Setup(a => a.Status).Returns(status);
    service.Setup(a => a.Stop()).Callback(() => service.Setup(a => a.Status).Returns(ServiceControllerStatus.Running));
    _serviceController.Setup(a => a.GetServices()).Returns([service.Object]);
    _elevationChecker.Setup(x => x.IsAdministrator).Returns(true);
    _fileSystem.AddEmptyFile(_ephemeralAgentPath);
    _fileSystem.AddEmptyFile(_installPath);
    _fileVersionProvider.Setup(a => a.GetFileProductVersion(It.IsAny<string>())).Returns("2.0.0");

    SetupAgentSoftwareRegistry(omitImmyAgentFromEntries: false);
    using var agentUninstallKey = GetAgentUninstallKey();
    // act
    var result = await _service.Update();
    // assert
    Assert.True(result.IsSuccess);
    Assert.True(result.Value.IsApplicationRegistryEntryPresent);
    Assert.True(result.Value.IsServicePresent);
    Assert.True(result.Value.IsServiceRunning);
    Assert.Equal("2.0.0", agentUninstallKey.GetValue("DisplayVersion"));
    Assert.Equal("2.0.0", result.Value.DetectedInstalledVersion);
    Assert.Equal("2.0.0", result.Value.RunningBinaryVersion);
  }

  [Fact]
  public async Task Update_ShouldStartTheService_AfterSuccessfullyUpdatingTheBinaryAndRegistryAsync()
  {
    var service = new Mock<IService>();

    service.Setup(a => a.ServiceName).Returns(_installationName);

    var status = ServiceControllerStatus.Running;

    service.Setup(a => a.Status).Returns(status);
    service.Setup(a => a.Stop()).Callback(() => service.Setup(a => a.Status).Returns(ServiceControllerStatus.Running));
    _serviceController.Setup(a => a.GetServices()).Returns([service.Object]);
    _elevationChecker.Setup(x => x.IsAdministrator).Returns(true);
    _fileSystem.AddEmptyFile(_ephemeralAgentPath);
    _fileSystem.AddEmptyFile(_installPath);
    _fileVersionProvider.Setup(a => a.GetFileProductVersion(It.IsAny<string>())).Returns("2.0.0");

    SetupAgentSoftwareRegistry(omitImmyAgentFromEntries: false);
    using var agentUninstallKey = GetAgentUninstallKey();
    // act
    var result = await _service.Update();
    // assert
    Assert.True(result.IsSuccess);
    Assert.True(result.Value.IsApplicationRegistryEntryPresent);
    Assert.True(result.Value.IsServicePresent);
    Assert.True(result.Value.IsServiceRunning);
    Assert.Equal("2.0.0", agentUninstallKey.GetValue("DisplayVersion"));
    service.Verify(x => x.Start(), Times.Once);
    Assert.Equal("2.0.0", result.Value.DetectedInstalledVersion);
    Assert.Equal("2.0.0", result.Value.RunningBinaryVersion);
  }

  [Theory]
  [CombinatorialData]
  public async Task Update_ShouldStopService_WhenServiceIsRunning(bool isServiceRunning)
  {
    // arrange
    _elevationChecker.Setup(a => a.IsAdministrator).Returns(true);

    var service = new Mock<IService>();
    service.Setup(a => a.ServiceName).Returns(_installationName);
    var status = isServiceRunning ? ServiceControllerStatus.Running : ServiceControllerStatus.Stopped;
    service.Setup(a => a.Status).Returns(status);
    service.Setup(a => a.Stop()).Callback(() => service.Setup(a => a.Status).Returns(ServiceControllerStatus.Stopped));
    _serviceController.Setup(a => a.GetServices()).Returns([service.Object]);
    _fileVersionProvider.Setup(a => a.GetFileProductVersion(It.IsAny<string>())).Returns("1.0.0");

    _fileSystem.AddEmptyFile(_ephemeralAgentPath);
    _fileSystem.AddEmptyFile(_installPath);

    // act
    _ = await _service.Update();

    // assert
    service.Verify(a => a.Stop(), isServiceRunning ? Times.AtLeastOnce : Times.Never);
  }

  [Fact]
  public async Task Update_ShouldKillProcesses_WhenStopServiceTimesOut()
  {
    var immybotProcess = new MockProcess() {
      Id = 41,
      ProcessName = _processName,
      HasExited = false,
      OnKill =  (process, _) =>
      {
        process.HasExited = true;
      }};

    // arrange
    _elevationChecker.Setup(x => x.IsAdministrator).Returns(true);

    var service = new Mock<IService>();
    service.Setup(x => x.ServiceName).Returns(_installationName);
    service.Setup(x => x.Status).Returns(ServiceControllerStatus.Running);
    service
      .Setup(x => x.Stop())
      .Callback(() =>
      {
        if (!immybotProcess.HasExited)
        {
          throw new TimeoutException();
        }

        service.Setup(x => x.Status).Returns(ServiceControllerStatus.Stopped);
      });

    _serviceController.Setup(x => x.GetServices()).Returns([service.Object]);
    _fileVersionProvider.Setup(x => x.GetFileProductVersion(It.IsAny<string>())).Returns("1.0.0");
    _processManager
      .Setup(x => x.GetProcessesByName(_processName))
      .Returns([immybotProcess]);

    _fileSystem.AddEmptyFile(_ephemeralAgentPath);
    _fileSystem.AddEmptyFile(_installPath);

    // act
    _ = await _service.Update();

    // assert
    // Should have called stop twice.  The first time should have timed out.
    // The second should have succeeded after stopping processes.
    service.Verify(a => a.Stop(), Times.Exactly(2));
    Assert.True(immybotProcess.HasExited);
    Assert.Equal(ServiceControllerStatus.Stopped, service.Object.Status);
  }

  private IRegistryKey GetAgentUninstallKey()
  {
    var uninstallAgentKeyPath = $"{_uninstallKeyPath}\\{_agentUninstallKeyName}";
    return _registryAccessor.LocalMachine.OpenSubKey(uninstallAgentKeyPath, true)
      ?? throw new InvalidOperationException("Agent uninstall key was not set up.");
  }

  private void SetupAgentSoftwareRegistry(bool omitImmyAgentFromEntries)
  {
    using var uninstallKey = _registryAccessor.LocalMachine.CreateSubKey(_uninstallKeyPath, true);

    if (omitImmyAgentFromEntries)
    {
      return;
    }

    using var agentKey = uninstallKey.CreateSubKey(_agentUninstallKeyName, true);
    agentKey.SetValue("DisplayName", "ImmyBot Agent");
    agentKey.SetValue("DisplayVersion", "1.0.0");
  }
}
