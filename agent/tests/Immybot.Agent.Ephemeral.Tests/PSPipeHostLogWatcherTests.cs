using System.Collections.Concurrent;
using Immybot.Agent.Ephemeral.Services;
using Immybot.Agent.Ephemeral.Services.PowerShellExecutionHost;
using Immybot.Agent.Tests.TestingUtilities;
using Immybot.Shared.Primitives;
using Serilog.Events;
using Xunit.Abstractions;
using ILoggerFactory = Microsoft.Extensions.Logging.ILoggerFactory;

namespace Immybot.Agent.Ephemeral.Tests;

public class PSPipeHostLogWatcherTests
{
  private readonly string _rawSampleTestData =
  """
{"@mt":"440 Powershell Named-Pipe Host {PSPipeHostVersion} Started. (Associated Agent PID: {AgentPID} [{PipeName}])","PSPipeHostVersion":"V0.2.0","@t":"2023-11-30T20:34:36.2723294Z","PipeName":"21bba0741d7d431085f0bc5b963d7ac4","Scope":[{"ContextTag":"48d3529b-a27a-4f73-9c74-d5eb7c072755"}],"AgentPID":768}
{"@mt":"Attempting connection to with {TimeoutSeconds} timeout)...","Scope":[{"ContextTag":"4d3d8a5c-eda7-4739-b9a1-79dbc96401f1"}],"TimeoutSeconds":10,"@t":"2023-11-30T20:34:36.3033368Z"}
{"@mt":"Connected to Ephemeral Agent Pipe server.","Scope":[{"ContextTag":"0750fc16-63ec-4c65-aaab-9eb5d4aa4645"}],"@t":"2023-11-30T20:34:36.3253415Z"}
{"@mt":"Command {Type} (Execute w/ Params) requested.","Type":2,"@t":"2023-11-30T20:34:36.3383456Z"}
{"@mt":"Reading in Params...","@t":"2023-11-30T20:34:36.3413480Z"}
{"@mt":"Read input params: {InputParams}. Passing to Invoker","Scope":[{"ContextTag":"78dcc771-4201-4952-97aa-cdcfb022396f"}],"InputParams":{"Parameters":{"DebugPreference":{"value":0,"Value":"SilentlyContinue"},"TenantName":"Girl Scouts Heart Of Central California","ComputerName":"GSSERVICES-N","IsPortable":false,"IsServer":true,"TenantId":15,"AzureTenantId":"60b7cd53-8241-4f03-b7fa-3c85a14dc485","TenantSlug":"GSHCC","VerbosePreference":{"value":0,"Value":"SilentlyContinue"},"DeviceId":"81420e42-3fdb-0d72-c7db-42ec7644c05c","RebootPreference":"Normal"},"TenantName":"Girl Scouts Heart Of Central California","IsServer":true,"ComputerName":"GSSERVICES-N","IsPortable":false,"RebootPreference":"Normal","TenantId":15,"AzureTenantId":"60b7cd53-8241-4f03-b7fa-3c85a14dc485","TenantSlug":"GSHCC","DeviceId":"81420e42-3fdb-0d72-c7db-42ec7644c05c","ArgumentList":[]},"@t":"2023-11-30T20:34:36.4123642Z"}
{"@mt":"Got Record of type: {RecordType}","RecordType":"System.IO.DirectoryInfo","@t":"2023-11-30T20:34:36.7284375Z"}
{"@mt":"Script execution completed","@t":"2023-11-30T20:34:36.8684732Z"}
{"@mt":"Completed Command scriptexec request","Scope":[{"ContextTag":"ffa2deef-f8aa-4d0d-92d8-bcf7585f8601"}],"@t":"2023-11-30T20:34:36.9634955Z"}
{"@mt":"Command {Type} (Execute w/ Params) requested.","Type":2,"@t":"2023-11-30T20:34:47.2012497Z"}
{"@mt":"Reading in Params...","@t":"2023-11-30T20:34:47.2012497Z"}
{"@mt":"Read input params: {InputParams}. Passing to Invoker","Scope":[{"ContextTag":"78dcc771-4201-4952-97aa-cdcfb022396f"}],"InputParams":{"Parameters":{"DebugPreference":{"value":0,"Value":"SilentlyContinue"},"TenantName":"Girl Scouts Heart Of Central California","ComputerName":"GSSERVICES-N","IsPortable":false,"IsServer":true,"TenantId":15,"AzureTenantId":"60b7cd53-8241-4f03-b7fa-3c85a14dc485","TenantSlug":"GSHCC","VerbosePreference":{"value":0,"Value":"SilentlyContinue"},"DeviceId":"81420e42-3fdb-0d72-c7db-42ec7644c05c","RebootPreference":"Normal"},"TenantName":"Girl Scouts Heart Of Central California","IsServer":true,"ComputerName":"GSSERVICES-N","IsPortable":false,"RebootPreference":"Normal","TenantId":15,"AzureTenantId":"60b7cd53-8241-4f03-b7fa-3c85a14dc485","TenantSlug":"GSHCC","DeviceId":"81420e42-3fdb-0d72-c7db-42ec7644c05c","ArgumentList":[]},"@t":"2023-11-30T20:34:47.2102488Z"}
{"@mt":"Got Record of type: {RecordType}","RecordType":"System.IO.DirectoryInfo","@t":"2023-11-30T20:34:47.2942678Z"}
{"@mt":"Got Record of type: {RecordType}","RecordType":"System.IO.FileInfo","@t":"2023-11-30T20:34:47.3332827Z"}
{"@mt":"Got Record of type: {RecordType}","RecordType":"System.Management.Automation.ErrorRecord","@t":"2023-11-30T20:35:16.5439585Z"}
{"@mt":"Had errors type 3","@l":"Warning","@t":"2023-11-30T20:35:16.6139766Z","@x":"A parameter cannot be found that matches parameter name \u0027asc\u0027."}
{"@mt":"Script execution completed","@t":"2023-11-30T20:35:16.6269780Z"}
{"@mt":"Completed Command scriptexec request","Scope":[{"ContextTag":"ffa2deef-f8aa-4d0d-92d8-bcf7585f8601"}],"@t":"2023-11-30T20:35:16.6299799Z"}
{"@mt":"ReadByte failed, pipe likely closed. Exiting.","Scope":[{"ContextTag":"5168a74f-3cb6-4c34-944d-2b1db625d8d0"}],"@l":"Error","@t":"2023-11-30T20:38:00.3565939Z","@x":"ErrorRecord: Exception calling \"ReadByte\" with \"0\" argument(s): \"Unable to read beyond the end of the stream."}
""";
  private string[] SampleTestData => _rawSampleTestData.Split('\n');
  private readonly ILoggerFactory _loggerFactory;
  public PSPipeHostLogWatcherTests(ITestOutputHelper helper)
  {
    _loggerFactory = new XunitLoggerFactory(helper);
  }

  private static IDisposable DisposableEnvironment(out string environmentPath)
  {
    var tempDir = Directory.CreateTempSubdirectory("ImmyPSPipeHostLogWatcherUnitTests").FullName;
    environmentPath = tempDir;
    return Disposable.Create(() => Directory.Delete(tempDir, recursive: true));
  }

  [Fact]
  public void LogWatcher_ThrowsExceptionOnNonExistantFile()
  {
    using var _ = DisposableEnvironment(out var environmentPath);
    Assert.Throws<FileNotFoundException>(() => new PSPipeHostLogWatcher(
      Path.Combine(environmentPath, "nonexistantfile.clef"),
      CancellationToken.None,
      evt => {
        Assert.Fail("Received an event on a file that doesn't exist!");
        return Task.CompletedTask;
      }));
  }

  [Fact]
  public async Task LogWatcher_ReadsAllLogEventsInFile()
  {
    using var _ = DisposableEnvironment(out var environmentPath);

    // Create a file with some sample data
    var filePath = Path.Combine(environmentPath, "sample.clef");
    await File.AppendAllLinesAsync(filePath, SampleTestData);

    // Create a log watcher and read all the events
    var logEventsReceived = new ConcurrentBag<LogEvent>();
    using var logWatcher = new PSPipeHostLogWatcher(filePath, CancellationToken.None,
      evt => {
        logEventsReceived.Add(evt);
        return Task.CompletedTask;
      });

    // Ensure we read all the events
    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
    while (!cts.IsCancellationRequested && logEventsReceived.Count < SampleTestData.Length)
      await Task.Delay(100);

    Assert.Equal(SampleTestData.Length, logEventsReceived.Count);
  }

  [Fact]
  public async Task LogWatcher_WithPreCancellation_DoesntOpenFile()
  {
    using var _ = DisposableEnvironment(out var environmentPath);

    // Create a file with some sample data
    var filePath = Path.Combine(environmentPath, "sample.clef");
    await File.AppendAllLinesAsync(filePath, SampleTestData);
    var cts = new CancellationTokenSource();
    await cts.CancelAsync();

    using var logWatcher = new PSPipeHostLogWatcher(filePath, cts.Token,
      evt => {
        Assert.Fail("Received an event even though the token was cancelled!");
        return Task.CompletedTask;
      });

    await logWatcher.ReaderTaskCompletion.WaitAsync(TimeSpan.FromSeconds(1));
    // Ensure we can delete the file (indicating the file handle is/was closed)
    File.Delete(filePath);
  }

  [Fact]
  public async Task LogWatcher_Cancellation_ResolvesTask()
  {
    using var _ = DisposableEnvironment(out var environmentPath);

    // Create a file with some sample data
    var filePath = Path.Combine(environmentPath, "sample.clef");
    // Drop an empty log file
    await File.WriteAllTextAsync(filePath, "");

    var cts = new CancellationTokenSource();

    using var logWatcher = new PSPipeHostLogWatcher(filePath, cts.Token,
      evt => {
        Assert.Fail("Received an event even though no events were in log file!");
        return Task.CompletedTask;
      });

    await cts.CancelAsync();
    await logWatcher.ReaderTaskCompletion.WaitAsync(TimeSpan.FromSeconds(2));
  }
  [Fact]
  public async Task LogWatcher_Disposal_ResolvesTask()
  {
    using var _ = DisposableEnvironment(out var environmentPath);

    // Create a file with some sample data
    var filePath = Path.Combine(environmentPath, "sample.clef");
    // Drop an empty log file
    await File.WriteAllTextAsync(filePath, "");

    using var logWatcher = new PSPipeHostLogWatcher(filePath, CancellationToken.None,
      evt => {
        Assert.Fail("Received an event even though no events were in log file!");
        return Task.CompletedTask;
      });

    await logWatcher.DisposeAsync();
    await logWatcher.ReaderTaskCompletion.WaitAsync(TimeSpan.FromSeconds(2));
  }

  [Fact]
  public async Task LogWatcher_AsyncDisposal_ResolvesTask()
  {
    using var _ = DisposableEnvironment(out var environmentPath);

    // Create a file with some sample data
    var filePath = Path.Combine(environmentPath, "sample.clef");
    // Drop an empty log file
    await File.WriteAllTextAsync(filePath, "");

    using var logWatcher = new PSPipeHostLogWatcher(filePath, CancellationToken.None,
      evt => {
        Assert.Fail("Received an event even though no events were in log file!");
        return Task.CompletedTask;
      });

    await logWatcher.DisposeAsync();
    await logWatcher.ReaderTaskCompletion.WaitAsync(TimeSpan.FromSeconds(2));
  }

  [Fact]
  public async Task LogWatcher_ReaderTask_ExitsAndCompletesAfterInvokingEvent()
  {
    using var _ = DisposableEnvironment(out var environmentPath);

    // Create a file with some sample data
    var filePath = Path.Combine(environmentPath, "sample.clef");
    // Drop a full log file
    await File.AppendAllLinesAsync(filePath, SampleTestData);

    var cts = new CancellationTokenSource();
    // Each event takes 1 second to process. If we the reader task takes > 1.5 second to complete,
    // then we know it is not checking between events if it should exit after receiving a batch of events from stream reader.
    using var logWatcher = new PSPipeHostLogWatcher(filePath, cts.Token, async evt => await Task.Delay(TimeSpan.FromSeconds(1)));
    await cts.CancelAsync();
    await logWatcher.ReaderTaskCompletion.WaitAsync(TimeSpan.FromSeconds(1.5f));
  }

  [Fact]
  public async Task LogWatcher_ReaderTask_ExitsIfToldToAfterEndOfFileIsReached()
  {
    using var _ = DisposableEnvironment(out var environmentPath);

    // Create a file with some sample data
    var filePath = Path.Combine(environmentPath, "sample.clef");
    // Drop a full log file
    await File.AppendAllLinesAsync(filePath, SampleTestData);

    // After all the events are read, the reader task should exit w/o being externally cancelled or disposed
    using var logWatcher = new PSPipeHostLogWatcher(filePath, CancellationToken.None, evt => Task.CompletedTask, exitAfterReachingEof: true);
    await logWatcher.ReaderTaskCompletion.WaitAsync(TimeSpan.FromSeconds(1));
  }

  [Fact]
  public async Task LogWatcher_TailsLogEventsInFile()
  {
    using var _ = DisposableEnvironment(out var environmentPath);

    // Create a file with some sample data
    var filePath = Path.Combine(environmentPath, "sample.clef");
    await File.AppendAllLinesAsync(filePath, SampleTestData.SkipLast(2));

    // Create a log watcher and read all the events
    var logEventsReceived = new ConcurrentBag<LogEvent>();
    using var logWatcher = new PSPipeHostLogWatcher(filePath, CancellationToken.None, evt => { logEventsReceived.Add(evt); return Task.CompletedTask; });

    // Append some more data to the file after some time
    while (logEventsReceived.Count < SampleTestData.Length - 2)
      await Task.Delay(100);

    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
    while (!cts.IsCancellationRequested && logEventsReceived.Count < SampleTestData.Length)
      await Task.Delay(100);

    await File.AppendAllLinesAsync(filePath, SampleTestData.TakeLast(2));

    using var cts2 = new CancellationTokenSource(TimeSpan.FromSeconds(5));
    while (!cts2.IsCancellationRequested && logEventsReceived.Count < SampleTestData.Length)
      await Task.Delay(100);

    Assert.Equal(SampleTestData.Length, logEventsReceived.Count);
  }
}
