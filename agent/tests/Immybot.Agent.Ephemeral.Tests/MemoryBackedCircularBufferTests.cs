using Immybot.Agent.Ephemeral.Services.MemoryBackedCircularBuffer;
using MemoryPack;

namespace Immybot.Agent.Ephemeral.Tests;

[MemoryPackable]
public partial class Person
{
  public required string Name { get; set; }
}

public class MemoryBackedCircularBufferTests
{
  [Fact]
  public void BufferCanHoldMultipleItems()
  {
    // Arrange
    var buffer = new MemoryBackedCircularBuffer<Person>(capacityInBytes: 74);
    var insertItems = new Person[]
    {
                new () { Name = "<PERSON>" },
                new () { Name = "<PERSON>" },
                new () { Name = "<PERSON>" },
                new () { Name = "<PERSON>" },
                new () { Name = "<PERSON>" },
    };
    // Act
    foreach (var person in insertItems)
    {
      buffer.PushBack(person);
    }
    // Assert
    var results = buffer.ToArray();

    // result of enumerating the buffer will allocate a new array of items in memory,
    // and thus will not be 'equal' to the original array, but the items' values should be the same
    Assert.Equivalent(insertItems, results, strict: true);
  }

  [Fact]
  public void BufferWillReplaceOldestItemIfOutOfMemory()
  {
    // Arrange
    var buffer = new MemoryBackedCircularBuffer<Person>(capacityInBytes: 74);

    // All of these elements will consume 74 bytes when stored
    var insertItems = new Person[]
    {
                new () { Name = "Christian" },
                new () { Name = "Jared" },
                new () { Name = "Colin" },
                new () { Name = "Darren" },
                new () { Name = "Nick" },
    };
    // Act
    foreach (var person in insertItems)
    {
      buffer.PushBack(person);
    }
    // Pushing this element in should force the buffer to replace the oldest element (Christian)
    buffer.PushBack(new() { Name = "New Guy" });
    var resultsAfterOverwrite = buffer.ToArray();
    // Assert
    Assert.DoesNotContain(resultsAfterOverwrite, p => p.Name == "Christian");
    // loop over insertItems skipping first element and Assert that the rest are still present
    for (int i = 1; i < insertItems.Length; i++)
    {
      Assert.Contains(resultsAfterOverwrite, p => p.Name == insertItems[i].Name);
    }
  }
  [Fact]
  public void BufferCanFragmentItemAroundEndofMemory()
  {
    // Arrange
    var buffer = new MemoryBackedCircularBuffer<Person>(capacityInBytes: 70);
    // All of these elements will consume 74 bytes when stored. This means, since we only have 70 bytes of memory,
    // that the first element (Nick) will be dropped, and the last element (Darren) will be fragmented around the end of the buffer
    // Darren Person is 15 bytes long, so the first 11 bytes of Darren will be stored at the end of the buffer, and the last 4 bytes will be stored at the beginning of the buffer
    var insertItems = new Person[]
    {
                new () { Name = "Nick" },
                new () { Name = "Christian" },
                new () { Name = "Jared" },
                new () { Name = "Colin" },
                new () { Name = "Darren" },
    };
    // Act
    foreach (var person in insertItems)
    {
      buffer.PushBack(person);
    }
    // Assert
    var results = buffer.ToArray();
    // Assert
    Assert.DoesNotContain(results, p => p.Name == "Nick");
    Assert.Contains(results, p => p.Name == "Darren");
  }

  [Fact]
  public void BufferCanFragmentItemAroundEndofMemoryAndHandleNextInserts()
  {
    // Arrange
    var buffer = new MemoryBackedCircularBuffer<Person>(capacityInBytes: 70);
    // All of these elements will consume 114 bytes when stored. This means, since we only have 70 bytes of memory,
    // that the first element (Nick) will be dropped, and the last element (Darren) will be fragmented around the end of the buffer
    // Darren Person is 15 bytes long, so the first 11 bytes of Darren will be stored at the end of the buffer, and the last 4 bytes will be stored at the beginning of the buffer.
    // Then, New Guy and Another New Guy will be inserted, causing Christian and Jared to be dropped as they 
    var insertItems = new Person[]
    {
                new () { Name = "Nick" },
                new () { Name = "Christian" },
                new () { Name = "Jared" },
                new () { Name = "Colin" },
                new () { Name = "Darren" },
                new () { Name = "New Guy" },
                new () { Name = "Another New Guy" },
    };
    // Act
    foreach (var person in insertItems)
    {
      buffer.PushBack(person);
    }
    // Assert
    var results = buffer.ToArray();
    // Assert
    Assert.DoesNotContain(results, p => p.Name == "Nick");
    Assert.Contains(results, p => p.Name == "Darren");
    Assert.Contains(results, p => p.Name == "New Guy");
    Assert.Contains(results, p => p.Name == "Another New Guy");
  }

  [Theory]
  [InlineData(10)]
  [InlineData(50)]
  [InlineData(100)]
  [InlineData(150)]
  [InlineData(200)]
  [InlineData(50000)]
  public void BufferCanHandleMultipleBufferRegionOverwrites(int overwriteCount)
  {
    // Arrange
    uint buffCapacity = 74;
    var buffer = new MemoryBackedCircularBuffer<Person>(buffCapacity);
    // All of these elements will consume 114 bytes when stored. This means, since we only have 70 bytes of memory,
    // that the first element (Nick) will be dropped, and the last element (Darren) will be fragmented around the end of the buffer
    // Darren Person is 15 bytes long, so the first 11 bytes of Darren will be stored at the end of the buffer, and the last 4 bytes will be stored at the beginning of the buffer.
    // This test will insert the same items over and over again until the buffer has been overwritten at least the number of times requested.
    // This is to ensure that the buffer can handle multiple overwrites of the same region of memory with fragmentation across the entire buffer.
    var insertItems = new Person[]
    {
                new () { Name = "Christian" },
                new () { Name = "Jared" },
                new () { Name = "Colin" },
                new () { Name = "Darren" },
                new () { Name = "Nick" },
    };
    // Act
    // Continuously insert items until we've overwritten the entire buffer at least the number of times requested
    while (buffer.TotalBytesWritten < buffCapacity * overwriteCount)
    {
      foreach (var person in insertItems)
      {
        buffer.PushBack(person);
      }

      // Assert
      var results = buffer.ToArray();
      // Assert
      //Assert.Contains(results, f => insertItems.Any(y => y.Name == f.Name));
      Assert.Equivalent(insertItems, results, strict: true);
    }
  }
}
