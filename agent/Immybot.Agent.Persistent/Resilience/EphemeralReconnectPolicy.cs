using <PERSON>;

namespace Immybot.Agent.Persistent.Resilience;

/// <summary>
///  A resilience policy for ephemeral connection retry attempts.
/// </summary>
public static class EphemeralReconnectPolicy
{
  public const string Key = nameof(EphemeralReconnectPolicy);
  private static readonly TimeSpan _delay = TimeSpan.FromSeconds(2);
  private static readonly TimeSpan _maxDelay = TimeSpan.FromSeconds(30);
  private static readonly int _maxRetryAttempts = 20;
  public static void BuildPolicy(ResiliencePipelineBuilder builder)
  {
    builder.AddRetry(new Polly.Retry.RetryStrategyOptions()
    {
      BackoffType = DelayBackoffType.Exponential,
      UseJitter = true,
      Delay = _delay,
      MaxDelay = _maxDelay,
      MaxRetryAttempts = _maxRetryAttempts,
    });
  }
}
