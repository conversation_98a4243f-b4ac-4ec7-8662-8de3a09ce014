
using System.Diagnostics;
using System.IO.Abstractions;
using System.Security.Cryptography;
using Immybot.Agent.Configuration.Options;
using Immybot.Agent.Http;
using Immybot.Shared.Abstractions.Device;
using Immybot.Shared.Abstractions.Device.Processes;
using Immybot.Shared.Abstractions.Device.Windows.Threading;
using Immybot.Shared.DataContracts.Agent;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Immybot.Agent.Persistent.Services;

/// <summary>
/// Allows the agent to perform an in-place update of itself.
/// </summary>
public interface IPersistentAgentUpdater
{
  /// <summary>
  /// Ensures that the agent is running the latest version.
  /// If the agent is outdated, a new version will be downloaded and installed.
  /// </summary>
  /// <param name="cancellationToken"></param>
  /// <returns></returns>
  Task EnsureLatestVersion(CancellationToken cancellationToken);
}

internal class PersistentAgentUpdater(
  IHttpClientFactory httpClientFactory,
  IBackendApi backendApi,
  ISystemEnvironment systemEnvironment,
  IFileSystem fileSystem,
  IProcessManager processManager,
  IGlobalSemaphoreProvider globalSemaphoreProvider,
  IFailedUpdatesManager failedUpdatesManager,
  IOptionsMonitor<DeveloperOptions> devOptions,
  ILogger<PersistentAgentUpdater> logger) : IPersistentAgentUpdater
{
  private readonly IBackendApi _backendApi = backendApi;
  private readonly IOptionsMonitor<DeveloperOptions> _devOptions = devOptions;
  private readonly IFileSystem _fileSystem = fileSystem;
  private readonly IHttpClientFactory _httpClientFactory = httpClientFactory;
  private readonly ILogger<PersistentAgentUpdater> _logger = logger;
  private readonly IProcessManager _processManager = processManager;
  private readonly ISystemEnvironment _systemEnvironment = systemEnvironment;
  private readonly IGlobalSemaphoreProvider _globalSemaphoreProvider = globalSemaphoreProvider;
  private readonly IFailedUpdatesManager _failedUpdatesManager = failedUpdatesManager;

  public async Task EnsureLatestVersion(CancellationToken cancellationToken)
  {
    _logger.LogInformation("Checking for agent updates.");

    if (_devOptions.CurrentValue.DisableAgentUpdates)
    {
      _logger.LogInformation("Skipping update check because agent updates are disabled.");
      return;
    }

    Debug.Assert(
      !Debugger.IsAttached,
      "Update check is running while debugger is attached.",
      "Did you forget to set DisableAgentUpdates in DeveloperOptions?");

    using var updateLock = _globalSemaphoreProvider.GetSecuredSemaphore(
      name: WellKnownSemaphoreNames.PersistentAgentUpdate,
      initialCount: 1,
      maximumCount: 1,
      createdNew: out _);

    if (!updateLock.WaitOne(0))
    {
      _logger.LogInformation("Another update check is already in progress.  Aborting check.");
      return;
    }

    try
    {
      if (!CheckIfDriveSpaceAvailable())
      {
        return;
      }

      var remoteHashResponse = await _backendApi.GetAgentSha256Hash(cancellationToken);

      if (!await _failedUpdatesManager.ShouldInstallUpdate(remoteHashResponse.AgentSha256Hash, cancellationToken))
      {
        return;
      }

      if (!await CheckIfRemoteHashIsDifferent(remoteHashResponse, cancellationToken))
      {
        return;
      }

      if (!await DownloadLatestVersion(remoteHashResponse, cancellationToken))
      {
        return;
      }

      await LaunchInstaller(remoteHashResponse.AgentSha256Hash, cancellationToken);
    }
    finally
    {
      updateLock.Release();
    }
  }

  private bool CheckIfDriveSpaceAvailable()
  {
    _logger.LogInformation("Checking for available space on primary drive.");

    if (!_systemEnvironment.TryGetPrimaryDrive(out var primaryDrive))
    {
      _logger.LogWarning("Failed to get primary drive info. Optimistically continuing with update.");
      return true;
    }

    var exeFile = _fileSystem.FileInfo.New(_systemEnvironment.StartupExe);
    // We'll be extremely conservative and assume we'll need at least 4x
    // the current EXE size for potential size increases, unpacking, etc.
    var requiredFreeSpace = exeFile.Length * 4;

    if (primaryDrive.AvailableFreeSpace < requiredFreeSpace)
    {
      _logger.LogWarning(
        "Skipping update check because there is insufficient free space on the drive. " +
        "Remaining Space: {AvailableSpace:N0}",
        primaryDrive.AvailableFreeSpace);
      return false;
    }

    return true;
  }

  private async Task<bool> CheckIfRemoteHashIsDifferent(
    GetAgentSha256HashResponse remoteHashResponse,
    CancellationToken cancellationToken)
  {
    _logger.LogInformation("Comparing hash of local binary to remote.");

    using var exeFs = _fileSystem.File.OpenRead(_systemEnvironment.StartupExe);
    var localHash = await SHA256.HashDataAsync(exeFs, cancellationToken);
    var localHexString = Convert.ToHexString(localHash);

    if (localHexString.Equals(remoteHashResponse.AgentSha256Hash, StringComparison.OrdinalIgnoreCase))
    {
      _logger.LogInformation("Remote hash matches local hash.  Agent is up-to-date.");
      return false;
    }

    _logger.LogInformation("Remote hash differs from local hash.  Downloading new agent version.");
    return true;
  }

  private async Task<bool> DownloadLatestVersion(
    GetAgentSha256HashResponse remoteHashResponse,
    CancellationToken cancellationToken)
  {
    _logger.LogInformation("Downloading new agent version.");

    // If we run into file lock issues, we can implement a file unlocker.
    // I have an example of this in another project.  This would probably
    // be a rare occurrence, though, and it'd just retry again later.
    // Reference: https://github.com/bitbound/Medior/blob/master/Medior/Services/FileLockSearcher.cs
    if (_fileSystem.File.Exists(PathHelpers.AgentExeTempPath))
    {
      _fileSystem.File.Delete(PathHelpers.AgentExeTempPath);
    }

    using var httpClient = _httpClientFactory.CreateClient();
    await using var responseStream = await httpClient.GetStreamAsync(remoteHashResponse.DownloadUrl, cancellationToken);
    await using var fs = _fileSystem.FileStream.New(PathHelpers.AgentExeTempPath, FileMode.Create, FileAccess.ReadWrite);
    await responseStream.CopyToAsync(fs, cancellationToken);
    return true;
  }

  private async Task LaunchInstaller(string sha256BinaryHash, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("Launching agent installer.");
      var startedProcess = _processManager.Start(PathHelpers.AgentExeTempPath, "service install");

      if (startedProcess.HasExited)
      {
        _logger.LogError("Installer process failed to start.");
        await _failedUpdatesManager.MarkUpdateAsFailed(sha256BinaryHash, cancellationToken);
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Failed to launch agent installer.");
      await _failedUpdatesManager.MarkUpdateAsFailed(sha256BinaryHash, cancellationToken);
    }
  }
}
