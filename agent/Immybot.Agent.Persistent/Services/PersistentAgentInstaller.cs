using System.IO.Abstractions;
using Microsoft.Extensions.Logging;
using Immybot.Shared.Abstractions.Device;
using Polly.Registry;
using Immybot.Agent.Persistent.Resilience;
using System.ServiceProcess;
using Immybot.Shared.Abstractions.Device.Processes;
using System.Security.Cryptography;
using Polly;
using Immybot.Shared.Abstractions.Device.Windows.Threading;
using Immybot.Shared.Abstractions.Device.FileSystem;
using System.Diagnostics.CodeAnalysis;
using DotNext.Threading;
using Immybot.Shared.Abstractions.Device.Windows;

namespace Immybot.Agent.Persistent.Services;

/// <summary>
/// Installs the currently-executing agent as a persistent service.
/// </summary>
public interface IPersistentAgentInstaller
{
  /// <summary>
  /// Installs the currently-executing agent as a persistent service.
  /// This requires an existing agent configuration file to be present
  /// on the system.
  /// </summary>
  /// <param name="cancellationToken"></param>
  /// <returns></returns>
  Task Install(CancellationToken cancellationToken);
}
internal class PersistentAgentInstaller(
  ResiliencePipelineProvider<string> resilienceProvider,
  IFileSystem fileSystem,
  ISystemEnvironment systemEnvironment,
  IElevationChecker elevationChecker,
  IServiceController serviceController,
  IProcessManager processManager,
  IGlobalEventWaitProvider globalEventWaitProvider,
  IGlobalSemaphoreProvider globalSemaphoreProvider,
  IRegistryAccessor registryAccessor,
  IFileVersionProvider fileVersionProvider,
  IFailedUpdatesManager failedUpdatesManager,
  ILogger<PersistentAgentInstaller> logger) : IPersistentAgentInstaller
{
  private readonly IElevationChecker _elevationChecker = elevationChecker;
  private readonly IFileSystem _fileSystem = fileSystem;
  private readonly IFileVersionProvider _fileVersionProvider = fileVersionProvider;
  private readonly IGlobalEventWaitProvider _globalEventWaitProvider = globalEventWaitProvider;
  private readonly IGlobalSemaphoreProvider _globalSemaphoreProvider = globalSemaphoreProvider;
  private readonly ILogger<PersistentAgentInstaller> _logger = logger;
  private readonly IProcessManager _processManager = processManager;
  private readonly IRegistryAccessor _registryAccessor = registryAccessor;
  private readonly ResiliencePipelineProvider<string> _resilienceProvider = resilienceProvider;
  private readonly IServiceController _serviceController = serviceController;
  private readonly ISystemEnvironment _systemEnvironment = systemEnvironment;
  private readonly IFailedUpdatesManager _failedUpdatesManager = failedUpdatesManager;

  public async Task Install(CancellationToken cancellationToken)
  {
    _logger.LogInformation("Starting persistent agent installation.");

    // The agent used to have a different file name, so we need to check
    // which one we'll be operating on.
    if (!TryGetEffectiveExePath(out var effectiveExePath))
    {
      return;
    }

    _logger.LogInformation("Attempting to acquire global agent installation lock.");
    using var semaphore = _globalSemaphoreProvider.GetSecuredSemaphore(
      name: WellKnownSemaphoreNames.PersistentAgentInstallation,
      initialCount: 1,
      maximumCount: 1,
      out _);

    if (!semaphore.WaitOne(0))
    {
      _logger.LogInformation("The installer's global lock is already taken. Aborting.");
      return;
    }

    try
    {
      if (!_elevationChecker.IsAdministrator)
      {
        _logger.LogWarning("The installer is not running with elevated permissions. Aborting.");
        return;
      }

      if (!_fileSystem.File.Exists(PathHelpers.ConfigFilePath))
      {
        _logger.LogError(
          "The agent configuration file is missing. Expected file to exist at '{ConfigFilePath}'. Aborting installation.",
          PathHelpers.ConfigFilePath);
        return;
      }

      // If we're currently running from the installed path, we'd just be
      // overwriting the EXE with the same EXE, and it'd be pointless. This
      // could only happen if someone was doing something silly.
      if (IsRunningFromInstalledPath(effectiveExePath))
      {
        return;
      }

      // Again, this shouldn't happen.  But if the hashes are identical, there's
      // no point in going through the motions.
      if (await AreAgentHashesTheSame(effectiveExePath, cancellationToken))
      {
        return;
      }

      // Stop the currently-running Windows service.
      if (!await StopAgentService(cancellationToken))
      {
        return;
      }

      // Agent might have started additional processes of itself (e.g. remote control),
      // so we need to ensure those are killed too.
      StopAgentProcesses(cancellationToken);

      // Sometimes file locks don't release immediately, so we'll do file operations
      // in a resiliency pipeline.
      var pipeline = _resilienceProvider.GetPipeline(PersistentAgentInstallerIoPolicy.Key);
      await CreateBackupExe(effectiveExePath, pipeline, cancellationToken);
      await CopyNewExeToInstallLocation(effectiveExePath, pipeline, cancellationToken);
      SetDisplayVersionInRegistry(effectiveExePath);
      await StartAgentService(cancellationToken);

      // When the agent starts, it will release a global mutex
      if (!await WaitForGlobalConnectionEvent(cancellationToken))
      {
        _logger.LogCritical("The new agent has not signaled successful startup.  Installation failed.");
        await RecoverBackupExe(effectiveExePath, cancellationToken);
      }

      _logger.LogInformation("The new agent has signaled successful startup.  Installation complete.");
    }
    catch (OperationCanceledException ex)
    {
      _logger.LogCritical(ex, "Installation timed out.");
      await RecoverBackupExe(effectiveExePath, cancellationToken);
    }
    catch (Exception ex)
    {
      _logger.LogCritical(ex, "Error while installing agent.");
      await RecoverBackupExe(effectiveExePath, cancellationToken);
    }
    finally
    {
      _logger.LogInformation("Releasing global agent installation lock.");
      semaphore.Release();
    }
  }

  private async Task AddCurrentExeHashToFailedList(CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("Adding current EXE hash to failed updates list.");
      using var currentFileFs = _fileSystem.FileStream.New(_systemEnvironment.StartupExe, FileMode.Open, FileAccess.Read, FileShare.Read);
      var hash = await SHA256.HashDataAsync(currentFileFs, cancellationToken);
      var hashString = Convert.ToHexString(hash);
      await _failedUpdatesManager.MarkUpdateAsFailed(hashString, cancellationToken);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while adding current EXE hash to failed updates list.");
    }
  }

  private async Task<bool> AreAgentHashesTheSame(string effectiveExePath, CancellationToken cancellationToken)
  {
    using var installedFs = _fileSystem.FileStream.New(effectiveExePath, FileMode.Open, FileAccess.Read, FileShare.Read);
    using var newFs = _fileSystem.FileStream.New(_systemEnvironment.StartupExe, FileMode.Open, FileAccess.Read, FileShare.Read);

    var hash1 = await SHA256.HashDataAsync(installedFs, cancellationToken);
    var hash2 = await SHA256.HashDataAsync(newFs, cancellationToken);
    if (hash1.SequenceEqual(hash2))
    {
      _logger.LogError("The new agent EXE hash is identical to the installed EXE.  Aborting installation.");
      return true;
    }
    return false;
  }

  private async Task CopyNewExeToInstallLocation(string effectiveExePath, ResiliencePipeline pipeline, CancellationToken cancellationToken)
  {
    await pipeline.ExecuteAsync(
      (ct) =>
      {
        _logger.LogInformation("Copying new (current) agent EXE to installation path.");
        _fileSystem.File.Copy(_systemEnvironment.StartupExe, effectiveExePath, overwrite: true);
        return ValueTask.CompletedTask;
      },
      cancellationToken);
  }

  private async Task CreateBackupExe(string effectiveExePath, ResiliencePipeline pipeline, CancellationToken cancellationToken)
  {
    await pipeline.ExecuteAsync(
      (ct) =>
      {
        if (_fileSystem.File.Exists(PathHelpers.AgentExeBackupPath))
        {
          _logger.LogInformation("Deleting previous backup EXE.");
          _fileSystem.File.Delete(PathHelpers.AgentExeBackupPath);
        }

        _logger.LogInformation("Backing up current agent EXE.");
        _fileSystem.File.Copy(effectiveExePath, PathHelpers.AgentExeBackupPath, overwrite: true);

        return ValueTask.CompletedTask;
      },
      cancellationToken);
  }

  private bool IsRunningFromInstalledPath(string effectiveExePath)
  {
    var isRunningFromInstalledPath = string.Equals(
      _systemEnvironment.StartupExe,
      effectiveExePath,
      StringComparison.OrdinalIgnoreCase);

    if (isRunningFromInstalledPath)
    {
      _logger.LogInformation("The installer is running from the installation path.  Aborting installation.");
    }
    return isRunningFromInstalledPath;
  }

  private async Task RecoverBackupExe(string effectiveExePath, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("Recovering from persistent agent installation failure.");

      await AddCurrentExeHashToFailedList(cancellationToken);

      using var agentService = _serviceController
        .GetServices()
        .FirstOrDefault(x => x.ServiceName == PathHelpers.AgentServiceName);

      if (agentService is null)
      {
        _logger.LogCritical("The agent service could not be found.  Manual intervention may be required.");
        return;
      }

      if (agentService.Status == ServiceControllerStatus.Running)
      {
        _logger.LogInformation("Agent service is already running.  Aborting recovery.");
        return;
      }

      if (_fileSystem.File.Exists(PathHelpers.AgentExeBackupPath))
      {
        _logger.LogInformation("Restoring agent EXE from backup.");

        var pipeline = _resilienceProvider.GetPipeline(PersistentAgentInstallerIoPolicy.Key);
        await pipeline.ExecuteAsync(
          (ct) =>
          {
            _fileSystem.File.Move(PathHelpers.AgentExeBackupPath, effectiveExePath, overwrite: true);
            return ValueTask.CompletedTask;
          },
          cancellationToken);

        SetDisplayVersionInRegistry(effectiveExePath);
      }

      _logger.LogInformation("Starting agent service.");
      agentService.Start();

      await agentService.WaitForStatusAsync(ServiceControllerStatus.Running, TimeSpan.FromSeconds(30), cancellationToken);
      _logger.LogInformation("Agent service started.");
    }
    catch (Exception ex)
    {
      _logger.LogCritical(ex, "Error while recovering from persistent agent installation failure.");
    }
  }
  private void SetDisplayVersionInRegistry(string agentExePath)
  {
    try
    {
      _logger.LogInformation("Setting agent display version in registry.");

      var exeVersionResult = _fileVersionProvider.GetFileProductVersion(agentExePath);

      if (!exeVersionResult.IsSuccess || string.IsNullOrWhiteSpace(exeVersionResult.Value))
      {
        _logger.LogCritical(exeVersionResult.Exception, "The agent EXE version could not be determined.  Manual intervention may be required.");
        return;
      }

      var exeVersion = exeVersionResult.Value;

      using var uninstallKey = _registryAccessor.LocalMachine.OpenSubKey(PathHelpers.UninstallRegistryKeyPath, writable: false);
      if (uninstallKey is null)
      {
        _logger.LogCritical("The uninstall key could not be found in the registry.  Manual intervention may be required.");
        return;
      }

      foreach (var subkey in uninstallKey.GetSubKeyNames())
      {
        try
        {
          using var key = uninstallKey.OpenSubKey(subkey, writable: true);

          if (key is null)
          {
            _logger.LogWarning("Subkey '{Subkey}' could not be opened.", subkey);
            continue;
          }

          if (key.GetValue("DisplayName") is string displayName && displayName.Contains(PathHelpers.AgentRegistryDisplayName))
          {
            key.SetValue("DisplayVersion", exeVersion);
            _logger.LogInformation("Agent display version set to {Version}.", exeVersion);
            return;
          }
        }
        catch (Exception ex)
        {
          _logger.LogError(ex, "Error while checking subkey '{Subkey}'.", subkey);
        }
      }

      _logger.LogCritical("The agent uninstall key could not be found in the registry. Unable to set current version.");
    }
    catch (Exception ex)
    {
      _logger.LogCritical(ex, "Error while setting agent display version in registry.");
    }
  }

  private async Task StartAgentService(CancellationToken cancellationToken)
  {
    _logger.LogInformation("Starting the agent service.");
    using var agentService = _serviceController
      .GetServices()
      .First(x => x.ServiceName == PathHelpers.AgentServiceName);

    if (agentService.Status == ServiceControllerStatus.Running)
    {
      _logger.LogInformation("The agent service is already running.");
      return;
    }

    agentService.Start();

    await agentService.WaitForStatusAsync(
      ServiceControllerStatus.Running,
      TimeSpan.FromSeconds(30),
      cancellationToken);

    _logger.LogInformation("Agent service started.");
  }

  private void StopAgentProcesses(CancellationToken cancellationToken)
  {
    var processName = _fileSystem.Path.GetFileNameWithoutExtension(_systemEnvironment.StartupExe);
    var currentProcessId = _processManager.GetCurrentProcess().Id;
    var agentProcs = _processManager
      .GetProcessesByName(processName)
      .Where(x => x.Id != currentProcessId)
      .ToArray();

    if (agentProcs.Length == 0)
    {
      _logger.LogInformation("No other agent processes are running.");
      return;
    }

    _logger.LogInformation("Stopping {ProcessCount} other agent processes.", agentProcs.Length);

    foreach (var process in agentProcs)
    {
      cancellationToken.ThrowIfCancellationRequested();

      try
      {
        process.Kill(entireProcessTree: true);
      }
      catch (Exception ex)
      {
        _logger.LogWarning(
          ex,
          "Failed to kill process ID {ProcessId}.  Optimistically continuing with installation.",
          process.Id);
      }
    }
  }

  private async Task<bool> StopAgentService(CancellationToken cancellationToken)
  {
    using var agentService = _serviceController
      .GetServices()
      .FirstOrDefault(x => x.ServiceName == PathHelpers.AgentServiceName);

    try
    {

      if (agentService is null)
      {
        _logger.LogError("The agent service could not be found.  Aborting installation.");
        return false;
      }

      if (agentService.Status == ServiceControllerStatus.Stopped)
      {
        _logger.LogInformation("The agent service is already stopped.");
      }
      else
      {
        _logger.LogInformation("Stopping the agent service.");
        agentService.Stop();
        await agentService.WaitForStatusAsync(ServiceControllerStatus.Stopped, TimeSpan.FromSeconds(30), cancellationToken);
        _logger.LogInformation("Service stopped.");
      }

      return true;
    }
    catch (System.ServiceProcess.TimeoutException)
    {
      _logger.LogError("The agent service failed to stop within the timeout period.  Aborting installation.");
      // Attempt to start the service back up before aborting.
      if (agentService?.Status == ServiceControllerStatus.Stopped)
      {
        agentService?.Start();
      }
      return false;
    }
  }

  private bool TryGetEffectiveExePath([NotNullWhen(true)] out string? targetExePath)
  {
    targetExePath =
      _fileSystem.File.Exists(PathHelpers.AgentExeInstallPath) ?
        PathHelpers.AgentExeInstallPath :
        _fileSystem.File.Exists(PathHelpers.AgentExeInstallPathOld) ?
          PathHelpers.AgentExeInstallPathOld :
          null;

    return targetExePath is not null;
  }
  private async Task<bool> WaitForGlobalConnectionEvent(CancellationToken cancellationToken)
  {
    using var waitHandle = _globalEventWaitProvider.GetSecuredWaitHandle(
      initialState: false,
      mode: EventResetMode.ManualReset,
      name: WellKnownEventNames.PersistentAgentConnected,
      createdNew: out _);

    return await waitHandle.WaitAsync(TimeSpan.FromMinutes(5), cancellationToken);
  }
}
