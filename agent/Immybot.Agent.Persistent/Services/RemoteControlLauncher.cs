using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Security.Principal;
using Immybot.Shared.Abstractions.Device;
using Immybot.Shared.Abstractions.Device.Processes;
using Immybot.Shared.Abstractions.Device.Windows.Native;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.Logging;

namespace Immybot.Agent.Persistent.Services;

/// <summary>
/// Launches the agent in remote control mode within a target Windows session.
/// </summary>
internal interface IRemoteControlLauncher
{
  /// <summary>
  /// Launch the remote control client in chat mode.
  /// </summary>
  /// <param name="targetWindowsSession">
  ///   The target Windows session ID where the process should be started.
  ///   Use -1 to indicate that the session should be started in the console session.
  /// </param>
  /// <param name="serverUri">The base URI of the server to which the client should connect (e.g. https://my.example.com).</param>
  /// <param name="pipeName">The name to use for the NamedPipeServer that the agent is expecting new process to open.</param>
  /// <param name="requesterName">The name of the tech/user requesting the chat session.</param>
  /// <param name="orgName">The organization or MSP name of the requester.</param>
  /// <returns></returns>
  Task<OpResult> LaunchChatClient(int targetWindowsSession, string pipeName, string requesterName, string orgName);

  /// <summary>
  /// Launches a new remote control session in unattended mode.
  /// </summary>
  /// <param name="targetWindowsSession">
  ///   The target Windows session ID where the process should be started.
  ///   Use -1 to indicate that the session should be started in the console session.
  /// </param>
  /// <param name="sessionId">The remote control session ID.</param>
  /// <param name="accessKey">The key that allows view/control access to the remote control session.</param>
  /// <param name="requesterName">The name of the person initiating the session.</param>
  /// <param name="orgName">The organization or MSP name.</param>
  /// <returns></returns>
  Task<OpResult> LaunchRemoteControl(
    int targetWindowsSession,
    Guid sessionId,
    string accessKey,
    string requesterName,
    string orgName);


  /// <summary>
  /// Restart a remote control session (e.g. due to session switching).  The viewers will
  /// be notified when the new session is ready, and the front-end will switch to it.
  /// </summary>
  /// <param name="viewerIds">A comma-separated list of viewer connection IDs that should rejoin the new session.</param>
  /// <param name="sessionId">The remote control session ID.</param>
  /// <param name="accessKey">The key that allows view/control access to the remote control session.</param>
  /// <param name="requesterName">The name of the person initiating the session.</param>
  /// <param name="orgName">The organization or MSP name.</param>
  /// <param name="targetWindowsSession">
  ///   The target Windows session ID where the process should be started.
  ///   Use -1 to indicate that the session should be restarted in the console session.
  /// </param>
  /// <returns></returns>
  Task<OpResult> RestartRemoteControlSession(IEnumerable<string> viewerIds, string sessionId, string accessKey, string requesterName, string orgName, int targetWindowsSession = -1);
}


/// <inheritdoc />
[SupportedOSPlatform("windows")]
internal class RemoteControlLauncher : IRemoteControlLauncher
{
  private readonly IDeviceRegistrar _deviceRegistrar;
  private readonly ILogger<RemoteControlLauncher> _logger;
  private readonly IProcessManager _processManager;
  private readonly ISystemEnvironment _systemEnvironment;
  private readonly IWin32Interop _win32Interop;
  public RemoteControlLauncher(
    IProcessManager processManager,
    ISystemEnvironment systemEnvironment,
    IDeviceRegistrar deviceRegistrar,
    IWin32Interop win32Interop,
    ILogger<RemoteControlLauncher> logger)
  {
    _processManager = processManager;
    _systemEnvironment = systemEnvironment;
    _deviceRegistrar = deviceRegistrar;
    _win32Interop = win32Interop;
    _logger = logger;
  }

  /// <inheritdoc />
  public Task<OpResult> LaunchChatClient(int targetWindowsSession, string pipeName, string requesterName, string orgName)
  {
    using var logScope = _logger.BeginMemberScope();

    var serverUrlResult = GetServerUrl();
    if (!serverUrlResult.IsSuccess)
    {
      return Task.FromResult(OpResult.Fail(serverUrlResult.Exception, serverUrlResult.Reason));
    }
    var args = " remote-control" +
                $" --mode Chat" +
                $" --host \"{serverUrlResult.Value}\"" +
                $" --pipe-name {pipeName}" +
                $" --requester-name \"{requesterName}\"" +
                $" --org-name \"{orgName}\"";

    return Task.FromResult(StartClientImpl(args, targetWindowsSession));
  }

  /// <inheritdoc />
  public Task<OpResult> LaunchRemoteControl(int targetWindowsSession, Guid sessionId, string accessKey, string requesterName, string orgName)
  {
    using var logScope = _logger.BeginMemberScope();

    var serverUrlResult = GetServerUrl();
    if (!serverUrlResult.IsSuccess)
    {
      return Task.FromResult(OpResult.Fail(serverUrlResult.Exception, serverUrlResult.Reason));
    }

    var args = " remote-control" +
                $" --mode Unattended" +
                $" --host {serverUrlResult.Value}" +
                $" --requester-name \"{requesterName}\"" +
                $" --org-name \"{orgName}\"" +
                $" --session-id \"{sessionId}\"" +
                $" --access-key \"{accessKey}\"";

    _logger.LogInformation(
      "Launching remote control in unattended mode. Target Windows Session: {WindowsSession}", targetWindowsSession);

    return Task.FromResult(StartClientImpl(args, targetWindowsSession));
  }

  /// <inheritdoc />
  public Task<OpResult> RestartRemoteControlSession(IEnumerable<string> viewerIds, string sessionId, string accessKey, string requesterName, string orgName, int targetWindowsSession = -1)
  {
    using var logScope = _logger.BeginMemberScope();

    var serverUrlResult = GetServerUrl();
    if (!serverUrlResult.IsSuccess)
    {
      return Task.FromResult(OpResult.Fail(serverUrlResult.Exception, serverUrlResult.Reason));
    }

    var args = " remote-control" +
                $" --mode Unattended" +
                $" --relaunch true" +
                $" --host {serverUrlResult.Value}" +
                $" --requester-name \"{requesterName}\"" +
                $" --org-name \"{orgName}\"" +
                $" --session-id \"{sessionId}\"" +
                $" --access-key \"{accessKey}\"" +
                $" --viewers {string.Join(",", viewerIds)}";

    return Task.FromResult(StartClientImpl(args, targetWindowsSession));
  }

  private OpResult<Uri> GetServerUrl()
  {
    try
    {
      var hubUrl = _deviceRegistrar.RegistrationInformation.AgentHubEndpoint;

      if (hubUrl is null)
      {
        _logger.LogError("AgentHubEndpoint is null in DeviceRegistrar.  This shouldn't be possible.");
        return OpResult.Fail<Uri>("Unable to determine server URL.");
      }

      return OpResult.Ok(hubUrl.GetOrigin());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while getting server URL.");
      return OpResult.Fail<Uri>(ex, "An error occurred while getting the server URL.");
    }
  }

  private OpResult StartClientImpl(string args, int targetWindowsSession)
  {
    try
    {
      var serverUrlResult = GetServerUrl();
      if (!serverUrlResult.IsSuccess)
      {
        return OpResult.Fail(serverUrlResult.Reason);
      }

      var binaryPath = _systemEnvironment.StartupExe;

      if (WindowsIdentity.GetCurrent().IsSystem)
      {
        var processResult = _win32Interop.CreateInteractiveSystemProcess(
              commandLine: $"{binaryPath} {args}",
              targetSessionId: targetWindowsSession,
              hiddenWindow: false,
              out var procInfo);

        if (!processResult)
        {
          var lastError = Marshal.GetLastWin32Error();
          _logger.LogError("Failed to start remote control client process.  Win32 Error Code: {lastError}", lastError);
          return OpResult.Fail($"Remote control client process failed to start.  Win32 Error Code: {lastError}");
        }

        _logger.LogInformation("Remote control client process started successfully.  PID: {pid}", procInfo.dwProcessId);
      }
      else
      {
        var startedProcess = _processManager.Start(binaryPath, args);
        if (startedProcess.HasExited)
        {
          return OpResult.Fail($"Remote control client process failed to start.  Exit code: {startedProcess.ExitCode}");
        }
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while starting remote control client.");
      return OpResult.Fail(ex, "An error occurred while starting the client.");
    }

    return OpResult.Ok();
  }
}
