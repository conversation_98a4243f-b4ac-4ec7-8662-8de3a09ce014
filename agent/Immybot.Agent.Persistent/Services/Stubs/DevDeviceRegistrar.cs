using Immybot.Agent.Configuration.Options;
using Immybot.Shared.DataContracts.DeviceRegistration;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.Options;

namespace Immybot.Agent.Persistent.Services.Stubs;

internal class DevDeviceRegistrar(IOptions<DeveloperOptions> developerOptions) : IDeviceRegistrar
{
  public RegistrationInformation RegistrationInformation { get; } = new()
  {
    DeviceId = developerOptions.Value.AgentDeviceId ?? AppConstants.DevAgentDeviceId.ToString(),
    AgentHubEndpoint = developerOptions.Value.AgentSignalrHubEndpoint ?? AppConstants.DevAgentSignalrHubEndpoint,
  };

  public bool IsDeviceRegistered { get; private set; }

  public Task<OpResult<RegistrationInformation>> RegisterDevice(CancellationToken cancellationToken)
  {
    IsDeviceRegistered = true;
    return Task.FromResult(OpResult.Ok(RegistrationInformation));
  }
}
