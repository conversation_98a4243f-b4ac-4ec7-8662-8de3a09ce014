using System.Text;
using Immybot.Agent.Configuration.Options;
using Immybot.Agent.Persistent.Configuration;
using Immybot.Shared.DataContracts.DeviceRegistration;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.Options;

namespace Immybot.Agent.Persistent.Services.Stubs;

internal class DevRegistrationPersistence : IRegistrationPersistence
{
  private readonly string _deviceId;
  private readonly Uri _agentHubEndpoint;
  private readonly ISystemDetailsProvider _systemDetailsProvider;
  private RegistrationInformation? _registrationInfo;

  public DevRegistrationPersistence(IOptions<DeveloperOptions> developerOptions,  ISystemDetailsProvider systemDetailsProvider)
  {
    _deviceId =
      developerOptions.Value.AgentDeviceId ??
      AppConstants.DevAgentDeviceId.ToString();

    _agentHubEndpoint =
      developerOptions.Value.AgentSignalrHubEndpoint ??
      AppConstants.DevAgentSignalrHubEndpoint;

    _systemDetailsProvider = systemDetailsProvider;
  }

  public Task<OpResult<RegistrationInformation>> GetConfigurationParameters()
  {
    _registrationInfo ??= new RegistrationInformation()
    {
      AgentHubEndpoint = _agentHubEndpoint,
      DeviceId = _deviceId,
      ReKeyEndpoint = $"{_agentHubEndpoint.Scheme}://{_agentHubEndpoint.Authority}/plugins/api/v1/2/installer/agent-rekey/request",
      BoardSerialNumber = _systemDetailsProvider.GetSerialNumber() ?? "Dev-Board",
    };
    return OpResult.Ok(_registrationInfo).AsTaskResult();
  }

  public Task<OpResult<AgentRegistrationParams>> GetRegistrationParameters()
  {
    var regParams = new AgentRegistrationParams()
    {
      AgentInstallerBackendAddress = $"{_agentHubEndpoint.Scheme}://{_agentHubEndpoint.Authority}",
      AgentInstallerID = "fake-installer-id",
      AgentInstallerKey = Encoding.UTF8.GetBytes("fake-installer-key")
    };
    return OpResult.Ok(regParams).AsTaskResult();
  }

  public Task<OpResult> SaveConfigurationParameters(RegistrationInformation deviceRegistration)
  {
    _registrationInfo = deviceRegistration;
    return OpResult.Ok().AsTaskResult();
  }
}
