using System.Net;
using Immybot.Shared.Abstractions.Device;
using Immybot.Shared.Abstractions.Device.Enums;
using Immybot.Shared.DataContracts.Agent;
using Immybot.Shared.DataContracts.Signalr;
using Immybot.Shared.DataContracts.Signalr.AgentDtos;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Immybot.Shared.Primitives.Helpers;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Immybot.Shared.Abstractions.Device.Processes;
using Immybot.Agent.Persistent.Messages;
using CommunityToolkit.Mvvm.Messaging;
using DotNext.Threading;
using Immybot.Shared.Abstractions.Device.Windows.Threading;
using Immybot.Shared.Abstractions.Device.Windows.Native;

namespace Immybot.Agent.Persistent.Services;

public interface IAgentHubConnection
{
  HubConnectionState ConnectionState { get; }

  bool IsConnected { get; }

  /// <summary>
  /// Attempts to connect to the supplied hub URL. After the initial connection
  /// is made, the internal retry logic of the <see cref="HubConnection"/> will take
  /// over, and no additional reconnect logic is required.
  /// </summary>
  /// <returns></returns>
  Task<OpResult> Connect();

  /// <summary>
  /// Attempts to register the agent with the server.  If it fails, the agent will
  /// not be considered online from the server's perspective.  Failure could indicate
  /// that another agent with the same device ID and hardware ID is already online.
  /// </summary>
  Task<AddAgentSessionResult> CreateAgentSessionOnServer();

  /// <summary>
  /// Attempts to create an ephemeral session on the server.
  /// If successful, the result will contain the session ID, which
  /// can be used to make an ephemeral RPC connection.
  /// </summary>
  /// <returns></returns>
  Task<CreateEphemeralSessionResult> CreateEphemeralSession();

  /// <summary>
  /// Checks for pending remote control sessions.  Any sessions that were awaiting
  /// a reconnect will attempt to start again and reconnect the viewer.
  /// </summary>
  Task CheckForPendingRemoteControlSessions();

  /// <summary>
  /// Attempt to rekey the agent with the backend.
  /// </summary>
  Task TryRekeyAgent();

  /// <summary>
  /// Waits for the connection to be established.
  /// </summary>
  /// <param name="cancellationToken"></param>
  /// <returns></returns>
  Task WaitForConnection(CancellationToken cancellationToken);
}

/// <inheritdoc cref="IAgentHubConnection" />
internal class AgentHubConnection(
  Func<IHubConnectionBuilder> hubBuilderFactory,
  IHostApplicationLifetime hostLifetime,
  IHardwareIdentifier hardwareIdentifier,
  ISystemEnvironment systemEnvironment,
  IProcessManager processManager,
  IRemoteControlLauncher remoteControlLauncher,
  IBackendApiClient backendApiClient,
  IRegistrationPersistence registrationPersistence,
  IWebProxy webProxy,
  IWin32Interop win32Interop,
  IMessenger messenger,
  IGlobalEventWaitProvider globalEventWaitProvider,
  ILogger<AgentHubConnection> logger) : IAgentHubConnection, IAgentHubClient
{
  private readonly SemaphoreSlim _connectLock = new(1, 1);
  private readonly AsyncManualResetEvent _connectionEstablishedEvent = new(false);
  private readonly Func<IHubConnectionBuilder> _hubBuilderFactory = hubBuilderFactory;
  private readonly IHostApplicationLifetime _hostLifetime = hostLifetime;
  private readonly IHardwareIdentifier _hardwareIdentifier = hardwareIdentifier;
  private readonly ISystemEnvironment _systemEnvironment = systemEnvironment;
  private readonly IProcessManager _processManager = processManager;
  private readonly IRemoteControlLauncher _remoteControlLauncher = remoteControlLauncher;
  private readonly IBackendApiClient _backendApiClient = backendApiClient;
  private readonly IRegistrationPersistence _registrationPersistence = registrationPersistence;
  private readonly IGlobalEventWaitProvider _globalEventWaitProvider = globalEventWaitProvider;
  private readonly IWebProxy _webProxy = webProxy;
  private readonly ILogger<AgentHubConnection> _logger = logger;
  private readonly IWin32Interop _win32Interop = win32Interop;
  private readonly IMessenger _messenger = messenger;
  private readonly TimeSpan _maxReconnectDelay = TimeSpan.FromSeconds(30);
  private readonly TimeSpan _maxReconnectJitter = TimeSpan.FromSeconds(20);
  private HubConnection? _connection;

  public HubConnectionState ConnectionState => _connection?.State ?? HubConnectionState.Disconnected;

  public bool IsConnected => _connection?.State == HubConnectionState.Connected;

  private HubConnection Connection =>
    _connection ?? throw new InvalidOperationException("Connection is not established.");

  public Task<string> CheckForDeadConnection(CancellationToken token)
  {
    _logger.LogError(
      "A connection check was initiated by the server, " +
      "and it was expecting this connection to be closed.");
    return Task.FromResult("Ok");
  }

  /// <inheritdoc />
  public async Task<OpResult> Connect()
  {
    using var _ = _logger.BeginMemberScope();

    if (!await _connectLock.WaitAsync(0, _hostLifetime.ApplicationStopping))
    {
      _logger.LogWarning("Attempted to acquire connection lock more than once.");
      return OpResult.Fail("Attempted to acquire connection lock more than once.");
    }

    try
    {
      var configResult = await _registrationPersistence.GetConfigurationParameters();
      if (!configResult.IsSuccess)
      {
        return OpResult.Fail("Failed to get configuration parameters.");
      }

      if (configResult.Value.AgentHubEndpoint is not { } hubEndpoint)
      {
        return OpResult.Fail(
          "AgentHubEndpoint is missing from the device registration information.  Unable to connect to SignalR hub.");
      }

      _logger.LogInformation("Connecting to SignalR hub ({AgentHubUrl}).", hubEndpoint);

      if (_connection is not null)
      {
        try
        {
          await _connection.StopAsync(_hostLifetime.ApplicationStopping);
          await _connection.DisposeAsync();
        }
        catch (Exception ex)
        {
          // This can happen if the connection is already stopped.  We could check the
          // state first, but it might change in between the check and the call to StopAsync.
          _logger.LogInformation(ex, "Exception while stopping connection.  This is probably normal.");
        }

        _connection = null;
      }

      _connection = BuildConnection(hubEndpoint);

      await _connection.StartAsync(_hostLifetime.ApplicationStopping);

      _logger.LogInformation("Connected to hub ({AgentHubUrl}).", hubEndpoint);
    }
    catch (OperationCanceledException)
    {
      return OpResult.Fail("Application is shutting down.");
    }
    catch (HttpRequestException ex)
    {
      return OpResult.Fail(ex, "Http request failed while connecting to server.");
    }
    catch (Exception ex)
    {
      return OpResult.Fail(ex, "Error while connecting to hub.");
    }
    finally
    {
      _connectLock.Release();
    }

    return OpResult.Ok();
  }

  /// <inheritdoc />
  public async Task CheckForPendingRemoteControlSessions()
  {
    await Connection.InvokeAsync(
      nameof(IAgentHub.CheckForPendingRemoteControlSessions),
      _hostLifetime.ApplicationStopping);
  }

  public Task<OpResult<WindowsSession[]>> GetWindowsSessions()
  {
    try
    {
      if (!OperatingSystem.IsWindows())
      {
        return Task.FromResult(OpResult.Fail<WindowsSession[]>("Only available on Windows."));
      }

      var sessions = _win32Interop.GetActiveSessions();
      return Task.FromResult(OpResult.Ok(sessions.ToArray()));
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while getting Windows sessions.");
      return Task.FromResult(OpResult.Fail<WindowsSession[]>(ex));
    }
  }

  public Task<OpResult> InvokeCtrlAltDel()
  {
    if (_systemEnvironment.Platform != SystemPlatform.Windows)
    {
      return Task.FromResult(OpResult.Fail("Only available on Windows."));
    }

    if (!_systemEnvironment.IsWindowsService)
    {
      return Task.FromResult(OpResult.Fail("Windows service required."));
    }

    _win32Interop.SendSecureAttentionSequence(false);
    return Task.FromResult(OpResult.Ok());
  }


  public async Task<OpResult> RestartRemoteControl(RestartRemoteControlRequest request)
  {
    return await _remoteControlLauncher.RestartRemoteControlSession(
      request.ViewerIds,
      $"{request.RemoteControlSessionId}",
      request.AccessKey,
      request.RequesterName,
      request.OrganizationName,
      request.TargetWindowsSession);
  }

  public async Task<string> RunScript(RunScriptRequest runScriptRequest)
  {
    using var cts = new CancellationTokenSource(runScriptRequest.Timeout);
    using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cts.Token, _hostLifetime.ApplicationStopping);
    try
    {
      var res = await _processManager.RunScript(
        runScriptRequest.ScriptCode,
        linkedCts.Token,
        $"{runScriptRequest.ScriptLanguage}",
        runScriptRequest.ScriptPath);

      return res;
    }
    catch (FileNotFoundException ex)
    {
      // TODO: This isn't the right solution.  Figure out why the file is missing and fix the root cause (e.g. logic error, race condition, etc.).
      // https://github.com/immense/immybot/issues/5628
      // kick off on a background task so we can respond to the backend
      await using var afterReturnAction = Disposable.CreateAsync(
        async () =>
        {
          await Task.Delay(1000, CancellationToken.None);

          // this will cause the service to stop and auto-restart. Restarting the service has resolved this issue in the past.
          _hostLifetime.StopApplication();
        });

      const string result =
        "Restarting the ImmyBot Agent Service to resolve an issue with a missing file. See the logs under \"%programdata%\\ImmyBotAgentService\" for more details.";
      _logger.LogError(ex, result);
      return RunScriptResponseHelper.ExceptionPrefix + ": " + result;
    }
    catch (Exception ex)
    {
      const string err = "Failed to execute script";
      _logger.LogError(ex, err);
      return RunScriptResponseHelper.ExceptionPrefix + err + ": " + ex.GenerateDetailedExceptionMessage();
    }
  }

  public async Task<OpResult> StartRemoteControlSession(StartRemoteControlRequest request)
  {
    return await _remoteControlLauncher.LaunchRemoteControl(
      request.TargetWindowsSession,
      request.RemoteControlSessionId,
      request.AccessKey,
      request.RequesterName,
      request.OrganizationName);
  }

  private HubConnection BuildConnection(Uri hubEndpoint)
  {
    var connection = _hubBuilderFactory()
      .WithUrl(hubEndpoint,
        options =>
        {
          options.Proxy = _webProxy;
        })
      .WithAutomaticReconnect(new RetryPolicy(_logger))
      .Build();

    connection.Reconnected += HandleReconnected;
    connection.Reconnecting += HandleReconnecting;
    connection.On<CancellationToken>(nameof(CheckForDeadConnection), CheckForDeadConnection);
    connection.On<RunScriptRequest, string>(nameof(RunScript), RunScript);
    connection.On<StartRemoteControlRequest, OpResult>(nameof(StartRemoteControlSession), StartRemoteControlSession);
    connection.On<RestartRemoteControlRequest, OpResult>(nameof(RestartRemoteControl), RestartRemoteControl);
    connection.On(nameof(InvokeCtrlAltDel), InvokeCtrlAltDel);
    connection.On(nameof(GetWindowsSessions), GetWindowsSessions);
    return connection;
  }


  private async Task HandleReconnected(string? arg)
  {
    _logger.LogInformation("Reconnected to hub.  New connection ID: {arg}", arg);

    var retryCount = 0;

    // While the current connection is established, attempt to create a
    // new agent session on the server.
    while (
      Connection.State == HubConnectionState.Connected &&
      Connection.ConnectionId == arg)
    {
      try
      {
        var result = await CreateAgentSessionOnServer();

        if (result.ResultType == AddAgentSessionResultType.Success)
        {
          _logger.LogInformation("Agent session created on server.");
          break;
        }

        if (result.ResultType == AddAgentSessionResultType.RekeyRequired)
        {
          _logger.LogInformation("Agent rekey required.");
          await TryRekeyAgent();
        }

        _logger.LogError(
          "Failed to create a session for this agent.  Reason: {FailureResultReason}",
          result.FailureReason);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error while reconnecting to the agent hub.");
      }

      await WaitHelper.WaitExponentially(
        currentCount: retryCount++,
        maxDelay: _maxReconnectDelay,
        maxJitter: _maxReconnectJitter,
        preWaitCallback: waitTime =>
        {
          _logger.LogInformation("Retrying session creation in {seconds} seconds.", waitTime.TotalSeconds);
          return Task.CompletedTask;
        },
        cancellationToken: _hostLifetime.ApplicationStopping);
    }
  }

  public async Task<AddAgentSessionResult> CreateAgentSessionOnServer()
  {
    _logger.LogInformation("Sending device info.");

    var deviceDetails = await _hardwareIdentifier.GenerateDeviceDetails();

    var result = await Connection.InvokeAsync<AddAgentSessionResult>(
      nameof(IAgentHub.AddAgentSession2),
      deviceDetails,
      cancellationToken: _hostLifetime.ApplicationStopping);

    // Both the initial connection and reconnection process are dependent on
    // this succeeding before we're officially "connected", so we'll publish
    // the event here.
    if (result.ResultType == AddAgentSessionResultType.Success)
    {
      _connectionEstablishedEvent.Set();
      _messenger.Send(new HubConnectionStateChangedMessage(ConnectionState));
      PublishGlobalConnectionEvent();
    }

    return result;
  }

  public async Task<CreateEphemeralSessionResult> CreateEphemeralSession()
  {
    try
    {
      _logger.LogInformation("Requesting ephemeral session from server.");
      var deviceDetails = await _hardwareIdentifier.GenerateDeviceDetails();
      var request = new CreateEphemeralSessionRequest(deviceDetails.AgentDeviceId);
      return await Connection.InvokeAsync<CreateEphemeralSessionResult>(
        nameof(IAgentHub.CreateEphemeralSession),
        request);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while creating ephemeral session.");
      return CreateEphemeralSessionResult.Fail(CreateEphemeralSessionResultStatus.ClientExceptionOccurred);
    }
  }

  private Task HandleReconnecting(Exception? arg)
  {
    _logger.LogInformation(arg, "Connection lost.  Reconnecting to the agent hub.");
    _connectionEstablishedEvent.Reset();
    _messenger.Send(new HubConnectionStateChangedMessage(ConnectionState));
    return Task.CompletedTask;
  }

  public async Task TryRekeyAgent()
  {
    var result = await _backendApiClient.RekeyDevice();
    if (!result.IsSuccess)
    {
      _logger.LogFailure(result);
      return;
    }

    var regResult = await _registrationPersistence.SaveConfigurationParameters(result.Value);
    if (!regResult.IsSuccess)
    {
      _logger.LogFailure(regResult);
    }
  }

  public async Task WaitForConnection(CancellationToken cancellationToken)
  {
    await _connectionEstablishedEvent.WaitAsync(cancellationToken);
  }

  private void PublishGlobalConnectionEvent()
  {
    try
    {
      _logger.LogInformation("Signaling global connection event.");

      using var waitHandle = _globalEventWaitProvider.GetSecuredWaitHandle(
        false,
        EventResetMode.ManualReset,
        WellKnownEventNames.PersistentAgentConnected,
        out var createdNew);

      if (createdNew)
      {
        _logger.LogInformation("New wait handle created, which means there are no listeners.");
        return;
      }

      if (!waitHandle.Set())
      {
        _logger.LogError("Failed to signal global connection event.");
        return;
      }

      _logger.LogInformation("Global connection event signaled.");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while signaling global connection event.");
    }
  }

  private sealed class RetryPolicy(ILogger<AgentHubConnection> logger) : IRetryPolicy
  {
    public TimeSpan? NextRetryDelay(RetryContext retryContext)
    {
      // Add a little randomness.
      var retrySeconds = Random.Shared.Next(3, 10);
      logger.LogDebug("Attempting to reconnect in {seconds} seconds.", retrySeconds);
      return TimeSpan.FromSeconds(retrySeconds);
    }
  }
}
