using Immybot.Shared.Abstractions.Device;
using Immybot.Shared.Abstractions.Device.Timers;
using Immybot.Shared.DataContracts.Signalr.AgentDtos;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Immybot.Shared.Primitives.Helpers;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Immybot.Agent.Persistent.Services;

// BackgroundService was chosen to support the possibility of offline features in the future.
internal class RealtimeConnectionManager(
  IAgentHubConnection hubConnection,
  IDeviceRegistrar deviceRegistrar,
  ISystemEnvironment systemEnvironment,
  IDelayer delayer,
  ILogger<RealtimeConnectionManager> logger)
  : BackgroundService
{
  private readonly TimeSpan _maxReconnectDelay = TimeSpan.FromSeconds(30);
  private readonly TimeSpan _maxReconnectJitter = TimeSpan.FromSeconds(20);
  private readonly SemaphoreSlim _connectLock = new(1, 1);

  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    try
    {
      await ConnectToHub(stoppingToken);
    }
    catch (OperationCanceledException)
    {
      logger.LogInformation("Application shutting down.  Aborting realtime connection attempt.");
    }
  }

  private async Task ConnectToHub(CancellationToken cancellationToken)
  {
    var count = 0;

    while (!cancellationToken.IsCancellationRequested)
    {
      // Wait until device is registered before attempting a SignalR connection.
      if (!deviceRegistrar.IsDeviceRegistered)
      {
        logger.LogInformation(
          "Waiting for device registration to complete before attempting realtime connection.");

        await delayer.WaitFor(
          condition: () => deviceRegistrar.IsDeviceRegistered,
          pollingInterval: TimeSpan.FromSeconds(1),
          cancellationToken: cancellationToken);

        continue;
      }

      if (await ConnectToHubImpl())
      {
        break;
      }

      await Pause(count++, cancellationToken);
    }
  }

  private async Task<bool> ConnectToHubImpl()
  {
    if (!await _connectLock.WaitAsync(0))
    {
      return false;
    }

    try
    {
      var connectResult = await hubConnection.Connect();

      if (!connectResult.IsSuccess)
      {
        logger.LogFailure(connectResult, LogLevel.Warning);
        return false;
      }

      var result = await hubConnection.CreateAgentSessionOnServer();

      switch (result.ResultType)
      {
        case AddAgentSessionResultType.Success:
          logger.LogInformation("Agent session created on server.");
          break;
        case AddAgentSessionResultType.Failure:
          {
            var createResult = OpResult.Fail($"Failed to create agent session on server.  Reason: {result.FailureReason}");
            logger.LogFailure(createResult);
            return false;
          }
        case AddAgentSessionResultType.RekeyRequired:
          {
            logger.LogWarning("Agent rekey required.");
            await hubConnection.TryRekeyAgent();
            return false;
          }
        case AddAgentSessionResultType.Unknown:
        default:
          {
            var createResult = OpResult.Fail(
              "Unknown result type from CreateAgentSessionOnServer.  " +
              $"Result: {result.FailureReason}.  " +
              $"Type: {result.ResultType}.");

            logger.LogFailure(createResult);
            return false;
          }
      }

      // If we get here, we were successful.
      await hubConnection.CheckForPendingRemoteControlSessions();

      return true;
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "Error while attempting to connect to the server.");
      return false;
    }
    finally
    {
      _connectLock.Release();
    }
  }


  private async Task Pause(double count, CancellationToken cancellationToken)
  {
    if (systemEnvironment.IsDebug)
    {
      logger.LogDebug("Retrying realtime connection in 3 seconds.");
      await Task.Delay(TimeSpan.FromSeconds(3), cancellationToken);
      return;
    }

    await WaitHelper.WaitExponentially(
      currentCount: count,
      maxDelay: _maxReconnectDelay,
      maxJitter: _maxReconnectJitter,
      preWaitCallback: async waitTime =>
        logger.LogInformation("Retrying realtime connection in {WaitSeconds} seconds.", waitTime.TotalSeconds),
      cancellationToken: cancellationToken);
  }
}
