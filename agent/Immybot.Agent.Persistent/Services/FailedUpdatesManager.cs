using System.IO.Abstractions;
using System.Text.Json;
using DotNext;
using Immybot.Agent.Persistent.Resilience;
using Immybot.Agent.Persistent.Updates;
using Immybot.Shared.Abstractions.Device.Extensions;
using Microsoft.Extensions.Logging;
using Polly.Registry;

namespace Immybot.Agent.Persistent.Services;

/// <summary>
///   Manages failed updates, which are identified by their binary hash, and determines
///   if they should be retried.
/// </summary>
public interface IFailedUpdatesManager
{
  Task<bool> ShouldInstallUpdate(string sha256BinaryHash, CancellationToken cancellationToken);
  Task MarkUpdateAsFailed(string sha256BinaryHash, CancellationToken cancellationToken);
}
internal class FailedUpdatesManager(
  TimeProvider timeProvider,
  ResiliencePipelineProvider<string> resilienceProvider,
  IFileSystem fileSystem,
  ILogger<FailedUpdatesManager> logger) : IFailedUpdatesManager
{
  private readonly TimeProvider _timeProvider = timeProvider;
  private readonly ResiliencePipelineProvider<string> _resilienceProvider = resilienceProvider;
  private readonly IFileSystem _fileSystem = fileSystem;
  private readonly ILogger<FailedUpdatesManager> _logger = logger;
  // Allow failed updates to retry after 5 hours, in case of transient issues.
  private readonly TimeSpan _retryInterval = TimeSpan.FromHours(5);

  public async Task<bool> ShouldInstallUpdate(string sha256BinaryHash, CancellationToken cancellationToken)
  {
    try
    {
      var pipeline = _resilienceProvider.GetPipeline(PersistentAgentInstallerIoPolicy.Key);

      return await pipeline.ExecuteAsync(
        async (ct) =>
        {
          _logger.LogInformation("Checking if remote hash has already failed.");

          if (!_fileSystem.File.Exists(PathHelpers.FailedUpdatesFilePath))
          {
            _logger.LogInformation("No failed updates file found.  Continuing with update check.");
            return true;
          }

          using var fs = _fileSystem.File.Open(PathHelpers.FailedUpdatesFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
          var failedUpdates = await JsonSerializer.DeserializeAsync<Dictionary<string, FailedUpdateEntry>>(fs, cancellationToken: ct);
          if (failedUpdates is null)
          {
            _logger.LogInformation("Failed updates file deserialized to null. Optimistically continuing.");
            return true;
          }
          if (!failedUpdates.TryGetValue(sha256BinaryHash, out var entry))
          {
            _logger.LogInformation("No failed update entry found for hash.  Continuing with update check.");
            return true;
          }

          if (entry.FailedAt + _retryInterval > _timeProvider.GetUtcNow())
          {
            _logger.LogWarning(
              "Update has failed recently.  Skipping update check. Failed At: {FailedAt}.  Hash: {BinaryHash}.",
              entry.FailedAt,
              sha256BinaryHash);

            return false;
          }

          _logger.LogInformation(
            "Update has failed previously, but is now eligible for retry. " +
            "Continuing with update check. Failed At: {FailedAt}.  Hash: {BinaryHash}.",
            entry.FailedAt,
            sha256BinaryHash);
          return true;
        },
        cancellationToken);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Failed to check failed updates list.  Optimistically proceeding.");
      return true;
    }
  }

  public async Task MarkUpdateAsFailed(string sha256BinaryHash, CancellationToken cancellationToken)
  {
    try
    {
      var pipeline = _resilienceProvider.GetPipeline(PersistentAgentInstallerIoPolicy.Key);
      await pipeline.ExecuteAsync(
        async (ct) =>
        {
          _logger.LogInformation("Marking update as failed.  Hash: {BinaryHash}", sha256BinaryHash);

          var failedUpdates = await GetFailureList();
          failedUpdates[sha256BinaryHash] = new FailedUpdateEntry(sha256BinaryHash, _timeProvider.GetUtcNow());

          _fileSystem.EnsureFileDirectoryCreated(PathHelpers.FailedUpdatesFilePath);

          await _fileSystem.File.WriteAllTextAsync(
            PathHelpers.FailedUpdatesFilePath,
            JsonSerializer.Serialize(failedUpdates),
            cancellationToken: ct);
        },
        cancellationToken);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Failed to mark update as failed.");
    }
  }

  private async Task<Dictionary<string, FailedUpdateEntry>> GetFailureList()
  {
    if (!_fileSystem.File.Exists(PathHelpers.FailedUpdatesFilePath))
    {
      return [];
    }

    try
    {
      using var fs = _fileSystem.File.Open(PathHelpers.FailedUpdatesFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
      var existingList = await JsonSerializer.DeserializeAsync<Dictionary<string, FailedUpdateEntry>>(fs);
      if (existingList is null)
      {
        _logger.LogInformation("Failed to deserialize failed updates file.  Returning empty dictionary.");
        existingList = [];
      }
      return existingList;
    }
    catch (Exception ex)
    {
      _logger.LogInformation(ex, "Failed to read existing failed updates file.  Returning empty dictionary.");
      return [];
    }
  }
}
