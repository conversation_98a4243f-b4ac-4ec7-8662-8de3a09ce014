using System.Diagnostics.CodeAnalysis;
using System.IO.Abstractions;
using System.Text.Json;
using Immybot.Agent.Configuration.Options;
using Immybot.Agent.Persistent.Configuration;
using Immybot.Shared.DataContracts.DeviceRegistration;
using Immybot.Shared.DataContracts.Signalr;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Immybot.Agent.Persistent.Services;

internal interface IRegistrationPersistence
{
  Task<OpResult<AgentRegistrationParams>> GetRegistrationParameters();
  Task<OpResult<RegistrationInformation>> GetConfigurationParameters();
  Task<OpResult> SaveConfigurationParameters(RegistrationInformation deviceRegistration);
}

internal class RegistrationPersistence : IRegistrationPersistence
{
  private readonly JsonSerializerOptions _jsonWriteOptions = new() { WriteIndented = true };
  private readonly IFileSystem _fileSystem;
  private readonly IOptionsMonitor<DeveloperOptions> _devOptions;
  private readonly ILogger<RegistrationPersistence> _logger;
  private readonly JsonSerializerOptions _serializerOptions = new()
  {
    PropertyNameCaseInsensitive = true
  };

  public RegistrationPersistence(
    IFileSystem fileSystem,
    IOptionsMonitor<DeveloperOptions> devOptions,
    ILogger<RegistrationPersistence> logger)
  {
    _fileSystem = fileSystem;
    _devOptions = devOptions;
    _logger = logger;
  }

  [Obsolete("This is being replaced by `IOptionsMonitor<ConfigOptions>`")]
  public async Task<OpResult<RegistrationInformation>> GetConfigurationParameters()
  {
    try
    {
      if (!_fileSystem.File.Exists(PathHelpers.ConfigFilePath))
      {
        return OpResult.Fail<RegistrationInformation>(new FileNotFoundException("Configuration file doesn't exist."));
      }

      var content = await _fileSystem.File.ReadAllTextAsync(PathHelpers.ConfigFilePath);

      var registrationInfo = JsonSerializer.Deserialize<RegistrationInformation>(content, _serializerOptions);

      if (registrationInfo is null)
      {
        return OpResult.Fail<RegistrationInformation>("Failed to deserialize configuration file content.");
      }

      // TODO (62): This is only needed until all instances no longer have an IoTHub
      if (registrationInfo.AgentHubEndpoint is null)
      {
        if (!TryGetHubEndpoint(registrationInfo, out var hubEndpoint))
        {
          return OpResult.Fail<RegistrationInformation>("Unable to determine agent hub endpoint.");
        }

        registrationInfo.AgentHubEndpoint = hubEndpoint;
        await SaveConfigurationParameters(registrationInfo);
      }

      return OpResult.Ok(registrationInfo);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while getting configuration parameters.");
      return OpResult.Fail<RegistrationInformation>(ex);
    }
  }

  public async Task<OpResult<AgentRegistrationParams>> GetRegistrationParameters()
  {
    try
    {
      if (!_fileSystem.File.Exists(PathHelpers.RegistrationFilePath))
      {
        return OpResult.Fail<AgentRegistrationParams>(new FileNotFoundException("Registration file doesn't exist."));
      }

      var content = await _fileSystem.File.ReadAllTextAsync(PathHelpers.RegistrationFilePath);
      var registrationParams = JsonSerializer.Deserialize<AgentRegistrationParams>(content, _serializerOptions);

      if (registrationParams is null)
      {
        return OpResult.Fail<AgentRegistrationParams>("Failed to deserialize registration file content.");
      }

      return OpResult.Ok(registrationParams);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while getting registration parameters.");
      return OpResult.Fail<AgentRegistrationParams>(ex);
    }
  }

  public async Task<OpResult> SaveConfigurationParameters(RegistrationInformation deviceRegistration)
  {
    try
    {
      var configJson = JsonSerializer.Serialize(deviceRegistration, _jsonWriteOptions);
      await _fileSystem.File.WriteAllTextAsync(PathHelpers.ConfigFilePath, configJson);
      return OpResult.Ok();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while saving configuration parameters.");
      return OpResult.Fail(ex);
    }
  }


  private bool TryGetHubEndpoint(
    RegistrationInformation deviceRegistration,
    [NotNullWhen(true)] out Uri? endpoint)
  {
    if (_devOptions.CurrentValue.UseMockDeviceRegistrar)
    {
      if (_devOptions.CurrentValue.AgentSignalrHubEndpoint is { } devEndpoint)
      {
        endpoint = devEndpoint;
        return true;
      }
      endpoint = AppConstants.DevAgentSignalrHubEndpoint;
      return true;
    }

    if (Uri.TryCreate(deviceRegistration.ReKeyEndpoint, UriKind.Absolute, out var rekeyUri) &&
         (rekeyUri.Scheme == Uri.UriSchemeHttp || rekeyUri.Scheme == Uri.UriSchemeHttps))
    {
      endpoint = new Uri($"{rekeyUri.Scheme}://{rekeyUri.Authority}{HubRoutesV1.AgentHubPath}");
      return true;
    }

    _logger.LogError(
      "Rekey endpoint URI ({uri}) is missing or malformed.  " +
      "Unable to determine hub endpoint address.",
      deviceRegistration.ReKeyEndpoint);
    endpoint = null;
    return false;
  }
}
