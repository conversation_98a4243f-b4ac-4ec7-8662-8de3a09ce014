using Immybot.Agent.Persistent.Helpers;
using Immybot.Shared.Abstractions.Device.Exceptions;
using Immybot.Shared.DataContracts.DeviceRegistration;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.Logging;

namespace Immybot.Agent.Persistent.Services;

internal interface IDeviceRegistrar
{
  Task<OpResult<RegistrationInformation>> RegisterDevice(CancellationToken cancellationToken);
  bool IsDeviceRegistered { get; }
  RegistrationInformation RegistrationInformation { get; }
}

internal class DeviceRegistrar : IDeviceRegistrar
{
  private readonly IHardwareIdentifier _hwid;
  private readonly IRegistrationPersistence _registrationPersistence;
  private readonly ILogger<DeviceRegistrar> _logger;
  private RegistrationInformation? _lastRegistrationResult;

  public DeviceRegistrar(
    IHardwareIdentifier hwid,
    IRegistrationPersistence registrationPersistence,
    ILogger<DeviceRegistrar> logger)
  {
    _hwid = hwid;
    _registrationPersistence = registrationPersistence;
    _logger = logger;
  }

  public bool IsDeviceRegistered => _lastRegistrationResult is not null;

  public RegistrationInformation RegistrationInformation =>
    _lastRegistrationResult ?? throw new InvalidOperationException("Device has not been registered yet.");

  public async Task<OpResult<RegistrationInformation>> RegisterDevice(CancellationToken cancellationToken)
  {
    try
    {
      using var _ = _logger.BeginMemberScope();

      _logger.LogInformation("Starting device registration.");

      while (!cancellationToken.IsCancellationRequested)
      {
        var res = await TryRegisterDevice(cancellationToken);

        if (res.IsSuccess)
        {
          _lastRegistrationResult = res.Value;
          return OpResult.Ok(res.Value);
        }

        // wait 30 seconds and then try again
        await Task.Delay(TimeSpan.FromSeconds(30), cancellationToken);
      }

      return OpResult.Fail<RegistrationInformation>("Failed to register device");
    }
    catch (TaskCanceledException ex)
    {
      _logger.LogWarning(ex, "Application shutdown started before device could be registered.");
      return OpResult.Fail<RegistrationInformation>(ex);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while attempting to register device.");
      return OpResult.Fail<RegistrationInformation>(ex);
    }
  }

  private async Task<OpResult<RegistrationInformation>> TryRegisterDevice(CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("Using configuration file stored at: {configPath}", PathHelpers.ConfigFilePath);

      var getConfigResult = await _registrationPersistence.GetConfigurationParameters();

      // seeing a FileNotFoundException is how we know we aren't registered
      if (getConfigResult.Exception is FileNotFoundException)
      {
        _logger.LogInformation("Configuration file not found. Attempting to register device.");
        return await RegisterDeviceImpl(cancellationToken);
      }

      return getConfigResult;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while attempting to register device.");
      return OpResult.Fail<RegistrationInformation>(ex, "Failed to register device.");
    }
  }

  private async Task<OpResult<RegistrationInformation>> RegisterDeviceImpl(CancellationToken cancellationToken)
  {
    _logger.LogWarning("Unable to find {path}, attempting device registration", PathHelpers.ConfigFilePath);

    if (!File.Exists(PathHelpers.RegistrationFilePath))
    {
      _logger.LogError("Registration Parameter file {path} not found, exiting.", PathHelpers.RegistrationFilePath);
      return OpResult.Fail<RegistrationInformation>("Both the config file and registration file could not be found.");
    }

    _logger.LogInformation("Found {path}, performing device registration.", PathHelpers.RegistrationFilePath);

    try
    {
      var getRegResult = await _registrationPersistence.GetRegistrationParameters();
      var regParams = getRegResult.Value;
      if (!getRegResult.IsSuccess || regParams is null)
      {
        return OpResult.Fail<RegistrationInformation>("Registration.json file returned no content. Will re-check in 30 seconds");
      }

      var deviceDetails = await _hwid.GenerateDeviceDetails(false);

      DeviceRegistrationHelper.RegisterDevice(
        regParams,
        deviceDetails,
        _logger);

      var getConfigResult = await _registrationPersistence.GetConfigurationParameters();
      var deviceRegistration = getConfigResult.Value;

      if (!getConfigResult.IsSuccess || deviceRegistration is null)
      {
        return OpResult.Fail<RegistrationInformation>("Registration.json file successfully parsed, but config file was empty. Will re-check in 30 seconds");
      }

      File.Delete(PathHelpers.RegistrationFilePath);

      return OpResult.Ok(deviceRegistration);
    }
    catch (DeviceOfflineException ex)
    {
      var secondsToWait = 15;
      _logger.LogError(ex, "Device Registration failed. Will retry. Waiting {secondsToWait} seconds", secondsToWait);
      await Task.Delay(TimeSpan.FromSeconds(secondsToWait), cancellationToken);
    }
    catch (BackendRejectedException ex)
    {
      var secondsToWait = 60;
      _logger.LogError(ex, "Backend Rejected registration. Will retry. Waiting {secondsToWait} seconds", secondsToWait);
      await Task.Delay(TimeSpan.FromSeconds(secondsToWait), cancellationToken);
    }

    return OpResult.Fail<RegistrationInformation>("Failed to register device");
  }
}
