using Immybot.Agent.Configuration.Options;
using Immybot.Agent.Ephemeral.Services;
using Immybot.Agent.Persistent.Resilience;
using Immybot.Agent.Startup.Shared.Services;
using Immybot.Shared.Configuration.InMemory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Polly.Registry;

namespace Immybot.Agent.Persistent.Services;

public class EphemeralConnectionManager(
  ResiliencePipelineProvider<string> resilienceProvider,
  TimeProvider timeProvider,
  IServiceProvider serviceProvider,
  IAgentHubConnection agentHubConnection,
  IAgentVersionProvider agentVersionProvider,
  IInMemoryConfigurationSource<EphemeralAgentOptions> ephemeralOptionsSource,
  ILogger<EphemeralConnectionManager> logger) : BackgroundService
{
  private readonly TimeSpan _maxReconnectDelay = TimeSpan.FromSeconds(10);
  private readonly string _agentInstanceId = Ulid.NewUlid().ToString();

  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    while (!stoppingToken.IsCancellationRequested)
    {
      try
      {
        var resiliencePipeline = resilienceProvider.GetPipeline(EphemeralReconnectPolicy.Key);

        // `EphemeralRpcConnection.Start` has its own lifetime logic, which will rethrow
        // exceptions in some cases.  It was written before it was possible for the
        // persistent agent to host it.  If the connection is successful, the Start
        // method won't return.  So for now, we'll just wrap it in this policy, which
        // will retry indefinitely.  We may decide to refactor EphemeralRpcConnection
        // later for clarity and maintainability.
        var shouldContinueTrying = await resiliencePipeline.ExecuteAsync(
          static async (state, ct) => await state.StartEphemeralConnection(ct),
          state: this,
          stoppingToken);

        logger.LogInformation("Ephemeral connection has ended.");
        if (!shouldContinueTrying)
        {
          logger.LogInformation("Ephemeral connection attempt yielded a result indicating it should no-longer attempt trying. Exiting loop.");
          break; // Exit the loop if we shouldn't retry.
        }
      }
      catch (Exception ex) when (!stoppingToken.IsCancellationRequested)
      {
        logger.LogError(ex, "Error occurred while running ephemeral connection.");
      }

      if (!stoppingToken.IsCancellationRequested)
      {
        var waitSeconds = _maxReconnectDelay.TotalSeconds * Random.Shared.NextDouble();
        logger.LogInformation("Waiting {WaitSeconds:N0} seconds to retry connection...", waitSeconds);
        await Task.Delay(TimeSpan.FromSeconds(waitSeconds), timeProvider, stoppingToken)
          .ConfigureAwait(ConfigureAwaitOptions.SuppressThrowing);
      }
    }

    logger.LogInformation("Ephemeral connection manager has stopped.");
  }

  private async Task<bool> StartEphemeralConnection(CancellationToken cancellationToken)
  {
    logger.LogInformation("Starting ephemeral connection.");

    if (!agentHubConnection.IsConnected)
    {
      logger.LogDebug("Waiting for persistent connection to agent hub.");
    }

    await agentHubConnection.WaitForConnection(cancellationToken);

    logger.LogInformation("Persistent connection to agent hub established.  Starting ephemeral connection.");
    var result = await agentHubConnection.CreateEphemeralSession();
    logger.LogInformation("Received create ephemeral session result: {ResultType}", result.Status);
    if (!result.IsSuccessStatus)
    {
      // Version mismatch is a special case where we don't want to retry the connection, since it will never succeed until the agent is updated.
      if (result.Status == Immybot.Shared.DataContracts.Signalr.AgentDtos.CreateEphemeralSessionResultStatus.AgentVersionMismatch)
      {
        logger.LogWarning("Backend rejected ephemeral session creation due to agent version mismatch. We will not continue trying to connect an ephemeral session.");
        return false;
      }

      return true;
    }

    var agentVersion = agentVersionProvider.GetAgentFileVersion();
    ephemeralOptionsSource.Update(_ => new EphemeralAgentOptions()
    {
      BackendAddress = result.BackendWebSocketAddress,
      SessionId = result.SessionId,
      AgentInstanceId = _agentInstanceId,
      ProviderAgentId = result.ProviderAgentId,
      ImmyScriptPath = Environment.ExpandEnvironmentVariables(result.ImmyScriptPath),
      AgentAssemblyVersion = agentVersion,
    });

    var ephemeralRpcConnection = serviceProvider.GetRequiredService<IEphemeralRpcConnection>();
    await ephemeralRpcConnection.RunAsync(cancellationToken);

    return true;
  }
}
