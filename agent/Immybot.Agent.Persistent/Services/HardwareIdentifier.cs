using System.Runtime.Versioning;
using Immybot.Shared.DataContracts.Signalr.AgentDtos;
using Microsoft.Extensions.Logging;
using NuGet.Versioning;
using Immybot.Agent.Startup.Shared.Services;
using Immybot.Shared.Extensions;
using System.Management;

namespace Immybot.Agent.Persistent.Services;

/// <summary>
/// Provides methods for creating and verifying unique device IDs based on hardware.
/// </summary>
public interface IHardwareIdentifier
{
  /// <summary>
  /// Retrieves a unique Hardware Identifier string derived from CPU, GPU, BIOS, MOBO, and TPM entropy sources where available.
  /// </summary>
  /// <returns></returns>
  string GetHardwareId();

  bool IsImmyHWID(string serial);

  // TODO (62): Remove this method once all instances no longer have an iothub
  /// <summary>
  /// Generates device details based on IoT Hub DeviceId and board serial number.
  /// </summary>
  [Obsolete("This will be replaced in version 0.58 with the parameterless overload.")]
  Task<AgentDeviceDetails> GenerateDeviceDetails(Guid deviceId, string serialNumber, bool removeFromIotHub);

  /// <summary>
  /// Generates device details, which are used on the backend for creating the ProviderAgent.
  /// </summary>
  /// <param name="requireRegisteredDevice">
  /// <para>
  ///   If true, this will attempt to populate the AgentDeviceId property on the returned
  ///   <see cref="AgentDeviceDetails"/>, which requires that the agent be registered with
  ///   the backend and the config.json file exists.
  /// </para>
  /// <para>
  ///   Use false if you are generating device details for a new agent that has not yet been registered.
  /// </para>
  /// </param>
  Task<AgentDeviceDetails> GenerateDeviceDetails(bool requireRegisteredDevice = true);
}

/// <inheritdoc />
[SupportedOSPlatform("windows")]
internal class HardwareIdentifier : IHardwareIdentifier
{
  private readonly TimeProvider _timeProvider;
  private readonly IRegistrationPersistence _registrationPersistence;
  private readonly IAgentVersionProvider _agentVersionProvider;
  private readonly ISystemDetailsProvider _systemDetailsProvider;
  private readonly ILogger<HardwareIdentifier> _logger;
  private string? _systemHwidString;
  private int? _domainRole;
  private bool _domainRoleFetched;

  public HardwareIdentifier(
    TimeProvider timeProvider,
    IRegistrationPersistence registrationPersistence,
    IAgentVersionProvider agentVersionProvider,
    ISystemDetailsProvider systemDetailsProvider,
    ILogger<HardwareIdentifier> logger)
  {
    _timeProvider = timeProvider;
    _registrationPersistence = registrationPersistence;
    _agentVersionProvider = agentVersionProvider;
    _systemDetailsProvider = systemDetailsProvider;
    _logger = logger;
  }

  /// <inheritdoc />
  public Task<AgentDeviceDetails> GenerateDeviceDetails(
    Guid deviceId,
    string serialNumber,
    bool removeFromIotHub)
  {
    var result = _agentVersionProvider.GetAgentVersion();

    var details = new AgentDeviceDetails
    {
      AgentDeviceId = deviceId,
      SerialNumber = serialNumber,
      DomainRole = GetDomainRole(),
      OperatingSystemName = _systemDetailsProvider.GetOperatingSystemFullName(),
      Manufacturer = _systemDetailsProvider.GetManufacturer() ?? string.Empty,
      DeviceName = Environment.MachineName,
      LastUpdated = _timeProvider.GetUtcNow(),
      IsOnline = true,
      HardwareIdentifier = GetHardwareId(),
      RemoveFromIotHub = removeFromIotHub,
      AgentVersion = result.IsSuccess ? result.Value : new SemanticVersion(0, 0, 0),
    };

    return Task.FromResult(details);
  }

  /// <inheritdoc />
  public async Task<AgentDeviceDetails> GenerateDeviceDetails(bool requireRegisteredDevice = true)
  {
    var deviceId = Guid.Empty;

    if (requireRegisteredDevice)
    {
      var configResult = await _registrationPersistence.GetConfigurationParameters();
      if (!configResult.IsSuccess)
      {
        _logger.LogFailure(configResult);
        throw new InvalidOperationException("Failed to get configuration parameters from disk.");
      }

      if (!configResult.Value.TryGetDeviceId(out deviceId))
      {
        throw new InvalidDataException("Device ID could not be parsed from configuration.");
      }
    }

    var result = _agentVersionProvider.GetAgentVersion();

    var details = new AgentDeviceDetails
    {
      AgentDeviceId = deviceId,
      OperatingSystemName = _systemDetailsProvider.GetOperatingSystemFullName(),
      Manufacturer = _systemDetailsProvider.GetManufacturer() ?? string.Empty,
      DeviceName = Environment.MachineName,
      LastUpdated = _timeProvider.GetUtcNow(),
      IsOnline = true,
      HardwareIdentifier = GetHardwareId(),
      SerialNumber = _systemDetailsProvider.GetSerialNumber() ?? string.Empty,
      DomainRole = GetDomainRole(),
      AgentVersion = result.IsSuccess ? result.Value : new SemanticVersion(0, 0, 0),
    };

    return details;
  }

  private int? GetDomainRole()
  {
    // Return cached value if we have already attempted the lookup
    if (_domainRoleFetched)
      return _domainRole;

    _domainRoleFetched = true;

    try
    {
      using var searcher = new ManagementObjectSearcher("SELECT DomainRole FROM Win32_ComputerSystem");
      using var collection = searcher.Get();
      var first = collection.Cast<ManagementObject>().FirstOrDefault();
      if (first is not null)
      {
        _domainRole = Convert.ToInt32(first["DomainRole"]);
      }
    }
    catch (Exception ex)
    {
      _logger.LogWarning(ex, "Failed to retrieve DomainRole from WMI");
      _domainRole = null;
    }
    return _domainRole;
  }

  /// <inheritdoc />
  public string GetHardwareId()
  {
    if (_systemHwidString is null)
      _systemHwidString = _systemDetailsProvider.GetHardwareId();

    return _systemHwidString;
  }

  /// <inheritdoc />
  public bool IsImmyHWID(string serial)
  {
    return serial.StartsWith("IMMYHWID==");
  }
}
