using System.Text;
using Immybot.Agent.Persistent.Configuration;
using Immybot.Shared.Abstractions.Device.Exceptions;
using Immybot.Shared.DataContracts.DeviceRegistration;
using Immybot.Shared.DataContracts.Signalr.AgentDtos;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using org.whispersystems.curve25519;
using RestSharp;

namespace Immybot.Agent.Persistent.Helpers;

// TODO: Put behind interface.  Create implementations for Windows and Linux.
public static class DeviceRegistrationHelper
{
  public delegate void Log(string msg);

  public enum LogLevel
  {
    INFO = 4,
    WARN = 2,
    ERR = 1
  }


  public static void RegisterDevice(
    AgentRegistrationParams regParams,
    AgentDeviceDetails agentDeviceDetails,
    ILogger logger)
  {
    var serialNumberOrHWID = !string.IsNullOrEmpty(agentDeviceDetails.SerialNumber) ?
      agentDeviceDetails.SerialNumber : agentDeviceDetails.HardwareIdentifier;

    var jsonSerializerSettings = GetJsonSerializerSettings();

    if (Directory.CreateDirectory(PathHelpers.ProgramCADPath).Exists)
    {
      logger.LogInformation("Created directory at {DirName}", PathHelpers.ProgramCADPath);

      var client = new RestClient(regParams.AgentInstallerBackendAddress);

      logger.LogInformation(
        "Attempting to register new device with serial number / hardware id: {SerialNumberOrHWID}",
        serialNumberOrHWID);

      var challengeRequest = new RestRequest("installer/challenge/request", Method.Post);
      challengeRequest.AddQueryParameter("agentId", regParams.AgentInstallerID);

      LogRequest(challengeRequest, logger, jsonSerializerSettings);
      var challengeResponse = client.Execute(challengeRequest);
      LogResponse(challengeResponse, logger, jsonSerializerSettings);

      if (!challengeResponse.IsSuccessful)
      {
        // TODO: This is not right.  Should be fixed when replacing RestSharp with Refit.
        // 0 StatusCode indicates connectivity issue and we should retry
        if (challengeResponse.StatusCode != 0)
        {
          throw new BackendRejectedException();
        }
        else
        {
          throw new DeviceOfflineException();
        }
      }

      if (string.IsNullOrWhiteSpace(challengeResponse.Content))
      {
        throw new HttpRequestException("Challenge response was empty.");
      }

      var challengeObj = JsonConvert.DeserializeObject<ChallengeRequest>(challengeResponse.Content);


      if (challengeObj is null)
      {
        logger.LogError("Failed to deserialize challenge response. {Content}",
          challengeResponse.Content);
        return;
      }

      if (challengeObj.Challenge is not { Length: > 0 })
      {
        logger.LogError("'{PropertyName}' property in response was empty. {Content}",
          nameof(challengeObj.Challenge),
          challengeResponse.Content);
        return;
      }

      if (challengeObj.ChallengeSignature is not { Length: > 0 })
      {
        logger.LogError("'{PropertyName}' property in response was empty. {Content}",
          nameof(challengeObj.ChallengeSignature),
          challengeResponse.Content);
        return;
      }


      logger.LogInformation("""
                            Successfully Fetched Challenge.
                            -------------Challenge Information-------------
                            Challenge: {Challenge}
                            Signature: {Signature}
                            -----------------------------------------------

                            """,
        Convert.ToBase64String(challengeObj.Challenge),
        Convert.ToBase64String(challengeObj.ChallengeSignature));

      logger.LogInformation("Computing challenge response and sending to server...");


      var verifyRequest = new RestRequest("installer/challenge/verify", Method.Post);

      // use the 'Donna' implementation by Google
      var curve = Curve25519.getInstance(Curve25519.BEST);
      var signedChallengeResponse = curve.calculateSignature(
         regParams.AgentInstallerKey,
        // Sign the message in format of TargetDeviceID + Exp + OrigChallenge + OrigChallengeSig
        [
          .. Encoding.UTF8.GetBytes(serialNumberOrHWID),
          .. BitConverter.GetBytes(challengeObj.Exp.ToBinary()),
          .. challengeObj.Challenge,
          .. challengeObj.ChallengeSignature,
        ]);

      var computedResponse = new ChallengeResponse(
        serialNumberOrHWID,
        challengeObj,
        signedChallengeResponse,
        agentDeviceDetails);

      verifyRequest.AddJsonBody(computedResponse);

      LogRequest(verifyRequest, logger, jsonSerializerSettings);
      var verifyResponse = client.Execute(verifyRequest);
      LogResponse(verifyResponse, logger, jsonSerializerSettings);

      if (!verifyResponse.IsSuccessful)
      {
        logger.LogError("Challenge response was not successful");

        if (verifyResponse.StatusCode != 0)
        {
          throw new BackendRejectedException();
        }
        else
        {
          throw new DeviceOfflineException();
        }
      }

      if (string.IsNullOrWhiteSpace(verifyResponse.Content))
      {
        throw new HttpRequestException("Verify response was empty.");
      }

      var challengeResponseObj = JsonConvert.DeserializeObject<RegistrationInformation>(verifyResponse.Content);

      if (challengeResponseObj is null)
      {
        logger.LogError("Challenge response object could not be deserialized. {Content}",
          verifyResponse.Content);
        return;
      }

      logger.LogInformation("""
                            Received Registration information.
                            -------------Registration Information-------------
                            Device ID: {DeviceId}
                            --------------------------------------------------

                            """,
        challengeResponseObj.DeviceId);
      challengeResponseObj.BoardSerialNumber = serialNumberOrHWID;

      try
      {
        // Generate client configuration file
        File.WriteAllText(PathHelpers.ConfigFilePath, JsonConvert.SerializeObject(challengeResponseObj, jsonSerializerSettings));
      }
      catch (Exception err)
      {
        throw new ConfigurationFileException("Failed to create configuration file at " + PathHelpers.ConfigFilePath, err);
      }

      logger.LogInformation(
        "Configuration file successfully created and stored at {ConfigFilePath}",
        PathHelpers.ConfigFilePath);
    }
    // Failed creating Directory. Fail install
    else
    {
      throw new DirectoryException("Failed to create directory " + PathHelpers.ProgramCADPath);
    }
  }

  private static JsonSerializerSettings GetJsonSerializerSettings()
  {
    var settings = new JsonSerializerSettings
    {
      NullValueHandling = NullValueHandling.Include,
      ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
      Formatting = Formatting.Indented,
    };

    settings.Converters.Add(new Newtonsoft.Json.Converters.StringEnumConverter());

    return settings;
  }

  private static void LogRequest(RestRequest req, ILogger logger, JsonSerializerSettings jsonSerializerSettings)
  {
    var obj = new
    {
      req.Resource,
      req.Method,
      req.Parameters,
    };
    var serialized = JsonConvert.SerializeObject(obj, jsonSerializerSettings);
    logger.LogInformation("REQUEST: {Request}", serialized);
  }

  private static void LogResponse(RestResponse res, ILogger logger, JsonSerializerSettings jsonSerializerSettings)
  {
    var obj = new
    {
      res.Request.Resource,
      res.Request.Method,
      res.StatusCode,
      res.ErrorException,
      res.ErrorMessage,
      res.Content,
      res.ContentLength,
      res.ContentType,
      res.ResponseUri,
      res.ResponseStatus,
      res.IsSuccessful,
      res.Server,
      res.Headers
    };
    var serialized = JsonConvert.SerializeObject(obj, jsonSerializerSettings);
    logger.LogInformation("RESPONSE: {Response}", serialized);
  }
}
