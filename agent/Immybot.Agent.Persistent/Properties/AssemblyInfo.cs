using System.Runtime.CompilerServices;
using System.Runtime.Versioning;

[assembly: InternalsVisibleTo("Immybot.Agent.AgentSimulator")]
[assembly: InternalsVisibleTo("Immybot.Agent.Startup")]
[assembly: InternalsVisibleTo("Immybot.Agent.Persistent.Tests")]
[assembly: InternalsVisibleTo("Immybot.Agent.Startup.Tests")]
[assembly: InternalsVisibleTo("DynamicProxyGenAssembly2")]
[assembly: SupportedOSPlatform("windows")]
