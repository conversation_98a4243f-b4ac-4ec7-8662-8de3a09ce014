using System.CommandLine;
using Immybot.Agent.Startup.Startup;
using Serilog;

var rootCommand = new RootCommand("Immybot Agent")
{
  CommandProvider.GetServiceCommand(args),
  CommandProvider.GetEphemeralCommand(args),
  CommandProvider.GetRemoteControlCommand(args)
};

try
{
  await rootCommand.InvokeAsync(args);
}
catch (Exception err)
{
  Log.Error(err, "A fatal exception occured when attempting to invoke command.");
}

Log.Information("Shutting down");

return -1;
