{"profiles": {"Ephemeral": {"commandName": "Project", "commandLineArgs": "ephemeral run --ImmyScriptPath \"%ProgramData%\\ImmyBot\\Scripts\\Development\" --BackendAddress http://localhost:5000 --SessionId \"00000000-0000-0000-0000-000000000000\" --ProviderAgentId 1", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "Service": {"commandName": "Project", "commandLineArgs": "service run", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "Install": {"commandName": "Project", "commandLineArgs": "service install", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "RemoteControl": {"commandName": "Project", "commandLineArgs": "remote-control --host http://localhost:5000", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "WSL": {"commandName": "WSL2", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}, "distributionName": ""}}}