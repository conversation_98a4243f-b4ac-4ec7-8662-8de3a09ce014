using System.IO;
using System.Net;
using System.Xml;
using Immybot.Agent.Shared;

namespace Immybot.Shared.Functions
{
  internal class InstallImmyAgentMsi : AInstallImmyAgentMsi
  {
    private static string Name => "Installing Embedded ImmyAgent MSI";
    private string MsiDownloadUri
    {
      get
      {
        #pragma warning disable SYSLIB0014 // Type or member is obsolete
        using WebClient wc = new WebClient();
        #pragma warning restore SYSLIB0014 // Type or member is obsolete
        return wc.DownloadString(HelpersAndConstants.SASInstallerURLEndpoint + AgentVersion);
      }
    }

    private string _localMSIPath;
    public XmlNode XmlCommand { get { return HelpersAndConstants.ReturnSingleCommandXml(Name, CommandScript, _localMSIPath).FirstChild; } }

    private string CommandScript
    {
      get
      {
        var fileName = "ImmyBotAgent.msi";
        var performDownload = true;
        _localMSIPath = Path.Combine(Path.GetTempPath(), fileName);

        if (System.Version.TryParse(AgentVersion, out var version))
        {
          fileName = $"ImmyBotAgent-{version.ToString().Replace(".","-")}.msi";
          _localMSIPath = Path.Combine(Path.GetTempPath(), fileName);
          if(File.Exists(_localMSIPath))
          {
            // Only want to use existing file if it is a specific version
            performDownload = false;
          }
        }
        if (performDownload)
        {
          #pragma warning disable SYSLIB0014 // Type or member is obsolete
          using WebClient wc = new WebClient();
          #pragma warning restore SYSLIB0014 // Type or member is obsolete
          wc.DownloadFile(MsiDownloadUri, _localMSIPath);
        }
        var installCmd = $"msiexec /i \"{fileName}\" /qn /l*v \"C:\\ImmyBotAgentInstall.log\" /norestart REBOOT=REALLYSUPPRESS ID=\"{InstallerID}\" ADDR=\"{BackendAddress}\" KEY=\"{InstallerKey}\"";
        return installCmd;
      }
    }
  }
}
