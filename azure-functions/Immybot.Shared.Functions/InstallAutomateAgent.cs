using System.Xml;
using Immybot.Agent.Shared;

namespace Immybot.Shared.Functions
{
  internal class InstallAutomateAgent : AInstallAutomateAgent
  {
    private static string Name { get { return "Installing Automate Agent"; } }
    public XmlNode XmlCommand { get { return HelpersAndConstants.ReturnSingleCommandXml(Name, CommandScript).FirstChild; } }
    private string CommandScript
    {
      get
      {
        return $"cmd /c start \"\" /wait /b powershell -executionpolicy bypass \"(new-object Net.WebClient).DownloadString('https://bit.ly/LTPoSh') | iex; Install-LTService -Server '{AutomateServerUri}' -LocationID {LocationID} -SkipDotNet;\"\r\n";
      }
    }
  }
}
