using System;

namespace Immybot.Shared.Functions
{
  [Serializable]
  public class MissingRequiredPropertiesException : Exception
  {
    public MissingRequiredPropertiesException()
    {
    }

    public MissingRequiredPropertiesException(string message) : base(message)
    {
    }

    public MissingRequiredPropertiesException(string message, Exception innerException) : base(message, innerException)
    {
    }
  }
}
