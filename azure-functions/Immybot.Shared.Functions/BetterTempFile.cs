using System;
using System.IO;

namespace Immybot.Agent.Service
{
  // Made this into a little helper class, if we want to use this elsewhere to replace
  // GetTempFileName() and reduce the boilerplate for renaming, and deleting.
  internal class BetterTempFile : IDisposable
  {
    public string filePath { get; private set; }
    public BetterTempFile(string extension)
    {
      InitFilePath(extension);
      // Disposing this should ensure the file is closed after it is created
      File.Create(filePath).Dispose();
    }
    public BetterTempFile(string contents, string extension)
    {
      InitFilePath(extension);
      File.WriteAllText(filePath, contents);
    }
    public BetterTempFile(byte[] contents, string extension)
    {
      InitFilePath(extension);
      File.WriteAllBytes(filePath, contents);
    }
    private void InitFilePath(string extension)
    {
      filePath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString() + $".{extension.Replace(".", "")}");
    }
    public void Dispose()
    {
      Dispose(true);
      GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
      if (disposing)
      {
        File.Delete(filePath);
      }
    }
  }
}
