meta {
  name: Create Agent EXE
  type: http
  seq: 1
}

post {
  url: {{Host}}/api/ImmyAgentInstallerBundler
  body: json
  auth: none
}

body:json {
  {
    "AgentVersion": "{{AgentVersion}}",
    "InstallerID": "{{InstallerId}}",
    "B64PrivateKey": "{{B64PrivateKey}}",
    "BackendAddress": "{{BackendAddress}}"
  }
}

vars:pre-request {
  UniqueInstallerIdPerRequest: true
  InstallerId: 00000000-0000-0000-0000-000000000000
  AgentVersion: 0.60.0-alpha.5
  B64PrivateKey: aGVsbG93b3JsZGhlbGxvd29ybGRoZWxsb3dvcmxkMTI=
  BackendAddress: https://ckt.immy.bot/plugins/api/v1/1
}

script:pre-request {
  const { v4: uuidv4 } = require('uuid');
  if(bru.getVar("UniqueInstallerIdPerRequest") == true){
    bru.setVar("InstallerId", uuidv4());
  }
}
