using System;
using System.IO;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using CliWrap;
using Immybot.Shared.DataContracts.Agent;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Immybot.Shared.Functions
{
  public class ImmyAgentInstallerBundler
  {
    private static readonly string _baseHome = Environment.GetEnvironmentVariable("HOME") ?? Environment.GetEnvironmentVariable("TEMP");
    private static readonly string _baseHomeStorageDirectory = Path.Combine(_baseHome, "data", "ImmyBot");
    // Storage directory of bundled Installer .EXEs
    private static readonly string _bundleDirectory = Path.Combine(_baseHomeStorageDirectory, "ImmyAgent_GeneratedInstallerBundles");
    // Storage directory of raw Installer .MSIs
    private static readonly string _installerDirectory = Path.Combine(_baseHomeStorageDirectory, "ImmyAgent_Installers");
    // Storage directory of 7z archived Installers
    private static readonly string _archivedInstallerDirectory = Path.Combine(_baseHomeStorageDirectory, "ImmyAgent_ArchivedInstallers");
    // Lets us get access to the 7Zip EXE in ICD Tools directory
    private static readonly string _ICDToolDirectory = Path.Combine(_baseHomeStorageDirectory, "ICDTool", "ICD");
    // We'll eventually need to reach out to blob storage directly
    private static readonly string _getSASInstallerURLEndpoint = Environment.GetEnvironmentVariable("MANAGER_GETSASINSTALLERURL", EnvironmentVariableTarget.Process);
    private static readonly string _azureStorageConnectionString = Environment.GetEnvironmentVariable("AZURE_STORAGE_CONNECTION_STRING");
    private readonly ILogger<ImmyAgentInstallerBundler> _logger;

    public ImmyAgentInstallerBundler(ILogger<ImmyAgentInstallerBundler> logger)
    {
      _logger = logger;
    }

    // ImmyAgentInstallerBundler will generate a bundled EXE installer from specified parameters
    [Function("ImmyAgentInstallerBundler")]
    public async Task<IActionResult> Run(
      [HttpTrigger(AuthorizationLevel.Anonymous, "post", "get", Route = null)] HttpRequest req)
    {

      if (string.IsNullOrEmpty(_azureStorageConnectionString))
      {
        _logger.LogError("Missing azure storage connection string.  Cannot continue");
        return new BadRequestObjectResult("Error: Internal Error attempting to bundle installer");
      }

      ImmyPpkgBuilder.EnsureICDToolsExist();

      var azureStorageBlobService = new AzureStorageBlobService(_azureStorageConnectionString);

      string payloadString = await new StreamReader(req.Body).ReadToEndAsync();
      AgentBundlerParameters bundleP = JsonConvert.DeserializeObject<AgentBundlerParameters>(payloadString);

      _logger.LogInformation("Processing request to build bundle");
      string missingParameterErrors = "";
      // Do some sanity checks to ensure all the required bits of info are here
      if (string.IsNullOrEmpty(bundleP.AgentVersion) || !Regex.IsMatch(bundleP.AgentVersion, @"\d+\.\d+\.\d+"))
        missingParameterErrors += "Error: Missing or Invalid target Agent version" + Environment.NewLine;
      if (string.IsNullOrEmpty(bundleP.BackendAddress) || !Regex.IsMatch(bundleP.BackendAddress, @"^(https)://[^\s/$.?#].[^\s]*$"))
        missingParameterErrors += "Error: Missing or Invalid Backend Endpoint address" + Environment.NewLine;
      if (string.IsNullOrEmpty(bundleP.InstallerID))
        missingParameterErrors += "Error: Missing or Invalid Installer ID" + Environment.NewLine;
      if (string.IsNullOrEmpty(bundleP.B64PrivateKey) || (Convert.FromBase64String(bundleP.B64PrivateKey).Length != 32))
        missingParameterErrors += "Error: Missing or Invalid Installer CryptoKey" + Environment.NewLine;

      if (missingParameterErrors.Length > 0)
      {
        _logger.LogInformation(
          "Bundle request failed for following reasons:\n{MissingParameterErrors}",
          missingParameterErrors);
        return new BadRequestObjectResult(missingParameterErrors);
      }

      // Installer parameters appear to be valid
      string targetArchivedInstallerPath = Path.Combine(_archivedInstallerDirectory, bundleP.AgentVersion + ".7z");
      string targetInstallerPath = Path.Combine(_installerDirectory, bundleP.AgentVersion + ".msi");
      string targetBundleParamHash = "";

      // Check if we have an archived binary available first
      bool archivedInstallerAvailable = File.Exists(targetArchivedInstallerPath);

      Directory.CreateDirectory(_archivedInstallerDirectory);
      Directory.CreateDirectory(_installerDirectory);
      Directory.CreateDirectory(_bundleDirectory);

      // If we dont have an archived installer, and not raw MSI either, we need to download it
      if (!archivedInstallerAvailable && !File.Exists(targetInstallerPath))
      {
        try
        {
          #pragma warning disable SYSLIB0014 // Type or member is obsolete
          using var client = new WebClient();
          #pragma warning restore SYSLIB0014 // Type or member is obsolete
          client.DownloadFile(client.DownloadString(_getSASInstallerURLEndpoint + bundleP.AgentVersion), targetInstallerPath);
        }
        catch (Exception err)
        {
          _logger.LogError(err, "Failed to download installer from blob storage");
          return new BadRequestObjectResult("Error: Internal Error attempting to bundle installer");
        }
      }

      // Target version of Agent installer has been downloaded from Blob storage.
      // We need to get the hash of the bundle params to figure out if we really
      // need to build the bundle, or can instead deliver a cached bundle.
      using (var sha256 = SHA256.Create())
      {
        // It would be perhaps a better idea to hash a serialized byte array of the
        // Parsed JSON object, as that would prevent the differences in orgin JSON string
        // (ex. Different NL feed characters) from producing different hashes for identical
        // bundle parameter requests. Something to consider, but likely uncessary optimization.
        targetBundleParamHash = Convert.ToHexString(sha256.ComputeHash(Encoding.UTF8.GetBytes(payloadString)));
      }
      var blobName = targetBundleParamHash + "/ImmyAgentInstallerBundle.exe";
      var result = await azureStorageBlobService.TryGetFileAsync("azure-bundle-exes", blobName);
      if (result.IsSuccess)
      {
        // return cached bundle exe
        return new OkObjectResult(result.Value);
      }

      _logger.LogInformation("No cached bundle available for delivery. Attempting to generate...");
      // We need to create bundle as we have no matching cached version

      // SFX installers binary assembly order: 7zSD + configTxt + installerArchive --> bundle.exe
      string targetBundlePath = Path.Combine(_bundleDirectory, targetBundleParamHash);
      string sas;
      try
      {
        await using (var finalizedBundle = File.Create(targetBundlePath))
        {
          _logger.LogInformation("Created empty bundle file {BundlePath}",
            targetBundleParamHash);

          await finalizedBundle.WriteAsync(Properties.Resources._7zSDImmyAgentPatched, 0, Properties.Resources._7zSDImmyAgentPatched.Length);
          _logger.LogInformation("Wrote 7zSD.sfx...");

          var archiverConfig = new StringBuilder();
          archiverConfig.AppendLine(";!@Install@!UTF-8!");
          archiverConfig.AppendLine("Title = \"ImmyBot Agent\"");
          archiverConfig.AppendLine($"ExecuteParameters = \"ID={bundleP.InstallerID} KEY={bundleP.B64PrivateKey} ADDR={bundleP.BackendAddress}\"");
          archiverConfig.AppendLine($"ExecuteFile=\"{bundleP.AgentVersion}.msi\"");
          archiverConfig.AppendLine(";!@InstallEnd@!");

          var configBytes = Encoding.UTF8.GetBytes(archiverConfig.ToString());
          await finalizedBundle.WriteAsync(configBytes, 0, configBytes.Length);
          _logger.LogInformation("Wrote config.txt...");

          if (archivedInstallerAvailable)
          {
            _logger.LogInformation("Using pre-archived installer file...");
            await using (var archivedInstaller = File.OpenRead(targetArchivedInstallerPath))
              await archivedInstaller.CopyToAsync(finalizedBundle);
          }
          else
          {
            _logger.LogInformation("Creating 7z archive of installer. (this will take some time)...");
            var command = await Cli.Wrap(Path.Combine(_ICDToolDirectory, "7zr.exe"))
                .WithArguments(a => a
                    .Add("a")
                    .Add(targetArchivedInstallerPath)
                    .Add(targetInstallerPath)
                    // -mx=0 switch basically means 'do no compression'. This may seem counter-intutive, but
                    // Even with max compression on BCJ2 algo, I haven't seen 7z take the file size down more than
                    // about 5MB. I suspect this is a result of some internal compression already happening in the MSI installer,
                    // and trying to compress already compressed data won't net much. Because of this, we might as well not waste
                    // any aditional CPU time trying to compress it, and instead just ship it as fast as possible to the user.
                    // We just need the 7z version for the SFX extractor to work.
                    .Add("-mx=0")
                    )
                .ExecuteAsync();
            if (command.ExitCode != 0)
            {
              _logger.LogInformation("Failed to use 7Zip binary to archive installer.");
              return new BadRequestObjectResult("Error: Internal Error attempting to bundle installer");
            }

            await using (var archivedInstaller = File.OpenRead(targetArchivedInstallerPath))
              await archivedInstaller.CopyToAsync(finalizedBundle);
          }
          await finalizedBundle.FlushAsync();
          finalizedBundle.Position = 0;
        }
        // Sign The bundle before we uploade it to blob storage
        _logger.LogInformation("Attempting to sign bundle with cert...");
        try
        {
          if (await FileEVSigner.SignFileAsync(targetBundlePath))
          {
            _logger.LogInformation("Successfully signed bundle!");
          }
        }
        catch (Exception e)
        {
          _logger.LogError(e, "Failed to run SignTool on generated EXE Bundle! Returning an Unsigned binary!");
        }

        await using (var finalizedBundle = File.OpenRead(targetBundlePath))
          sas = await azureStorageBlobService.UploadAsync("azure-bundle-exes", blobName, finalizedBundle);

        _logger.LogInformation("Wrote installerArchive.7z...");
      }
      catch (Exception err)
      {
        _logger.LogError(err, "Failed to bundle installer");
        return new BadRequestObjectResult("Error: Internal Error attempting to bundle installer");
      }
      finally
      {
        // Clean up files
        File.Delete(targetBundlePath);
      }

      _logger.LogInformation(
        "Successfully generated bundle {BundlePath} . Delivering sas to client.",
        targetBundleParamHash);
      return new OkObjectResult(sas);
    }
  }
}
