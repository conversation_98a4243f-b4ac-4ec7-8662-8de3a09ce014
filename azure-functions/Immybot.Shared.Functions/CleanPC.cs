using System.Xml;
using Immybot.Agent.Shared;
using Immybot.Shared.AgentResources;

namespace Immybot.Shared.Functions
{
  internal class CleanPC : ACleanPC
  {
    #pragma warning disable S1144
    private static string Name => "CleanPC";
    #pragma warning restore S1144
    private static XmlNode CleanPCNode
    {
      get
      {
        string configXML = Resources.CleanPC;
        var document = new XmlDocument();
        document.LoadXml(configXML);
        return document.FirstChild;
      }
    }

    public static XmlDocument PatchBaseDocument(XmlDocument basedoc)
    {
      var importedpatch = basedoc.ImportNode(CleanPCNode, true);
      basedoc.GetElementsByTagName("Common").Item(0).AppendChild(importedpatch);
      return basedoc;
    }
  }
}
