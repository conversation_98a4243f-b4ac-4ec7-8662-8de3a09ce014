using System.Security;
using System.Xml;
using Immybot.Agent.Shared;
using Immybot.Shared.AgentResources;

namespace Immybot.Shared.Functions
{
  public class JoinAzureAD : AJoinAzureAD
  {
    private XmlNode AzureADConfigurationNode
    {
      get
      {
        string configXML = Resources.JoinAzureADXML.Replace("{BPRT}", SecurityElement.Escape(BPRT));
        var document = new XmlDocument();
        document.LoadXml(configXML);
        return document.FirstChild;
      }
    }

    public XmlDocument patchBaseDocument(XmlDocument basedoc)
    {
      var importedpatch = basedoc.ImportNode(AzureADConfigurationNode, true);
      basedoc.GetElementsByTagName("Common").Item(0).AppendChild(importedpatch);
      return basedoc;
    }
  }

}
