using System;
using System.IO;
using System.Text;
using System.Xml;
using Immybot.Shared.AgentResources;

namespace Immybot.Shared.Functions
{
  internal static class HelpersAndConstants
  {
    public static readonly string SASInstallerURLEndpoint = Environment.GetEnvironmentVariable("MANAGER_GETSASINSTALLERURL", EnvironmentVariableTarget.Process);
    public static string ReturnEncodedPowershellCommand(string script)
    {
      var encodedPowershell = Convert.ToBase64String(Encoding.Unicode.GetBytes(script));
      return $"cmd /c powershell -ExecutionPolicy Bypass -EncodedCommand  \"{encodedPowershell}\"";
    }
    public static XmlDocument ReturnSingleCommandXml(string name, string script, string commandFile = null)
    {
      var document = new XmlDocument();
      var commandFileXml = string.IsNullOrEmpty(commandFile) && File.Exists(commandFile) ? "" : $"<CommandFile>{commandFile}</CommandFile>";
      document.LoadXml($"<Command><CommandConfig Name=\"{name}\">{commandFileXml}<CommandLine>{script}</CommandLine> </CommandConfig></Command>");
      return document;
    }
    public static XmlDocument[] ReturnMultipleCommandXml(string baseName, string[] scripts)
    {
      XmlDocument[] docArray = new XmlDocument[scripts.Length];
      for (int i = 0; i < scripts.Length; i++)
      {
        var commandName = $"{baseName} [Part {i + 1} of {scripts.Length}]";
        docArray[i] = ReturnSingleCommandXml(commandName, scripts[i]);
      }
      return docArray;
    }
    public static XmlDocument ReturnBasePPKGDocumentXml(string packageName)
    {
      var newGuid = Guid.NewGuid().ToString();
      var document = new XmlDocument();
      document.LoadXml(Resources.BasePPKGXML.Replace("{newGuid}", newGuid).Replace("{packageName}", packageName));
      return document;
    }
  }
}
