using System;
using System.IO;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using Immybot.Shared.Primitives;

namespace Immybot.Shared.Functions
{
  internal class AzureStorageBlobService
  {
    private readonly BlobServiceClient _blobServiceClient;

    public AzureStorageBlobService(string connectionString)
    {
      ArgumentException.ThrowIfNullOrEmpty(connectionString);

      _blobServiceClient = new BlobServiceClient(connectionString);
    }

    /// <summary>
    /// Uploads a file to azure and returns a SAS token good for 24 hours
    /// </summary>
    /// <param name="containerName"></param>
    /// <param name="fileName"></param>
    /// <param name="stream"></param>
    public async Task<string> UploadAsync(string containerName, string fileName, Stream stream)
    {
      try
      {
        var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
        await containerClient.CreateIfNotExistsAsync();

        var blobClient = containerClient.GetBlobClient(fileName);
        await blobClient.UploadAsync(stream, overwrite: true);

        var sasBuilder = new BlobSasBuilder
        {
          BlobContainerName = containerName,
          BlobName = fileName,
          Resource = "b", // b for blob
          ExpiresOn = DateTimeOffset.UtcNow.AddHours(24)
        };
        sasBuilder.SetPermissions(BlobSasPermissions.Read);

        return blobClient.GenerateSasUri(sasBuilder).ToString();
      }
      catch (Exception ex)
      {
        throw new InvalidOperationException($"Failed to upload {fileName} to container {containerName}", ex);
      }
    }

    public async Task<OpResult<string>> TryGetFileAsync(string containerName, string blobName)
    {
      try
      {
        var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
        var blobClient = containerClient.GetBlobClient(blobName);

        if (!await blobClient.ExistsAsync())
          return OpResult.Fail<string>($"Blob {blobName} not found in container {containerName}");

        var sasBuilder = new BlobSasBuilder
        {
          BlobContainerName = containerName,
          BlobName = blobName,
          Resource = "b",
          ExpiresOn = DateTimeOffset.UtcNow.AddHours(24)
        };
        sasBuilder.SetPermissions(BlobSasPermissions.Read);
        var sasUri = blobClient.GenerateSasUri(sasBuilder).ToString();

        return OpResult.Ok(sasUri);
      }
      catch (Exception ex)
      {
        return OpResult.Fail<string>(ex, $"Failed to check/retrieve {blobName} from container {containerName}");
      }
    }
  }
}
