$ErrorActionPreference = 'Stop'

"Building SharedFunctions..." | Write-Host
dotnet publish -o "published"
if ($LASTEXITCODE -ne 0)
{
		throw "Building SharedFunctions Failed!"
}
"SharedFunctions Built. Generating archive..." | Write-Host
Compress-Archive -Path "published/*" -DestinationPath "published/SharedFunctions.zip" -Force
Remove-Item -Path "published/" -Recurse -Exclude *.zip
"Archive generated." | Write-Host
