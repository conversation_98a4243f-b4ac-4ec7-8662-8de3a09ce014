# Immbot.Shared.Functions
This project contains Azure functions that will be shared across all instances of Immybot.
Currently, The project contains the following functions:
 * **ImmyAgentInstallerBundler** - This Function is a publically accessable endpoint which produces bundled Agent installer EXEs from given configuration parameters.
 * **ImmyPpkgBuilder** - This Function is a publically accessable endpoint which produces PPKG files from a given configuration. PPKGs are utilized most in fresh Windows installs during the "OOBE" screen. This function supports several automated features (called 'Modules' in configuration schema & code), such as auto installing ImmyAgent (from MSI or bundle EXE), installing Automate Agent, skipping Privacy screens, and more. It's modular design lends us the capability to potentially add more optional, and user selectable, features down the line.

------------
# Local Development
An example settings file is available for local development. Simply copy the example configuration and make desired adjustments.
```shell
cp example.local.settings.json local.settings.json
```
- Set the OVERRIDE_WEBSITE_HOSTNAME to an https enabled url
  - Recommend using Ngrok

With the most recent update, we are now signing all generated binaries from the ImmyAgentInstallerBundler and ImmyPpkgBuilder endpoints. To successfully generate EXEs and PPKGs from either endpoint locally, you will need to supply a valid certificate and key to sign with.
For local development, you should utilize a self-signed certificate for this purpose. To generate one, follow the simple steps lined out in the ImmyAgent README under the 'Generate, install, and utilize a Self-Signed certificate' section.

- To utilze self-signed certificate for local development, you should:
  - set SIGN_CERT_PRIV_KEY to the key used in the certificate export from above steps
  - set DEV_SELFSIGNED_CERT_PATH to the path of the exported .pfx certificate.

- NOTE: The current Immense Networks certificate utilized in the CI pipeline and located in Shared.Functions Resources file is set to expire 3/2/2022
