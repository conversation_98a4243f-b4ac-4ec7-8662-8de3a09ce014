using System.Collections.Generic;
using Immybot.Agent.Shared;
using Newtonsoft.Json;

namespace Immybot.Shared.Functions
{
  internal class PpkgBundleParams : IPPKGBundleParams
  {
    public string PackageName { get; set; }
    public bool DownloadISO { get; set; }
    [JsonConverter(typeof(ModuleConverter))]
    public List<object> Modules { get; set; }
    public string EncryptionPassword { get; set; }
    public bool EmbedInstallerIntoPPKG { get; set; }
  }
}
