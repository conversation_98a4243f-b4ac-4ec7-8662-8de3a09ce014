using System.Xml;
using Immybot.Agent.Shared;
using Immybot.Shared.AgentResources;

namespace Immybot.Shared.Functions
{
  internal class SkipPrivacyExperience : ASkipPrivacyExperience
  {
    private static string Name { get { return "Skipping Privacy Experience"; } }
    public static XmlNode XmlCommand { get { return HelpersAndConstants.ReturnSingleCommandXml(Name, CommandScript).FirstChild; } }
    private static string CommandScript
    {
      get
      {
        return Resources.SkipPrivacyExperienceCMD;
      }
    }
  }
}
