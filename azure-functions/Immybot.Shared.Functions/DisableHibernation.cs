using System.Linq;
using System.Xml;
using Immybot.Agent.Shared;

#nullable enable

namespace Immybot.Shared.Functions
{
  internal class DisableHibernation : ADisableHibernation
  {
    private static string Name => "Disable Hibernation";
    private static string[] CommandScripts => ["cmd /c powercfg /change standby-timeout-ac 0", "cmd /c powercfg /change standby-timeout-dc 0", "cmd /c powercfg.exe /hibernate off"];
    private XmlNode[]? _xmlNodes;

    // There are no references to this method but keep it for now
    public XmlNode[] GetXmlCommand()
    {
      return _xmlNodes ??= HelpersAndConstants.ReturnMultipleCommandXml(Name, CommandScripts)
        .Select(x => x.FirstChild!)
        .ToArray();
    }
  }
}
#nullable disable
