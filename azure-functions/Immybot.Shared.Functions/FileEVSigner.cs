using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace Immybot.Shared.Functions;

internal static class FileEVSigner
{
  private static HttpClient httpClient = new HttpClient();
  public async static Task<bool> SignFileAsync(string pathToFile)
  {
    var azureSignerFunctionUri = new Uri(Environment.GetEnvironmentVariable("EVSIGNER_URL"));
    var azureSignerFunctionAuthKey = Environment.GetEnvironmentVariable("EVSIGNER_AUTHKEY");
    using var form = new MultipartFormDataContent();
    await using (var fileStream = File.OpenRead(pathToFile))
    {
      form.Add(new StreamContent(fileStream), "signme", Path.GetFileName(pathToFile));
      // This call essentially 'builds' our multipart-form request payload into memory
      // preemtively, so that we may exit this scope and the file will no-longer be opened.
      // Typically, this happens internally/lazyily on the 'SendAsync' call, but issue with doing that
      // is the file stream will have already been disposed before we generate the request.
      await form.LoadIntoBufferAsync();
    }
    var request = new HttpRequestMessage
    {
      RequestUri = azureSignerFunctionUri,
      Method = HttpMethod.Post,
      Headers = { { "x-functions-key", azureSignerFunctionAuthKey } },
      Content = form
    };
    var response = await httpClient.SendAsync(request);

    if (!response.IsSuccessStatusCode)
    {
      return false;
    }
    var signedFile = await response.Content.ReadAsStreamAsync();
    await using var writeFileStream = File.Open(pathToFile, FileMode.Create, FileAccess.Write);
    await signedFile.CopyToAsync(writeFileStream);

    return true;
  }
}
