using System.Xml;
using Immybot.Agent.Shared;
using System.Text;
using Immybot.Shared.AgentResources;

namespace Immybot.Shared.Functions
{
  // This isn't going to follow the convention the other modules do, unfortunately, as we need to do something to base XML that isn't related to powershell
  internal class SetupWiFiAccess : ASetupWiFiNetworkAccess
  {
   public XmlNode XmlCommand
    {
      get
      {
        return HelpersAndConstants.ReturnSingleCommandXml("Setup WiFi",
          HelpersAndConstants.ReturnEncodedPowershellCommand(Resources.SetupWiFiNetworkScript
            .Replace("{{SSID}}", EscapeSingleQuotedStringContent(SSID))
            .Replace("{{PASSPHRASE}}", EscapeSingleQuotedStringContent(Key))
          )
          ).FirstChild;
      }
    }

    private static string EscapeSingleQuotedStringContent(string value)
    {
      if (string.IsNullOrEmpty(value))
      {
        return string.Empty;
      }

      StringBuilder sb = new StringBuilder(value.Length);
      foreach (char c in value)
      {
        sb.Append(c);
        if (CharExtensions.IsSingleQuote(c))
        {
          // double-up quotes to escape them
          sb.Append(c);
        }
      }

      return sb.ToString();
    }
  }
}
