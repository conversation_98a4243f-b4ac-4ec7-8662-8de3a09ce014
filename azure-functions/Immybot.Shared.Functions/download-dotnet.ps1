# The old Win-Build server doesn't use PS Core, and therefore suffers from this TLS
# issue where it will default to only allowing TLS 1.0, which most sites
# block. This will let it use whatever TLS 1.0-1.2
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls -bor [Net.SecurityProtocolType]::Tls11 -bor [Net.SecurityProtocolType]::Tls12
Invoke-WebRequest -Uri "https://dot.net/v1/dotnet-install.ps1" -OutFile "dotnet-install.ps1"