using System.Xml;
using Immybot.Agent.Shared;

namespace Immybot.Shared.Functions
{
  // This Module is a bit of a special case, as we really want to combine upto 4 commands into a single
  // to prevent the client needing to tediously specifiy each account setup command in the process.
  internal class SetAdminCredentails : ASetAdminCredentials
  {
    private string Name { get { return $"Setting up '{(string.IsNullOrEmpty(Username) ? "Administrator" : Username)}' Account"; } }
    public XmlNode XmlCommand { get { return HelpersAndConstants.ReturnSingleCommandXml(Name, CommandScript).FirstChild; } }
    private string CommandScript
    {
      get
      {
        // We are replacing "'" with "`'" here to ensure quotes are properly escaped (Issue #567)
        Username = string.IsNullOrEmpty(Username) ? "Administrator" : Username.Replace("'", "`'");
        Password = Password.Replace("'", "`'");

        string[] commands;
        if (Username == "Administrator")
        {
          var cmdSetLocalAdminPass = $"net user Administrator '{Password}' /active:yes";
          commands = new string[] { cmdSetLocalAdminPass };
        }
        else
        {
          var cmdCreateUser = $"New-LocalUser -Name '{Username}' -AccountNeverExpires -Disabled:$false -Password ('{Password}' | ConvertTo-SecureString -asPlainText -Force)";
          var cmdAddUserToAdmin = $"Add-LocalGroupMember -SID 'S-1-5-32-544' -Member '{Username}' -ErrorAction SilentlyContinue";
          if (HideUser)
          {
            // This path doesn't exist on new computers and New-ItemProperty doesn't create it automatically
            var createSpecialAccountsList = "$Path = \"HKLM:\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon\\SpecialAccounts\\UserList\"; if(!(Test-Path $Path)) {New-Item $Path -Force -ErrorAction SilentlyContinue;}\r\n";
            var cmdSetUserHidden = $"{createSpecialAccountsList}New-ItemProperty -Path $Path -PropertyType DWORD -Name '{Username}' -Value 0";
            commands = new string[] { cmdCreateUser, cmdAddUserToAdmin, cmdSetUserHidden };
          }
          else
          {
            commands = new string[] { cmdCreateUser, cmdAddUserToAdmin };
          }
        }
        return HelpersAndConstants.ReturnEncodedPowershellCommand(string.Join(";", commands));
      }
    }
  }
}
