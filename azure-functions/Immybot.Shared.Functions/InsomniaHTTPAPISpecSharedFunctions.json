{"_type": "export", "__export_format": 4, "__export_date": "2020-12-04T15:44:10.374Z", "__export_source": "insomnia.desktop.app:v2020.4.2", "resources": [{"_id": "req_wrk_c45c68ff65d34d95a96d76c7c8c980bc222db75d1", "parentId": "wrk_c45c68ff65d34d95a96d76c7c8c980bc", "modified": 1606235212501, "created": 1606235212501, "url": "{{ base_url }}http://localhost:7071/api/ImmyPpkgBuilder", "name": "Generates a PPKG binary from options/modules specified in request", "description": "", "method": "POST", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1606235212501, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "wrk_c45c68ff65d34d95a96d76c7c8c980bc", "parentId": null, "modified": 1606235212513, "created": 1589218197121, "name": "ImmyBot.Shared.Functions 0.0.1", "description": "HTTP API for the Immybot.Shared.Functions", "scope": "spec", "_type": "workspace"}, {"_id": "req_wrk_c45c68ff65d34d95a96d76c7c8c980bc222db75d2", "parentId": "wrk_c45c68ff65d34d95a96d76c7c8c980bc", "modified": 1606235212498, "created": 1606235212498, "url": "{{ base_url }}https://immybotsharedfunctions.azurewebsites.net/api/ImmyAgentInstallerBundler", "name": "Generates an EXE agent-install binary from request properties. Returns a blob URI download link.", "description": "", "method": "POST", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1606235212498, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_wrk_c45c68ff65d34d95a96d76c7c8c980bc222db75d3", "parentId": "wrk_c45c68ff65d34d95a96d76c7c8c980bc", "modified": 1606235212496, "created": 1606235212496, "url": "{{ base_url }}https://immybotsharedfunctions.azurewebsites.net/api/ImmyPpkgBuilder", "name": "Generates a PPKG binary from options/modules specified in request", "description": "", "method": "POST", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1606235212496, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_wrk_c45c68ff65d34d95a96d76c7c8c980bc222db75d", "parentId": "wrk_c45c68ff65d34d95a96d76c7c8c980bc", "modified": 1606235212504, "created": 1606164717625, "url": "{{ base_url }}http://localhost:7071/api/ImmyAgentInstallerBundler", "name": "Generates an EXE agent-install binary from request properties. Returns a blob URI download link.", "description": "", "method": "POST", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1606164717625, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_a524e84e83a74586a8b0e06f7f3a7983", "parentId": "wrk_c45c68ff65d34d95a96d76c7c8c980bc", "modified": 1589218329224, "created": 1589218327459, "url": "http://localhost:7071/api/ImmyPpkgBuilder", "name": "(LOCAL) Create PPKG", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n\t\"PackageName\": \"Testing PPKG V1\",\n\t\"Modules\": [\n\t\t{\n\t\t\t\"Type\": 0,\n\t\t\t\"BundleDownloadUri\": \"https://www.gooogle.ca/sdlagent\"\n\t\t},\n\t\t{\n\t\t\t\"Type\": 5,\n\t\t\t\"Username\": \"NickLD\",\n\t\t\t\"Password\": \"ABC123\",\n\t\t\t\"HideUser\": false\n\t\t},\n\t\t{\n\t\t\t\"Type\": 4\n\t\t}, \n\t\t{\n\t\t\t\"Type\": 3\n\t\t}\n\t]\n}"}, "parameters": [], "headers": [{"name": "content-type", "value": "application/json"}], "authentication": {}, "metaSortKey": -1589218327459, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "env_71949a248ffda452e3a3999ab5d3f51fd1851e65", "parentId": "wrk_c45c68ff65d34d95a96d76c7c8c980bc", "modified": 1606235212510, "created": 1589218198729, "name": "Base environment", "data": {"base_url": "{{ scheme }}://{{ host }}{{ base_path }}"}, "dataPropertyOrder": null, "color": null, "isPrivate": false, "metaSortKey": 1589218198729, "_type": "environment"}, {"_id": "jar_71949a248ffda452e3a3999ab5d3f51fd1851e65", "parentId": "wrk_c45c68ff65d34d95a96d76c7c8c980bc", "modified": 1589218198731, "created": 1589218198731, "name": "<PERSON><PERSON><PERSON>", "cookies": [], "_type": "cookie_jar"}, {"_id": "spc_ba835cada3d54a099d55f071ba37e1f0", "parentId": "wrk_c45c68ff65d34d95a96d76c7c8c980bc", "modified": 1606235380107, "created": 1589218197124, "fileName": "ImmyBot.Shared.Functions", "contents": "openapi: 3.0.9\ninfo:\n  description: \"HTTP API for the Immybot.Shared.Functions\"\n  version: 0.0.1\n  title: ImmyBot.Shared.Functions\n\npaths:\n  http://localhost:7071/api/ImmyAgentInstallerBundler:\n    post:\n      tags:\n        - (Localhost)EXE Generator endpoint\n      summary: Generates an EXE agent-install binary from request properties. Returns a blob URI download link.\n      description: \"Generates an EXE agent-install binary from request properties. Returns a blob URI download link.\"\n      operationId: createPPKG\n      requestBody:\n        $ref: '#/components/requestBodies/BundlerBody'\n      responses:\n        \"200\":\n          description: Returned when EXE generation completed successfully. Body will be the Azure Blob download URI for generated bundle. Note that these URIs are short-lived (24hrs)\n        \"400\":\n          description: Usually returned when input is malformed. See body for error details.\n  http://localhost:7071/api/ImmyPpkgBuilder:\n    post:\n      tags:\n        - (Localhost)PPKG Generator endpoint\n      summary: Generates a PPKG binary from options/modules specified in request\n      description: \"Generates a PPKG binary from options/modules specified in request\"\n      operationId: createPPKG\n      requestBody:\n        $ref: '#/components/requestBodies/PPKGBody'\n      responses:\n        \"200\":\n          description: Returned when PPKG generation completed successfully. Body is raw PPKG binary.\n        \"400\":\n          description: Usually returned when input is malformed. See body for error details.\n  https://immybotsharedfunctions.azurewebsites.net/api/ImmyAgentInstallerBundler:\n    post:\n      tags:\n        - (Production)EXE Generator endpoint\n      summary: Generates an EXE agent-install binary from request properties. Returns a blob URI download link.\n      description: \"Generates an EXE agent-install binary from request properties. Returns a blob URI download link.\"\n      operationId: createPPKG\n      requestBody:\n        $ref: '#/components/requestBodies/BundlerBody'\n      responses:\n        \"200\":\n          description: Returned when EXE generation completed successfully. Body will be the Azure Blob download URI for generated bundle. Note that these URIs are short-lived (24hrs)\n        \"400\":\n          description: Usually returned when input is malformed. See body for error details.\n  https://immybotsharedfunctions.azurewebsites.net/api/ImmyPpkgBuilder:\n    post:\n      tags:\n        - (Production)PPKG Generator endpoint\n      summary: Generates a PPKG binary from options/modules specified in request\n      description: \"Generates a PPKG binary from options/modules specified in request\"\n      operationId: createPPKG\n      requestBody:\n        $ref: '#/components/requestBodies/PPKGBody'\n      responses:\n        \"200\":\n          description: Returned when PPKG generation completed successfully. Body is raw PPKG binary octet-stream.\n        \"400\":\n          description: Usually returned when input is malformed. See body for error details.\n          \ncomponents:\n  requestBodies:\n    BundlerBody:\n        description: A JSON object containing example EXEBundle\n        required: true\n        content:\n          application/json:\n            schema:\n              $ref: '#/definitions/BundlerBase'\n    PPKGBody:\n      description: A JSON object containing example PPKG base body\n      required: true\n      content:\n        application/json:\n          schema:\n            $ref: '#/definitions/PPKGBase'\ndefinitions:\n  BundlerBase:\n      type: \"object\"\n      properties:\n        AgentVersion:\n          description: \"The specific agent version to use when bundling\"\n          type: \"string\"\n          format: \"string\"\n          example: \"0.36.0\"\n        InstallerID:\n          description: \"The agentInstaller GUID\"\n          type: \"string\"\n          format: \"string\"\n          example: \"4d8211ee-986d-4964-8402-b73a72eccf0\"\n        B64PrivateKey:\n          description: \"The agentInstaller key in Base64 format\"\n          type: \"string\"\n          format: \"string\"\n          example: \"6J+LDKIMiuKb+SUT0WFFuH+UhrDi0vhvnwCU/I7mzlk=\"  \n        BackendAddress:\n          description: \"The backend address the AgentInstaller will use\"\n          type: \"string\"\n          format: \"string\"\n          example: \"https://www.example.com\"    \n  PPKGBase:\n    type: \"object\"\n    properties:\n      PackageName:\n        description: \"Defines the name of the package\"\n        type: \"string\"\n        format: \"string\"\n        example: \"Testing Agent PPKG v1\"\n      Modules:\n        description: \"Array of 'modules' that will be built into the PPKG\"\n        type: \"array\"\n        format: \"array\"\n        example: [{\n        \"Type\": 3\n        },{\n          \"Type\": 1,\n          \"AgentVersion\": \"0.37.0\",\n          \"BackendAddress\": \"https://www.example.com\",\n          \"InstallerID\": \"4d8211ee-986d-4964-8402-b73a72ececf0\",\n          \"InstallerKey\" : \"6J+LDKIMiuKb+SUT0WFFuH+UhrDi0vhvnwCU/I7mzlk=\"\n        }]\n        items:\n          types: \"object\"\n          properties:\n            Type:\n              type: integer\n              format: int32\n              title: Module type\n              description: This determines what the rest of the module object will be processed as\n              oneOf:\n                - const: 0\n                  description: Installs Immy Agent Bundle (EXE) from 'BundleDownloadUri' property\n                - const: 1\n                  description: Installs ImmyAgent from 'AgentVersion', 'BackendAddress', 'InstallerID', and 'InstallerKey' properties \n                - const: 2\n                  description: Installs Automate Agent (Tied to Immense Networks!) from 'AutomateServerUri', and 'LocationID' properties\n                - const: 3\n                  description: Will Skip the privacy experience screen on Windows setup screen\n                - const: 4\n                  description: Disables computer hibernation\n                - const: 5\n                  description: Creates/setup local-admin account from 'Username', 'Password', and 'HideUser' properties\n                - const: 6\n                  description: Will setup WiFi connection details from 'SSID', 'Key', and 'IsOpen' properties\n                - const: 7\n                  description: Will join an Azure AD from 'BPRT' property\n    json:\n      name: \"Base\"", "contentType": "yaml", "_type": "api_spec"}, {"_id": "env_190de39ce3354e83b93b9a5a0d23860d", "parentId": "env_71949a248ffda452e3a3999ab5d3f51fd1851e65", "modified": *************, "created": *************, "name": "ImmyBot.Shared.Functions", "data": {}, "dataPropertyOrder": null, "color": null, "isPrivate": false, "metaSortKey": *************, "_type": "environment"}, {"_id": "env_env_71949a248ffda452e3a3999ab5d3f51fd1851e65_sub", "parentId": "env_71949a248ffda452e3a3999ab5d3f51fd1851e65", "modified": *************, "created": *************, "name": "OpenAPI env", "data": {"scheme": "http", "base_path": "", "host": "example.com"}, "dataPropertyOrder": null, "color": null, "isPrivate": false, "metaSortKey": *************, "_type": "environment"}]}