{
  "IsEncrypted": false,
  "Values": {
    "MANAGER_GETSASINSTALLERURL": "https://immybotmanager.azurewebsites.net/sites-api/immy-agent/GetInstallerSasUrl/",
    "OVERRIDE_WEBSITE_HOSTNAME": "", // e.g. "17051ceb.ngrok.io" - OMIT "https",
    "AZURE_STORAGE_CONNECTION_STRING": "UseDevelopmentStorage=true",
	"SIGN_CERT_PRIV_KEY": "123abc",
    "DEV_SELFSIGNED_CERT_PATH": "C:\\dev_immyagent_cert.pfx"
  }
}
