using System.Xml;
using Immybot.Agent.Shared;

namespace Immybot.Shared.Functions
{
  internal class InstallImmyAgentBundle : AInstallImmyAgentBundle
  {
    private static string Name { get { return "Installing ImmyAgent Bundle"; } }
    public XmlNode XmlCommand { get { return HelpersAndConstants.ReturnSingleCommandXml(Name, CommandScript).FirstChild; } }
    private string CommandScript
    {
      get
      {
        return $"powershell \"\r\n[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12;$tmpFilePath = New-TemporaryFile;$newTmp = Rename-Item $tmpFilePath $tmpFilePath.Name.Replace('.tmp', '.exe') -PassThru;$url = '{BundleDownloadUri.Replace("&", "&amp;")}';(New-Object System.Net.WebClient).DownloadFile($url, $newTmp.FullName);Start-Process -Wait -Filepath $newTmp.FullName;Remove-Item $newTmp.FullName -Force;\"";
      }
    }
  }
}
