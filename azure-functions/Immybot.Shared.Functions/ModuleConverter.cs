using System;
using System.Collections.Generic;
using System.Linq;
using Immybot.Agent.Shared;
using Newtonsoft.Json;
using JsonConverter = Newtonsoft.Json.JsonConverter;

namespace Immybot.Shared.Functions
{
  // As we are working with an Array of ICommandModules, JSONNET wont be able to understand
  // What type to cast each CommandModule as. Therefore we need to write a
  // custom Converter to re-create the Modules list.
  // This may not be the best way to do this, but It's the best way I could figure out.
  internal class ModuleConverter : JsonConverter
  {
    public override bool CanConvert(Type objectType)
    {
      return (objectType == typeof(List<object>));
    }

    public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
    {
      // Will come in as a JArray from JSON representation.
      var modulesTarget = serializer.Deserialize<Newtonsoft.Json.Linq.JArray>(reader);
      var moduleList = new List<object>();

      foreach (var module in modulesTarget)
      {
        // Get the CommandModuleType from the JToken so we can correctly cast it
        var commandType = (CommandModuleType)module.Value<int?>("Type");
        ICommandModule newModule = null;
        switch (commandType)
        {
          case CommandModuleType.INSTALL_IMMYAGENTBUNDLE:
            newModule = module.ToObject<InstallImmyAgentBundle>();
            break;
          case CommandModuleType.INSTALL_IMMYAGENTMSI:
            newModule = module.ToObject<InstallImmyAgentMsi>();
            break;
          case CommandModuleType.INSTALL_AUTOMATEAGENT:
            newModule = module.ToObject<InstallAutomateAgent>();
            break;
          case CommandModuleType.SKIP_PRIVACY_EXPERIENCE:
            newModule = module.ToObject<SkipPrivacyExperience>();
            break;
          case CommandModuleType.DISABLE_HIBERNATION:
            newModule = module.ToObject<DisableHibernation>();
            break;
          case CommandModuleType.SET_LOCALADMINCREDENTIALS:
            newModule = module.ToObject<SetAdminCredentails>();
            break;
          case CommandModuleType.SET_WIFINETWORKCREDENTIALS:
            newModule = module.ToObject<SetupWiFiAccess>();
            break;
          case CommandModuleType.JOIN_AZURE_AD:
            newModule = module.ToObject<JoinAzureAD>();
            break;
          case CommandModuleType.CLEAN_PC:
            newModule = module.ToObject<CleanPC>();
            break;
          default:
            break;
        }
        // Before we add the module, it is important we do
        // a verification check to see if the object contains all
        // of it's necessary data (!= null). If it doesn't, we should
        // throw an exception
        var properties = newModule.GetType().GetProperties().ToList();

        if (properties.TrueForAll(p => p.GetValue(newModule) != null))
        {
          moduleList.Add(newModule);
        }
        else
        {
          var nullProperties = properties
            .Where(p => p.GetValue(newModule) == null)
            .Select(p => p.Name);

          throw new MissingRequiredPropertiesException(
            $"The following properties are null: {string.Join(", ", nullProperties)}");
        }
      }
      return moduleList;
    }

    public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
    {
      serializer.Serialize(writer, value);
    }
  }
}
