using Immybot.Shared.DataContracts.Converters.SemanticVersioning;
using Immybot.Shared.JsonConverters;
using MessagePack;

namespace Immybot.Shared.DataContracts.Tests;
public class SemanticVersionMsgPackFormatterTests
{
  [Theory]
  [InlineData("0.0.0")]
  [InlineData("0.57.2")]
  [InlineData("0.57.2.1")]
  [InlineData("0.57.2-pre.1")]
  internal void SerializeDeserialize_GivenNonNullValue_Ok(string versionString)
  {
    var semVer = SemanticVersionParser.ParseVersionString(versionString);
    var obj = new SemanticVersionTestClass(semVer);
    var bytes = MessagePackSerializer.Serialize(obj);

    var deserialized = MessagePackSerializer.Deserialize<SemanticVersionTestClass>(bytes);

    Assert.Equal(versionString, deserialized.SemanticVersion?.ToNormalizedString());
  }

  [Fact]
  public void SerializeDeserialize_GivenNullValue_Ok()
  {
    var obj = new SemanticVersionTestClass(null);
    var bytes = MessagePackSerializer.Serialize(obj);

    var deserialized = MessagePackSerializer.Deserialize<SemanticVersionTestClass>(bytes);

    Assert.NotNull(deserialized);
    Assert.Null(deserialized.SemanticVersion);
  }

  //[Theory]
  //[InlineData("0.0.0")]
  //[InlineData("0.57.2")]
  //[InlineData("0.57.2.1")]
  //[InlineData("0.57.2-pre.1")]
  //public void Deserialize_GivenValidVersionStrings_Ok(string versionString)
  //{
  //  var json = $"{{\"SemanticVersion\":\"{versionString}\"}}";
  //  var obj = JsonSerializer.Deserialize<SemanticVersionTestClass>(json);

  //  Assert.Equal(versionString, obj!.SemanticVersion!.ToNormalizedString());
  //}

  //[Theory]
  //[InlineData(null)]
  //[InlineData("")]
  //[InlineData("     ")]
  //[InlineData("I'm not a version.")]
  //public void Deserialize_GivenInvalidVersionStrings_HasNullValue(string? versionString)
  //{
  //  var json = $"{{\"SemanticVersion\":\"{versionString}\"}}";
  //  var obj = JsonSerializer.Deserialize<SemanticVersionTestClass>(json);

  //  Assert.Null(obj?.SemanticVersion);
  //}
}
