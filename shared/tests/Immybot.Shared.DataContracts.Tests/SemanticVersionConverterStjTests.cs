using System.Text.Json;
using Immybot.Shared.DataContracts.Converters.SemanticVersioning;
using Immybot.Shared.JsonConverters;

namespace Immybot.Shared.DataContracts.Tests;

public class SemanticVersionConverterStjTests
{
  [Theory]
  [InlineData("0.0.0")]
  [InlineData("0.57.2")]
  [InlineData("0.57.2.1")]
  [InlineData("0.57.2-pre.1")]
  public void Serialize_GivenNonNullValue_Ok(string versionString)
  {
    var semVer = SemanticVersionParser.ParseVersionString(versionString);
    var obj = new SemanticVersionTestClass(semVer);
    var json = JsonSerializer.Serialize(obj);

    Assert.StartsWith("{\"SemanticVersion\":\"", json);

    var serializedValue = json
      .TrimStart("{\"SemanticVersion\":\"".ToCharArray())
      .TrimEnd("\"}".ToCharArray());

    Assert.Equal(versionString, serializedValue);
  }

  [Fact]
  public void Serialize_GivenNullValue_Ok()
  {
    var obj = new SemanticVersionTestClass(null);
    var json = JsonSerializer.Serialize(obj);

    Assert.Equal("{\"SemanticVersion\":null}", json);
  }

  [Theory]
  [InlineData("0.0.0")]
  [InlineData("0.57.2")]
  [InlineData("0.57.2.1")]
  [InlineData("0.57.2-pre.1")]
  public void Deserialize_GivenValidVersionStrings_Ok(string versionString)
  {
    var json = $"{{\"SemanticVersion\":\"{versionString}\"}}";
    var obj = JsonSerializer.Deserialize<SemanticVersionTestClass>(json);

    Assert.Equal(versionString, obj!.SemanticVersion!.ToNormalizedString());
  }

  [Theory]
  [InlineData("     ")]
  [InlineData("I'm not a version.")]
  public void Deserialize_GivenInvalidVersionStrings_Throws(string? versionString)
  {
    var json = $"{{\"SemanticVersion\":\"{versionString}\"}}";

    Assert.Throws<JsonException>(() =>
    {
      _ = JsonSerializer.Deserialize<SemanticVersionTestClass>(json);
    });
  }

  [Theory]
  [InlineData("")]
  [InlineData(null)]
  public void Deserialize_NullOrEmptyVersionStrings_HasNullValue(string? versionString)
  {
    versionString = versionString is null ?
      "null" :
      $"\"{versionString}\"";

    var json = $"{{\"SemanticVersion\":{versionString}}}";
    var obj = JsonSerializer.Deserialize<SemanticVersionTestClass>(json);

    Assert.Null(obj?.SemanticVersion);
  }
}
