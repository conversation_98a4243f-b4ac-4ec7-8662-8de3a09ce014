using System;

namespace Immybot.Backend.Domain.Models;

/// <summary>
/// Provider audit log entity for tracking provider method calls and their results.
/// This entity is stored in a partitioned table for efficient time-based queries and retention management.
/// </summary>
public class ProviderAuditLog
{
  /// <summary>
  /// Unique id for the audit record
  /// </summary>
  public Guid Id { get; set; } = Guid.NewGuid();

  /// <summary>
  /// The id of the provider link associated with the audit record
  /// </summary>
  public int ProviderLinkId { get; set; }

  /// <summary>
  /// The name of the provider link associated with the audit record
  /// </summary>
  public string ProviderLinkName { get; set; } = null!;

  /// <summary>
  /// The name of the method that was called for this provider
  /// </summary>
  public string MethodName { get; set; } = null!;

  /// <summary>
  /// An error message that was returned from the method call
  /// </summary>
  public string? ErrorMessage { get; set; }

  /// <summary>
  /// The input data for the executed method (stored as JSONB)
  /// </summary>
  public object? Input { get; set; }

  /// <summary>
  /// The output data for the executed method (stored as JSONB)
  /// </summary>
  public object? Output { get; set; }

  /// <summary>
  /// The correlation id for the audit record to another audit record's id
  /// </summary>
  public Guid? CorrelationId { get; set; }

  /// <summary>
  /// The date the audit record was created (partition key)
  /// </summary>
  public DateTime TimeUtc { get; set; } = DateTime.UtcNow;

  public ProviderAuditLog()
  {
  }

  public ProviderAuditLog(
    int providerLinkId,
    string providerLinkName,
    string methodName,
    string? errorMessage = null,
    object? input = null,
    object? output = null,
    Guid? correlationId = null)
  {
    ProviderLinkId = providerLinkId;
    ProviderLinkName = providerLinkName;
    MethodName = methodName;
    ErrorMessage = errorMessage;
    Input = input;
    Output = output;
    CorrelationId = correlationId;
  }
}
