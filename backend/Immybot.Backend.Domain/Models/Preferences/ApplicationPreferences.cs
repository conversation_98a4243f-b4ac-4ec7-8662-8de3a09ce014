using Immybot.Backend.Domain.Interfaces;


namespace Immybot.Backend.Domain.Models.Preferences;

public class ApplicationPreferences : IAuditableLoggableEntity
{
  public int Id { get; set; }
  public bool EnableOnboarding { get; set; }
  public bool EnableAzureUserSync { get; set; }
  public DefaultEmailBccList? DefaultEmailBccList { get; set; }
  public bool EnableNiniteIntegration { get; set; }
  public int? DefaultBrandingId { get; set; }
  public bool EnableUserAffinitySync { get; set; }
  public bool EnableSessionEmails { get; set; } = true;
  public Branding? DefaultBranding { get; set; }
  public DefaultScriptTimeouts DefaultScriptTimeouts { get; set; } = new();
  public bool UseImmyBotChocolateyFeed { get; set; } = true;
  public bool HideChocolateyPackages { get; set; }
  public bool OverwriteExistingDeviceIfOSIsNew { get; set; } = true;
  public bool EnableNonEssentialDeviceInventory { get; set; } = true;
  public bool RequireConsentForExternalSessionProviders { get; set; } = true;

  [Obsolete("Use RBAC claim checks instead")]
  public bool AllowNonAdminsToManageAssignments { get; set; } = true;
  public bool ShowGettingStartedWizard { get; set; }
  public bool EnableHistoricalInventory { get; set; }
  public string? DefaultTimeZone { get; set; }
  public string? ImmyScriptPath { get; set; }
  public bool EnableRequestAccess { get; set; } = true;

  [Obsolete("Removed functionality in agent")]
  public bool EnableEphemeralAgentDebugMode { get; set; } // TODO remove

  public int StaleComputersLastAgentConnectionAgeDays { get; set; }

  [Obsolete("Use RBAC claim checks instead")]
  public bool AllowNonAdminsAndNonMspUsersToUseTerminalsAndEditScripts { get; set; }

  /// <summary>
  /// Indicates whether we are allowing the use of preflight scripts
  /// </summary>
  public bool EnablePreflightScripts { get; set; } = true;

  /// <summary>
  /// Indicates whether we should allow users to globally use the ImmyBot Remote Control functionality
  /// </summary>
  public bool EnableImmyBotRemoteControl { get; set; }

  /// <summary>
  /// Whether ImmyBot remote control sessions should be recorded.
  /// </summary>
  public bool EnableImmyBotRemoteControlRecording { get; set; }

  /// <summary>
  /// Indicates whether provider audit logging is enabled
  /// </summary>
  public bool EnableProviderAuditLogging { get; set; }

  /// <summary>
  /// Number of days to retain provider audit logs before automatic cleanup
  /// </summary>
  public int ProviderAuditLogRetentionDays { get; set; }

  /// <summary>
  /// Indicates whether msp non-admins are required to submit change requests for cross-tenant deployments
  /// </summary>
  [Obsolete("Use RBAC claim checks instead")]
  public bool MspNonAdminsRequireChangeRequestsForCrossTenantDeployments { get; set; }

  /// <summary>
  /// Indicates whether we should insert action activities into the database and show them on the frontend
  /// </summary>
  public bool EnableMaintenanceActionActivities { get; set; }

  /// <summary>
  /// Indicates whether we should be automatically releasing updates to this ImmyBot instance when they become available
  /// </summary>
  public bool EnableAutomaticImmyBotReleaseUpdates { get; set; }

  /// <summary>
  /// Set the hour of the day when we should automatically release updates to this ImmyBot instance.
  /// The application preference timezone will be used.
  /// </summary>
  public int AutomaticImmyBotReleaseUpdateHour { get; set; }

  /// <summary>
  /// Indicates how many days an update must be available before we automatically update it
  /// </summary>
  public int DaysToWaitBeforeAutomaticImmyBotUpdate { get; set; } = 14;

  /// <summary>
  /// Indicates whether scheduled inventory jobs should run as maintenance sessions
  /// </summary>
  public bool RunScheduledInventoryAsMaintenanceSessions { get; set; }

  /// <summary>
  /// Allows dynamic integrations with the "Beta" tag to migrate deployments.
  /// </summary>
  public bool EnableBetaDynamicIntegrationMigrations { get; set; }

  /// <summary>
  /// Allows MSP admins to impersonate other users
  /// </summary>
  public bool EnableUserImpersonation { get; set; }

  public bool DisconnectLeastActiveEditorServiceWhenLimitReached { get; set; }
}
