using System;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ImmyAgentProvider;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Immybot.Backend.UnitTests.Shared.Lib;
using Immybot.Backend.Web.Common.Infrastructure;
using Immybot.Shared.Tests.TestingUtilities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Moq;

namespace Immybot.Backend.UnitTests.Infrastructure;
public class DevEntitiesInitFilterTests : BaseUnitTests
{
  private readonly Func<ImmybotDbContext> _dbFactory;
  private readonly Mock<IOptions<ImmyAgentOptions>> _agentOptions;
  private readonly Mock<ILogger<DevEntitiesInitFilter>> _logger;
  private readonly DevEntitiesInitFilter _filter;

  public DevEntitiesInitFilterTests()
  {
    _dbFactory = () => GetNewSqliteDbContext();
    _agentOptions = new Mock<IOptions<ImmyAgentOptions>>();
    _logger = new Mock<ILogger<DevEntitiesInitFilter>>();
    _filter = new DevEntitiesInitFilter(_dbFactory, _agentOptions.Object, _logger.Object);
  }

  [Fact]
  public async Task ExecuteAsync_GivenDisabledInOptions_ShouldWarn()
  {
    var options = new ImmyAgentOptions();

    _agentOptions
      .SetupGet(x => x.Value)
      .Returns(options);

    await _filter.ExecuteAsync();

    _agentOptions.VerifyGet(x => x.Value, Times.Once);
    _agentOptions.VerifyNoOtherCalls();

    _logger.VerifyLogEntryContains(
      LogLevel.Warning,
      "should not be run",
      Times.Once);

    _logger.VerifyNoOtherCalls();
  }

  [Fact]
  public async Task ExecuteAsync_GivenNoDevAgents_ShouldWarn()
  {
    var options = new ImmyAgentOptions()
    {
      UseDevTestAgents = true,
    };

    _agentOptions
      .SetupGet(x => x.Value)
      .Returns(options);

    await _filter.ExecuteAsync();

    _agentOptions.VerifyGet(x => x.Value, Times.Exactly(2));
    _agentOptions.VerifyNoOtherCalls();

    _logger.VerifyLogEntryContains(
      LogLevel.Warning,
      "No dev/test agents",
      Times.Once);

    _logger.VerifyNoOtherCalls();
  }

  [Fact]
  public async Task ExecuteAsync_GivenUseDevTestAgents_ShouldCreateAgents()
  {
    var msp = GetOrCreateMspTenant();
    _ = CreateProviderLink(msp.Id, providerTypeId: Guid.Parse(Constants.ProviderId));

    var options = await GetOptionsFromConfig();

    _agentOptions
      .SetupGet(x => x.Value)
      .Returns(options.Value);

    await _filter.ExecuteAsync();

    _agentOptions.VerifyGet(x => x.Value, Times.Exactly(3));
    _agentOptions.VerifyNoOtherCalls();

    _logger.VerifyNoOtherCalls();

    // The agent with invalid GUIDs should not have even been
    // realized via IOptions.
    Assert.Single(options.Value.DevTestAgents);
    var agent1 = options.Value.DevTestAgents.First();

    // Verify ProviderAgent was created in the database.
    await using var ctx = _dbFactory.Invoke();
    var agents = await ctx.ProviderAgents.ToListAsync();
    Assert.Single(agents);
    Assert.Equal(
      agent1.AgentDeviceId.ToString(),
      agents[0].ExternalAgentId);

    var computers = await ctx.Computers
      .Include(x => x.Agents)
      .ToListAsync();

    Assert.Single(computers);
    Assert.Equal(
      agent1.AgentDeviceId,
      Guid.Parse(computers[0].Agents.First().ExternalAgentId));
  }

  /// <summary>
  /// Simulate getting options from a configuration file with IOptions pattern.
  /// </summary>
  private static async Task<IOptions<ImmyAgentOptions>> GetOptionsFromConfig()
  {
    var configFile = new
    {
      ImmyAgentOptions = new
      {
        UseDevTestAgents = true,
        DevTestAgents = new object[]
        {
          new
          {
            AgentDeviceId = "not-a-valid-guid",
            AgentInstallerId = "not-a-valid-guid"
          },
          new
          {
            AgentDeviceId = Guid.Parse("5ceab5cb-3bb2-4076-a841-679e18c37fdf"),
            AgentInstallerId = Guid.Parse("0fc07714-fd18-4377-b903-d109e5124d4d")
          },
        }
      }
    };
    using var ms = new MemoryStream();
    await JsonSerializer.SerializeAsync(ms, configFile);
    ms.Seek(0, SeekOrigin.Begin);

    var builder = Host.CreateDefaultBuilder();
    builder
      .ConfigureAppConfiguration((_, config) =>
      {
        // ReSharper disable once AccessToDisposedClosure
        config.AddJsonStream(ms);
      })
      .ConfigureServices((context, services) =>
      {
        services.Configure<ImmyAgentOptions>(
          context.Configuration.GetSection(ImmyAgentOptions.SectionKey));
      });
    var host = builder.Build();
    return host.Services.GetRequiredService<IOptions<ImmyAgentOptions>>();
  }
}
