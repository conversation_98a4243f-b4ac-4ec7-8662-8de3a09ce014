using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.UnitTests;
public class ScriptTests
{
  [Fact]
  public void Copy_ShouldCloneParameters()
  {
    // Arrange
    var script = new Script { Name = "", Action = "" };
    script.Variables.Add("test", "test");
    script.Variables.Add("test2", "test2");
    // Act
    var copy = Script.Copy(script);
    // Assert
    Assert.NotSame(script.Variables, copy.Variables);
    Assert.Equal(script.Variables, copy.Variables);
  }

  [Fact]
  public void Copy_ShouldCloneParamBlockParameters()
  {
    // Arrange
    var script = new Script { Name = "" };
    script.Parameters.Add("test", "test");
    script.Parameters.Add("test2", "test2");
    // Act
    var copy = Script.Copy(script);
    // Assert
    Assert.NotSame(script.Parameters, copy.Parameters);
    Assert.Equal(script.Parameters, copy.Parameters);
  }
}
