using System;
using System.Linq;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.InventorySinks;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.ContextTests;
using Immybot.Shared.Tests.TestingUtilities;
using Immybot.Shared.Abstractions.Device;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace Immybot.Backend.UnitTests.InventorySinkTests;
public class SoftwareInventorySinkTests : ActionsTestBase
{
  private readonly AdjustableSystemTime _systemTime;
  private readonly Mock<ILogger<SoftwareInventorySink>> _logger;
  private readonly Tenant _tenant;
  private readonly Person _person;
  private readonly ProviderLink _providerLink;
  private readonly ProviderClient _providerClient;
  private readonly Computer _computer;

  public SoftwareInventorySinkTests()
  {
    _systemTime = new AdjustableSystemTime();
    _logger = new Mock<ILogger<SoftwareInventorySink>>();
    _tenant = CreateTenant();
    _person = CreatePerson(_tenant.Id, "<EMAIL>");
    _providerLink = CreateProviderLink(_tenant.Id);
    _providerClient = CreateProviderClient(_providerLink.Id, _tenant.Id);

    _computer = CreateComputer(
      _providerLink.Id,
      _providerClient.ExternalClientId,
      "Agent ID 1",
      _tenant.Id,
      "Computer Name 1",
      Guid.NewGuid(),
      isOnline: true,
      primaryPersonId: _person.Id);
  }


  [Fact]
  public async Task GivenValidOutput_CreatesDetectedSoftwareRecords()
  {
    var sink = new SoftwareInventorySink(
      () => GetNewSqliteDbContext(),
      _systemTime,
      _logger.Object);

    var output = await GetRawSoftwareInventoryOutput();

    await using (var dbContext = GetNewSqliteDbContext())
    {
      // Verify state before action.
      Assert.Empty(dbContext.DetectedComputerSoftware);

      var computer = await dbContext.Computers
        .Include(x => x.DetectedSoftware)
        .FirstAsync(x => x.Id == _computer.Id);

      Assert.Empty(computer.DetectedSoftware);

    }

    var result = await sink.ProcessResults(_computer.Id, output);
    Assert.True(result.IsSuccess);

    await using (var dbContext = GetNewSqliteDbContext())
    {
      Assert.NotEmpty(dbContext.DetectedComputerSoftware);

      var computer = await dbContext.Computers
        .Include(x => x.DetectedSoftware)
        .FirstAsync(x => x.Id == _computer.Id);

      Assert.NotEmpty(computer.DetectedSoftware);
      Assert.True(
        await dbContext.DetectedComputerSoftware.AllAsync(x =>
          x.ComputerId == _computer.Id &&
          x.PrimaryPersonId == _person.Id));
    }

    _logger.VerifyBeginScopeCalled(
      "Processing software inventory results for computer ID '1'.",
      Times.Once);
    _logger.VerifyNoOtherCalls();
  }

  [Fact]
  public async Task GivenPersonIsDeleted_PrimaryPersonIdIsSetToNull()
  {
    var sink = new SoftwareInventorySink(
      () => GetNewSqliteDbContext(),
      _systemTime,
      _logger.Object);

    var output = await GetRawSoftwareInventoryOutput();

    var result = await sink.ProcessResults(_computer.Id, output);
    Assert.True(result.IsSuccess);

    await using var dbContext = GetNewSqliteDbContext();

    // All software should be associated to the primary person.
    Assert.True(
      await dbContext.DetectedComputerSoftware.AllAsync(x =>
        x.ComputerId == _computer.Id &&
        x.PrimaryPersonId == _person.Id));

    var person = await dbContext.Persons.FindAsync(_person.Id);
    dbContext.Persons.Remove(person!);
    await dbContext.SaveChangesAsync();

    // Now all software should still exist, except PrimaryPersonId should be null.
    Assert.True(
      await dbContext.DetectedComputerSoftware.AllAsync(x =>
        x.ComputerId == _computer.Id &&
        x.PrimaryPersonId == null));
  }

  [Fact]
  public async Task GivenComputerIsDeleted_DetectedSoftwareShouldBeDeleted()
  {
    var sink = new SoftwareInventorySink(
      () => GetNewSqliteDbContext(),
      _systemTime,
      _logger.Object);

    var output = await GetRawSoftwareInventoryOutput();

    var result = await sink.ProcessResults(_computer.Id, output);
    Assert.True(result.IsSuccess);

    await using var dbContext = GetNewSqliteDbContext();
    Assert.NotEmpty(dbContext.DetectedComputerSoftware);

    var computer = await dbContext.Computers.FindAsync(_computer.Id);
    dbContext.Computers.Remove(computer!);
    await dbContext.SaveChangesAsync();

    // All the software should be deleted now.
    Assert.Empty(dbContext.DetectedComputerSoftware);
  }

  [Fact]
  public async Task GivenInvalidOutput_ThrowsAndLogs()
  {
    var sink = new SoftwareInventorySink(
      () => GetNewSqliteDbContext(),
      _systemTime,
      _logger.Object);

    var output = "This is not the JSON you're looking for.";

    var result = await sink.ProcessResults(_computer.Id, output);
    Assert.False(result.IsSuccess);
    Assert.True(result.HadException);
    Assert.Equal("'T' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.", result.Exception!.Message);

    _logger.VerifyBeginScopeCalled(
      "Processing software inventory results for computer ID '1'.",
      Times.Once);
    _logger.VerifyLogEntryContains(LogLevel.Error, "Error while processing output.", Times.Once);
    _logger.VerifyNoOtherCalls();
  }

  [Fact]
  public async Task GivenInvalidJsonOutput_ThrowsAndLogs()
  {
    var sink = new SoftwareInventorySink(
      () => GetNewSqliteDbContext(),
      _systemTime,
      _logger.Object);

    var output = "[]";

    var result = await sink.ProcessResults(_computer.Id, output);
    Assert.False(result.IsSuccess);
    Assert.True(result.HadException);
    Assert.Contains("The JSON value could not be converted to", result.Exception!.Message);

    _logger.VerifyBeginScopeCalled(
      "Processing software inventory results for computer ID '1'.",
      Times.Once);
    _logger.VerifyLogEntryContains(LogLevel.Error, "Error while processing output.", Times.Once);
    _logger.VerifyNoOtherCalls();
  }

  [Fact]
  public async Task GivenEmptyOutput_ReturnsFailure()
  {
    var sink = new SoftwareInventorySink(
      () => GetNewSqliteDbContext(),
      _systemTime,
      _logger.Object);

    var output = "{ \"Output\": [] }";

    var result = await sink.ProcessResults(_computer.Id, output);
    Assert.False(result.IsSuccess);
    Assert.False(result.HadException);
    Assert.Contains("Deserialized software output is empty.", result.Reason);

    _logger.VerifyBeginScopeCalled(
      "Processing software inventory results for computer ID '1'.",
      Times.Once);
    _logger.VerifyLogEntryContains(LogLevel.Warning, "Deserialized software output is empty.", Times.Once);
    _logger.VerifyNoOtherCalls();
  }


}
