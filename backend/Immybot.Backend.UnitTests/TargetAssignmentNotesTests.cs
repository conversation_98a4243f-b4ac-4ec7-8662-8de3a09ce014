using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using Immybot.Backend.UnitTests.ContextTests;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.UnitTests;

public class TargetAssignmentNotesTests : ActionsTestBase
{
  private readonly AuthUserDto _user;
  private readonly Func<ImmybotDbContext> _dbContextFactory;
  private readonly Func<SoftwareDbContext> _softwareDbContextFactory;
  private readonly TargetAssignmentStore _store;

  public TargetAssignmentNotesTests()
  {
    _user = GetAuthUser(GetOrCreateUser(UserType.MspAdmin));
    _dbContextFactory = GetSqliteDbContextFactory();
    _softwareDbContextFactory = GetSqliteSoftwareDbContextFactory();
    _store = new TargetAssignmentStore(_dbContextFactory, _softwareDbContextFactory);
  }

  [Fact]
  public async Task UpdateNotesLocalAsync_ShouldAddAudit()
  {
    // arrange
    var assignment = CreateTargetAssignment();
    var notes = "some notes";

    // act
    await _store.UpdateNotesLocalAsync(_user, assignment, notes, CancellationToken.None);

    // assert

    await using var ctx = _dbContextFactory();

    var audit = await ctx.Audits.FirstOrDefaultAsync(a => a.ObjectType == "TargetAssignmentNotes" && a.Type == "Create");

    Assert.NotNull(audit);
    Assert.Equal(audit.UserDisplayName, _user.DisplayName);
  }

  [Fact]
  public async Task UpdateNotesGlobalAsync_ShouldAddAudit()
  {
    // arrange
    var assignment = CreateTargetAssignment(isGlobal: true);
    var notes = "some notes";

    // act
    await _store.UpdateNotesGlobalAsync(_user, assignment, notes, CancellationToken.None);

    // assert

    await using var ctx = _softwareDbContextFactory();

    var audit = await ctx.Audits.FirstOrDefaultAsync(a => a.ObjectType == "TargetAssignmentNotes" && a.Type == "Create");

    Assert.NotNull(audit);
    Assert.Equal(audit.UserDisplayName, _user.DisplayName);
  }

  [Fact]
  public async Task UpdateNotesLocalAsync_ShouldCreateNotes_WhenDoesNotExist()
  {
    // arrange
    var assignment = CreateTargetAssignment();
    var notes = "some notes";

    // act
    await _store.UpdateNotesLocalAsync(_user, assignment, notes, CancellationToken.None);

    // assert

    await using var ctx = _dbContextFactory();

    var assignmentWithNotes = await ctx.TargetAssignments.Include(a => a.Notes).FirstOrDefaultAsync(a => a.Id == assignment.Id);

    Assert.Equal(notes, assignmentWithNotes?.Notes?.Notes);
  }

  [Fact]
  public async Task UpdateNotesGlobalAsync_ShouldCreateNotes_WhenDoesNotExist()
  {
    // arrange
    var assignment = CreateTargetAssignment(isGlobal: true);
    var notes = "some notes";

    // act
    await _store.UpdateNotesGlobalAsync(_user, assignment, notes, CancellationToken.None);

    // assert

    await using var ctx = _softwareDbContextFactory();

    var assignmentWithNotes = await ctx.TargetAssignments.Include(a => a.Notes).FirstOrDefaultAsync(a => a.Id == assignment.Id);

    Assert.Equal(notes, assignmentWithNotes?.Notes?.Notes);
  }

  [Fact]
  public async Task UpdateNotesGlobalAsync_ShouldUpdateNotes_WhenExists()
  {
    // arrange
    var assignment = CreateTargetAssignment(isGlobal: true);
    var notes = "some notes";

    // act
    await _store.UpdateNotesGlobalAsync(_user, assignment, notes, CancellationToken.None);

    var newNotes = "new notes";

    await _store.UpdateNotesGlobalAsync(_user, assignment, newNotes, CancellationToken.None);

    // assert

    await using var ctx = _softwareDbContextFactory();

    var assignmentWithNotes = await ctx.TargetAssignments.Include(a => a.Notes).FirstOrDefaultAsync(a => a.Id == assignment.Id);

    Assert.Equal(newNotes, assignmentWithNotes?.Notes?.Notes);
  }

  [Fact]
  public async Task UpdateNotesLocalAsync_ShouldUpdateNotes_WhenExists()
  {
    // arrange
    var assignment = CreateTargetAssignment();
    var notes = "some notes";

    // act
    await _store.UpdateNotesLocalAsync(_user, assignment, notes, CancellationToken.None);

    var newNotes = "new notes";

    await _store.UpdateNotesLocalAsync(_user, assignment, newNotes, CancellationToken.None);

    // assert
    await using var ctx = _dbContextFactory();

    var assignmentWithNotes = await ctx.TargetAssignments.Include(a => a.Notes).FirstOrDefaultAsync(a => a.Id == assignment.Id);

    Assert.Equal(newNotes, assignmentWithNotes?.Notes?.Notes);
  }

}
