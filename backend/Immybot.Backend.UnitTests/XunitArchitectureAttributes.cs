using System.Runtime.InteropServices;

namespace Immybot.Backend.UnitTests;

[AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
public sealed class FactX64OnlyAttribute : FactAttribute
{
  public FactX64OnlyAttribute()
  {
    if (RuntimeInformation.ProcessArchitecture != Architecture.X64)
    {
      Skip = $"Requires x64; running on {RuntimeInformation.ProcessArchitecture}.";
    }
  }
}

[AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
public sealed class TheoryX64OnlyAttribute : TheoryAttribute
{
  public TheoryX64OnlyAttribute()
  {
    if (RuntimeInformation.ProcessArchitecture != Architecture.X64)
    {
      Skip = $"Requires x64; running on {RuntimeInformation.ProcessArchitecture}.";
    }
  }
}


