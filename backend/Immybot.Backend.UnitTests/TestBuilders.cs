using System.Threading;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Moq;
using NuGet.Versioning;
using Polly;

namespace Immybot.Backend.UnitTests;

internal static class TestBuilders
{
  internal static ImmyDetectionResolver WithDetectSoftwareVersionResult(this ImmyDetectionResolver resolver, MockedServices mocks, ImmyOperationResult<SemanticVersion?> result)
  {
    mocks.IMachineSoftwareOperations!
      .Setup(a => a.DetectSoftwareVersion(It.IsAny<IStageRunContext>(), It.IsAny<Software>(), It.IsAny<bool>(), It.IsAny<TargetAssignment?>()))
      .ReturnsAsync(() => result);
    return resolver;
  }

  internal static IActionRunContext WithMaintenanceAction(this IActionRunContext ctx, MockedServices mocks, MaintenanceAction action)
  {
    mocks.IActionRunContext!.SetupGet(a => a.Action).Returns(action);
    return ctx;
  }

  internal static IActionRunContext WithSpecifiedLicense(this IActionRunContext ctx, MockedServices mocks, License license)
  {
    mocks.IActionRunContext!.SetupGet(a => a.SpecifiedLicense).Returns(license);
    return ctx;
  }

  internal static IActionRunContext WithDesiredSoftware(this IActionRunContext ctx, MockedServices mocks, Software software, SoftwareVersion version)
  {
    mocks.IActionRunContext!.SetupGet(a => a.DesiredSoftware).Returns(software);
    mocks.ISoftwareActions!
      .Setup(a => a.GetLatestSoftwareVersion(It.IsAny<SoftwareType>(), It.IsAny<string>(), It.IsAny<CancellationToken>(), It.IsAny<SemanticVersion?>(), It.IsAny<Context?>(), It.IsAny<IAsyncPolicy?>()))
      .ReturnsAsync(version);
    return ctx;
  }

  internal static IActionRunContext WithMaintenanceTask(this IActionRunContext ctx, MockedServices mocks, MaintenanceTask task)
  {
    mocks.IActionRunContext!.Setup(a => a.GetMaintenanceTask()).ReturnsAsync(task);
    return ctx;
  }

  internal static IActionRunContext SetupAnnouceActionPhase(this IActionRunContext ctx, MockedServices mocks, ActionProgressPhaseName phaseName)
  {
    mocks.IActionRunContext!
      .Setup(a => a.AnnounceActionPhase(phaseName))
      .ReturnsAsync(new SessionPhase { ActionProgressPhaseName = phaseName });
    return ctx;
  }

  internal static MaintenanceSession BuildMaintenanceSession(this MockedServices mocks)
  {
    mocks.Session = new MaintenanceSession() { JobArgs = new() };
    return mocks.Session;
  }

  internal static MaintenanceSession WithDetectionStage(this MaintenanceSession session)
  {
    session.Stages.Add(new() { Type = SessionStageType.Resolution });
    session.Stages.Add(new() { Type = SessionStageType.Detection });
    return session;
  }

  internal static MaintenanceSession WithExecutionStage(this MaintenanceSession session)
  {
    session.Stages.Add(new() { Type = SessionStageType.Execution });
    return session;
  }

  internal static MaintenanceSession WithCompliantMaintenanceAction(this MaintenanceSession session)
  {
    session.MaintenanceActions.Add(new MaintenanceAction() { MaintenanceIdentifier = "test", ActionResult = MaintenanceActionResult.Success });
    return session;
  }

  internal static SessionJobArgs BuildSessionJobArgs(this MockedServices mocks)
  {
    mocks.SessionJobArgs = new SessionJobArgs();
    return mocks.SessionJobArgs;
  }

  internal static SessionJobArgs WithEmailConfiguration(this SessionJobArgs args, MaintenanceEmailConfiguration config)
  {
    args.MaintenanceEmailConfiguration = config;
    return args;
  }
}
