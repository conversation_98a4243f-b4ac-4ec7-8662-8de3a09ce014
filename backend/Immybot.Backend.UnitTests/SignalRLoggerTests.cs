using System.Collections.Generic;
using System.Threading.Tasks;
using Immybot.Backend.Web.Common.Lib.SignalRHubs;
using Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources;
using Immybot.Backend.Web.Common.Lib.SignalRLogger;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Range = Moq.Range;

namespace Immybot.Backend.UnitTests;

public class SignalRLoggerTests
{
  private static Mock<IHubContext<ImmyBotUserHub, IImmyBotUserHubClient>> MockHub(
    // ReSharper disable once OutParameterValueIsAlwaysDiscarded.Local
    out Mock<IHubClients<IImmyBotUserHubClient>> clientsMock,
    out Mock<IImmyBotUserHubClient> clientMock)
  {
    var hubMock = new Mock<IHubContext<ImmyBotUserHub, IImmyBotUserHubClient>>();
    clientsMock = new Mock<IHubClients<IImmyBotUserHubClient>>();
    clientMock = new Mock<IImmyBotUserHubClient>();

    hubMock.SetupGet(h => h.Clients)
      .Returns(clientsMock.Object);
    clientsMock.Setup(c => c.Group("ApplicationLogs"))
      .Returns(clientMock.Object);
    clientMock.Setup(c => c.ApplicationLogAdded(It.IsAny<ApplicationLogResource>()))
      .Returns(Task.CompletedTask)
      .Verifiable();
    return hubMock;
  }

  // Running the web project should add the SignalRLoggerProvider to the logging pipeline, and no logs should be streamed to the SignalR hub by default.
  [Theory]
  [CombinatorialData]
  public async Task SignalRLoggerProvider_ShouldUseDefaultLoggingConfigurationLogLevels(
    [CombinatorialValues(LogLevel.Warning, LogLevel.Debug)]
    LogLevel defaultLogLevel,
    [CombinatorialValues(null, LogLevel.Warning, LogLevel.Debug)]
    LogLevel? parentCategoryLogLevel,
    [CombinatorialValues(null, LogLevel.Warning, LogLevel.Debug)]
    LogLevel? ourCategoryLogLevel)
  {
    // Arrange
    var hubMock = MockHub(out var _, out var _);

    var builder = WebApplication.CreateSlimBuilder(new WebApplicationOptions());
    var configs = new Dictionary<string, string?>()
    {
      ["Logging:LogLevel:Default"] = defaultLogLevel.ToString(),
    };
    if (ourCategoryLogLevel != null)
    {
      configs[$"Logging:LogLevel:{typeof(SignalRLoggerTests).FullName}"] =
        ourCategoryLogLevel.ToString();
    }

    if (parentCategoryLogLevel != null)
    {
      configs[$"Logging:LogLevel:{typeof(SignalRLoggerTests).Namespace}"] =
        parentCategoryLogLevel.ToString();
    }

    builder.Configuration.Sources.Clear();
    builder.Configuration.AddInMemoryCollection(configs);
    builder.Services.AddSingleton(hubMock.Object);
    builder.Services.AddLogging(loggingBuilder => loggingBuilder.AddSignalRLogger());

    var app = builder.Build();

    var logger = app.Services.GetRequiredService<ILogger<SignalRLoggerTests>>();

    // Act
    var store = app.Services.GetRequiredService<ISignalRLoggerInMemoryOptionsStore>();
    store.ToggleStreaming(true);
    logger.LogInformation("Test log message");


    // Sleep for a short time to allow the SignalRLoggerProcessor to process the log message
    await Task.Delay(100);

    var relevantLogLevel = ourCategoryLogLevel ?? parentCategoryLogLevel ?? defaultLogLevel;

    // Assert
    if (relevantLogLevel == LogLevel.Debug) hubMock.Verify();
    else hubMock.VerifyNoOtherCalls();
  }

  [Theory]
  [CombinatorialData]
  public async Task
    SignalRLoggerProvider_ShouldStreamLogsAppropriatelyWhenSourceContextsAreAddedAndRemove(
      bool streamingEnabled,
      [CombinatorialValues(null, LogLevel.Debug)]
      LogLevel? ourCategoryLogLevel,
      bool unsetDefaultParentCategoryLogLevel)
  {
    // Arrange
    var builder = WebApplication.CreateSlimBuilder(new WebApplicationOptions());
    var hubMock = MockHub(out var _, out var _);
    builder.Configuration.Sources.Clear();
    builder.Configuration.AddInMemoryCollection(new Dictionary<string, string?>()
    {
      ["Logging:LogLevel:Default"] = LogLevel.Warning.ToString(),
      [$"Logging:LogLevel:{typeof(SignalRLoggerTests).Namespace}"] = LogLevel.Debug.ToString(),
    });
    builder.Services.AddSingleton(hubMock.Object);
    builder.Services.AddLogging(loggingBuilder => loggingBuilder.AddSignalRLogger());

    var app = builder.Build();

    var logger = app.Services.GetRequiredService<ILogger<SignalRLoggerTests>>();

    // Act
    var store = app.Services.GetRequiredService<ISignalRLoggerInMemoryOptionsStore>();
    if (streamingEnabled) store.ToggleStreaming(true);
    if (ourCategoryLogLevel != null)
      store.AddOrUpdateSourceContextLogLevel(typeof(SignalRLoggerTests).FullName,
        ourCategoryLogLevel.Value);
    if (unsetDefaultParentCategoryLogLevel)
      store.ClearSourceContextLogLevel(typeof(SignalRLoggerTests).Namespace!);

    logger.LogInformation("Test log message");

    // Sleep for a short time to allow the SignalRLoggerProcessor to process the log message
    await Task.Delay(100);

    // Assert
    if (!streamingEnabled) hubMock.VerifyNoOtherCalls();
    else if (ourCategoryLogLevel == LogLevel.Debug) hubMock.Verify();
    else if (unsetDefaultParentCategoryLogLevel)
      hubMock.VerifyNoOtherCalls(); // removing parent log level of Debug should put it back to default of Warning
    else hubMock.Verify();
  }

  [Theory, CombinatorialData]
  public async Task SignalRLoggerProvider_DropsLogsWhenQueueIsFull_IfConfiguredToDoSo(
    SignalRLoggerQueueFullMode fullMode)
  {
    // Arrange
    var builder = WebApplication.CreateSlimBuilder(new WebApplicationOptions());
    var hubMock = MockHub(out var _, out var clientMock);
    builder.Configuration.Sources.Clear();
    builder.Configuration.AddInMemoryCollection(new Dictionary<string, string?>()
    {
      ["Logging:SignalR:MaxQueueLength"] = "1",
      ["Logging:SignalR:QueueFullMode"] = fullMode.ToString(),
      [$"Logging:LogLevel:{typeof(SignalRLoggerTests).Namespace}"] = LogLevel.Debug.ToString(),
    });
    builder.Services.AddSingleton(hubMock.Object);
    builder.Services.AddLogging(loggingBuilder => loggingBuilder.AddSignalRLogger());

    var app = builder.Build();

    var logger = app.Services.GetRequiredService<ILogger<SignalRLoggerTests>>();

    // Act
    var store = app.Services.GetRequiredService<ISignalRLoggerInMemoryOptionsStore>();
    store.ToggleStreaming(true);

    logger.LogInformation("Test log message");
    logger.LogInformation("Test log message");
    logger.LogInformation("Test log message");
    logger.LogInformation("Test log message");
    logger.LogInformation("Test log message");

    // Sleep for a short time to allow the SignalRLoggerProcessor to process the message queue
    await Task.Delay(1000);

    // Write another one to trigger the "Dropped messages" message
    logger.LogInformation("Test log message");

    // Sleep for a short time to allow the SignalRLoggerProcessor to process the message queue
    await Task.Delay(1000);

    // Assert
    if (fullMode == SignalRLoggerQueueFullMode.DropWrite)
    {
      clientMock.Verify(
        c => c.ApplicationLogAdded(
          It.Is<ApplicationLogResource>(r => r.Message.Contains("Test log message"))),
        Times.Between(1, 5, Range.Inclusive));
      clientMock.Verify(
        c => c.ApplicationLogAdded(
          It.Is<ApplicationLogResource>(r => r.Message.Contains("Dropped messages"))),
        Times.Between(1, 5, Range.Inclusive));
    }
    else
    {
      clientMock.Verify(
        c => c.ApplicationLogAdded(
          It.Is<ApplicationLogResource>(r => r.Message.Contains("Test log message"))),
        Times.Exactly(6));
    }
  }
}
