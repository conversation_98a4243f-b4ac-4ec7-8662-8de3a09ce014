using System;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Commands.Payloads;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Manager;
using Immybot.Backend.Manager.Domain;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Backend.UnitTests.ContextTests;
using Immybot.Manager.SitesApi;
using Microsoft.Extensions.Options;
using Moq;

namespace Immybot.Backend.UnitTests;

public class SupportRequestGeneratorTests : ActionsTestBase
{
  private readonly Mock<IManagerProvidedSettings> _mgrSettings;
  private readonly MaintenanceSession _session;
  private readonly Person _person;
  private readonly AuthUserDto _user;
  private readonly SupportRequestGenerator _handler;
  private readonly IOptionsMonitor<AppSettingsOptions> _appSettings;

  public SupportRequestGeneratorTests()
  {
    _appSettings = Helpers.MockOptionsMonitor<AppSettingsOptions>(new() { RootUrl = new System.Uri("http://foobar.com") });
    _mgrSettings = new Mock<IManagerProvidedSettings>();
    Mock<IProviderActions> providerActions = new();
    providerActions
      .Setup(x => x.GetProvidersOfType<ISupportsSupportTicketDetailOverride>(It.IsAny<CancellationToken>()))
      .ReturnsAsync(new List<ISupportsSupportTicketDetailOverride>());

    providerActions
      .Setup(x => x.GetProviderLinksOfType<ISupportsSupportTicketDetailOverride>(It.IsAny<CancellationToken>()))
      .ReturnsAsync(new List<ProviderLink>());

    GetSqliteDbContext(_loggerFactory);

    var tenant = CreateTenant();
    var providerLink = CreateProviderLink(tenant.Id);
    var computer = CreateBlankComputer(tenantId: tenant.Id, linkId: providerLink.Id);
    var blobStorageSasService = new Mock<IAzureBlobStorageSasService>();
    _session = CreateSession(computer.Id);
    _person = CreatePerson(computer.TenantId, "<EMAIL>", "Foo", "Bar", MspUserPrincipalId);
    _user = GetAuthUser(CreateUser(computer.TenantId, personId: _person.Id, isAdmin: true));
    _handler = new SupportRequestGenerator(
      _mgrSettings.Object,
      () => GetNewSqliteDbContext(),
      _appSettings,
      blobStorageSasService.Object,
      providerActions.Object
      );
  }

  [Fact]
  public async Task GenerateSessionSupportTicket_ShouldGenerateTicketWithCorrectNotes()
  {
    const string ticketSubject = "Ticket Subject";
    const string ticketNotes = "Ticket Notes";

    var sessionUrl = new Uri(_appSettings.CurrentValue.RootUrl, $"/computers/{_session.ComputerId}/sessions/{_session.Id}");
    var expectedNotesFormat = SupportRequestGenerator.GetSessionNotes(
      _appSettings.CurrentValue.RootUrl,
      technicianAccessAllowed: false,
      _session.Id,
      sessionUrl.ToString(),
      ticketNotes);
    await _handler.GenerateSessionSupportTicket(
      _user,
      new(
        _session.Id,
        ticketSubject,
        ticketNotes,
        AllowTechnicianAccess: false),
      CancellationToken.None
    );

    // Assert
    _mgrSettings.Verify(
      s => s.CreateSupportTicket(It.Is<CreateSupportTicketRequestBodyV2>(b =>
        b.RequesterFirstName == _user.FirstName
        && b.RequesterLastName == _user.LastName
          && b.RequesterEmail == _person.EmailAddress
          && b.TicketSubject == ticketSubject
          && b.TicketNotes == expectedNotesFormat
      )),
      Times.Once);
  }

  [Fact]
  public async Task GenerateFormSupportTicket_ShouldGenerateTicketWithCorrectNotes()
  {
    // Arrange
    const string ticketSubject = "Form Ticket Subject";
    const string ticketNotes = "Form Ticket Notes";
    const string requesterEmail = "<EMAIL>";
    _mgrSettings.SetupGet(x => x.IsImmySupportAccessEnabled).Returns(false);

    var payload = new GenerateFormSupportTicketPayload(
      RequesterEmail: requesterEmail,
      TicketSubject: ticketSubject,
      TicketNotes: ticketNotes,
      AllowTechnicianAccess: false,
      BlobNames: []);

    // Act
    await _handler.GenerateFormSupportTicket(_user, payload, CancellationToken.None);

    // Assert
    var expectedNotes = string.Join('\n',
      $"Logged in user: {_user.DisplayName} - {_user.Email}",
      $"Instance: {_appSettings.CurrentValue.RootUrl}",
      $"Technician consent provided: No",
      "\n",
      ticketNotes);

    _mgrSettings.Verify(
      s => s.CreateSupportTicket(It.Is<CreateSupportTicketRequestBodyV2>(b =>
        b.RequesterFirstName == _user.FirstName
        && b.RequesterLastName == _user.LastName
          && b.RequesterEmail == requesterEmail
          && b.TicketSubject == ticketSubject
          && b.TicketNotes == expectedNotes
      )),
      Times.Once);
  }
}
