using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.UnitTests.ContextTests;

namespace Immybot.Backend.UnitTests;
public class TenantStoreTests : ActionsTestBase
{
  /*
  Currently verified deletions:
  - Tenant
  - Computers
  - Licenses
  - Brandings
  - Media
  - Schedules
  - SMTP configs
  - Azure errors
  - Maintenance sessions
  - Persons
  - Users
  - Target assignments
  - Computer inventory task script results
  - Agents
  - Maintenance actions
  - Maintenance tasks
  - Session logs
  - Scripts
  - Software
  - User preferences
  */

  [Fact]
  public async Task DeleteTenantData_DeletesAllRelatedEntities()
  {
    // Arrange
    var ctx = GetSqliteDbContext(_loggerFactory);
    var msp = GetOrCreateMspTenant();
    var providerLink = GetOrCreateDefaultProviderLink(msp.Id);

    var tenant = CreateTenant(msp.Id, principalId: "foobar");
    var providerClient = CreateProviderClient(providerLink.Id, tenant.Id);
    var tenantId = tenant.Id;

    // Create computer and related data
    var computer = CreateComputer(
      providerLinkId: providerLink.Id,
      externalClientId: providerClient.ExternalClientId,
      externalAgentId: "d1",
      tenantId: tenantId,
      computerName: "test-computer",
      deviceId: Guid.NewGuid());

    CreateWindowsSystemInfoInventoryResult(computer.Id, "machineid");
    CreatePendingProviderAgent(providerLink.Id, providerClient.ExternalClientId, "d2");

    // Create other tenant-related entities
    var person = CreatePerson(tenantId, "<EMAIL>");
    var user = CreateUser(tenantId);
    CreateTargetAssignment(new TargetAssignment
    {
      Target = "test",
      MaintenanceIdentifier = "test",
      MaintenanceType = MaintenanceType.ChocolateySoftware,
      TenantId = tenantId
    });
    CreateMaintenanceTask(new MaintenanceTask
    {
      Name = "test",
      TenantRelationships = new HashSet<TenantMaintenanceTask> { new() { TenantId = tenantId } },
      DatabaseType = DatabaseType.Local
    });

    var session = CreateSession(computer.Id, tenantId: tenantId);
    session.Logs.Add(new SessionLog { Message = "test", MaintenanceSessionId = session.Id });
    CreateLicense(
      name: "test",
      softwareIdentifier: "test",
      softwareType: SoftwareType.Chocolatey,
      licenseValue: "test",
      licenseType: LicenseType.Key,
      version: NuGet.Versioning.SemanticVersion.Parse("1.0.0"),
      tenantId: tenantId);

    CreateBranding(user, tenantId: tenantId);
    CreateMedia(ownerTenantId: tenantId);
    CreateSchedule(new Schedule { TenantId = tenantId });
    CreateSmtpConfig(tenantId);
    CreateAzureError(ctx, tenant.AzureTenantLink?.AzureTenant?.PrincipalId);
    CreateLocalScript(
      name: "test script",
      tenants: [new() { TenantId = tenantId }]
    );
    CreateSoftware(
      softwareType: SoftwareType.LocalSoftware,
      tenantAssignments: new[] { tenantId }
    );

    var userPreferences = new UserPreferences { UserId = user.Id };
    ctx.Add(userPreferences);
    await ctx.SaveChangesAsync();

    // Verify initial state
    Assert.Equal(2, ctx.Tenants.Count()); // MSP + tenant
    Assert.Single(ctx.GetAllComputers());
    Assert.Single(ctx.GetProviderAgents().Where(a => a.ExternalAgentId == "d1"));

    // Act
    var store = new TenantStore(GetSqliteDbContextFactory());
    await store.DeleteTenantData(new TenantDeletion { TenantId = tenantId }, default);

    // Assert
    await using var assertCtx = GetSqliteDbContext(_loggerFactory);

    // Core tenant verification
    Assert.Single(assertCtx.Tenants);
    Assert.Equal(msp.Id, assertCtx.Tenants.Single().Id);
    Assert.Single(assertCtx.GetProviderAgents());
    Assert.Equal("d2", assertCtx.GetProviderAgents().Single().ExternalAgentId);

    // Verify deletions
    Assert.Empty(assertCtx.GetAllComputers());
    Assert.Empty(assertCtx.Licenses);
    Assert.Empty(assertCtx.Brandings);
    Assert.Empty(assertCtx.Media.Where(m => m.TenantRelationships.Any(t => t.TenantId == tenantId)));
    Assert.Empty(assertCtx.Schedules);
    Assert.Empty(assertCtx.SmtpConfigs);
    Assert.Empty(assertCtx.AzureErrors);
    Assert.Empty(assertCtx.MaintenanceSessions);
    Assert.Empty(assertCtx.Persons);
    Assert.Empty(assertCtx.Users);
    Assert.Empty(assertCtx.TargetAssignments);
    Assert.Empty(assertCtx.ComputerInventoryTaskScriptResults);
    Assert.Empty(assertCtx.MaintenanceActions);
    Assert.Empty(assertCtx.MaintenanceTasks.Where(mt => mt.TenantRelationships.Any(t => t.TenantId == tenantId)));
    Assert.Empty(assertCtx.SessionLogs);
    Assert.Empty(assertCtx.TenantScripts.Where(ts => ts.TenantId == tenantId));
    Assert.Empty(assertCtx.TenantSoftware.Where(ts => ts.TenantId == tenantId));
    Assert.Empty(assertCtx.UserPreferences);
  }
}
