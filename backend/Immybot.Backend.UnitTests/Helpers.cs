using System.Collections;
using System.Collections.Immutable;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using App.Metrics;
using App.Metrics.Counter;
using App.Metrics.Gauge;
using App.Metrics.Histogram;
using App.Metrics.Meter;
using Azure.Core;
using Hangfire;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DynamicProviders;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Jobs;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.AgentIdentification;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Application.Lib.MetaScripts.Modules;
using Immybot.Backend.Application.Lib.Ninite;
using Immybot.Backend.Application.Lib.Policies;
using Immybot.Backend.Application.Lib.Providers;
using Immybot.Backend.Application.Lib.Scripts;
using Immybot.Backend.Application.Lib.Scripts.EphemeralAgent;
using Immybot.Backend.Application.Lib.SessionLogs;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Application.Maintenance.AssignmentResolution;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Infrastructure.Configuration.Application;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Shared.Abstractions.Device;
using Immybot.Shared.Primitives;
using MessagePipe;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.Threading;
using Moq;
using Moq.Language.Flow;
using NuGet.Versioning;
using Polly;
using Polly.Registry;
using IInvocation = Castle.DynamicProxy.IInvocation;
using ScriptBlock = System.Management.Automation.ScriptBlock;
using System.Management.Automation;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Shared.Primitives.Helpers;
using SemanticVersion = NuGet.Versioning.SemanticVersion;
using System.Management.Automation.Runspaces;
using Immybot.Backend.Application.Interface.Language;
using Immybot.Backend.Manager.Domain;
using Immybot.Shared.Scripts;
using Immybot.Shared.Services;

namespace Immybot.Backend.UnitTests;

internal static class Helpers
{
  public static int LastSoftwareIdentifier { get; private set; }
  public static string NextSoftwareIdentifier()
  {
    return (++LastSoftwareIdentifier).ToString();
  }

  #region Entities

  public static DynamicFormBindResultWithConvertedParameters CreateBindResultWithParameterNames(Dictionary<string, object?> parameters)
  {
    var paramList = new List<Parameter>();

    foreach (var (paramName, paramValue) in parameters)
    {
      paramList.Add(new Parameter { Name = paramName, DefaultValue = paramValue, ParameterType = CommandInfoConverter.GetParameterType(paramValue?.GetType() ?? typeof(object)) });
    }

    return new DynamicFormBindResultWithConvertedParameters(
      BindErrors: [],
      ShowCommandInfo: new ShowCommandInfo("", [new ParameterSet { Name = "", Parameters = paramList.ToArray() }]),
      ConvertedParameters: parameters,
      ParameterSetName: ""
      );
  }

  public static ParameterValue CreateParameterValue(object? value, bool allowOverride = false)
  {
    var el = JsonSerializer.SerializeToElement(value);

    return new ParameterValue(el);
  }

  public static DeploymentParameterValue CreateDeploymentParameterValue(object? value, bool allowOverride = false)
  {
    var el = JsonSerializer.SerializeToElement(value);

    return new DeploymentParameterValue(el, AllowOverride: allowOverride);
  }
  public static Computer MakeComputer(
    ComputerOnboardingStatus? onboardingStatus = null,
    ICollection<ProviderAgent>? agents = null,
    bool isOnline = true,
    DateTime? createdDate = null,
    DateTime? onboardingDate = null)
  {
    var computer = new Computer
    {
      CreatedDate = createdDate ?? DateTime.UtcNow,
      OnboardedDateUtc = onboardingDate ?? (onboardingStatus == ComputerOnboardingStatus.Onboarded ? DateTime.UtcNow : null),
      Agents = agents ?? new ProviderAgent[]
      {
        new()
        {
          ExternalClientId = "",
          ExternalAgentId = "",
          IsOnline = isOnline,
          ProviderLink = new ProviderLink { Name = "", HealthStatus = HealthStatus.Healthy },
          SupportsRunningScripts = true
        }
      },
      OwnerTenant = new Tenant { Name = "SomeTenant" },
    };
    if (onboardingStatus != null)
    {
      computer.OnboardingStatus = onboardingStatus.Value;
    }
    return computer;
  }

  public static (MaintenanceAction action, Software software) MakeMaintenanceActionAndSoftware(
    Software? software = null,
    string? version = null,
    string? desiredSoftwareVersion = null,
    DesiredSoftwareState desiredSoftwareState = DesiredSoftwareState.NewerOrEqualVersion,
    MaintenanceActionType actionType = MaintenanceActionType.Undetermined,
    MaintenanceActionReason actionReason = MaintenanceActionReason.Undetermined,
    MaintenanceActionResult actionResult = MaintenanceActionResult.Pending)
  {
    if (software == null)
    {
      software = MakeSoftware(version: version);
    }
    var action = new MaintenanceAction
    {
      MaintenanceIdentifier = software.Identifier,
      MaintenanceType = software.MaintenanceType,
      DesiredSoftwareState = desiredSoftwareState,
      DesiredVersion = desiredSoftwareVersion != null ? NuGetVersion.Parse(desiredSoftwareVersion) : null,
      ActionType = actionType,
      ActionReason = actionReason,
      ActionResult = actionResult,
    };
    return (action, software);
  }

  public static (MaintenanceAction action, MaintenanceTask task) MakeMaintenanceActionAndTask(
    MaintenanceTask? task = null,
    bool withGetScript = false,
    bool withSetScript = false,
    bool withTestScript = false,
    bool hasDeterminedTaskGetResult = false,
    string? taskGetResult = null,
    bool hasDeterminedTaskTestResult = false,
    bool? taskTestResult = null,
    int? assignmentId = null,
    DatabaseType? assignmentType = null,
    MaintenanceTaskMode mode = MaintenanceTaskMode.Enforce,
    MaintenanceActionType actionType = MaintenanceActionType.Undetermined,
    MaintenanceActionReason actionReason = MaintenanceActionReason.Undetermined,
    MaintenanceActionResult actionResult = MaintenanceActionResult.Pending)
  {
    task ??= MakeTask(
      withGetScript: withGetScript,
      withSetScript: withSetScript,
      withTestScript: withTestScript);

    var action = new MaintenanceAction
    {
      MaintenanceDisplayName = $"Action{NextSoftwareIdentifier()}",
      MaintenanceIdentifier = task.Id.ToString(),
      MaintenanceType = task.MaintenanceType,
      MaintenanceTaskMode = mode,
      ActionType = actionType,
      ActionReason = actionReason,
      ActionResult = actionResult,
      HasDeterminedTaskGetResult = hasDeterminedTaskGetResult,
      MaintenanceTaskGetResult = taskGetResult,
      HasDeterminedTaskTestResult = hasDeterminedTaskTestResult,
      TaskTestResult = taskTestResult,
      AssignmentId = assignmentId,
      AssignmentType = assignmentType,
    };
    return (action, task);
  }

  public static MaintenanceAction MakeSoftwareMaintenanceAction(
    Software? software = null,
    MaintenanceActionType actionType = MaintenanceActionType.Install,
    MaintenanceActionReason actionReason = MaintenanceActionReason.SoftwareMissing,
    MaintenanceActionStatus actionStatus = MaintenanceActionStatus.NotStarted,
    MaintenanceActionResult actionResult = MaintenanceActionResult.Pending,
    SemanticVersion? detectedVersion = null,
    SemanticVersion? desiredVersion = null,
    int? id = null,
    DesiredSoftwareState? desiredSoftwareState = null)
  {
    if (software == null)
    {
      software = MakeSoftware();
    }

    var action = new MaintenanceAction
    {
      Id = id ?? 0,
      MaintenanceIdentifier = software.Identifier,
      MaintenanceType = software.MaintenanceType,
      ActionType = actionType,
      ActionResult = actionResult,
      ActionReason = actionReason,
      DetectedVersion = detectedVersion,
      DesiredVersion = desiredVersion,
      ActionStatus = actionStatus,
      DesiredSoftwareState = desiredSoftwareState
    };
    return action;
  }
  public static Script MakeFilterScript()
    => MakeScript(scriptCategory: ScriptCategory.FilterScriptDeploymentTarget);
  public static Script MakeCloudScript()
    => MakeScript(executionContext: ScriptExecutionContext.CloudScript);
  public static Script MakeIntegrationScript(string? script = null)
    => MakeScript(scriptCategory: ScriptCategory.Integration, action: script, executionContext: ScriptExecutionContext.Metascript);

  public static MaintenanceTask MakeTask(
    bool withGetScript = false,
    string? getScriptText = null,
    bool withSetScript = false,
    string? setScriptText = null,
    bool withTestScript = false,
    string? testScriptText = null,
    bool isConfigTask = false,
    MaintenanceTaskParameter[]? parameters = null)
  {
    var getScript = (withGetScript || getScriptText != null) ? MakeScript(action: getScriptText ?? "foobar", scriptLanguage: ScriptLanguage.PowerShell, timeout: 500, executionContext: ScriptExecutionContext.Metascript) : null;
    var setScript = (withSetScript || setScriptText != null) ? MakeScript(action: setScriptText ?? "foobar", scriptLanguage: ScriptLanguage.PowerShell, timeout: 500, executionContext: ScriptExecutionContext.Metascript) : null;
    var testScript = (withTestScript || testScriptText != null) ? MakeScript(action: testScriptText ?? "foobar", scriptLanguage: ScriptLanguage.PowerShell, timeout: 500, executionContext: ScriptExecutionContext.Metascript) : null;

    var task = new MaintenanceTask
    {
      Name = "",
      Id = ++LastSoftwareIdentifier,
      GetEnabled = withGetScript || getScriptText != null,
      GetScript = getScript,
      GetScriptId = getScript?.Id,
      GetScriptType = getScript?.ScriptType,
      SetEnabled = withSetScript || setScriptText != null,
      SetScript = setScript,
      SetScriptId = setScript?.Id,
      SetScriptType = setScript?.ScriptType,
      TestEnabled = withTestScript || testScriptText != null,
      TestScript = testScript,
      TestScriptId = testScript?.Id,
      TestScriptType = testScript?.ScriptType,
      Parameters = parameters ?? Array.Empty<MaintenanceTaskParameter>(),
      IsConfigurationTask = isConfigTask,
    };
    return task;
  }

  public static Script MakeScript(
    string? identifier = null,
    string? name = null,
    DatabaseType scriptType = DatabaseType.Local,
    ScriptLanguage scriptLanguage = default,
    ScriptCategory scriptCategory = default,
    string? action = null,
    Dictionary<string, object?>? variables = null,
    Dictionary<string, object?>? parameters = null,
    ScriptExecutionContext executionContext = default,
    ScriptOutputType outputType = default,
    int? timeout = null,
    bool useNullScriptName = false)
  {
    if (identifier == null)
    {
      identifier = NextSoftwareIdentifier();
    }

    name ??= useNullScriptName ? null! : string.Empty;
    return new Script
    {
      Id = int.Parse(identifier),
      Name = name,
      ScriptType = scriptType,
      ScriptLanguage = scriptLanguage,
      ScriptCategory = scriptCategory,
      Action = action ?? string.Empty,
      Variables = variables ?? [],
      Parameters = parameters ?? [],
      ScriptExecutionContext = executionContext,
      OutputType = outputType,
      Timeout = timeout,
    };
  }

  public static string MakeDynamicIntegrationScript(
    string paramType = "Uri"
    )
  {
    return $$"""
        $Integration = New-DynamicIntegration -Init {
          param(
              [Parameter(Mandatory)]
              [{{paramType}}]$TestParam
          )
          [OpResult]::Ok()
        } -HealthCheck {
          [CmdletBinding()]
          [OutputType([HealthCheckResult])]
          param()
          return New-HealthyResult
        }
        $Integration
        """;
  }

  public static Software MakeSoftware(
    SoftwareType softwareType = SoftwareType.LocalSoftware,
    string? identifier = null,
    string? chocoProviderSoftwareId = null,
    string? niniteProviderSoftwareId = null,
    string? version = null,
    (int taskId, DatabaseType taskType)? configTask = null,
    UpdateActionType upgradeStrategy = UpdateActionType.None,
    Script? detectionScript = null,
    Script? installScript = null,
    Script? uninstallScript = null,
    Script? postInstallScript = null,
    Script? postUninstallScript = null,
    Script? testScript = null,
    Script? upgradeScript = null,
    Script? dynamicVersionsScript = null,
    RepairActionType repairType = RepairActionType.None,
    bool testRequired = false,
    Guid? agentIntegrationTypeId = null)
  {
    if (identifier == null)
    {
      identifier = NextSoftwareIdentifier();
    }
    Software software = softwareType switch
    {
      SoftwareType.LocalSoftware => new LocalSoftware
      {
        Id = int.Parse(identifier),
        ChocoProviderSoftwareId = chocoProviderSoftwareId,
        NiniteProviderSoftwareId = niniteProviderSoftwareId,
        MaintenanceTaskId = configTask?.taskId,
        MaintenanceTaskType = configTask?.taskType,
        UpgradeStrategy = upgradeStrategy,
        UpgradeScript = upgradeScript,
        RepairType = repairType,
        UseDynamicVersions = dynamicVersionsScript != null,
        DynamicVersionsScript = dynamicVersionsScript,
        DetectionScript = detectionScript,
        InstallScript = installScript,
        UninstallScript = uninstallScript,
        PostInstallScript = postInstallScript,
        PostUninstallScript = postUninstallScript,
        TestScript = testScript,
        TestRequired = testRequired,
        AgentIntegrationTypeId = agentIntegrationTypeId,
      },
      SoftwareType.GlobalSoftware => new GlobalSoftware
      {
        Id = int.Parse(identifier),
        ChocoProviderSoftwareId = chocoProviderSoftwareId,
        NiniteProviderSoftwareId = niniteProviderSoftwareId,
        MaintenanceTaskId = configTask?.taskId,
        UpgradeStrategy = upgradeStrategy,
        UpgradeScript = upgradeScript,
        RepairType = repairType,
        UseDynamicVersions = dynamicVersionsScript != null,
        DynamicVersionsScript = dynamicVersionsScript,
        DetectionScript = detectionScript,
        InstallScript = installScript,
        UninstallScript = uninstallScript,
        PostInstallScript = postInstallScript,
        PostUninstallScript = postUninstallScript,
        TestScript = testScript,
        TestRequired = testRequired,
        AgentIntegrationTypeId = agentIntegrationTypeId,
      },
      SoftwareType.Chocolatey => new ChocolateySoftware
      {
        Id = identifier,
        ChocoProviderSoftwareId = chocoProviderSoftwareId,
        NiniteProviderSoftwareId = niniteProviderSoftwareId,
        UpgradeStrategy = upgradeStrategy,
        UpgradeScript = upgradeScript,
        RepairType = repairType,
        UseDynamicVersions = dynamicVersionsScript != null,
        DynamicVersionsScript = dynamicVersionsScript,
        DetectionScript = detectionScript,
        InstallScript = installScript,
        UninstallScript = uninstallScript,
        PostInstallScript = postInstallScript,
        PostUninstallScript = postUninstallScript,
        TestScript = testScript,
        TestRequired = testRequired,
        AgentIntegrationTypeId = agentIntegrationTypeId,
      },
      SoftwareType.Ninite => new NiniteSoftware
      {
        Id = identifier,
        ChocoProviderSoftwareId = chocoProviderSoftwareId,
        NiniteProviderSoftwareId = niniteProviderSoftwareId,
        UpgradeStrategy = upgradeStrategy,
        UpgradeScript = upgradeScript,
        RepairType = repairType,
        UseDynamicVersions = dynamicVersionsScript != null,
        DynamicVersionsScript = dynamicVersionsScript,
        DetectionScript = detectionScript,
        InstallScript = installScript,
        UninstallScript = uninstallScript,
        PostInstallScript = postInstallScript,
        PostUninstallScript = postUninstallScript,
        TestScript = testScript,
        TestRequired = testRequired,
        AgentIntegrationTypeId = agentIntegrationTypeId,
      },
      SoftwareType.WindowsUpdate => new WindowsPatch { MicrosoftUpdateId = Guid.NewGuid() },
      _ => throw new NotImplementedException(),
    };
    if (version != null)
    {
      MakeSoftwareVersion(software, version);
    }
    return software;
  }

  public static (Software, SoftwareVersion) MakeSoftwareAndVersion(
    string version,
    SoftwareType softwareType = SoftwareType.LocalSoftware,
    string? identifier = null,
    Script? installScript = null,
    UpdateActionType softwareUpgradeStrategy = UpdateActionType.None,
    Script? softwareUpgradeScript = null,
    UpdateActionType versionUpgradeStrategy = UpdateActionType.None,
    Script? versionUpgradeScript = null,
    RepairActionType softwareRepairType = RepairActionType.None)
  {
    var software = MakeSoftware(softwareType, identifier, upgradeStrategy: softwareUpgradeStrategy, upgradeScript: softwareUpgradeScript, repairType: softwareRepairType);
    return (software, MakeSoftwareVersion(software, version, installScript: installScript, upgradeStrategy: versionUpgradeStrategy, upgradeScript: versionUpgradeScript));
  }

  public static SoftwareVersion MakeSoftwareVersion(
    Software software,
    string version,
    Script? installScript = null,
    Script? postInstallScript = null,
    UpdateActionType upgradeStrategy = UpdateActionType.None,
    Script? upgradeScript = null,
    bool testRequired = false)
  {
    SoftwareVersion? ver = null;
    switch (software.SoftwareType)
    {
      case SoftwareType.LocalSoftware:
        var localSoftware = software as LocalSoftware;
        Assert.NotNull(localSoftware);
        ver = new LocalSoftwareVersion
        {
          SoftwareId = int.Parse(software.Identifier),
          SemanticVersion = NuGetVersion.Parse(version),
          Software = localSoftware,
          InstallScript = installScript,
          UpgradeStrategy = upgradeStrategy,
          UpgradeScript = upgradeScript,
          PostInstallScript = postInstallScript,
          TestRequired = testRequired
        };
        localSoftware.SoftwareVersions
          .Add((ver as LocalSoftwareVersion)!);
        break;
      case SoftwareType.GlobalSoftware:
        var globalSoftware = software as GlobalSoftware;
        Assert.NotNull(globalSoftware);
        ver = new GlobalSoftwareVersion
        {
          SoftwareId = int.Parse(software.Identifier),
          SemanticVersion = NuGetVersion.Parse(version),
          Software = globalSoftware,
          InstallScript = installScript,
          UpgradeStrategy = upgradeStrategy,
          UpgradeScript = upgradeScript,
          PostInstallScript = postInstallScript,
          TestRequired = testRequired
        };
        globalSoftware.SoftwareVersions
          .Add((ver as GlobalSoftwareVersion)!);
        break;
      case SoftwareType.Chocolatey:
        ver = new ChocolateySoftwareVersion
        {
          SoftwareId = software.Identifier,
          SemanticVersion = NuGetVersion.Parse(version),
          Software = (software as ChocolateySoftware)!,
          InstallScript = installScript,
          UpgradeStrategy = upgradeStrategy,
          UpgradeScript = upgradeScript,
          PostInstallScript = postInstallScript,
          TestRequired = testRequired
        };
        (software as ChocolateySoftware)?.SoftwareVersions
          .Add((ver as ChocolateySoftwareVersion)!);
        break;
      case SoftwareType.Ninite:
        ver = new NiniteSoftwareVersion
        {
          SoftwareId = software.Identifier,
          SemanticVersion = NuGetVersion.Parse(version),
          Software = (software as NiniteSoftware)!,
          InstallScript = installScript,
          UpgradeStrategy = upgradeStrategy,
          UpgradeScript = upgradeScript,
          PostInstallScript = postInstallScript,
          TestRequired = testRequired
        };
        (software as NiniteSoftware)?.SoftwareVersions
          .Add((ver as NiniteSoftwareVersion)!);
        break;
    }
    if (ver == null) throw new NotImplementedException();
    return ver;
  }
  #endregion Entities

  #region Mocks

  public static IUserService MockUserService(this MockedServices mocks, Tenant tenant, AuthUserDto user)
  {
    mocks.IUserService = new();
    mocks.IUserService
      .Setup(m => m.GetCurrentUser())
      .Returns(user);
    return mocks.IUserService.Object;
  }

  // Mocks the IServiceScopeFactory on MockedServices such that it will return a
  // scope/service-provider that returns the provided service
  public static IServiceScopeFactory MockServiceScopeFactoryService<T>(
    this MockedServices mocks,
    T obj)
    where T : notnull
  {
    (mocks.ScopedServiceProvider ??= new()).Setup(p => p.GetService(typeof(T))).Returns(obj);

    (mocks.IServiceScope ??= new())
      .SetupGet(s => s.ServiceProvider)
      .Returns(mocks.ScopedServiceProvider.Object);

    (mocks.IServiceScopeFactory ??= new())
      .Setup(f => f.CreateScope())
      .Returns(() => mocks.IServiceScope.Object);

    return mocks.IServiceScopeFactory.Object;
  }

  public static IScriptInvoker MockScriptInvoker(this MockedServices mocks)
  {
    if (mocks.IScriptInvoker == null)
    {
      mocks.IScriptInvoker = new();
    }

    return mocks.IScriptInvoker.Object;
  }

  public static IReturnsResult<IComputerAssignmentActions> MockComputerAssignmentActions_GetComputersInTarget(
    this MockedServices mocks,
    TargetType targetTypeToMock,
    int? tenantIdToMock,
    bool includeChildTenantsToMock,
    bool allowAccessToParentTenantToMock,
    List<Computer> computers,
    Action? callback = null)
  {
    var setup = (mocks.IComputerAssignmentActions ??= new())
      .Setup(a => a.GetComputersInTarget(
        targetTypeToMock,
        It.IsAny<TargetGroupFilter>(),
        It.IsAny<string?>(),
        tenantIdToMock,
        includeChildTenantsToMock,
        allowAccessToParentTenantToMock,
        It.IsAny<int?>(),
        It.IsAny<Guid?>(),
        It.IsAny<Guid?>(),
        It.IsAny<Guid?>(),
        It.IsAny<IAsyncPolicy?>(),
        It.IsAny<Context?>(),
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.IsAny<int?>(),
        It.IsAny<bool>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.IsAny<ICollection<string>?>(),
        It.IsAny<bool>(),
        It.IsAny<bool>()
        ))
      .ReturnsAsync(DisposableValue.Create(computers.AsQueryable(), onDisposedCallback: () => { }));

    if (callback != null) setup.Callback(callback);
    return setup;
  }

  public static IScriptInvoker MockIScriptInvoker_RunScript(
    this MockedServices mocks,
    Script? script = null)
  {
    _ = (mocks.IScriptInvoker ??= new())
      .Setup(BuildScriptInvokerRunScriptAsyncExpression(script: script))
      .ReturnsAsync(new SerializedPowerShellScriptResult());

    return mocks.IScriptInvoker.Object;
  }

  public static Mock<IGraphApi> MockGraphApi()
  {
    var graphApi = new Mock<IGraphApi>();
    graphApi.Setup(g => g.GetClient(It.IsAny<TokenCredential>(), It.IsAny<bool>()))
      .Throws(new Exception("Cannot get client"));
    graphApi.Setup(g => g.GetAllUsers(It.IsAny<TokenCredential>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<bool>()))
      .ReturnsAsync([]);
    return graphApi;
  }

  public static IMetascriptInvoker MockIMetascriptInvoker_RunMetascript<T>(
    this MockedServices mocks,
    T? obj,
    Script? script = null,
    Action? callback = null)
  {
    var outputCollection = obj is null ? [] : new List<object>() { obj };
    var result = new MetaScriptResult<T>(outputCollection, [], null);

    var setup = (mocks.IMetascriptInvoker ??= new())
      .Setup(BuildRunMetaScriptExpression<T>(script))
      .Returns(Task.FromResult(result));

    if (callback != null) setup.Callback(callback);

    return mocks.IMetascriptInvoker.Object;
  }

  public static Mock<ISyncAzureUsersAndImmyPersonsCmd> MockSyncAzureUsersAndImmyPersonsCmd()
  {
    var mock = new Mock<ISyncAzureUsersAndImmyPersonsCmd>();
    return mock;
  }

  public static Expression<Func<IInventoryDeviceCmd, Task<InventoryDeviceCmdResponse>>> BuildIInventoryDeviceCmdRunExpression()
  {
    return a => a.Run(
      It.IsAny<InventoryDeviceCmdPayload>(),
      It.IsAny<CancellationToken>(),
      It.IsAny<Action<InventoryDeviceCmdScriptEvent>?>(),
      It.IsAny<Action<string>?>(), It.IsAny<IRunContext?>());
  }

  public static IActionRunContext MockActionRunContext_RunScript<T>(
    MockedServices mocks,
    object? val,
    Script? script = null)
  {
    var outputCollection = val is null ? [] : new List<object>() { val };
    var result = new MetaScriptResult<T>(outputCollection, [], null);

    (mocks.IActionRunContext ??= new())
      .As<IRunContext>()
      .Setup(BuildRunScriptExpression<T>(script))
      .ReturnsAsync(result);

    return mocks.IActionRunContext.Object;
  }

  public static IActionRunContext MockActionRunContext_RunScript(
    MockedServices mocks,
    object? val,
    Script? script = null)
  {
    var outputCollection = val is null ? [] : new List<object>() { val };
    var result = new MetaScriptResult<object>(outputCollection, [], null);

    (mocks.IActionRunContext ??= new())
      .As<IRunContext>()
      .Setup(BuildRunScriptExpression(script))
      .ReturnsAsync(result);

    return mocks.IActionRunContext.Object;
  }


  public static Expression<Func<IRunContext, Task<MetaScriptResult<T>>>> BuildRunCloudScriptExpression<T>(
    Script? script = null)
  {
    return ctx => ctx.RunScript<T>(
      script ?? new Script { Name = "" },
      It.IsAny<int>(),
      It.IsAny<CancellationToken>(),
      It.IsAny<Computer?>(),
      It.IsAny<ScriptContextParameters?>(),
      It.IsAny<string>(),
      It.IsAny<bool>(),
      It.IsAny<bool>());
  }

  public static Expression<Func<IMetascriptInvoker, Task<MetaScriptResult<T>>>> BuildRunMetaScriptExpression<T>(
    Script? script = null)
  {
    // if script is provided, then it must have a name because that is how we match
    if (script != null)
      return metascriptInvoker => metascriptInvoker.RunMetascript<T>(
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.Is((Script s) => script.Name == s.Name),
        It.IsAny<CancellationToken>(),
        It.IsAny<TimeSpan>(),
        It.IsAny<IRunContext>(),
        It.IsAny<Guid?>(),
        It.IsAny<Computer?>(),
        It.IsAny<int?>(),
        It.IsAny<int?>(),
        It.IsAny<bool>(),
        It.IsAny<IAsyncPolicy?>(),
        It.IsAny<Context?>(),
        It.IsAny<Guid?>(),
        It.IsAny<bool>());

    return metascriptInvoker => metascriptInvoker.RunMetascript<T>(
      It.IsAny<bool>(),
      It.IsAny<bool>(),
      It.IsAny<Script>(),
      It.IsAny<CancellationToken>(),
      It.IsAny<TimeSpan>(),
      It.IsAny<IRunContext>(),
      It.IsAny<Guid?>(),
      It.IsAny<Computer?>(),
      It.IsAny<int?>(),
      It.IsAny<int?>(),
      It.IsAny<bool>(),
      It.IsAny<IAsyncPolicy?>(),
      It.IsAny<Context?>(),
      It.IsAny<Guid?>(),
      It.IsAny<bool>());
  }

  public static Expression<Func<IMetascriptInvoker, Task<MetaScriptResult<object>>>> BuildRunMetaScriptExpression(
    Script? script = null)
  {
    // if script is provided, then it must have a name because that is how we match
    if (script != null)
      return metascriptInvoker => metascriptInvoker.RunMetascript<object>(
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.Is((Script s) => script.Name == s.Name),
        It.IsAny<CancellationToken>(),
        It.IsAny<TimeSpan>(),
        It.IsAny<IRunContext>(),
        It.IsAny<Guid?>(),
        It.IsAny<Computer?>(),
        It.IsAny<int?>(),
        It.IsAny<int?>(),
        It.IsAny<bool>(),
        It.IsAny<IAsyncPolicy?>(),
        It.IsAny<Context?>(),
        It.IsAny<Guid?>(),
        It.IsAny<bool>());

    return metascriptInvoker => metascriptInvoker.RunMetascript<object>(
      It.IsAny<bool>(),
      It.IsAny<bool>(),
      It.IsAny<Script>(),
      It.IsAny<CancellationToken>(),
      It.IsAny<TimeSpan>(),
      It.IsAny<IRunContext>(),
      It.IsAny<Guid?>(),
      It.IsAny<Computer?>(),
      It.IsAny<int?>(),
      It.IsAny<int?>(),
      It.IsAny<bool>(),
      It.IsAny<IAsyncPolicy?>(),
      It.IsAny<Context?>(),
      It.IsAny<Guid?>(),
      It.IsAny<bool>());
  }

  public static Expression<Func<IRunContext, Task<MetaScriptResult<object>>>> BuildRunScriptExpression(
    Script? script = null,
    bool matchScriptByActionAndScriptCategory = false)
  {
    // if script is provided, then it must have a name because that is how we match
    if (script is null)
      return ctx => ctx.RunScript(
        It.IsAny<Script>(),
        It.IsAny<int>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<Computer?>(),
        It.IsAny<ScriptContextParameters?>(),
        It.IsAny<string>(),
        It.IsAny<bool>(),
        It.IsAny<bool>());

    if (matchScriptByActionAndScriptCategory)
      return ctx => ctx.RunScript(
        It.Is<Script>(s => s.Action == script.Action && s.ScriptCategory == script.ScriptCategory),
        It.IsAny<int>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<Computer?>(),
        It.IsAny<ScriptContextParameters?>(),
        It.IsAny<string>(),
        It.IsAny<bool>(),
        It.IsAny<bool>());

    return ctx => ctx.RunScript(
      script,
      It.IsAny<int>(),
      It.IsAny<CancellationToken>(),
      It.IsAny<Computer?>(),
      It.IsAny<ScriptContextParameters?>(),
      It.IsAny<string>(),
      It.IsAny<bool>(),
      It.IsAny<bool>());
  }

  public static Expression<Func<IRunContext, Task<MetaScriptResult<T>>>> BuildRunScriptExpression<T>(
    Script? script = null,
    bool matchScriptByActionAndScriptCategory = false)
  {
    // if script is provided, then it must have a name because that is how we match
    if (script is null)
      return ctx => ctx.RunScript<T>(
        It.IsAny<Script>(),
        It.IsAny<int>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<Computer?>(),
        It.IsAny<ScriptContextParameters?>(),
        It.IsAny<string>(),
        It.IsAny<bool>(),
        It.IsAny<bool>());

    if (matchScriptByActionAndScriptCategory)
      return ctx => ctx.RunScript<T>(
        It.Is<Script>(s => s.Action == script.Action && s.ScriptCategory == script.ScriptCategory),
        It.IsAny<int>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<Computer?>(),
        It.IsAny<ScriptContextParameters?>(),
        It.IsAny<string>(),
        It.IsAny<bool>(),
        It.IsAny<bool>());

    return ctx => ctx.RunScript<T>(
      script,
      It.IsAny<int>(),
      It.IsAny<CancellationToken>(),
      It.IsAny<Computer?>(),
      It.IsAny<ScriptContextParameters?>(),
      It.IsAny<string>(),
      It.IsAny<bool>(),
      It.IsAny<bool>());
  }

  public static Expression<Func<IScriptInvoker, Task<SerializedPowerShellScriptResult>>> BuildScriptInvokerRunScriptAsyncExpression(
    Script? script = null)
  {
    // if script is provided, then it must have a name because that is how we match
    if (script != null)
      return v => v.RunScriptAsync(
        It.IsAny<Dictionary<int, IRunScriptProvider>>(),
        It.IsAny<EphemeralSessionLink>(),
        It.Is((Script s) => script.Name == s.Name),
        It.IsAny<int>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<int?>(),
        It.IsAny<PSTaskDataStreamWriter?>(),
        It.IsAny<Action<string>?>(),
        It.IsAny<ComputerCircuitBreakerPolicy>(),
        It.IsAny<int?>());
    return v => v.RunScriptAsync(
        It.IsAny<Dictionary<int, IRunScriptProvider>>(),
        It.IsAny<EphemeralSessionLink>(),
        It.IsAny<Script>(),
        It.IsAny<int>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<int?>(),
        It.IsAny<PSTaskDataStreamWriter?>(),
        It.IsAny<Action<string>?>(),
        It.IsAny<ComputerCircuitBreakerPolicy>(),
        It.IsAny<int?>());
  }



  public static IMachineSoftwareOperations MockMachineSoftwareOperations(
    MockedServices mocks,
    bool defaultOperationResult,
    string? defaultOperationString = null)
  {
    mocks.IMachineSoftwareOperations ??= new();

    mocks.IMachineSoftwareOperations.DefaultValueProvider = new ImmyOperationResultProvider(defaultOperationResult, defaultOperationString);
    mocks.IMachineSoftwareOperations
      .Setup(a => a.TestSoftware(It.IsAny<IActionRunContext>(), It.IsAny<Software>(), It.IsAny<SoftwareVersion>(), It.IsAny<TargetAssignment>(), It.IsAny<ILogPhaseHandle?>()))
      .ReturnsAsync(new ImmyOperationResult<bool>(success: true, result: true));

    return mocks.IMachineSoftwareOperations.Object;
  }

  public static IMachineOperations MockMachineOperations(MockedServices mocks)
  {
    mocks.IMachineOperations ??= new();
    return mocks.IMachineOperations.Object;
  }

  public static IMachineMaintenanceTaskOperations MockMachineTaskOperations(
    MockedServices mocks,
    bool defaultOperationResult,
    string? defaultOperationString = null)
  {
    if (mocks.IMachineMaintenanceTaskOperations == null)
    {
      mocks.IMachineMaintenanceTaskOperations = new();
      mocks.IMachineMaintenanceTaskOperations.DefaultValueProvider = new ImmyOperationResultProvider(defaultOperationResult, defaultOperationString);
    }
    return mocks.IMachineMaintenanceTaskOperations.Object;
  }

  public static IFeatureManager MockFeatureManager(
    MockedServices mocks)
  {
    mocks.IFeatureManager ??= new();
    return mocks.IFeatureManager.Object;
  }

  public static IDependencyResolver MockDependencyResolver(
    MockedServices mocks,
    IEnumerable<MaintenanceAction>? maintenanceActions = null,
    Action<IRunContext, ICollection<MaintenanceAction>>? callback = null)
  {
    if (maintenanceActions == null)
    {
      if (mocks.IDependencyResolver == null)
      {
        // by default, return what gets passed in
        (mocks.IDependencyResolver = new())
          .Setup(d => d.ResolveDependencies(It.IsAny<IStageRunContext>(), It.IsAny<ICollection<MaintenanceAction>>()))
          .Returns((IRunContext _, ICollection<MaintenanceAction> a) => Task.FromResult(a))
          .Callback((IRunContext c, ICollection<MaintenanceAction> a) => callback?.Invoke(c, a));
      }
    }
    else
    {
      ICollection<MaintenanceAction> resolved = maintenanceActions.ToList();
      (mocks.IDependencyResolver ??= new())
        .Setup(d => d.ResolveDependencies(It.IsAny<IStageRunContext>(), It.IsAny<ICollection<MaintenanceAction>>()))
        .Returns((IRunContext _, ICollection<MaintenanceAction> _) => Task.FromResult(resolved))
        .Callback((IRunContext c, ICollection<MaintenanceAction> a) => callback?.Invoke(c, a));
    }

    return mocks.IDependencyResolver.Object;
  }

  public static IImmyExecutionRunner BuildImmyExecutionRunner(MockedServices mocks)
  {
    var executionResolver = new ImmyExecutionResolver(
      Mock.Of<ILogger<ImmyExecutionResolver>>(),
      MockMachineSoftwareOperations(mocks, true),
      MockMachineTaskOperations(mocks, true),
      (mocks.IDynamicFormService ??= new()).Object,
      Mock.Of<IPendoEventManagementService>());

    return new ImmyExecutionRunner(
      MockMachineOperations(mocks),
      Mock.Of<ILogger<ImmyExecutionRunner>>(),
      executionResolver);
  }

  public static IWindowsPatchResolver MockWindowsPatchResolver(
    MockedServices mocks,
    IEnumerable<MaintenanceAction>? maintenanceActions = null)
  {
    if (maintenanceActions == null)
    {
      if (mocks.IWindowsPatchResolver == null)
      {
        ICollection<MaintenanceAction> actions = new List<MaintenanceAction>();
        (mocks.IWindowsPatchResolver = new())
          .Setup(t => t.GetMaintenanceActions(It.IsAny<IStageRunContext>()))
          .ReturnsAsync(actions);
      }
    }
    else
    {
      ICollection<MaintenanceAction> actions = maintenanceActions.ToList();
      (mocks.IWindowsPatchResolver ??= new())
          .Setup(t => t.GetMaintenanceActions(It.IsAny<IStageRunContext>()))
          .ReturnsAsync(actions);
    }
    return mocks.IWindowsPatchResolver.Object;
  }

  public static IMaintenanceActionInitializer MockMaintenanceActionResolver(MockedServices mocks)
  {
    mocks.IMaintenanceActionResolver ??= new();
    return mocks.IMaintenanceActionResolver.Object;
  }


  public static ITargetAssignmentResolver MockTargetAssignmentResolver(
    MockedServices mocks,
    IEnumerable<MaintenanceAction>? maintenanceActions = null,
    IEnumerable<TargetAssignment>? targetAssignments = null)
  {
    var mock = (mocks.ITargetAssignmentResolver ??= new());
    if (maintenanceActions != null)
      mock
        .Setup(t => t.GetMaintenanceActionsFromAssignments(It.IsAny<IStageRunContext>(), It.IsAny<List<TargetAssignment>>(), It.IsAny<bool>()))
        .Returns((IRunContext _, List<TargetAssignment> _, bool _) =>
        {
          List<(TargetAssignment, MaintenanceAction)> data = [];
          foreach (var action in maintenanceActions)
          {
            data.Add((null!, action));
          }
          return Task.FromResult(data);
        });
    if (targetAssignments != null)
      mock
        .Setup(t => t.GetTargetAssignments(It.IsAny<IStageRunContext>(), It.IsAny<MaintenanceSpecifier?>()))
        .ReturnsAsync(targetAssignments.ToList());
    return mock.Object;
  }

  public static IMaintenanceTaskActions MockMaintenanceTaskActions(
    MockedServices mocks,
    IEnumerable<MaintenanceTask> tasksInDb)
  {
    // mock GetMaintenanceTask method
    (mocks.IMaintenanceTaskActions ??= new())
     .Setup(a => a.GetMaintenanceTask(
       It.IsAny<MaintenanceType>(),
       It.IsAny<int>(),
       It.IsAny<IAsyncPolicy>(),
       It.IsAny<Context>(),
       It.IsAny<CancellationToken>()))
     .Returns((MaintenanceType _, int i, IAsyncPolicy _, Context _, CancellationToken _) =>
        Task.FromResult(tasksInDb.FirstOrDefault(s => s.Id == i)));

    return mocks.IMaintenanceTaskActions.Object;
  }

  public static ISoftwareActions MockSoftwareActions(
    MockedServices mocks,
    IEnumerable<Software> softwaresInDb,
    IEnumerable<Script>? scriptsInDb = null)
  {
    // Mock the ISoftwareActions#GetSoftware method to return items from the `softwaresInDb` list
    (mocks.ISoftwareActions ??= new())
      .Setup(sa => sa.GetSoftware(It.IsAny<SoftwareType>(), It.IsAny<string>(), It.IsAny<CancellationToken>(), It.IsAny<Context>(), It.IsAny<IAsyncPolicy>()))
      .Returns((SoftwareType t, string identifier, CancellationToken _, Context _, IAsyncPolicy _) => Task.FromResult(
        softwaresInDb
        .FirstOrDefault(s => s.SoftwareType == t && s.Identifier == identifier)));

    // Mock the ISoftwareActions#GetLatestSoftwareVersion method to return items from the `softwaresInDb` list
    mocks.ISoftwareActions
      .Setup(sa => sa.GetLatestSoftwareVersion(It.IsAny<SoftwareType>(),
        It.IsAny<string>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<SemanticVersion?>(),
        It.IsAny<Context>(),
        It.IsAny<IAsyncPolicy>()))
      .Returns((SoftwareType t,
        string i,
        CancellationToken _,
        SemanticVersion? v,
        Context _,
        IAsyncPolicy _) => Task.FromResult(
        softwaresInDb
        .FirstOrDefault(s => s.SoftwareType == t && s.Identifier == i)
        ?.GetSoftwareVersions()
        .OrderBy(vv => vv.SemanticVersion)
        // if atOrBelow was provided, make sure we return the latest that's less than or equal to that
        .LastOrDefault(vv => v == null || vv.SemanticVersion <= v)));

    mocks.ISoftwareActions
      .Setup(sa => sa.GetSoftwareVersion(It.IsAny<SoftwareType>(),
        It.IsAny<string>(),
        It.IsAny<SemanticVersion>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<bool>(),
        It.IsAny<Context>(),
        It.IsAny<IAsyncPolicy>()))
      .Returns((SoftwareType t,
        string i,
        SemanticVersion v,
        CancellationToken _,
        bool _,
        Context _,
        IAsyncPolicy _) => Task.FromResult(
        softwaresInDb
        .FirstOrDefault(s => s.SoftwareType == t && s.Identifier == i)
        ?.GetSoftwareVersions()
        .FirstOrDefault(vv => vv.SemanticVersion == v)
      ));

    mocks.ISoftwareActions
      .Setup(sa => sa.GetScript(It.IsAny<DatabaseType>(), It.IsAny<string>(), It.IsAny<Context>(), It.IsAny<IAsyncPolicy?>(), It.IsAny<CancellationToken>()))
      .Returns((DatabaseType t, string i, Context _, IAsyncPolicy _, CancellationToken _) =>
      {
        return Task.FromResult(scriptsInDb?.FirstOrDefault(s => s.ScriptType == t && s.Identifier == i));
      });

    return mocks.ISoftwareActions.Object;
  }

  public static IImmyCancellationManager MockImmyCancellationManager(
    this MockedServices mocks,
    (Guid, CancellationToken)? existingCancellationId = null)
  {
    var mock = mocks.IImmyCancellationManager ??= new();
    if (existingCancellationId is var (id, token))
    {
      mock.Setup(h => h.GetOrCreateScriptCancellationToken(id)).Returns(token);
    }
    MockServiceScopeFactoryService(mocks, mocks.IImmyCancellationManager.Object);
    return mocks.IImmyCancellationManager.Object;
  }

  public static IActionRunContext MockActionRunContext(
    this MockedServices mocks,
    MaintenanceAction? action = null,
    MaintenanceTask? maintenanceTask = null,
    Software? desiredSoftware = null,
    SoftwareVersion? desiredSoftwareVersion = null,
    bool isComputerTarget = false)
  {
    mocks.IActionRunContext = new();
    mocks.IActionRunContext
      .Setup(a => a.GetMaintenanceTaskParameterValues())
      .ReturnsAsync([]);
    mocks.IActionRunContext.SetupGet(a => a.Action).Returns(action ?? new() { MaintenanceIdentifier = "test", });
    mocks.IActionRunContext.SetupGet(a => a.DesiredSoftware).Returns(desiredSoftware);
    mocks.IActionRunContext.SetupGet(a => a.DesiredSoftwareVersion)
      .Returns(desiredSoftwareVersion);
    mocks.IActionRunContext.Setup(a => a.GetMaintenanceTask()).ReturnsAsync(maintenanceTask);

    var phaseHandleMock = new Mock<ILogPhaseHandle>();
    phaseHandleMock.SetupGet(h => h.Phase).Returns(new SessionPhase());
    mocks.IActionRunContext.Setup(c => c.BeginNewActionPhase(It.IsAny<ActionProgressPhaseName>()))
      .ReturnsAsync(phaseHandleMock.Object);
    SetupStageRunContextMock(mocks, mocks.IActionRunContext.As<IStageRunContext>(), isComputerTarget);

    return mocks.IActionRunContext.Object;
  }

  public static IStageRunContext MockStageRunContext(
    this MockedServices mocks,
    MaintenanceSessionStage? stage = null,
    bool isComputerTarget = false)
  {
    mocks.IStageRunContext = new();
    if (stage is not null)
    {
      mocks.IStageRunContext.SetupGet(a => a.Stage).Returns(stage);
    }

    mocks.IStageRunContext.SetupGet(a => a.ProviderInfo).Returns([]);
    SetupStageRunContextMock(mocks, mocks.IStageRunContext, isComputerTarget: isComputerTarget);

    return mocks.IStageRunContext.Object;
  }

  private static void SetupStageRunContextMock(
    MockedServices mocks,
    Mock<IStageRunContext> runContextMock,
    bool isComputerTarget)
  {
    var phaseHandleMock = new Mock<ILogPhaseHandle>();
    phaseHandleMock.SetupGet(h => h.Phase).Returns(new SessionPhase());
    runContextMock
      .Setup(c => c.BeginNewStagePhase(It.IsAny<string>()))
      .ReturnsAsync(phaseHandleMock.Object);
    SetupRunContextMock(mocks, runContextMock.As<IRunContext>(), isComputerTarget);
  }

  private static void SetupRunContextMock(
    MockedServices mocks,
    Mock<IRunContext> runContextMock,
    bool isComputerTarget)
  {
    runContextMock.SetupGet(a => a.Args).Returns(mocks.MockRunContextArgs());
    runContextMock.SetupGet(a => a.ApplicationPreferences).Returns(MockAppPrefsCache(mocks).Value);
    runContextMock.SetupGet(a => a.IsComputerTarget).Returns(isComputerTarget);
    runContextMock.As<IRunContext>().Setup(BuildRunScriptExpression())
      .ReturnsAsync(new MetaScriptResult<object>(null, null, null));
    runContextMock.As<IRunContext>().Setup(BuildRunScriptExpression<bool>())
      .ReturnsAsync(new MetaScriptResult<bool>(null, null, null));
  }

  public static IActionRunContext BuildActionRunContext(
    MockedServices mocks,
    MaintenanceSession? session = null,
    MaintenanceSessionStage? stage = null,
    MaintenanceAction? action = null,
    Computer? computer = null,
    IMetascriptInvoker? metascriptInvoker = null,
    bool hasChocoInstalled = true,
    bool hasNiniteInstalled = true,
    bool isRepair = false,
    CancellationToken stopProcessing = default,
    Func<ImmybotDbContext>? ctxFactory = null)
  {
    if (mocks.RunContext == null)
    {
      if (session == null)
      {
        session = new MaintenanceSession();
      }
      if (stage == null)
      {
        stage = new MaintenanceSessionStage();
      }
      session.Stages.Add(stage);
      if (action == null)
      {
        action = new MaintenanceAction() { MaintenanceIdentifier = "test", };
      }
      session.MaintenanceActions.Add(action);
      var args = MockRunContextArgs(mocks: mocks,
        computer ?? MakeComputer(),
        stopProcessing: stopProcessing,
        metascriptInvoker: metascriptInvoker,
        repair: isRepair);
      mocks.RunContext = new ActionRunContext(session, stage, action, args);
    }
    if (mocks.RunContext is not IActionRunContext s) throw new NotImplementedException();
    s.Args.Fields.HasChocolatey = hasChocoInstalled;
    s.Args.Fields.HasNiniteOneExe = hasNiniteInstalled;
    return s;
  }

  public static IStageRunContext BuildStageRunContext(
    MockedServices mocks,
    MaintenanceSession? session = null,
    MaintenanceSessionStage? stage = null,
    Computer? computer = null,
    Tenant? tenant = null,
    IMetascriptInvoker? metascriptInvoker = null,
    MaintenanceItem? maintenanceItem = null,
    bool hasChocoInstalled = true,
    bool hasNiniteInstalled = true,
    bool installWindowsUpdates = true,
    bool sendAllEmails = false,
    bool cacheOnly = false,
    bool repair = false,
    CancellationToken stopProcessing = default)
  {
    // When send all emails is set to true the context needs to be recreated with those set to true
    if (sendAllEmails) mocks.RunContext = null;
    if (mocks.RunContext == null)
    {
      session ??= new MaintenanceSession();
      stage ??= new MaintenanceSessionStage();
      session.Stages.Add(stage);
      if (computer != null)
      {
        tenant = computer.OwnerTenant;
      }
      else if (tenant == null)
      {
        computer = MakeComputer();
        tenant = computer.OwnerTenant;
      }
      if (mocks.IPowershellLoader is null) BuildPowershellLoader(mocks);
      //
      var psScript = mocks.IPowershellLoader!.Object.GetPowerShellScript("Get-PowerShellVersion", null);
      if (metascriptInvoker == null)
      {
        mocks.IMetascriptInvoker ??= new();
        mocks.IMetascriptInvoker
          .Setup(BuildRunMetaScriptExpression(psScript))
          .ReturnsAsync(new MetaScriptResult<object>([], [], null));
      }
      else
      {
        // If an actual MetascriptInvoker is used, make IScriptInvoker return something non-null
        // when Get-PowerShellVersion script is ran because Invoke-ImmyCommand will call it when
        // the run context attempts to test script execution
        MockIScriptInvoker_RunScript(mocks, script: psScript);
      }

      mocks.IEphemeralAgentSession ??= new();
      mocks.IEphemeralAgentSession.SetupGet(a => a.IsConnected).Returns(true);
      mocks.IEphemeralAgentAcquisition ??= new();
      mocks.IEphemeralAgentAcquisition
        .SetupAcquireEphemeralAgentWithRetryAsync()
        .ReturnsAsync(mocks.IEphemeralAgentSession.Object);

      mocks.IScriptInvoker ??= new();
      mocks.IScriptInvoker.SetupRunScriptAsync()
        .ReturnsAsync(new SerializedPowerShellScriptResult());

      var args = MockRunContextArgs(mocks,
        computer: computer,
        tenant: tenant,
        installWindowsUpdates: installWindowsUpdates,
        sendAllEmails: sendAllEmails,
        cacheOnly: cacheOnly,
        repair: repair,
        metascriptInvoker: metascriptInvoker,
        maintenanceItem: maintenanceItem,
        stopProcessing: stopProcessing);

      mocks.RunContext = new StageRunContext(session, stage, args);
    }

    if (mocks.RunContext is not IStageRunContext s) throw new NotImplementedException();
    s.Args.Fields.HasChocolatey = hasChocoInstalled;
    s.Args.Fields.HasNiniteOneExe = hasNiniteInstalled;
    return s;
  }

  public static ISessionRunContext BuildSessionRunContext(
    MockedServices mocks,
    MaintenanceSession? session = null,
    Computer? computer = null,
    bool hasChocoInstalled = true,
    bool hasNiniteInstalled = true,
    bool includeOnboardingStage = true,
    bool includeDetectionStage = true,
    bool includeExecutionStage = true,
    bool includeAgentUpdatesStage = true,
    bool includeInventoryStage = false,
    bool rerunningAction = false,
    IRunScriptProvider? preferredProvider = null,
    CancellationToken stopProcessing = default,
    Func<ImmybotDbContext>? ctxFactory = null,
    SessionJobArgs? sessionJobArgs = null)
  {
    if (mocks.RunContext == null)
    {
      if (session == null)
      {
        session = new MaintenanceSession();
        session.JobArgs.RerunningAction = rerunningAction;
        session.MaintenanceActions.Add(new MaintenanceAction() { MaintenanceIdentifier = "test", });
        if (includeOnboardingStage) session.Stages.Add(new() { Type = SessionStageType.Onboarding });
        if (includeAgentUpdatesStage) session.Stages.Add(new() { Type = SessionStageType.AgentUpdates });
        if (includeDetectionStage)
        {
          session.Stages.Add(new() { Type = SessionStageType.Resolution });
          session.Stages.Add(new() { Type = SessionStageType.Detection });
        }
        if (includeExecutionStage) session.Stages.Add(new() { Type = SessionStageType.Execution });
        if (includeInventoryStage) session.Stages.Add(new() { Type = SessionStageType.Inventory });
        mocks.Session = session;
      }
      var args = mocks.IRunContextArgsMock?.Object ?? MockRunContextArgs(mocks,
        computer: computer ?? MakeComputer(),
        rerunningAction: rerunningAction,
        stopProcessing: stopProcessing);

      var sja = sessionJobArgs ?? session.JobArgs;
      RunContextFactory.PopulateRunContextArgsFromSessionJobArgsForSessionRunContext(
        sja,
        args,
        args.Computer,
        args.Person,
        args.Tenant,
        args.ManuallyTriggeredBy,
        args.CachePolicy,
        args.PolicyContext,
        args.StopProcessing);
      session.JobArgs = sja;

      mocks.RunContext = new SessionRunContext(session, args);

      // mock objects
      mocks.IRunContextMock = new();
      mocks.IRunContextMock.As<ISessionRunContext>().Setup(b => b.Session).Returns(session);
      mocks.IRunContextMock.Setup(b => b.Args).Returns(args);
    }
    if (mocks.RunContext is not ISessionRunContext s) throw new NotImplementedException();
    s.Args.Fields.HasChocolatey = hasChocoInstalled;
    s.Args.Fields.HasNiniteOneExe = hasNiniteInstalled;
    return s;
  }

  public static IRunContextFactory MockRunContextFactory(
    MockedServices mocks,
    MaintenanceSession? session = null)
  {
    if (mocks.IRunContextFactory == null)
    {
      (mocks.IRunContextFactory = new())
        .Setup(fac => fac.GenerateSessionRunContext(It.IsAny<int>(), It.IsAny<int?>(), It.IsAny<CancellationToken>(), It.IsAny<bool>()))
        .Returns((int _, int? _, CancellationToken _, bool _) =>
        {
          var sessionRunContext = BuildSessionRunContext(mocks, session);
          return Task.FromResult(sessionRunContext);
        });
    }

    return mocks.IRunContextFactory.Object;
  }

  public static IMachineSoftwareOperations MockSoftwareOperations(
    MockedServices mocks,
    Dictionary<Software, SemanticVersion>? softwareVersionsOnMachine = null)
  {
    if (mocks.IMachineSoftwareOperations == null)
    {
      mocks.IMachineSoftwareOperations = new();
      // Mock the IMachineSoftwareOperations#DetectSoftwareVersion method to return true if the provided software is in the `softwareVersionsOnMachine` list
      mocks.IMachineSoftwareOperations
        .Setup(so => so.DetectSoftwareVersion(It.IsAny<IStageRunContext>(), It.IsAny<Software>(), It.IsAny<bool>(), It.IsAny<TargetAssignment>()))
        .Returns((IRunContext _, Software s, bool _, TargetAssignment _) =>
        {
          if (softwareVersionsOnMachine != null && softwareVersionsOnMachine.TryGetValue(s, out var detected)) return Task.FromResult(new ImmyOperationResult<SemanticVersion?>(true, null, detected));
          return Task.FromResult(new ImmyOperationResult<SemanticVersion?>(true));
        });
    }
    return mocks.IMachineSoftwareOperations.Object;
  }

  public static IImmyDetectionResolver MockImmyDetectionResolver(
    MockedServices mocks)
  {
    if (mocks.IImmyDetectionResolver == null)
    {
      mocks.IImmyDetectionResolver = new();
    }
    return mocks.IImmyDetectionResolver.Object;
  }

  public static IImmyExecutionResolver MockImmyExecutionResolver(
    MockedServices mocks)
  {
    if (mocks.IImmyExecutionResolver == null)
    {
      mocks.IImmyExecutionResolver = new();
    }
    return mocks.IImmyExecutionResolver.Object;
  }

  public static IUpdateAzureTenantLinkCmd MockUpdateAzureTenantLinkCmd()
  {
    return new Mock<IUpdateAzureTenantLinkCmd>().Object;
  }

  public static ICachedCollection<TenantPreferences> MockTenantPrefsCache(MockedServices mocks)
  {
    if (mocks.ITenantPrefsCache == null)
    {
      var mock = new Mock<ICachedCollection<TenantPreferences>>();
      mocks.ITenantPrefsCache = mock;

      mock.SetupGet(c => c.Value).Returns(new List<TenantPreferences>());
    }

    return mocks.ITenantPrefsCache.Object;
  }

  /// <summary>
  /// Returns an <see cref="IHostApplicationLifetime"/> where the application has already started
  /// and will stop 3 seconds after calling <see cref="IHostApplicationLifetime.StopApplication"/>.
  /// </summary>
  /// <returns></returns>
  public static Mock<IHostApplicationLifetime> MockHostApplicationLifetime()
  {
    return MockHostApplicationLifetime(TimeSpan.Zero, TimeSpan.FromSeconds(3));
  }

  public static Mock<IHostApplicationLifetime> MockHostApplicationLifetime(
    TimeSpan applicationStartDelay,
    TimeSpan applicationStopDelay)
  {

    var mock = new Mock<IHostApplicationLifetime>();
    var startCts = new CancellationTokenSource(applicationStartDelay);
    mock.SetupGet(a => a.ApplicationStarted).Returns(startCts.Token);

    var stoppingCts = new CancellationTokenSource();
    mock.SetupGet(a => a.ApplicationStopping).Returns(stoppingCts.Token);

    var stopCts = new CancellationTokenSource();
    stopCts.Token.Register(() =>
    {
      startCts.Dispose();
      stoppingCts.Dispose();
      stopCts.Dispose();
    });

    mock.SetupGet(a => a.ApplicationStopped).Returns(stopCts.Token);

    mock
      .Setup(a => a.StopApplication())
      .Callback(() =>
      {
        stoppingCts.Cancel();
        stopCts.CancelAfter(applicationStopDelay);
      });

    return mock;
  }

  public static IMetrics MockMetrics(MockedServices mocks)
  {
    var metrics = mocks.IMetrics ?? new();
    var measureMetricsGauage = new Mock<IMeasureGaugeMetrics>();

    measureMetricsGauage.Setup(a => a.SetValue(It.IsAny<GaugeOptions>(), It.IsAny<double>()));
    var measureCounterMetrics = new Mock<IMeasureCounterMetrics>();

    measureCounterMetrics.Setup(a => a.Increment(It.IsAny<CounterOptions>(), It.IsAny<long>()));

    var measureMeterMetrics = new Mock<IMeasureMeterMetrics>();
    measureMeterMetrics.Setup(a => a.Mark(It.IsAny<MeterOptions>()));

    var measureHistogramMetrics = new Mock<IMeasureHistogramMetrics>();
    measureHistogramMetrics.Setup(a => a.Update(It.IsAny<HistogramOptions>(), It.IsAny<long>()));

    var measureMetrics = new Mock<IMeasureMetrics>();
    measureMetrics.SetupGet(a => a.Gauge).Returns(measureMetricsGauage.Object);
    measureMetrics.SetupGet(a => a.Counter).Returns(measureCounterMetrics.Object);
    measureMetrics.SetupGet(a => a.Meter).Returns(measureMeterMetrics.Object);
    measureMetrics.SetupGet(a => a.Histogram).Returns(measureHistogramMetrics.Object);
    metrics.SetupGet(a => a.Measure).Returns(measureMetrics.Object);

    return metrics.Object;
  }

  public static ICachedSingleton<ApplicationPreferences> MockAppPrefsCache(
    MockedServices mocks,
    DefaultScriptTimeouts? defaultScriptTimeouts = null,
    bool? enableNiniteIntegration = null,
    bool enablePreflightScripts = false)
  {
    if (mocks.IAppPrefsCache == null)
    {
      var mock = new Mock<ICachedSingleton<ApplicationPreferences>>();
      defaultScriptTimeouts ??= new DefaultScriptTimeouts
      {
        Action = 100,
        Detection = 200,
        Install = 300,
        Uninstall = 400,
        Upgrade = 500,
        DynamicVersions = 120,
      };
      mock.SetupGet(c => c.Value).Returns(new ApplicationPreferences
      {
        DefaultScriptTimeouts = defaultScriptTimeouts,
        EnableNiniteIntegration = enableNiniteIntegration ?? false,
        EnablePreflightScripts = enablePreflightScripts,
      });
      mocks.IAppPrefsCache = mock;
    }
    else
    {
      if (defaultScriptTimeouts != null)
        mocks.IAppPrefsCache.Object.Value.DefaultScriptTimeouts = defaultScriptTimeouts;
      if (enableNiniteIntegration != null)
        mocks.IAppPrefsCache.Object.Value.EnableNiniteIntegration = enableNiniteIntegration.Value;
    }
    return mocks.IAppPrefsCache.Object;
  }

  // Creates an execution stage runner that simply updates the stage status
  public static IImmyExecutionStageRunner MockImmyExecution(
    MockedServices mocks,
    ImmyOperationResult? result = null,
    Action<IStageRunContext>? callback = null)
  {
    if (mocks.IImmyExecution == null)
    {
      var mock = mocks.IImmyExecution = new();
      mock.Setup(o => o.RunExecution(It.IsAny<IStageRunContext>()))
        .ReturnsAsync(result ?? new ImmyOperationResult(true))
        .Callback(callback ?? ((ctx) =>
        {
          ctx.UpdateStage(stageStatus: SessionStatus.Passed).Forget();
        }));
    }
    return mocks.IImmyExecution.Object;
  }

  // Creates an inventory stage runner that simply updates the stage status
  public static IImmyInventoryStageRunner MockImmyInventory(
    MockedServices mocks,
    OpResult? result = null,
    Action<IStageRunContext>? callback = null)
  {
    if (mocks.IImmyInventory == null)
    {
      var mock = mocks.IImmyInventory = new();
      mock.Setup(o => o.RunInventory(It.IsAny<IStageRunContext>()))
        .ReturnsAsync(result ?? OpResult.Ok())
        .Callback(callback ?? ((ctx) =>
        {
          ctx.UpdateStage(stageStatus: SessionStatus.Passed).Forget();
        }));
    }
    return mocks.IImmyInventory.Object;
  }

  // creates a detection stage runner that simply updates the stage status
  public static IImmyDetectionStageRunner MockImmyDetection(
    MockedServices mocks,
    ImmyOperationResult? result = null,
    Action<IStageRunContext, List<(TargetAssignment, MaintenanceAction)>>? callback = null)
  {
    if (mocks.IImmyDetection == null)
    {
      var mock = mocks.IImmyDetection = new();
      mock.Setup(o => o.RunDetection(It.IsAny<IStageRunContext>(), It.IsAny<List<(TargetAssignment, MaintenanceAction)>>()))
        .Returns(Task.FromResult(result ?? new ImmyOperationResult(true)))
        .Callback(callback ?? ((ctx, _) =>
        {
          ctx.UpdateStage(stageStatus: SessionStatus.Passed).Forget();
        }));
    }
    return mocks.IImmyDetection.Object;
  }
  public static IImmyAgentUpdatesStageRunner MockImmyAgentUpdates(
    MockedServices mocks,
    ImmyOperationResult? result = null,
    Action<IStageRunContext>? callback = null)
  {
    if (mocks.IImmyAgentUpdates == null)
    {
      var mock = mocks.IImmyAgentUpdates = new();
      mock.Setup(o => o.RunAgentUpdates(It.IsAny<IStageRunContext>()))
        .Returns(Task.FromResult(result ?? new ImmyOperationResult(true)))
        .Callback(callback ?? ((ctx) =>
        {
          ctx.UpdateStage(stageStatus: SessionStatus.Passed).Forget();
        }));
    }
    return mocks.IImmyAgentUpdates.Object;
  }

  public static IImmyResolutionStageRunner MockImmyResolution(
    MockedServices mocks,
    ImmyOperationResult<List<(TargetAssignment, MaintenanceAction)>>? result = null,
    Action<IStageRunContext>? callback = null)
  {
    if (mocks.IImmyResolution == null)
    {
      var mock = mocks.IImmyResolution = new();
      mock.Setup(o => o.RunResolution(It.IsAny<IStageRunContext>()))
        .Returns(Task.FromResult(result ?? new ImmyOperationResult<List<(TargetAssignment, MaintenanceAction)>>(true, result: [])))
        .Callback(callback ?? ((ctx) =>
        {
          ctx.UpdateStage(stageStatus: SessionStatus.Passed).Forget();
        }));
    }
    return mocks.IImmyResolution.Object;
  }

  // creates an onboarding stage runner that simply updates the stage status
  public static IImmyOnboardingStageRunner MockImmyOnboarding(
    MockedServices mocks,
    ImmyOperationResult? result = null,
    Action<IStageRunContext>? callback = null)
  {
    if (mocks.IImmyOnboarding == null)
    {
      var mock = mocks.IImmyOnboarding = new();
      mock.Setup(o => o.RunOnboarding(It.IsAny<IStageRunContext>()))
        .Returns(Task.FromResult(result ?? new ImmyOperationResult(true)))
        .Callback(callback ?? ((ctx) =>
        {
          ctx.UpdateStage(stageStatus: SessionStatus.Passed).Forget();
        }));
    }
    return mocks.IImmyOnboarding.Object;
  }

  // Mocks the IProviderActions interface to return a non-null value from GetProvider
  public static IProviderActions MockProviderActions(
    MockedServices mocks,
    IProvider? getProviderResult = null)
  {
    if (mocks.IProviderActions == null)
    {
      var mock = mocks.IProviderActions = new();
      var providerMock = new Mock<IProvider>();
      var runScriptProvider = providerMock.As<IRunScriptProvider>();
      getProviderResult ??= providerMock.Object;
      mock
        .Setup(a => a.GetProvider(It.IsAny<ProviderLink>(), It.IsAny<CancellationToken>(), It.IsAny<TimeSpan>(), It.IsAny<bool>(), It.IsAny<bool>()))
        .ReturnsAsync(getProviderResult);
      mock
        .Setup(a => a.GetRunScriptProvider(It.IsAny<ProviderLink>(), It.IsAny<CancellationToken>(), It.IsAny<TimeSpan>()))
        .ReturnsAsync(runScriptProvider.Object);
    }
    return mocks.IProviderActions.Object;
  }

  public static Mock<IInvocation> MockDynamicProviderInvocation(string _methodName, object[] _methodArgs)
  {
    // This function doesn't setup any callbacks on the invocation

    // Get all the interfaces supported by dynamic integrations
    var allInterfaces = new DynamicProviderCapabilitiesSet().GetValidValues().ToList();
    var allowedInterfaces = Assembly.Load("Immybot.Backend.Providers.Interfaces").GetTypes().Where(t => allInterfaces.Contains(t.Name)).ToList();

    MethodInfo foundMethod()
    {
      foreach (var allowedInterface in allowedInterfaces)
      {
        // Get all the methods that are allowed on the interface
        foreach (var method in allowedInterface.GetMethods())
        {
          if (method.Name == _methodName) return method;
        }
      }
      throw new Exception($"Could not find method {_methodName} on any of the allowed interfaces");
    }
    var methodInfo = foundMethod();

    // Generate some arguments for the method
    var invocation = new Mock<IInvocation>();
    invocation.SetupGet(a => a.Method.Name).Returns(methodInfo.Name);
    invocation.SetupGet(a => a.Arguments).Returns(_methodArgs);
    invocation.Setup(a => a.Method.GetParameters()).Returns(methodInfo.GetParameters());
    invocation.SetupGet(a => a.Method.ReturnType).Returns(methodInfo.ReturnType);

    return invocation;
  }

  public static void MockIEphemeralAgentAcquisition(MockedServices mocks, IEphemeralAgentSession agent)
  {
    mocks.IEphemeralAgentAcquisition ??= new();
    mocks.IEphemeralAgentAcquisition
     .SetupAcquireEphemeralAgentWithRetryAsync()
     .ReturnsAsync(agent);
  }

  public static KeyedLocker SetupKeyedLocker()
  {
    var sc = new ServiceCollection();
    sc.AddMessagePipe();
    var sp = sc.BuildServiceProvider();
    var eventFactory = sp.GetRequiredService<EventFactory>();
    var locker = new KeyedLocker(eventFactory);
    return locker;
  }

  public static RunContextArgs MockRunContextArgs(
    this MockedServices mocks,
    Computer? computer = null,
    Tenant? tenant = null,
    AuthUserDto? manuallyTriggeredBy = null,
    int? scheduleId = null,
    MaintenanceOnboardingConfiguration? maintenanceOnboardingConfiguration = null,
    IMetascriptInvoker? metascriptInvoker = null,
    ITargetAssignmentActions? targetAssignmentActions = null,
    IFeatureManager? featureManager = null,
    MaintenanceItem? maintenanceItem = null,
    bool installWindowsUpdates = false,
    bool sendAllEmails = false,
    bool rerunningAction = false,
    bool onlyResolveOnboardingAssignments = false,
    bool cacheOnly = false,
    bool repair = false,
    CancellationToken stopProcessing = default)
  {
    if (mocks.RunContextArgs == null)
    {
      var args = new RunContextArgs(
        (mocks.IFindScriptByNameCmd ??= new()).Object,
        (mocks.ISoftwareActions ??= new()).Object,
        (mocks.IScriptActions ??= new()).Object,
        MockScriptInvoker(mocks),
        (mocks.IEphemeralAgentAcquisition ??= new()).Object,
        (mocks.IPreflightScriptInvoker ??= new()).Object,
        MockAppPrefsCache(mocks),
        MockTenantPrefsCache(mocks),
        MockProviderActions(mocks),
        (mocks.IRunContextActionsList ??= new()).Object,
        (mocks.IMaintenanceTaskActions ??= new()).Object,
        targetAssignmentActions ?? (mocks.ITargetAssignmentActions ??= new()).Object,
        (mocks.IMediaActions ??= new()).Object,
        metascriptInvoker ?? (mocks.IMetascriptInvoker ??= new()).Object,
        mocks.NiniteSoftwareCache ??= new(),
        (mocks.IDomainEventEmitter ??= new()).Object,
        (mocks.IDomainEventReceiver ??= new()).Object,
        (mocks.IPowershellLoader ??= new()).Object,
        (mocks.IRunContextComputerActions ??= new()).Object,
        (mocks.TargetPopulator ??= new()).Object,
        (mocks.IEphemeralAgentSessionHandler ??= new()).Object,
        (mocks.IDynamicFormService ??= new()).Object,
        featureManager ?? (mocks.IFeatureManager ??= new()).Object,
        (mocks.IComputerMaintenanceDateOperations ??= new()).Object,
        (mocks.IMaintenanceActionEventHubService ??= new()).Object,
        (mocks.ISystemTime ??= new()).Object,
        (mocks.ILicenseActions ??= new()).Object,
        (mocks.ITenantActions ??= new()).Object,
        (mocks.IPersonActions ??= new()).Object,
        (mocks.IProviderLinkActions ??= new()).Object,
        (mocks.IMaintenanceItemOrderActions ??= new()).Object,
        (mocks.IScheduleActions ??= new()).Object,
        (mocks.IComputerInventoryActions ??= new()).Object,
        (mocks.IMaintenanceActionActions ??= new()).Object,
        SetupKeyedLocker())
      {
        PolicyContext = (mocks.PolicyContext ??= []),
        CachePolicy = (mocks.CachePolicy ??= Policy.NoOpAsync()),
        InstallWindowsUpdates = installWindowsUpdates,
        MaintenanceEmailConfiguration = new MaintenanceEmailConfiguration
        {
          SendDetectionEmail = sendAllEmails,
          SendFollowUpEmail = sendAllEmails,
          SendFollowUpOnlyIfActionNeeded = false,
        },
        MaintenanceOnboardingConfiguration =
          maintenanceOnboardingConfiguration ?? new MaintenanceOnboardingConfiguration(),
        Computer = computer,
        Tenant = tenant ?? computer?.OwnerTenant ?? new Tenant { Name = "test" },
        Fields = new(),
        RerunningAction = rerunningAction,
        StopProcessing = stopProcessing,
        OnlyResolveOnboardingAssignments = onlyResolveOnboardingAssignments,
        CacheOnly = cacheOnly,
        Repair = repair,
        ScheduleId = scheduleId,
        MaintenanceItem = maintenanceItem,
        ManuallyTriggeredBy = manuallyTriggeredBy,
      };
      mocks.RunContextArgs = args;

      mocks.TargetPopulator
        !.Setup(a => a.Populate(It.IsAny<IQueryable<PopulatedTargetAssignment>>(), It.IsAny<CancellationToken>()))
        .ReturnsAsync(new List<PopulatedTargetAssignment>()
        {
          new PopulatedTargetAssignment(new TargetAssignment() { MaintenanceIdentifier = "1", })
        });


      if (computer != null)
      {
        mocks.IRunContextComputerActions.Setup(a => a.GetComputerById(computer.Id)).Returns(computer);
      }
    }
    return mocks.RunContextArgs;
  }
  #endregion Mocks

  #region Service Constructors
  public static (DependencyResolver, MockedServices) BuildDependencyResolver(
    IEnumerable<Software> softwaresInDb,
    Dictionary<Software, SemanticVersion> softwareVersionsOnMachine,
    List<MaintenanceTask>? tasks = null)
  {
    var mocks = new MockedServices();
    var softwareOperations = MockSoftwareOperations(mocks, softwareVersionsOnMachine);
    var maintenanceTaskOperations = MockMaintenanceTaskActions(mocks, tasks ?? []);
    mocks.IMachineMaintenanceTaskOperations = new();
    var maintenanceActionResolver = new MaintenanceActionInitializer();
    var detectionResolver = new ImmyDetectionResolver(
      maintenanceActionResolver,
      mocks.IMachineMaintenanceTaskOperations.Object,
      maintenanceTaskOperations,
      softwareOperations);

    MockSoftwareActions(mocks, softwaresInDb);
    BuildStageRunContext(mocks);

    return (
      new DependencyResolver(softwareOperations, maintenanceActionResolver, detectionResolver),
      mocks
    );
  }

  public static void BuildPowershellLoader(MockedServices mocks)
  {
    mocks.IPowershellLoader ??= new();
    mocks.IPowershellLoader.Setup(ps =>
        ps.GetPowerShellScript(It.IsAny<string>(), It.IsAny<int?>(), It.IsAny<Dictionary<string, object?>>()))
      .Returns(new Script { Name = "", Action = "$PSVersionTable" });
  }

  public static (ImmyService, MockedServices) BuildImmyService(
    MockedServices? mocks = null,
    MaintenanceSession? session = null,
    bool mockRunResolutionToReturnData = true)
  {
    mocks ??= new MockedServices();

    ImmyOperationResult<List<(TargetAssignment, MaintenanceAction)>>? result = null;

    if (mockRunResolutionToReturnData)
    {
      result = new ImmyOperationResult<List<(TargetAssignment, MaintenanceAction)>>(true, result:
      [
        new(new() { MaintenanceIdentifier = "1", },
          new() { MaintenanceIdentifier = "2", ActionResult = MaintenanceActionResult.Success })
      ]);
    }

    var immyService = new ImmyService(
      MockRunContextFactory(mocks, session),
      MockImmyOnboarding(mocks),
      MockImmyDetection(mocks),
      MockImmyAgentUpdates(mocks),
      MockImmyResolution(mocks, result: result),
      MockImmyExecution(mocks),
      MockImmyInventory(mocks),
      (mocks.IImmyServiceJob ??= new()).Object,
      (mocks.IImmyCancellationManager ??= new()).Object,
      (mocks.IImmyEmail ??= new()).Object,
      (mocks.IManagerProvidedSetings ??= new()).Object,
      (mocks.IMaintenanceSessionPendingConnectivityHandler ??= new()).Object,
      new ComputerMaintenanceDateOperations(TimeProvider.System),
      (mocks.IHostApplicationLifetime ??= new()).Object,
      (mocks.ISessionStageActions ??= new()).Object,
      Mock.Of<IPendoEventManagementService>(),
      Mock.Of<ILogger<ImmyService>>());
    return (immyService, mocks);
  }

  public static (ImmyDetectionStageRunner, MockedServices) BuildDetectionStageRunner(
    MockedServices? mocks = null,
    IMachineMaintenanceTaskOperations? maintTaskOperations = null,
    IImmyDetectionResolver? immyDetectionResolver = null)
  {
    mocks ??= new MockedServices();
    var softwareOperations = MockMachineSoftwareOperations(mocks, true);
    maintTaskOperations ??= MockMachineTaskOperations(mocks, true);
    var maintenanceTaskActions = MockMaintenanceTaskActions(mocks, Array.Empty<MaintenanceTask>());

    // Detection Resolver - resolves a single action's detection
    immyDetectionResolver ??= new ImmyDetectionResolver(
      BuildMaintenanceActionInitializer(mocks).Item1,
      maintTaskOperations,
      maintenanceTaskActions,
      softwareOperations);

    // Detection Runner - runs detection for all actions in the stage run context
    var immyDetectionRunner = new ImmyDetectionRunner(
      MockWindowsPatchResolver(mocks),
      MockDependencyResolver(mocks),
      softwareOperations,
      Mock.Of<ILogger<ImmyDetectionRunner>>(),
      immyDetectionResolver,
      MockFeatureManager(mocks));

    // Detection Stage Runner - calls the detection runner and updates the stage status
    var detectionStageRunner = new ImmyDetectionStageRunner(immyDetectionRunner);

    return (detectionStageRunner, mocks);
  }

  public static (ImmyResolutionStageRunner, MockedServices) BuildResolutionStageRunner(
    MockedServices? mocks = null)
  {
    mocks ??= new MockedServices();
    var resolutionStageRunner = new ImmyResolutionStageRunner(
      MockTargetAssignmentResolver(mocks),
      MockMaintenanceTaskActions(mocks, new List<MaintenanceTask>()),
      (mocks.IInventoryDeviceCmd ??= new()).Object,
      Mock.Of<ILogger<ImmyResolutionStageRunner>>());
    return (resolutionStageRunner, mocks);
  }

  public static (ImmyExecutionStageRunner, MockedServices) BuildExecutionStageRunner(
    MockedServices? mocks = null)
  {
    mocks ??= new MockedServices();

    var stageRunner = new ImmyExecutionStageRunner(
      MockMachineOperations(mocks),
      BuildImmyExecutionRunner(mocks),
      (mocks.IImmyEmail ??= new()).Object);

    return (stageRunner, mocks);
  }

  public static (MachineSoftwareOperations, MockedServices) BuildMachineSoftwareOperations(
    MockedServices? mocks = null)
  {
    mocks ??= new MockedServices();
    var machineSoftwareOperations = new MachineSoftwareOperations(
      Mock.Of<ILogger<MachineSoftwareOperations>>(),
      Mock.Of<IAzureBlobStorageSasService>(),
      Mock.Of<IInventoryDeviceCmd>(),
      mocks.IPowershellLoader?.Object ?? Mock.Of<IPowershellLoader>(),
      mocks.IMachineOperations?.Object ?? Mock.Of<IMachineOperations>(),
      Mock.Of<Func<ImmybotDbContext>>());

    return (machineSoftwareOperations, mocks);
  }

  public static (MachineOperations, MockedServices) BuildMachineOperations(
    MockedServices? mocks = null)
  {
    mocks ??= new MockedServices();
    var machineSoftwareOperations = new MachineOperations(
      Mock.Of<IHostEnvironment>(),
      Mock.Of<IPowershellLoader>(),
      Mock.Of<ILogger<MachineOperations>>());

    return (machineSoftwareOperations, mocks);
  }

  public static (TargetAssignmentResolver, MockedServices) BuildTargetAssignmentResolver(
    ImmybotDbContext ctx,
    MockedServices? mocks = null,
    ITargetAssignmentActions? targetAssignmentActions = null)
  {
    mocks ??= new();
    var targetAssignmentResolver = new TargetAssignmentResolver(ctx,
      (mocks.IProviderActions ??= new()).Object,
      BuildMaintenanceActionInitializer(mocks).Item1,
      (mocks.ISoftwareActions ??= new()).Object,
      (mocks.IComputerAssignmentActions ??= new()).Object,
      (mocks.IMaintenanceTaskActions ??= new()).Object,
      targetAssignmentActions ?? (mocks.ITargetAssignmentActions ??= new()).Object,
      (mocks.IAzureActions ??= new()).Object,
      Mock.Of<ILogger<TargetAssignmentResolver>>()
    );

    return (targetAssignmentResolver, mocks);
  }
  public static (RunContextFactory, MockedServices) BuildRunContextFactory(
    Func<ImmybotDbContext> ctxFactory,
    MockedServices? mocks = null)
  {
    mocks ??= new();
    var factory = new RunContextFactory(
      MockRunContextArgs(mocks),
      ctxFactory,
      (mocks.IImmyCancellationManager ??= new()).Object,
      (mocks.IReadOnlyPolicyRegistry ??= new()).Object,
      (mocks.IMetascriptMessageHandler ??= new()).Object);
    return (factory, mocks);
  }

  public static (EphemeralAgentSessionStore, MockedServices) BuildEphemeralAgentSessionHandler(Func<ImmybotDbContext> dbFactory, MockedServices? mockedServices = null, ILogger<EphemeralAgentSessionStore>? logger = null)
  {
    mockedServices ??= new();
    mockedServices.ILoggerFactory ??= new();
    mockedServices.IPowershellLoader ??= new();
    logger ??= Mock.Of<ILogger<EphemeralAgentSessionStore>>();
    var metrics = MockMetrics(mockedServices);
    _ = new Mock<IOptions<ImmyAgentOptions>>();
    var ephemeralAgentSettings = new Mock<IOptionsMonitor<EphemeralAgentSettings>>();
    ephemeralAgentSettings.SetupGet(s => s.CurrentValue)
      .Returns(new EphemeralAgentSettings { AgentCheckinTimeoutSeconds = 60 });
    var locker = SetupKeyedLocker();

    var computerNameRetrievalStoreMock = new Mock<IComputerNameRetrievalStore>();

    var handler = new EphemeralAgentSessionStore(
      logger,
      null!,
      computerNameRetrievalStoreMock.Object,
      dbFactory,
      metrics,
      locker);

    return (handler, mockedServices);
  }

  public static ILoggerFactory MockLoggerFactory()
  {
    var loggerFactory = new Mock<ILoggerFactory>();
    var loggerMock = new Mock<ILogger>();
    loggerFactory.Setup(a => a.CreateLogger(It.IsAny<string>())).Returns(loggerMock.Object);
    return loggerFactory.Object;
  }

  public static (ScriptInvoker, MockedServices) BuildScriptInvoker(
    MockedServices? mockedServices = null,
    bool enablePreflightScripts = false)
  {
    mockedServices ??= new();
    MockAppPrefsCache(mockedServices, enablePreflightScripts: enablePreflightScripts);
    mockedServices.IEphemeralAgentAcquisition ??= new();
    mockedServices.IPreflightScriptInvoker ??= new();
    var scriptInvoker = new ScriptInvoker(
      mockedServices.IEphemeralAgentAcquisition.Object);

    return (scriptInvoker, mockedServices);
  }

  public static IOptionsMonitor<TOptions> MockOptionsMonitor<TOptions>(TOptions options)
    where TOptions : class
  {
    var optionsMonitor = new Mock<IOptionsMonitor<TOptions>>();
    optionsMonitor.SetupGet(a => a.CurrentValue).Returns(options);
    return optionsMonitor.Object;
  }

  public static IOptionsMonitor<TOptions> MockOptionsMonitor<TOptions>()
    where TOptions : class, new()
  {
    return MockOptionsMonitor(new TOptions());
  }

  public static (EphemeralAgentAcquisition, MockedServices) BuildEphemeralAgentAcquisition(
    Func<ImmybotDbContext> dbFactory,
    IEphemeralAgentSessionStore ephemeralAgentSessionStore,
    MockedServices? mockedServices = null,
    ILoggerFactory? loggerFactory = null)
  {
    mockedServices ??= new();
    var logger = loggerFactory?.CreateLogger<EphemeralAgentAcquisition>() ?? Mock.Of<ILogger<EphemeralAgentAcquisition>>();
    mockedServices.IEphemeralAgentSessionHandler ??= new();
    mockedServices.IPolicyRegistry ??= new();
    mockedServices.IDomainEventEmitter ??= new();
    mockedServices.IScriptSerializer ??= new();
    mockedServices.IDomainEventReceiver ??= new();
    mockedServices.IProviderScriptInvoker ??= new();
    mockedServices.IProviderActions ??= new();

    var ephemeralAgentAcquisition = new EphemeralAgentAcquisition(
      logger,
      ephemeralAgentSessionStore,
      mockedServices.IPolicyRegistry.Object,
      mockedServices.IDomainEventEmitter.Object,
      mockedServices.IScriptSerializer.Object,
      mockedServices.IDomainEventReceiver.Object,
      mockedServices.IProviderScriptInvoker.Object,
      dbFactory,
      MockAppPrefsCache(mockedServices),
      mockedServices.IProviderActions.Object,
      MockOptionsMonitor<AppSettingsOptions>(new()));

    return (ephemeralAgentAcquisition, mockedServices);
  }

  public static (MaintenanceSessionActions, MockedServices) BuildMaintenanceSessionActions(
    Func<ImmybotDbContext> ctxFactory,
    MockedServices? mocks = null)
  {
    mocks = mocks ?? new();
    var o = new Mock<IOptionsMonitor<SessionObjectsUpdateHandlerOptions>>();
    o.SetupGet(a => a.CurrentValue).Returns(new SessionObjectsUpdateHandlerOptions
    {
      MaintenanceActionsBatchSize = 1,
      MaintenanceSessionsBatchSize = 1,
      MaintenanceSessionStagesBatchSize = 1,
      SessionLogsBatchSize = 1,
      SessionPhasesBatchSize = 1,
    });
    var handler = new SessionObjectsUpdateHandler(
      ctxFactory,
      MockMetrics(mocks),
      MockHostApplicationLifetime().Object,
      Mock.Of<ILogger<SessionObjectsUpdateHandler>>(), o.Object);

    var sessionLogUpdateQueue = new SessionLogUpdateQueue(MockMetrics(mocks));

    var sessionActions = new MaintenanceSessionActions(
      ctxFactory,
      Mock.Of<ILogger<MaintenanceSessionActions>>(),
      (mocks.IServiceScopeFactory ??= new()).Object,
      (mocks.IImmyCancellationManager ??= new()).Object,
      (mocks.IBackgroundJobClient ??= new()).Object,
      (mocks.IDomainEventEmitter ??= new()).Object,
      sessionLogUpdateQueue,
      handler);

    return (sessionActions, mocks);
  }

  public static (MaintenanceActionInitializer, MockedServices) BuildMaintenanceActionInitializer(
    MockedServices? mocks = null)
  {
    mocks ??= new();
    var resolver = new MaintenanceActionInitializer();

    return (resolver, mocks);
  }

  public static IRunContext BuildRunContext(
    this MockedServices mocks,
    Computer? computer = null,
    Tenant? tenant = null,
    AuthUserDto? manuallyTriggeredBy = null,
    int? scheduleId = null,
    bool hasChocoInstalled = true,
    bool hasNiniteInstalled = true,
    IRunScriptProvider? preferredProvider = null,
    Func<ImmybotDbContext>? ctxFactory = null,
    CancellationToken stopProcessing = default)
  {
    if (mocks.RunContext == null)
    {
      computer ??= MakeComputer();
      tenant ??= computer.OwnerTenant;
      var args = mocks.MockRunContextArgs(computer: computer,
        tenant: tenant,
        manuallyTriggeredBy: manuallyTriggeredBy,
        scheduleId: scheduleId,
        stopProcessing: stopProcessing);
      mocks.RunContext = new RunContext(args);
    }

    mocks.RunContext.Args.Fields.HasChocolatey = hasChocoInstalled;
    mocks.RunContext.Args.Fields.HasNiniteOneExe = hasNiniteInstalled;
    mocks.RunContext.Args.CachePolicy = null;
    return mocks.RunContext;
  }

  public static (IRunContext, MockedServices) BuildRunContext(
    MockedServices? mocks = null,
    Computer? computer = null,
    CancellationToken cancellationToken = default)
  {
    mocks ??= new();
    mocks.IMetascriptInvoker ??= new();
    computer ??= MakeComputer();
    var args = MockRunContextArgs(mocks, computer: computer);
    var runContext = new RunContext(args);
    return (runContext, mocks);
  }

  public static (DomainEventBroker, MockedServices) BuildDomainEventBroker(
    MockedServices? mocks = null,
    CancellationToken cancellationToken = default)
  {
    mocks ??= new();

    var logger = Mock.Of<ILogger<DomainEventBroker>>();
    mocks.DomainEventBroker = new DomainEventBroker(logger);

    mocks.MockServiceScopeFactoryService<IDomainEventBroker>(mocks.DomainEventBroker);
    mocks.MockServiceScopeFactoryService<IDomainEventEmitter>(mocks.DomainEventBroker);
    mocks.MockServiceScopeFactoryService<IDomainEventReceiver>(mocks.DomainEventBroker);

    return (mocks.DomainEventBroker, mocks);
  }

  public static ProviderTypeDto MakeProviderTypeDto(Guid providerTypeId, List<string> providerCapabilities, DynamicFormBindResultWithConvertedParameters? providerFormData = null)
  {

    providerFormData ??= CreateBindResultWithParameterNames(new()
    {
      { "Foo", "Bar"}
    });

    return new ProviderTypeDto(
      providerTypeId,
      ProviderTypeSource.Local,
      false,
      string.Empty,
      DisplayName: "UnitTestProvider",
      ConfigurationForm: providerFormData,
      string.Empty,
      string.Empty,
      string.Empty,
      true,
      [],
      [],
      false,
      providerCapabilities,
      null,
      [],
      []);
  }

  private class UnitTestProviderFormData
  {
    [Parameter(Mandatory = true)] public required string Foo { get; set; }
  };

  public static (IProviderActions, MockedServices) BuildProviderActionsMock(
    MockedServices? mocks = null,
    bool verifiable = true,
    bool getAllProviderTypesVerifiable = false,
    Func<Type?>? integrationFormDataTypeFunc = null,
    Func<ProviderTypeDto>? providerTypeFunc = null,
    Func<DisposableValue<IQueryable<ProviderLink>>>? providerLinksFunc = null,
    Func<Task<bool>>? agentOnlineStatusFunc = null,
    List<string>? providerCapabilities = null)
  {
    mocks ??= new();


    providerTypeFunc ??= () => MakeProviderTypeDto(Guid.Empty, providerCapabilities ?? []);
    integrationFormDataTypeFunc ??= () => typeof(UnitTestProviderFormData);
    providerLinksFunc ??=
      () =>
        DisposableValue.Create(new List<ProviderLink>
            {
              new ProviderLink
              {
                Name = "",
                Id = 1,
                ProviderTypeId = Guid.Empty,
                HealthStatus = HealthStatus.Healthy,
                ProviderTypeFormData =
                  JsonSerializer.SerializeToElement(providerTypeFunc().ConfigurationForm?.ConvertedParameters)
              }
            }
            .AsQueryable(),
          () => { });

    agentOnlineStatusFunc ??= () => Task.FromResult(false);

    mocks.IProviderActions ??= new();
    mocks.IProviderStore ??= new();

    mocks.IProviderStore
  .Setup(a => a.GetProviderLinks(It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<Guid?>()))
  .Returns(providerLinksFunc);

    var providerTypeFlow = mocks.IProviderActions
      .Setup(a => a.GetProviderType(It.IsAny<Guid>(), It.IsAny<bool>()))
      .Returns(providerTypeFunc);

    _ = mocks.IProviderActions
      .Setup(a => a.GetProviderFormType(It.IsAny<Guid>()))
      .Returns(integrationFormDataTypeFunc);

    var allProviderTypeFlow = mocks.IProviderActions
      .Setup(a => a.GetAllProviderTypes(It.IsAny<bool>()))
      .Returns(() => [providerTypeFunc()]);

    var refreshAgentStatusFlow = mocks.IProviderActions
      .Setup(a => a.RefreshAgentOnlineStatus(It.IsAny<ProviderLink>(), It.IsAny<ProviderAgent>(), It.IsAny<CancellationToken>()))
      .Returns(agentOnlineStatusFunc);

    if (verifiable)
    {
      providerTypeFlow.Verifiable();
      refreshAgentStatusFlow.Verifiable();
    }

    if (getAllProviderTypesVerifiable)
    {
      allProviderTypeFlow.Verifiable();
    }

    mocks.MockServiceScopeFactoryService(mocks.IProviderActions.Object);

    mocks.MockServiceScopeFactoryService(mocks.IProviderStore.Object);

    return (mocks.IProviderActions.Object, mocks);
  }

  private static MetascriptInvokerDefaults? _metascriptInvokerDefaults;
  private static PowerShellHookService? _powerShellHookService; // perpetuate the existing HACK
  private static readonly Lock _metascriptLockObj = new();

  private static MetascriptInvokerDefaults GetSharedMetascriptInvokerDefaults()
  {
    if (_metascriptInvokerDefaults != null) return _metascriptInvokerDefaults;
    lock (_metascriptLockObj)
    {
      if (_powerShellHookService == null)
      {
        _powerShellHookService = new PowerShellHookService();
        TaskHelper.RunAsJoinableTask(() => _powerShellHookService.StartingAsync(CancellationToken.None));
      }

      return _metascriptInvokerDefaults ??= new MetascriptInvokerDefaults();
    }
  }

  public static IFunctionScriptManager BuildFunctionScriptManager(
      Func<ImmybotDbContext>? ctxFactory = null,
      Func<SoftwareDbContext>? globalCtxFactory = null,
      IPolicyRegistry<string>? policyRegistry = null)
  {
    var appSettings = new Mock<IOptionsMonitor<AppSettingsOptions>>();
    appSettings.SetupGet(it => it.CurrentValue).Returns(new AppSettingsOptions());

    return new FunctionScriptManager(
      Mock.Of<ILogger<FunctionScriptManager>>(),
      appSettings.Object,
      ctxFactory ?? Mock.Of<Func<ImmybotDbContext>>(),
      globalCtxFactory ?? Mock.Of<Func<SoftwareDbContext>>());
  }


  public static (IInitialSessionStateFactory, MockedServices) BuildInitialSessionStateFactory(
    MockedServices? mocks = null,
    Func<ImmybotDbContext>? ctxFactory = null,
    Func<SoftwareDbContext>? globalCtxFactory = null,
    IServiceScopeFactory? serviceScopeFactory = null,
    IPolicyRegistry<string>? policyRegistry = null)
  {
    mocks ??= new();
    mocks.MockServiceScopeFactoryService(new FindScriptByNameCmd(ctxFactory!, globalCtxFactory!) as IFindScriptByNameCmd);

    if (ctxFactory != null) mocks.MockServiceScopeFactoryService(ctxFactory());
    if (globalCtxFactory != null) mocks.MockServiceScopeFactoryService(globalCtxFactory());

    var functionScriptManager = BuildFunctionScriptManager(ctxFactory, globalCtxFactory);

    var factory = new InitialSessionStateFactory(
      GetSharedMetascriptInvokerDefaults(),
      serviceScopeFactory ?? (mocks.IServiceScopeFactory ??= new()).Object,
      functionScriptManager);

    return (factory, mocks);
  }


  public static (IMetascriptRunspaceServer, MockedServices) BuildMetascriptRunspaceServer(
    MockedServices mocks,
    Func<ImmybotDbContext>? ctxFactory = null,
    Func<SoftwareDbContext>? globalCtxFactory = null,
    IServiceScopeFactory? serviceScopeFactory = null,
    IPolicyRegistry<string>? policyRegistry = null)
  {
    mocks.MockServiceScopeFactoryService(new FindScriptByNameCmd(ctxFactory!, globalCtxFactory!) as IFindScriptByNameCmd);
    mocks.MockServiceScopeFactoryService(MockOptionsMonitor<AppSettingsOptions>());

    if (ctxFactory != null) mocks.MockServiceScopeFactoryService(ctxFactory());
    if (globalCtxFactory != null) mocks.MockServiceScopeFactoryService(globalCtxFactory());

    var functionScriptManager = BuildFunctionScriptManager(ctxFactory, globalCtxFactory);

    var options = new RunspacePoolOptions { RecycleStaleEntries = true };
    var optionsMonitor = new Mock<IOptionsMonitor<RunspacePoolOptions>>();
    optionsMonitor.SetupGet(it => it.CurrentValue).Returns(options);

    var runspaceServer = new TestMetascriptRunspaceServer(
      functionScriptManager,
      new DynamicProviderStore(),
      BuildInitialSessionStateFactory(
          mocks: mocks,
          ctxFactory: ctxFactory,
          globalCtxFactory: globalCtxFactory,
          serviceScopeFactory: serviceScopeFactory)
        .Item1,
      Mock.Of<ILogger<MetascriptRunspaceServer>>(),
      TimeProvider.System,
      optionsMonitor.Object);

    // not required with new TestMetascriptRunspaceServer
    //TaskHelper.RunAsJoinableTask(() => runspaceServer.StartAsync(default)); // HACK

    return (runspaceServer, mocks);
  }

  class TestMetascriptRunspaceServer(
    IFunctionScriptManager functionScriptManager,
    IDynamicProviderStore dynamicProviderStore,
    IInitialSessionStateFactory initialSessionStateFactory,
    ILogger<MetascriptRunspaceServer> logger,
    TimeProvider timeProvider,
    IOptionsMonitor<RunspacePoolOptions> optionsMonitor)
    : MetascriptRunspaceServer(functionScriptManager,
      dynamicProviderStore,
      initialSessionStateFactory,
      optionsMonitor,
      logger,
      timeProvider)
  {
    class Rental(TestMetascriptRunspaceServer server, MetascriptRunspaceServerItem item, MetascriptRunspaceServerRequest request, MetascriptRunspaceServerRentalState state) : IObjectPoolRental<MetascriptRunspaceServerItem, MetascriptRunspaceServerRequest>
    {
      public MetascriptRunspaceServerItem Object => item;
      public MetascriptRunspaceServerRequest Request => request;

      public async ValueTask DisposeAsync()
      {
        await server.OnReturnedAsync(item, state, CancellationToken.None);
        item.Runspace.Dispose();
      }
    }

    public override async Task<IObjectPoolRental<MetascriptRunspaceServerItem, MetascriptRunspaceServerRequest>> BorrowAsync(MetascriptRunspaceServerRequest request, CancellationToken token)
    {
      var @object = await this.CreateObjectAsync(DeriveKey(request), token);
      var state = await this.OnBorrowingAsync(request, @object, token);
      return new Rental(this, @object, request, state);
    }
  }

  public static (IMetascriptInvoker, MockedServices) BuildMetascriptInvoker(
    MockedServices? mocks = null,
    Func<ImmybotDbContext>? ctxFactory = null,
    Func<SoftwareDbContext>? globalCtxFactory = null,
    IServiceScopeFactory? serviceScopeFactory = null,
    IPolicyRegistry<string>? policyRegistry = null)
  {
    mocks ??= new();

    var ephemeralAgentSettings = new Mock<IOptionsMonitor<EphemeralAgentSettings>>();
    ephemeralAgentSettings.SetupGet(s => s.CurrentValue)
      .Returns(new EphemeralAgentSettings());

    var metascriptInvoker = new MetascriptInvoker(
      BuildMetascriptRunspaceServer(mocks, ctxFactory, globalCtxFactory, serviceScopeFactory, policyRegistry).Item1,
      MockAppPrefsCache(mocks),
      (mocks.IMetascriptMessageHandler ??= new()).Object,
      ephemeralAgentSettings.Object,
      (mocks.IPowershellLoader ??= new()).Object,
      (mocks.IImmyCancellationManager ??= new()).Object,
      Mock.Of<IPowerShellErrorEventHubService>());
    return (metascriptInvoker, mocks);
  }

  public static (KeyedLocker, ImmyCacheRepository, MockedServices) BuildKeyedSemaphoreLockerAndCacheRepo(
    MockedServices? mocks = null)
  {
    mocks ??= new();
    // There may be a better way to do this, but Im not sure how. This is basically how its tested in the MessagePipe repo.
    var sc = new ServiceCollection();
    sc.AddMessagePipe();
    var sp = sc.BuildServiceProvider();
    var eventFactory = sp.GetRequiredService<EventFactory>();
    var locker = new KeyedLocker(eventFactory);
    var repo = new ImmyCacheRepository(eventFactory, keyedLocker: locker);
    return (locker, repo, mocks);
  }

  public static (IDynamicFormService, IActionRunContext, MockedServices) BuildDynamicFormService(
    MockedServices? mocks = null,
    Func<ImmybotDbContext>? ctx = null,
    Func<SoftwareDbContext>? sftCtx = null,
    IMetascriptInvoker? metascriptInvoker = null,
    IActionRunContext? actionRunContext = null,
    IServiceScopeFactory? serviceScopeFactory = null,
    IPolicyRegistry<string>? policyRegistry = null)
  {
    mocks ??= new();

    var registry = new PolicyRegistry();
    registry.AddShowCommandInfoCachePolicy();
    if (metascriptInvoker is null)
      (metascriptInvoker, _) = BuildMetascriptInvoker(mocks, ctx, sftCtx);

    if (actionRunContext is null)
      actionRunContext = BuildActionRunContext(mocks: mocks,
        metascriptInvoker: (MetascriptInvoker?)metascriptInvoker,
        ctxFactory: ctx);

    var azureOptsMock = new Mock<IOptions<AzureActiveDirectoryAuthOptions>>();
    var azureOpts = new AzureActiveDirectoryAuthOptions { ClientId = "test" };
    azureOptsMock.SetupGet(a => a.Value).Returns(azureOpts);
    var dynamicFormService = new DynamicFormService(
      BuildMetascriptRunspaceServer(mocks, ctx, sftCtx, serviceScopeFactory, policyRegistry).Item1,
      new CommandInfoConverter(azureOptsMock.Object),
      Mock.Of<IMetascriptMessageHandler>());

    return (dynamicFormService, actionRunContext, mocks);
  }

  public static (DynamicProviderInterceptorFactory, MockedServices) BuildDynamicProviderInterceptorFactory(
    MockedServices? mocks = null,
    Func<ImmybotDbContext>? ctx = null,
    Func<SoftwareDbContext>? sftCtx = null,
    IMetascriptInvoker? metascriptInvoker = null,
    IDomainEventEmitter? domainEventEmitter = null,
    IDynamicFormService? dynamicFormService = null)
  {
    mocks ??= new();

    if (metascriptInvoker is null)
      (metascriptInvoker, mocks) = BuildMetascriptInvoker(mocks, ctx, sftCtx);

    if (dynamicFormService is null)
      (dynamicFormService, _, mocks) = BuildDynamicFormService(mocks, ctx, sftCtx, metascriptInvoker);

    var dynamicProviderInterceptorFactory = new DynamicProviderInterceptorFactory(
           metascriptInvoker,
           domainEventEmitter ?? new Mock<IDomainEventEmitter>().Object,
           dynamicFormService,
           MockAppPrefsCache(mocks));

    return (dynamicProviderInterceptorFactory, mocks);
  }

  public static (IDynamicProviderRegistrationFactory, MockedServices) BuildDynamicProviderRegistrationFactory(
    Func<ImmybotDbContext> ctx,
    Func<SoftwareDbContext> sftCtx,
    MockedServices? mocks = null,
    IMetascriptInvoker? metascriptInvoker = null,
    ProviderLinkActions? providerLinkActions = null,
    IDynamicProviderInterceptorFactory? dynamicInterceptorFactory = null)
  {
    mocks ??= new();

    if (metascriptInvoker is null)
      (metascriptInvoker, _) = BuildMetascriptInvoker(mocks, ctx, sftCtx);

    if (dynamicInterceptorFactory is null)
      (dynamicInterceptorFactory, _) = BuildDynamicProviderInterceptorFactory(mocks, ctx, sftCtx, metascriptInvoker);

    var dynamicProviderRegistration = new DynamicProviderRegistrationFactory(
      providerLinkActions ?? new ProviderLinkActions(ctx),
      metascriptInvoker,
      dynamicInterceptorFactory);

    return (dynamicProviderRegistration, mocks);
  }

  public static (IProviderMetadataFactory, MockedServices) BuildProviderMetadataFactory(
    Func<ImmybotDbContext> ctx,
    Func<SoftwareDbContext> sftCtx,
    MockedServices? mocks = null,
    IDynamicFormService? dynamicFormService = null)
  {
    mocks ??= new();

    if (dynamicFormService is null)
      (dynamicFormService, _, mocks) = BuildDynamicFormService(mocks, ctx, sftCtx);

    var providerMetadataFactory = new ProviderMetadataFactory(
           dynamicFormService,
           Mock.Of<ILogger<ProviderMetadataFactory>>());

    return (providerMetadataFactory, mocks);
  }

  public static (ProviderRegistrationService, MockedServices) BuildProviderRegistrationService(
    MockedServices? mocks,
    TimeProvider? timeProvider = null,
    IServiceScopeFactory? serviceScopeFactory = null)
  {
    mocks ??= new();
    mocks.IProviderRegistrationService ??= new();

    var providerRegistrationService = new ProviderRegistrationService(
      serviceScopeFactory ?? new Mock<IServiceScopeFactory>().Object,
      timeProvider ?? TimeProvider.System,
      MockLoggerFactory().CreateLogger<ProviderRegistrationService>());

    return (providerRegistrationService, mocks);
  }

  public static RecommendedProviderLinksGetter BuildRecommendedProviderLinksGetter(
    Func<ImmybotDbContext> ctx,
    Func<SoftwareDbContext> sftCtx,
    IDynamicFormService? dynamicFormService = null,
    IProviderRegistrationService? providerRegistrationService = null,
    IProviderActions? providerActions = null,
    ILogger<RecommendedProviderLinksGetter>? logger = null
    )
  {
    return new RecommendedProviderLinksGetter(
      ctx,
      sftCtx,
      dynamicFormService ?? new Mock<IDynamicFormService>().Object,
      providerRegistrationService ?? new Mock<IProviderRegistrationService>().Object,
      providerActions ?? new Mock<IProviderActions>().Object,
      logger ?? new Mock<ILogger<RecommendedProviderLinksGetter>>().Object
      );
  }

  public static List<BaseParamSetInfo> BuildBaseParamSetInfo(
    DynamicIntegrationType dynamicIntegrationType,
    object param
    )
  {

    return new List<BaseParamSetInfo>
    {
      new BaseParamSetInfo
      {
        IntegrationType = dynamicIntegrationType,
        IntegrationTypeId = dynamicIntegrationType.IntegrationTypeId,
        SoftwareName = "Test Software",
        TargetAssignment = new TargetAssignment
        {
          Id = 1,
          MaintenanceIdentifier = "0",
          MaintenanceType = MaintenanceType.LocalSoftware,
          TaskParameterValues = new Dictionary<string, DeploymentParameterValue>
          {
            { "TestParam", new DeploymentParameterValue(JsonSerializer.SerializeToElement(param)) }
          }.ToImmutableDictionary()
        }
      }
    };
  }

  public static (DynamicProviderInterceptor, MockedServices) BuildDynamicProviderInterceptor(
    MockedServices? mocks = null,
    string dynamicProviderStoreId = "-1",
    int providerLinkId = -1,
    string providerLinkName = "TestProvider",
    DatabaseType databaseType = DatabaseType.Global,
    IDictionary<string, ScriptBlock>? methodScripts = null,
    Hashtable? providerTypeSecrets = null,
    IMetascriptInvoker? metascriptInvoker = null,
    IDomainEventEmitter? domainEventEmitter = null,
    IDynamicFormService? dynamicFormService = null)
  {
    mocks ??= new();

    var interceptor = new DynamicProviderInterceptor(
      dynamicProviderStoreId,
      providerLinkId,
      providerLinkName,
      databaseType,
      methodScripts ?? new Dictionary<string, ScriptBlock>(),
      providerTypeSecrets ?? new(),
      metascriptInvoker ?? (mocks.IMetascriptInvoker ??= new()).Object,
      domainEventEmitter ?? (mocks.IDomainEventEmitter ??= new()).Object,
      dynamicFormService ?? (mocks.IDynamicFormService ??= new()).Object,
      MockAppPrefsCache(mocks).Value);
    return (interceptor, mocks);
  }

  public static (ProviderActions, MockedServices) BuildProviderActions(
    MockedServices? mocks = null,
    ILogger<ProviderActions>? logger = null,
    Func<ImmybotDbContext>? immybotDbContextFactory = null,
    Func<SoftwareDbContext>? softwareDbContextFactory = null,
    IProviderFactory? providerFactory = null,
    IProviderRegistrationService? providerRegistrationService = null,
    IProviderLinkDataConsistencyLocker? providerLinkLocker = null,
    IProviderAgentEventHandler? providerEventHandler = null,
    ICachedSingleton<ProviderLinkNames>? linkNamesCache = null,
    IDynamicFormService? dynamicFormService = null,
    IServiceScopeFactory? serviceScopeFactory = null,
    IPolicyCacheStore? policyCacheStore = null,
    IPolicyRegistry<string>? policyRegistry = null,
    IDynamicIntegrationsGlobalStore? dynamicIntegrationsGlobalStore = null)
  {
    mocks ??= new();
    var providerActions = new ProviderActions(
      logger ?? Mock.Of<ILogger<ProviderActions>>(),
      immybotDbContextFactory ?? Mock.Of<Func<ImmybotDbContext>>(),
      providerFactory ?? (mocks.IProviderFactory ??= new()).Object,
      providerRegistrationService ?? (mocks.IProviderRegistrationService ??= new()).Object,
      providerLinkLocker ?? (mocks.IProviderLinkDataConsistencyLocker ??= new()).Object,
      providerEventHandler ?? (mocks.IProviderAgentEventHandler ??= new()).Object,
      linkNamesCache ?? (mocks.ICachedSingleton ??= new()).Object,
      serviceScopeFactory ?? (mocks.IServiceScopeFactory ??= new()).Object,
      policyCacheStore ?? (mocks.IPolicyCacheStore ??= new()).Object,
      policyRegistry ?? (mocks.IPolicyRegistry ??= new()).Object
      );

    return (providerActions, mocks);
  }

  public static DynamicIntegrationType BuildDynamicIntegrationType(
    string script,
    Guid? _providerGuid = null,
    int? _providerLinkId = null)
  {
    var providerLinkdId = _providerLinkId ?? 0;
    var dynamicIntegrationScript = MakeIntegrationScript(script);
    var dynamicIntegrationType = new DynamicIntegrationType
    {
      Id = providerLinkdId,
      Name = "Test",
      IntegrationTypeId = _providerGuid ?? Guid.NewGuid(),
      DatabaseType = DatabaseType.Global,
      Tag = IntegrationTag.Production,
      Enabled = true,
      Script = dynamicIntegrationScript
    };
    return dynamicIntegrationType;
  }

  public static (InteractiveScriptExecutionService, MockedServices) BuildInteractiveScriptExecutionServiceHandler(
    Func<ImmybotDbContext> ctxFactory,
    MockedServices? mocks = null,
    (Guid, CancellationToken)? existingCancellationId = null,
    bool setupServiceScopeFactory = false)
  {
    mocks ??= new();
    if (setupServiceScopeFactory)
    {
      (IRunContextFactory rcFactory, _) = BuildRunContextFactory(ctxFactory, mocks);
      mocks.MockServiceScopeFactoryService(rcFactory);
      mocks.MockServiceScopeFactoryService(mocks.MockIMetascriptInvoker_RunMetascript<object>(null));
    }

    var appSettings = new Mock<IOptionsMonitor<AppSettingsOptions>>();
    appSettings.SetupGet(it => it.CurrentValue).Returns(new AppSettingsOptions());

    var sessionHandler = new InteractiveScriptExecutionService(
      cachedAppPrefs: mocks.IAppPrefsCache!.Object,
      appSettings.Object,
      ctxFactory,
      (mocks.IServiceScopeFactory ??= new()).Object,
      mocks.MockImmyCancellationManager(existingCancellationId));

    return (sessionHandler, mocks);
  }

  public static (MachineMaintenanceTaskOperations, MockedServices) BuildMaintenanceTaskOperations(
    MockedServices? mocks = null,
    ILoggerFactory? loggerFactory = null)
  {
    mocks ??= new MockedServices();

    mocks.IMachineSoftwareOperations = new();
    mocks.IAzureBlobStorageSasService = new();
    mocks.IDynamicFormService = new();
    var machineMaintenanceTaskOperations = new MachineMaintenanceTaskOperations(
      mocks.IMachineSoftwareOperations.Object,
      mocks.IAzureBlobStorageSasService.Object,
      mocks.IDynamicFormService.Object,
      loggerFactory?.CreateLogger<MachineMaintenanceTaskOperations>() ?? Mock.Of<ILogger<MachineMaintenanceTaskOperations>>(),
      Mock.Of<IPowershellLoader>());

    return (machineMaintenanceTaskOperations, mocks);
  }

  public static ImmyDetectionResolver BuildImmyDetectionResolver(MockedServices mocks)
  {
    var resolver = new ImmyDetectionResolver(
      MockMaintenanceActionResolver(mocks),
      (mocks.IMachineMaintenanceTaskOperations ??= new()).Object,
      (mocks.IMaintenanceTaskActions ??= new()).Object,
      (mocks.IMachineSoftwareOperations ??= new()).Object);

    return resolver;
  }
  #endregion Service Constructors
}

internal class MockedServices
{
  public Mock<IImmyDetectionRunner>? IImmyDetectionRunner { get; set; }
  public Mock<IImmyExecutionRunner>? IImmyExecutionRunner { get; set; }
  public Mock<IEphemeralAgentSessionStore>? IEphemeralAgentSessionHandler { get; set; }
  public Mock<ITargetPopulator>? TargetPopulator { get; set; }
  public Mock<IPendingComputerResolver>? IPendingComputerResolver { get; set; }
  public Mock<IMetascriptMessageHandler>? IMetascriptMessageHandler { get; set; }
  public Mock<IPendingAgentResolverActions>? IPendingAgentResolverActions { get; set; }
  public Mock<IAgentIdentificationActions>? IAgentIdentificationActions { get; set; }
  public Mock<IDeleteComputerCmd>? IDeleteComputerCmd { get; set; }
  public Mock<IInventoryDeviceCmd>? IInventoryDeviceCmd { get; set; }
  public RunContextArgs? RunContextArgs { get; set; }
  public Mock<IRunContextArgs>? IRunContextArgsMock { get; set; }
  public Mock<ILoggerFactory>? ILoggerFactory { get; set; }
  public Mock<IRunContextFactory>? IRunContextFactory { get; set; }
  public Mock<ITargetAssignmentResolver>? ITargetAssignmentResolver { get; set; }
  public Mock<IDependencyResolver>? IDependencyResolver { get; set; }
  public Mock<IImmyEmail>? IImmyEmail { get; set; }
  public Mock<IMachineSoftwareOperations>? IMachineSoftwareOperations { get; set; }
  public Mock<IMachineOperations>? IMachineOperations { get; set; }
  public Mock<IImmyDetectionResolver>? IImmyDetectionResolver { get; set; }
  public Mock<IImmyExecutionResolver>? IImmyExecutionResolver { get; set; }
  public Mock<IMachineMaintenanceTaskOperations>? IMachineMaintenanceTaskOperations { get; set; }
  public Mock<ISoftwareActions>? ISoftwareActions { get; set; }
  public Mock<IScriptActions>? IScriptActions { get; set; }
  public Mock<IRunContextActions>? IRunContextActions { get; set; }
  public Mock<IRunContextActionsList>? IRunContextActionsList { get; set; }
  public Mock<IImmyOnboardingStageRunner>? IImmyOnboarding { get; set; }
  public Mock<ISmtpConfigActions>? ISmtpConfigActions { get; set; }
  public IRunContext? RunContext { get; set; }
  public NiniteSoftwareCache? NiniteSoftwareCache { get; set; }
  public Mock<IWindowsPatchResolver>? IWindowsPatchResolver { get; set; }
  public Mock<ICachedSingleton<ApplicationPreferences>>? IAppPrefsCache { get; set; }
  public Mock<ICachedCollection<TenantPreferences>>? ITenantPrefsCache { get; set; }
  public Mock<IMaintenanceTaskActions>? IMaintenanceTaskActions { get; set; }
  public Mock<IReadOnlyPolicyRegistry<string>>? IReadOnlyPolicyRegistry { get; set; }
  public Mock<IPolicyRegistry<string>>? IPolicyRegistry { get; set; }
  public Context? PolicyContext { get; set; }
  public IAsyncPolicy? CachePolicy { get; set; }
  public Mock<IMetascriptInvoker>? IMetascriptInvoker { get; set; }
  public Mock<IInitialSessionStateFactory>? IInitialSessionStateFactory { get; set; }
  public Mock<IImmyCancellationManager>? IImmyCancellationManager { get; set; }
  public Mock<IImmyExecutionStageRunner>? IImmyExecution { get; set; }
  public Mock<IImmyInventoryStageRunner>? IImmyInventory { get; set; }
  public Mock<IImmyResolutionStageRunner>? IImmyResolution { get; set; }
  public Mock<IImmyDetectionStageRunner>? IImmyDetection { get; set; }
  public Mock<IImmyAgentUpdatesStageRunner>? IImmyAgentUpdates { get; set; }
  public Mock<IBackgroundJobClient>? IBackgroundJobClient { get; set; }
  public Mock<IRecurringJobManager>? IRecurringJobManager { get; set; }
  public Mock<IProviderActions>? IProviderActions { get; set; }
  public Mock<IProviderStore>? IProviderStore { get; set; }
  public Mock<IMaintenanceSessionActions>? IMaintenanceSessionActions { get; set; }
  public Mock<IAzureActions>? IAzureActions { get; set; }
  internal Mock<IMaintenanceActionInitializer>? IMaintenanceActionResolver { get; set; }
  public Mock<IComputerAssignmentActions>? IComputerAssignmentActions { get; set; }
  public Mock<ITargetAssignmentActions>? ITargetAssignmentActions { get; set; }
  public Mock<IMediaActions>? IMediaActions { get; set; }
  public Mock<IServiceScopeFactory>? IServiceScopeFactory { get; set; }
  public Mock<IServiceScope>? IServiceScope { get; set; }
  public Mock<IServiceProvider>? ScopedServiceProvider { get; set; }
  public Mock<IDomainEventBroker>? IDomainEventBroker { get; set; }
  public Mock<IDomainEventEmitter>? IDomainEventEmitter { get; set; }
  public Mock<IDomainEventReceiver>? IDomainEventReceiver { get; set; }
  public Mock<IPowershellLoader>? IPowershellLoader { get; set; }
  public Mock<IProviderLinkDataConsistencyLocker>? IProviderLinkDataConsistencyLocker { get; set; }
  public Mock<IImmyServiceJob>? IImmyServiceJob { get; set; }
  public Mock<IImmyService>? IImmyService { get; set; }
  public Mock<IAzureBlobStorageSasService>? IAzureBlobStorageSasService { get; set; }
  public Mock<IDynamicFormService>? IDynamicFormService { get; set; }
  public Mock<IActionRunContext>? IActionRunContext { get; set; }
  public Mock<IStageRunContext>? IStageRunContext { get; set; }
  public Mock<ISessionRunContext>? ISessionRunContext { get; set; }
  public Mock<ISessionRunContext>? ISessionRunContextMock { get; set; }
  public Mock<IRunContext>? IRunContextMock { get; set; }

  public Mock<IFindFunctionScriptCmd>? IFindFunctionScriptCmd { get; set; }
  public Mock<IFindScriptByNameCmd>? IFindScriptByNameCmd { get; set; }
  public Mock<IScriptSerializer>? IScriptSerializer { get; set; }
  public Mock<IScriptInvoker>? IScriptInvoker { get; set; }
  public Mock<IManagerProvidedSettings>? IManagerProvidedSetings { get; set; }
  public Mock<IHostApplicationLifetime>? IHostApplicationLifetime { get; set; }
  public Mock<IMaintenanceSessionPendingConnectivityHandler>? IMaintenanceSessionPendingConnectivityHandler { get; set; }
  public Mock<ILanguageService>? ILanguageService { get; set; }
  public Mock<IUserService>? IUserService { get; set; }
  public Mock<IRunContextComputerActions>? IRunContextComputerActions { get; set; }
  public Mock<IFeatureManager>? IFeatureManager { get; set; }

  public MaintenanceSession? Session { get; set; }
  public SessionJobArgs? SessionJobArgs { get; set; }

  public Mock<IAgentIdentificationHandler>? IAgentIdentificationHandler { get; set; }
  public Mock<IAgentResolutionHandler>? IAgentResolutionHandler { get; set; }
  public DomainEventBroker? DomainEventBroker { get; set; }
  public Mock<IAgentIdentificationManagerActions>? IAgentIdentificationManagerActions { get; set; }
  public Mock<IRunScriptProvider>? IRunScriptProviderMock { get; set; }
  public Mock<IAgentIdentificationLogHandler>? IAgentIdentificationLogHandler { get; set; }
  public Mock<IMetrics>? IMetrics { get; set; }
  public Mock<ISessionObjectsUpdateHandler>? ISessionObjectUpdateHandler { get; set; }
  public Mock<IEphemeralAgentAcquisition>? IEphemeralAgentAcquisition { get; set; }
  public Mock<IProviderScriptInvoker>? IProviderScriptInvoker { get; set; }
  public Mock<IPreflightScriptInvoker>? IPreflightScriptInvoker { get; set; }
  public Mock<IMaintenanceSessionPreflightHandlerActions>? IMaintenanceSessionPreflightHandlerActions { get; set; }
  public Mock<IEphemeralAgentSession>? IEphemeralAgentSession { get; set; }
  public Mock<JobStorage>? JobStorage { get; set; }
  public Mock<IComputerMaintenanceDateOperations>? IComputerMaintenanceDateOperations { get; set; }
  public Mock<IMaintenanceActionDiagnosticEventHubService>? IMaintenanceActionEventHubService { get; set; }
  public Mock<IProviderFactory>? IProviderFactory { get; set; }
  public Mock<IProviderRegistrationService>? IProviderRegistrationService { get; set; }
  public Mock<IProviderAgentEventHandler>? IProviderAgentEventHandler { get; set; }
  public Mock<ICachedSingleton<ProviderLinkNames>>? ICachedSingleton { get; set; }
  public Mock<IPolicyCacheStore>? IPolicyCacheStore { get; set; }
  public Mock<IDynamicIntegrationsGlobalStore>? IDynamicIntegrationsGlobalStore { get; set; }
  public Mock<ISystemTime>? ISystemTime { get; set; }
  public Mock<ILicenseActions>? ILicenseActions { get; set; }
  public Mock<ITenantActions>? ITenantActions { get; set; }
  public Mock<IPersonActions>? IPersonActions { get; set; }
  public Mock<IProviderLinkActions>? IProviderLinkActions { get; set; }
  public Mock<IMaintenanceItemOrderActions>? IMaintenanceItemOrderActions { get; set; }
  public Mock<IScheduleActions>? IScheduleActions { get; set; }
  public Mock<IComputerInventoryActions>? IComputerInventoryActions { get; set; }
  public Mock<IMaintenanceActionActions>? IMaintenanceActionActions { get; set; }

  public Mock<ISessionStageActions>? ISessionStageActions { get; set; }
}
