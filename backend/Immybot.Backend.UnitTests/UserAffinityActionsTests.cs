using System;
using System.Linq;
using System.Threading.Tasks;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.ContextTests;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.UnitTests;

public class UserAffinityActionsTests : ActionsTestBase
{
  [Theory]
  [CombinatorialData]
  public async Task UpdateUserAffinity_ShouldNotAssignPrimaryPersonFromAnotherTenant(bool fromSameTenant)
  {
    // arrange
    var ctx = GetSqliteDbContext();

    var actions = new UserAffinityActions();
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    var computer = CreateComputer(link.Id,
      client.ExternalClientId,
      "test",
      tenant.Id,
      "test",
      Guid.NewGuid());

    var anotherTenant = CreateTenant();
    var person = CreatePerson(fromSameTenant ? tenant.Id : anotherTenant.Id, "<EMAIL>");
    var userAffinity = new UserAffinity { ComputerId = computer.Id, PersonId = person.Id };

    // act

    await actions.UpdateUserAffinity(
      ctx,
      computer.Id,
      computer.TenantId,
      computer.PrimaryPersonId,
      person.TenantId,
      userAffinity,
      []);

    // assert

    var updatedComputer = await ctx.Computers.AsNoTracking().FirstOrDefaultAsync(a => a.Id == computer.Id);
    Assert.NotNull(updatedComputer);
    if (fromSameTenant)
    {
      Assert.NotNull(updatedComputer.PrimaryPersonId);
    }
    else
    {
      Assert.Null(updatedComputer.PrimaryPersonId);
    }
  }
}
