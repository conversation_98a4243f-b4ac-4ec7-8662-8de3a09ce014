using Immybot.Backend.Application.Maintenance;

namespace Immybot.Backend.UnitTests;

public class StringEscapeTests
{
  [Theory]
  [InlineData("pa$sword", "pa`$sword")]
  [InlineData("My`r`nString", "My``r``nString")]
  [InlineData("``", "````")]
  [InlineData("`", "``")]
  [InlineData("p*ssword`", "p*ssword``")]
  [InlineData("pa??*", "pa??*")]
  [InlineData("{$null}", "{`$null}")]
  [InlineData("`{$null}", "``{`$null}")]
  [InlineData(@"""''""", @"""''""")]
  [InlineData(@"```^$null", @"``````^`$null")]

  public void EscapeDoubleQuotedString_ShouldEscape(string str, string expected)
  {
    // act
    var escaped = MachineMaintenanceTaskOperations.EscapeDoubleQuotedString(str);

    // assert

    Assert.Equal(expected, escaped);

  }
}
