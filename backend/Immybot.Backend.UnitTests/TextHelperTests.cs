using System.Collections.Generic;
using Immybot.Backend.Application.Lib.Helpers;

namespace Immybot.Backend.UnitTests;

public class TextHelperTests
{
  public static IEnumerable<object?[]> Data =>
    new List<object?[]>
  {
    new object?[] { "asdf wer grfalse", true, false },
    new object?[] { "asdfrgetrhgeth true", true, true },
    new object?[] { "aSDFrwgrevcsvs FALSE", true, false },
    new object?[] { "asfasdfgehbdgger\nTRUE", true, true },
    new object?[] { "asdfrea\t0", true, false },
    new object?[] { "asdfs agf\r1", true, true },
    new object?[] { "asdfs agf\r1vsafghrdsh", false, false },
    new object?[] { "", false, false },
    new object?[] { null, false, false }
  };

  [Theory]
  [MemberData(nameof(Data))]
  public void EndsInBooleanValue_Should_ReturnTrueOrFalseWithReturnBool(string text, bool expectedParsable, bool expectedResult)
  {
    // act
    var success = TextHelpers.EndsInBooleanValue(text, out var result);

    // assert
    Assert.Equal(success, expectedParsable);
    Assert.Equal(result, expectedResult);
  }

  public static IEnumerable<object?[]> LastWordData => new List<object?[]>
    {
      new object?[] { "some text 1.0.0", "1.0.0" },
      new object?[] { "some text 1.0.0\r\n\r\n", "1.0.0" },
      new object?[] { "asdf\n1.0.0", "1.0.0" },
      new object?[] { "asdf\n\r1.0.0", "1.0.0" },
      new object?[] { "asdf\n some text 1.0.0", "1.0.0" },
      new object?[] { null, null },
      new object?[] { "", null },
    };

  [Theory]
  [MemberData(nameof(LastWordData))]
  public void GetLastWordInString_Should_ReturnLastWord(string text, string expected)
  {
    // act
    var lastWord = TextHelpers.GetLastWordInString(text);

    // assert
    Assert.Equal(expected, lastWord);
  }
}
