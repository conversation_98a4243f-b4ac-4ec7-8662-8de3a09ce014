using Immybot.Backend.Application.Lib.Helpers;
using NuGet.Versioning;

namespace Immybot.Backend.UnitTests;

public class VersionConversionsTests
{
  [Fact]
  public void ToAutomationSemanticVersion_ShouldThrowExceptionWhenInvalid()
  {
    // arrange
    const string v = "1.0.2--2";
    var semver = SemanticVersion.Parse(v);

    // act & assert

    var ex = Assert.Throws<FormatException>(() => semver.ToAutomationSemanticVersion("Foxit Reader"));
    Assert.Equal($"Foxit Reader {v} could not be parsed to a System.Management.Automation.SemanticVersion.",
      ex.Message);
  }
}
