using System;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Lib.RemoteControl;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.UnitTests.Shared.Lib;
using Moq;

namespace Immybot.Backend.UnitTests;

public class ViewerOptionsProviderTests
{
  private readonly Mock<ICachedSingleton<ApplicationPreferences>> _appPrefs;
  private readonly Mock<ICachedCollection<TenantPreferences>> _tenantPrefs;
  private readonly Mock<IUserService> _userService;
  private readonly Mock<ILogger<ViewerOptionsProvider>> _logger;
  private readonly ViewerOptionsProvider _viewerOptionsProvider;
  private readonly AuthUserDto _user;

  public ViewerOptionsProviderTests()
  {
    _user = BaseUnitTests.GetAuthUser(new User() { TenantId = 3, Id = 4 });
    _appPrefs = new Mock<ICachedSingleton<ApplicationPreferences>>();
    _tenantPrefs = new Mock<ICachedCollection<TenantPreferences>>();
    _userService = new Mock<IUserService>();
    _logger = new Mock<ILogger<ViewerOptionsProvider>>();

    _viewerOptionsProvider = new ViewerOptionsProvider(
      _appPrefs.Object,
      _tenantPrefs.Object,
      _userService.Object,
      _logger.Object);
  }

  [Fact(Skip = "Remove when fully implemented.")]
  public async Task GetViewerOptions_GivenUserDoesNotExist_ShouldReturnDefaultPrefs()
  {
    _userService
      .Setup(x => x.GetCurrentUser())
      .Throws(new Exception());

    var result = await _viewerOptionsProvider.GetViewerOptions();
    Assert.False(result.ShouldRecordSession);
  }

  [Fact(Skip = "Remove when fully implemented.")]
  public async Task GetViewerOptions_GivenTenantPrefsDoNotExist_ShouldUseAppPrefs()
  {
    _userService
      .Setup(x => x.GetCurrentUser())
      .Returns(_user);

    _tenantPrefs
      .Setup(x => x.Value)
      .Returns(Array.Empty<TenantPreferences>());

    _appPrefs
      .Setup(x => x.Value)
      .Returns(new ApplicationPreferences() { EnableImmyBotRemoteControlRecording = true });

    var result = await _viewerOptionsProvider.GetViewerOptions();
    Assert.True(result.ShouldRecordSession);
  }

  [Fact(Skip = "Remove when fully implemented.")]
  public async Task GetViewerOptions_TenantPrefsExist_ShouldOverrideAppPrefs()
  {
    _userService
      .Setup(x => x.GetCurrentUser())
      .Returns(_user);

    var tenantPref = new TenantPreferences()
    {
      TenantId = _user.TenantId,
      EnableImmyBotRemoteControlRecording = false
    };

    _tenantPrefs
      .Setup(x => x.Value)
      .Returns(new[]{ tenantPref  });

    _appPrefs
      .Setup(x => x.Value)
      .Returns(new ApplicationPreferences() { EnableImmyBotRemoteControlRecording = true });

    var result = await _viewerOptionsProvider.GetViewerOptions();
    Assert.False(result.ShouldRecordSession);
  }
}
