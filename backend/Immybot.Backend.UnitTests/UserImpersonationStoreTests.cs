using System;
using System.Threading.Tasks;

namespace Immybot.Backend.UnitTests;

public static class UserImpersonationStoreTests
{
  public static Task ImpersonateUser_ShouldUpdateUserImpersonations()
  {
    throw new NotImplementedException();
  }

  public static Task StopImpersonating_ShouldRemoveUserImpersonations()
  {
    throw new NotImplementedException();
  }

  public static Task InvalidatePersonatorsCacheForUser_ShouldInvalidateCache()
  {
    throw new NotImplementedException();
  }
}
