using System;
using System.Threading.Tasks;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Shared.Abstractions.Device;
using JetBrains.Annotations;
using Microsoft.EntityFrameworkCore;
using Testcontainers.PostgreSql;

namespace Immybot.Backend.UnitTests.Fixtures;

public class PgDatabaseFixture : IAsyncLifetime
{
  private protected PostgreSqlContainer? _pgContainer;
  private protected DbContextOptionsBuilder<ImmybotDbContext>? _builder;
  private protected DbContextOptionsBuilder<SoftwareDbContext>? _globalBuilder;
  private int dbNum;
  private int globalDbNum;

  /// <summary>
  /// This gets called once after all tests in the collection have run
  /// </summary>
  /// <returns></returns>
  public Task DisposeAsync()
  {
    return Task.CompletedTask;
  }

  /// <summary>
  /// This gets called once before the first test in the collection
  /// </summary>
  /// <returns></returns>
  public Task InitializeAsync()
  {
    return Task.CompletedTask;
  }
  /// <summary>
  /// On first call (e.g. before the first test in the collection): Sets up pg db docker
  ///   container if it isn't already running and migrates the immy_template database with
  ///   our migrations
  /// On subsequent calls (e.g. before each test): Recreates the immy_test database
  ///   from the immy_template template
  /// </summary>
  public async Task ResetImmybotDb(ILoggerFactory? loggerFactory = null)
  {
    var pgContainer = await SetupTemplateDbs(loggerFactory);

    _ = await pgContainer.ExecScriptAsync(
      $@"SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = 'immy_template';
CREATE DATABASE immy_test_{++dbNum} TEMPLATE immy_template;");
    // Beard: attempting fix for "Npgsql.PostgresException : 3D000: database "immy_test_4" does not exist"
    //        by waiting a bit after creating the db
    await Task.Delay(50);

    var connectionString = pgContainer.GetConnectionString().Replace("Database=postgres", $"Database=immy_test_{dbNum}");

    _builder = new DbContextOptionsBuilder<ImmybotDbContext>();
    _builder.EnableSensitiveDataLogging(true);
    _builder.UseLoggerFactory(loggerFactory).EnableSensitiveDataLogging();
    _builder
      .UseNpgsql(connectionString, options =>
      {
        options.CommandTimeout(360);
        options.SetPostgresVersion(11, 6);
      })
      .UseSnakeCaseNamingConvention()
      .AddInterceptors(new AuditableEntitySaveChangesInterceptor(new SystemTime(), true));
    AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
  }

  /// <summary>
  /// On first call (e.g. before the first test in the collection): Sets up pg db docker
  ///   container if it isn't already running and migrates the global_template database with
  ///   our migrations
  /// On subsequent calls (e.g. before each test): Recreates the global_test database
  ///   from the global_template template
  /// </summary>
  public async Task ResetGlobalDb(ILoggerFactory? loggerFactory = null)
  {
    var pgContainer = await SetupTemplateDbs(loggerFactory);

    _ = await pgContainer.ExecScriptAsync(
      $@"SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = 'global_template';
CREATE DATABASE global_test_{++globalDbNum} TEMPLATE global_template;");

    // Beard: attempting fix for "Npgsql.PostgresException : 3D000: database "immy_test_4" does not exist"
    //        by waiting a bit after creating the db
    await Task.Delay(50);

    var connectionString = pgContainer.GetConnectionString().Replace("Database=postgres", $"Database=global_test_{globalDbNum}");

    _globalBuilder = new DbContextOptionsBuilder<SoftwareDbContext>();
    _globalBuilder.EnableSensitiveDataLogging(true);
    _globalBuilder.UseLoggerFactory(loggerFactory).EnableSensitiveDataLogging();
    _globalBuilder
      .UseNpgsql(connectionString, options =>
      {
        options.CommandTimeout(360);
        options.SetPostgresVersion(11, 6);
      })
      .UseSnakeCaseNamingConvention()
      .AddInterceptors(new AuditableEntitySaveChangesInterceptor(new SystemTime(), true));
    AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
  }

  private async Task<PostgreSqlContainer> SetupTemplateDbs(ILoggerFactory? loggerFactory)
  {
    if (_pgContainer != null) return _pgContainer;

    AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);

    var pgContainer = new PostgreSqlBuilder()
      .WithImage("timescale/timescaledb:latest-pg12")
      .WithDatabase("postgres")
      .Build();
    await pgContainer.StartAsync();
    var connectionString = pgContainer.GetConnectionString().Replace("Database=postgres", "Database=immy_template");
    var templatebuilder = new DbContextOptionsBuilder<ImmybotDbContext>();
    templatebuilder.EnableSensitiveDataLogging(true);

    templatebuilder.UseLoggerFactory(loggerFactory).EnableSensitiveDataLogging();

    templatebuilder
      .UseNpgsql(connectionString, options =>
      {
        options.CommandTimeout(360);
        options.SetPostgresVersion(11, 6);
      })
      .UseSnakeCaseNamingConvention()
      .AddInterceptors(new AuditableEntitySaveChangesInterceptor(new SystemTime(), true));

    // Migrate the immy template
    await using var dbContext = new ImmybotDbContext(templatebuilder.Options);
    await dbContext.Database.MigrateAsync();
    await dbContext.Database.ExecuteSqlRawAsync("CREATE EXTENSION IF NOT EXISTS pg_trgm;");
    await dbContext.Database.ExecuteSqlRawAsync("CREATE EXTENSION IF NOT EXISTS intarray;");
    await dbContext.Database.ExecuteSqlRawAsync("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";");
    await dbContext.Database.CloseConnectionAsync();

    var globalConnectionString = pgContainer.GetConnectionString().Replace("Database=postgres", "Database=global_template");
    var globalTemplateBuilder = new DbContextOptionsBuilder<ImmybotDbContext>();
    globalTemplateBuilder.EnableSensitiveDataLogging(true);

    globalTemplateBuilder.UseLoggerFactory(loggerFactory).EnableSensitiveDataLogging();

    globalTemplateBuilder
      .UseNpgsql(globalConnectionString, options =>
      {
        options.CommandTimeout(360);
        options.SetPostgresVersion(11, 6);
      })
      .UseSnakeCaseNamingConvention()
      .AddInterceptors(new AuditableEntitySaveChangesInterceptor(new SystemTime(), true));

    // Migrate the global template
    await using var globalDbContext = new ImmybotDbContext(globalTemplateBuilder.Options);
    await globalDbContext.Database.MigrateAsync();
    await globalDbContext.Database.ExecuteSqlRawAsync("CREATE EXTENSION IF NOT EXISTS pg_trgm;");
    await globalDbContext.Database.ExecuteSqlRawAsync("CREATE EXTENSION IF NOT EXISTS intarray;");
    await globalDbContext.Database.ExecuteSqlRawAsync("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";");
    await globalDbContext.Database.CloseConnectionAsync();
    return _pgContainer = pgContainer;
  }

  /// <summary>
  /// Returns a fresh db context for the immy_test database
  /// </summary>
  [MustDisposeResource]
  public ImmybotDbContext GetNewImmybotDbContext()
  {
    if (_builder == null) throw new Exception("Local postgres container not initialized - Call ResetImmybotDb before each test");
    return new ImmybotDbContext(_builder.Options);
  }

  /// <summary>
  /// Returns a fresh db context for the immy_test database
  /// </summary>
  [MustDisposeResource]
  public SoftwareDbContext GetNewGlobalDbContext()
  {
    if (_globalBuilder == null) throw new Exception("Global postgres container not initialized - Call ResetGlobalDb before each test");
    return new SoftwareDbContext(_globalBuilder.Options);
  }
}
