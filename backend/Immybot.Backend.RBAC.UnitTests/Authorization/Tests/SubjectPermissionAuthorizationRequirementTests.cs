using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationRequirements;
using Immybot.Backend.RBAC.UnitTests.Authorization.Mocks;
using Immybot.Backend.UnitTests.Shared.Lib;

namespace Immybot.Backend.RBAC.UnitTests.Authorization.Tests;

/// <summary>
/// Tests for the SubjectPermissionAuthorizationRequirement class.
/// </summary>
public class SubjectPermissionAuthorizationRequirementTests : BaseUnitTests
{
    [Fact]
    public void Constructor_WithValidPermission_ShouldCreateInstance()
    {
        // Arrange
        var mockPermission = MockPermissions.CreateMockPermission();

        // Act
        var requirement = new SubjectPermissionAuthorizationRequirement([mockPermission.Object]);

        // Assert
        Assert.NotNull(requirement);
        Assert.Same(mockPermission.Object, requirement.AnyPermissions[0]);
    }
}
