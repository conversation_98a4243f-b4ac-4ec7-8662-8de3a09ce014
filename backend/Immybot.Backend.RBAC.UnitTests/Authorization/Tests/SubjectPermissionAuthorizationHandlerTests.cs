using System.Security.Claims;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.RBAC.AuthorizationPolicyManagement.Extensions;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationHandlers;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationRequirements;
using Immybot.Backend.RBAC.UnitTests.Authorization.Mocks;
using Immybot.Backend.UnitTests.Shared.Lib;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.RBAC.UnitTests.Authorization.Tests;

/// <summary>
/// Tests for the SubjectPermissionAuthorizationHandler class.
/// Focus on RBAC enabled vs disabled behavior.
/// </summary>
public class SubjectPermissionAuthorizationHandlerTests : BaseUnitTests
{
    // Test-specific subclass that exposes the protected method for testing
    private class TestableAuthorizationHandler : SubjectPermissionAuthorizationHandler
    {
      // Expose the protected method for testing
        public new Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            SubjectPermissionAuthorizationRequirement requirement)
        {
            return base.HandleRequirementAsync(context, requirement);
        }
    }

    [Fact]
    public async Task HandleRequirementAsync_ShouldFailWithoutClaims()
    {
        // This test verifies that when RBAC is enabled, authorization fails without required claims

        // Arrange
        var mockPermission = MockPermissions.CreateMockPermission();
        var requirement = new SubjectPermissionAuthorizationRequirement([mockPermission.Object]);

        // Create an authenticated user with NO claims
        var identity = new ClaimsIdentity([], "TestAuth");
        var principal = new ClaimsPrincipal(identity);
        var context = new AuthorizationHandlerContext([requirement], principal, null);

        var handler = new TestableAuthorizationHandler();

        // Act
        await handler.HandleRequirementAsync(context, requirement);

        // Assert - with RBAC enabled, authorization should fail due to no claims
        Assert.False(context.HasSucceeded);
    }

    [Fact]
    public async Task HandleRequirementAsync_ShouldSucceed_WhenAtLeastOnePermissionIsGranted()
    {
      // This test verifies that when RBAC is enabled, authorization succeeds if at least one permission is granted
      // and multiple permissions are specified in the requirement.

      // Arrange
      var grantedPermission = MockPermissions.CreateMockPermission(subject: "test1");
      var ungrantedPermission = MockPermissions.CreateMockPermission(subject: "test2");
      var requirement =
        new SubjectPermissionAuthorizationRequirement([grantedPermission.Object, ungrantedPermission.Object]);

      // Create an authenticated user with the granted permission claim only
      var claim = new Claim(grantedPermission.Object.SubjectClaimType, grantedPermission.Object.AllowClaim);
      var identity = new ClaimsIdentity([claim], "TestAuth");
      var principal = new ClaimsPrincipal(identity);

      var context = new AuthorizationHandlerContext([requirement], principal, null);

      var handler = new TestableAuthorizationHandler();

      // Act
      await handler.HandleRequirementAsync(context, requirement);

      // Assert - with RBAC enabled, authorization should succeed because one permission is granted
      Assert.True(context.HasSucceeded);
    }


    [Theory]
    [CombinatorialData]
    public async Task HandleRequirementAsync_ShouldHandleMspOnlyFlag(bool isMspUser)
    {
      // Arrange
      var host = CreateHost(b =>
      {
        var authService = b.Services.FirstOrDefault(d => d.ServiceType == typeof(IAuthorizationService));
        if (authService is not null)
          b.Services.Remove(authService);

        b.Services.AddRouting();
        b.Services.AddAuthorization();
        b.Services.AddDiscoveredAuthorizationPolicies();
        b.Services.AddAuthorizationBuilder();
      });
      using var scope = host.Services.CreateScope();

      // Create an authenticated user with NO claims
      var permission = host.Services.GetRequiredService<IApplicationLocksViewPermission>();
      var identity = new ClaimsIdentity([], "TestAuth");
      identity.AddClaim(new Claim(ClaimConstants.IsMsp, isMspUser.ToString()));
      identity.AddClaim(new Claim(permission.SubjectClaimType, permission.AllowClaim));
      var principal = new ClaimsPrincipal(identity);

      // Act
      var service = scope.ServiceProvider.GetRequiredService<ISubjectPermissionAuthorizationService>();
      var res = await service.AuthorizeAsync<IApplicationLocksViewPermission>(principal, strict: false);

      Assert.Equal(isMspUser, res);
    }
}
