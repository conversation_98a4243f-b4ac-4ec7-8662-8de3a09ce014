using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Moq;

namespace Immybot.Backend.RBAC.UnitTests.Authorization.Mocks;

/// <summary>
/// Provides mock implementations and setup helpers for permission-related tests.
/// </summary>
public static class MockPermissions
{
    /// <summary>
    /// Creates a mock IPermissionMetadata with default setup.
    /// </summary>
    public static Mock<IPermissionMetadata> CreateMockPermission(string subject = "TestSubject")
    {
        var mockSubject = new Mock<ISubjectMetadata>();
        mockSubject.Setup(s => s.Id).Returns(subject);
        mockSubject.Setup(s => s.Name).Returns(subject);
        mockSubject.Setup(s => s.DisplayName).Returns($"{subject} Display Name");
        mockSubject.Setup(s => s.Description).Returns($"{subject} Description");
        mockSubject.Setup(s => s.SortOrder).Returns(0);
        mockSubject.Setup(s => s.Permissions).Returns([]);

        var mockPermission = new Mock<IPermissionMetadata>();
        mockPermission.Setup(p => p.Id).Returns($"{subject} Permission Id");
        mockPermission.Setup(p => p.PermissionName).Returns($"{subject}TestPermission");
        mockPermission.Setup(p => p.DisplayName).Returns($"{subject} Test Permission");
        mockPermission.Setup(p => p.Description).Returns($"{subject} Test permission for unit tests");
        mockPermission.Setup(p => p.Category).Returns(PermissionCategory.CoreCapability);
        mockPermission.Setup(p => p.SortOrder).Returns(0);
        mockPermission.Setup(p => p.HasGreatPower).Returns(false);
        mockPermission.Setup(p => p.Dependencies).Returns([]);
        mockPermission.Setup(p => p.Subject).Returns(mockSubject.Object);
        mockPermission.Setup(p => p.Claim).Returns($"{subject}:TestPermission");
        mockPermission.Setup(p => p.AllowClaim).Returns($"{subject}:TestPermission:Allow");
        mockPermission.Setup(p => p.DenyClaim).Returns($"{subject}:TestPermission:Deny");
        mockPermission.Setup(p => p.SubjectClaimType).Returns($"{subject}:TestSubject");

        return mockPermission;
    }

    /// <summary>
    /// Creates a mock IPermissionMetadata with a dependency on another permission.
    /// </summary>
    public static Mock<IPermissionMetadata> CreateMockPermissionWithDependency(Mock<IPermissionMetadata> dependency)
    {
        var mockPermission = CreateMockPermission();
        mockPermission.Setup(p => p.Dependencies).Returns(new[] { dependency.Object });
        return mockPermission;
    }

    /// <summary>
    /// Creates a mock IPermissionMetadata to use as a dependency.
    /// </summary>
    public static Mock<IPermissionMetadata> CreateMockDependencyPermission()
    {
        var mockSubject = new Mock<ISubjectMetadata>();
        mockSubject.Setup(s => s.Id).Returns("test-subject-id");
        mockSubject.Setup(s => s.Name).Returns("TestSubject");
        mockSubject.Setup(s => s.DisplayName).Returns("Test Subject");
        mockSubject.Setup(s => s.Description).Returns("Test subject for unit tests");
        mockSubject.Setup(s => s.SortOrder).Returns(0);
        mockSubject.Setup(s => s.Permissions).Returns(Array.Empty<IPermissionMetadata>());

        var mockDependency = new Mock<IPermissionMetadata>();
        mockDependency.Setup(p => p.Id).Returns("dependency-id");
        mockDependency.Setup(p => p.PermissionName).Returns("DependencyPermission");
        mockDependency.Setup(p => p.DisplayName).Returns("Dependency Permission");
        mockDependency.Setup(p => p.Description).Returns("Dependency permission for unit tests");
        mockDependency.Setup(p => p.Category).Returns(PermissionCategory.CoreCapability);
        mockDependency.Setup(p => p.SortOrder).Returns(0);
        mockDependency.Setup(p => p.HasGreatPower).Returns(false);
        mockDependency.Setup(p => p.Dependencies).Returns(Array.Empty<IPermissionMetadata>());
        mockDependency.Setup(p => p.Subject).Returns(mockSubject.Object);
        mockDependency.Setup(p => p.Claim).Returns("TestSubject:DependencyPermission");
        mockDependency.Setup(p => p.AllowClaim).Returns("TestSubject:DependencyPermission:Allow");
        mockDependency.Setup(p => p.DenyClaim).Returns("TestSubject:DependencyPermission:Deny");

        return mockDependency;
    }
}
