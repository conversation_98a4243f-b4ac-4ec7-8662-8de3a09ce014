SELECT "c"."id", "c"."chassis_types", "c"."computer_name", "c"."created_date", "c"."deleted_at", "c"."deleted_reason", "c"."detection_outdated", "c"."dev_lab_vm_claim_expiration_date_utc", "c"."dev_lab_vm_name", "c"."dev_lab_vm_unclaimed", "c"."device_id", "c"."domain", "c"."domain_role", "c"."exclude_from_maintenance", "c"."excluded_from_user_affinity", "c"."external_ip_address", "c"."has_pending_reboot", "c"."internal_ip_address", "c"."inventory_started_date", "c"."is_sandbox", "c"."last_boot_time_utc", "c"."last_logged_on_user", "c"."manufacturer", "c"."model", "c"."os_install_date", "c"."onboarded_date_utc", "c"."onboarding_status", "c"."operating_system", "c"."primary_person_id", "c"."serial_number", "c"."successor_computer_id", "c"."tenant_id", "c"."updated_date"
FROM "computers" AS "c"
WHERE "c"."deleted_at" IS NULL AND "c"."tenant_id" = 1