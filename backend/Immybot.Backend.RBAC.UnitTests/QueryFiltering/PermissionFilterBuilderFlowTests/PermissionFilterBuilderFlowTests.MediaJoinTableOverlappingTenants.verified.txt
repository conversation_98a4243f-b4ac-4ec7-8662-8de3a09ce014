-- @__tenantIds_0={ '1', '2', '4' } (DbType = Object)
SELECT m.id, m.blob_reference, m.category, m.created_by, m.created_date, m.database_type, m.file_name, m.mime_type, m.name, m.package_hash, m.relative_cache_source_path, m.updated_by, m.updated_date
FROM media AS m
WHERE (
SELECT count(*)::int
FROM tenant_media AS t
WHERE m.id = t.media_id) = 0 OR EXISTS (
SELECT 1
FROM tenant_media AS t0
WHERE m.id = t0.media_id AND t0.tenant_id = ANY (@__tenantIds_0))
