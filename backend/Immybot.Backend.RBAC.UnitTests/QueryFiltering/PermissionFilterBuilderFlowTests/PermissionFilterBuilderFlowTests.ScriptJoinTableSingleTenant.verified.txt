-- @__tenantIds_0={ '1' } (DbType = Object)
SELECT s.id, s.action, s.created_by, s.created_date, s.hidden, s.name, s.output_type, s.public_storage_download_url, s.script_cache_name, s.script_category, s.script_execution_context, s.script_hash, s.script_action_type, s.script_type, s.timeout, s.updated_by, s.updated_date
FROM scripts AS s
WHERE (
SELECT count(*)::int
FROM tenant_scripts AS t
WHERE s.id = t.script_id) = 0 OR EXISTS (
SELECT 1
FROM tenant_scripts AS t0
WHERE s.id = t0.script_id AND t0.tenant_id = ANY (@__tenantIds_0))
