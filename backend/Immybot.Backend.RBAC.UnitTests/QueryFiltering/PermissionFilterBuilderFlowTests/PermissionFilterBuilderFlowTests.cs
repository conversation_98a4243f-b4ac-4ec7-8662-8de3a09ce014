using System.Security.Claims;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Extensions;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.UnitTests.Shared.Lib;

namespace Immybot.Backend.RBAC.UnitTests.QueryFiltering;

/// <summary>
/// Integration tests for PermissionFilterBuilder - testing the complete query filtering workflow
/// using real RBAC implementations and mocking only external dependencies.
/// Includes SQL query snapshot testing to ensure generated queries remain consistent.
/// </summary>
public class PermissionFilterBuilderFlowTests : BaseUnitTests
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ImmybotDbContext _dbContext;
    private readonly Mock<IUserService> _mockUserService;
    private readonly IPermissionFilterBuilder _permissionFilterBuilder;


    public PermissionFilterBuilderFlowTests()
    {
        // Create our own user service mock that we can control in tests
        _mockUserService = new Mock<IUserService>();

        // Use base class infrastructure instead of manual service collection setup
        var host = CreateHost(builder =>
        {
            // Register permission evaluation services (our new system)
            builder.Services.AddPermissionEvaluationServices();

            // Override the user service with our controllable mock
            builder.Services.AddSingleton(_mockUserService.Object);
        });

        _serviceProvider = host.Services.CreateScope().ServiceProvider;
        _dbContext = GetSqliteDbContext();
        _permissionFilterBuilder = _serviceProvider.GetRequiredService<IPermissionFilterBuilder>();
    }


    /// <summary>
    /// Helper method to show the SQL query that would be generated for a given filter
    /// </summary>
    private string GetGeneratedSql<TResource, TPermission>()
        where TResource : class
        where TPermission : class, IPermissionMetadata, IResourceBased
    {
      var options = new DbContextOptionsBuilder<ImmybotDbContext>()
        .UseNpgsql("Host=localhost;Database=*****;Username=*****;Password=*****")
        .UseSnakeCaseNamingConvention()
        .Options;
      using var context = new ImmybotDbContext(options);
        var filter = _permissionFilterBuilder.BuildFilterExpression<TResource, TPermission>();
        var query = context.Set<TResource>().Where(filter);
        return query.ToQueryString();
    }


    /// <summary>
    /// Helper method to verify SQL query snapshots using Verify framework
    /// </summary>
    private async Task VerifyGeneratedSql<TResource, TPermission>(string scenarioName)
        where TResource : class
        where TPermission : class, IPermissionMetadata, IResourceBased
    {
        var sql = GetGeneratedSql<TResource, TPermission>();

        // Clean up the SQL for consistent snapshots by normalizing whitespace and removing volatile parts
        var normalizedSql = NormalizeSqlForSnapshot(sql);

        var settings = new VerifySettings();
        settings.UseMethodName(scenarioName);
        await Verify(normalizedSql, settings);
    }

    /// <summary>
    /// Normalizes SQL queries for consistent snapshot testing by removing volatile elements
    /// </summary>
    private static string NormalizeSqlForSnapshot(string sql)
    {
        // Remove line numbers and clean up whitespace for consistent snapshots
        var lines = sql.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries)
            .Select(line => line.Trim())
            .Where(line => !string.IsNullOrWhiteSpace(line));

        var normalizedSql = string.Join("\n", lines);

        return normalizedSql;
    }

    [Fact]
    public async Task BuildFilterExpression_SingleResourcePermissions_TestsAllowAndDeny()
    {
        // ARRANGE
        var tenant = CreateTenant();
        var person = CreatePerson(tenant.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant.Id, person.Id);
        var tenantId = tenant.Id;

        // Create computers
        var computer1 = CreateBlankComputer(tenantId: tenantId, ctx: _dbContext);
        var computer2 = CreateBlankComputer(tenantId: tenantId, ctx: _dbContext);
        var computer3 = CreateBlankComputer(tenantId: tenantId, ctx: _dbContext);

        // TEST 1: Single resource allow
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<IComputersViewPermission>();
        var claimValue = claimsProvider.GetResourceAllowClaim(computer1.Id.ToString(), permission);
        identity.AddClaim(new Claim(permission.ResourceClaimType, claimValue));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = BaseUnitTests.GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query matches the expected snapshot
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("SingleResourceAllow");

        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Single(filteredComputers);
        Assert.Equal(computer1.Id, filteredComputers[0].Id);

        // TEST 2: Single resource deny (with tenant allow)
        identity = new ClaimsIdentity("test");
        var tenantAllowClaimValue = claimsProvider.GetTenantAllowClaim(tenantId, permission);
        var resourceDenyClaimValue = claimsProvider.GetResourceDenyClaim(computer2.Id.ToString(), permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, tenantAllowClaimValue));  // Allow all in tenant
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceDenyClaimValue));           // Deny specific computer
        claimsPrincipal = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for deny scenario
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("SingleResourceDeny");

        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(2, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == computer1.Id);
        Assert.Contains(filteredComputers, c => c.Id == computer3.Id);
        Assert.DoesNotContain(filteredComputers, c => c.Id == computer2.Id); // Denied
    }

    [Fact]
    public async Task BuildFilterExpression_MultipleResourcePermissions_TestsAllowAndDeny()
    {
        // ARRANGE
        var tenant = CreateTenant();
        var person = CreatePerson(tenant.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant.Id, person.Id);
        var tenantId = tenant.Id;

        // Create computers
        var computer1 = CreateBlankComputer(tenantId: tenantId, ctx: _dbContext);
        var computer2 = CreateBlankComputer(tenantId: tenantId, ctx: _dbContext);
        var computer3 = CreateBlankComputer(tenantId: tenantId, ctx: _dbContext);
        var computer4 = CreateBlankComputer(tenantId: tenantId, ctx: _dbContext);
        var computer5 = CreateBlankComputer(tenantId: tenantId, ctx: _dbContext);
        var computer6 = CreateBlankComputer(tenantId: tenantId, ctx: _dbContext);

        // TEST 1: Multiple resource allow
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<IComputersViewPermission>();
        var claimValue1 = claimsProvider.GetResourceAllowClaim(computer1.Id.ToString(), permission);
        var claimValue3 = claimsProvider.GetResourceAllowClaim(computer3.Id.ToString(), permission);
        var claimValue5 = claimsProvider.GetResourceAllowClaim(computer5.Id.ToString(), permission);
        identity.AddClaim(new Claim(permission.ResourceClaimType, claimValue1));
        identity.AddClaim(new Claim(permission.ResourceClaimType, claimValue3));
        identity.AddClaim(new Claim(permission.ResourceClaimType, claimValue5));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for multiple resource allow
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("MultipleResourceAllow");

        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(3, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == computer1.Id);
        Assert.Contains(filteredComputers, c => c.Id == computer3.Id);
        Assert.Contains(filteredComputers, c => c.Id == computer5.Id);

        // TEST 2: Multiple resource deny (with wildcard allow)
        identity = new ClaimsIdentity("test");
        var allTenantsClaimValue = claimsProvider.GetAllTenantAllowClaim(permission);
        var resourceDenyClaimValue2 = claimsProvider.GetResourceDenyClaim(computer2.Id.ToString(), permission);
        var resourceDenyClaimValue4 = claimsProvider.GetResourceDenyClaim(computer4.Id.ToString(), permission);
        var resourceDenyClaimValue6 = claimsProvider.GetResourceDenyClaim(computer6.Id.ToString(), permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, allTenantsClaimValue));   // Allow all
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceDenyClaimValue2));           // Deny specific computers
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceDenyClaimValue4));
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceDenyClaimValue6));
        claimsPrincipal = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for multiple resource deny
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("MultipleResourceDeny");

        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(3, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == computer1.Id);
        Assert.Contains(filteredComputers, c => c.Id == computer3.Id);
        Assert.Contains(filteredComputers, c => c.Id == computer5.Id);
        Assert.DoesNotContain(filteredComputers, c => c.Id == computer2.Id); // Denied
        Assert.DoesNotContain(filteredComputers, c => c.Id == computer4.Id); // Denied
        Assert.DoesNotContain(filteredComputers, c => c.Id == computer6.Id); // Denied
    }

    [Fact]
    public async Task BuildFilterExpression_SingleTenantPermissions_TestsAllowAndDeny()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);
        var tenantId1 = tenant1.Id;
        var tenantId2 = tenant2.Id;

        // Create computers in different tenants
        var computer1 = CreateBlankComputer(tenantId: tenantId1, ctx: _dbContext);
        var computer2 = CreateBlankComputer(tenantId: tenantId2, ctx: _dbContext);

        // TEST 1: Single tenant allow (specific tenant)
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<IComputersViewPermission>();
        var claimValue = claimsProvider.GetTenantAllowClaim(tenantId2, permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for single tenant allow
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("SingleTenantAllow");

        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Single(filteredComputers);
        Assert.Equal(computer2.Id, filteredComputers[0].Id);
        Assert.Equal(tenantId2, filteredComputers[0].TenantId);

        // TEST 2: User's own tenant (my tenant)
        identity = new ClaimsIdentity("test");
        var myTenantClaimValue = claimsProvider.GetMyTenantAllowClaim(permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, myTenantClaimValue));
        claimsPrincipal = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for my tenant allow
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("MyTenantAllow");

        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Single(filteredComputers);
        Assert.Equal(computer1.Id, filteredComputers[0].Id);
        Assert.Equal(tenantId1, filteredComputers[0].TenantId);
    }

    [Fact]
    public async Task BuildFilterExpression_MultipleTenantPermissions_TestsAllowAndDeny()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var tenant3 = CreateTenant();
        var tenant4 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create computers in different tenants
        var computer1 = CreateBlankComputer(tenantId: tenant1.Id, ctx: _dbContext);
        var computer2 = CreateBlankComputer(tenantId: tenant2.Id, ctx: _dbContext);
        var computer3 = CreateBlankComputer(tenantId: tenant3.Id, ctx: _dbContext);
        var computer4 = CreateBlankComputer(tenantId: tenant4.Id, ctx: _dbContext);

        // TEST 1: Multiple tenant allow
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<IComputersViewPermission>();
        var claimValue1 = claimsProvider.GetTenantAllowClaim(tenant1.Id, permission);
        var claimValue2 = claimsProvider.GetTenantAllowClaim(tenant2.Id, permission);
        var claimValue3 = claimsProvider.GetTenantAllowClaim(tenant3.Id, permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue1));
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue2));
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue3));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for multiple tenant allow
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("MultipleTenantAllow");

        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(3, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == computer1.Id && c.TenantId == tenant1.Id);
        Assert.Contains(filteredComputers, c => c.Id == computer2.Id && c.TenantId == tenant2.Id);
        Assert.Contains(filteredComputers, c => c.Id == computer3.Id && c.TenantId == tenant3.Id);
        Assert.DoesNotContain(filteredComputers, c => c.Id == computer4.Id);

        // TEST 2: Wildcard allow
        identity = new ClaimsIdentity("test");
        var allTenantsClaimValue = claimsProvider.GetAllTenantAllowClaim(permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, allTenantsClaimValue));
        claimsPrincipal = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for wildcard tenant allow
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("WildcardTenantAllow");

        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(4, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == 1);
        Assert.Contains(filteredComputers, c => c.Id == 2);
        Assert.Contains(filteredComputers, c => c.Id == 3);
        Assert.Contains(filteredComputers, c => c.Id == 4);
    }

    [Fact]
    public async Task BuildFilterExpression_MixedTenantAndResourcePermissions_TestsComplexScenarios()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create computers in different tenants
        var computer1 = CreateBlankComputer(tenantId: tenant1.Id, ctx: _dbContext);
        var computer2 = CreateBlankComputer(tenantId: tenant1.Id, ctx: _dbContext);
        var computer3 = CreateBlankComputer(tenantId: tenant2.Id, ctx: _dbContext);
        var computer4 = CreateBlankComputer(tenantId: tenant2.Id, ctx: _dbContext);

        // TEST 1: Multiple tenant allow + single resource deny
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<IComputersViewPermission>();
        var claimValue1 = claimsProvider.GetTenantAllowClaim(tenant1.Id, permission);
        var claimValue2 = claimsProvider.GetTenantAllowClaim(tenant2.Id, permission);
        var resourceDenyClaimValue = claimsProvider.GetResourceDenyClaim(computer2.Id.ToString(), permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue1));
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue2));
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceDenyClaimValue)); // Deny specific computer
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for mixed tenant and resource permissions
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("MixedTenantResourcePermissions");

        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(3, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == computer1.Id);
        Assert.Contains(filteredComputers, c => c.Id == computer3.Id);
        Assert.Contains(filteredComputers, c => c.Id == computer4.Id);
        Assert.DoesNotContain(filteredComputers, c => c.Id == computer2.Id); // Denied despite tenant allow

        // TEST 2: Resource-specific allow + tenant deny pattern simulation
        identity = new ClaimsIdentity("test");
        var resourceAllowClaimValue2 = claimsProvider.GetResourceAllowClaim(computer2.Id.ToString(), permission);
        var resourceAllowClaimValue4 = claimsProvider.GetResourceAllowClaim(computer4.Id.ToString(), permission);
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceAllowClaimValue2)); // Allow specific computer
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceAllowClaimValue4)); // Allow specific computer
        claimsPrincipal = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for resource-specific only permissions
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("ResourceSpecificOnly");

        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Equal(2, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == 2);
        Assert.Contains(filteredComputers, c => c.Id == 4);
        Assert.DoesNotContain(filteredComputers, c => c.Id == 1); // No permission
        Assert.DoesNotContain(filteredComputers, c => c.Id == 3); // No permission

        // TEST 3: Complex deny pattern - exclude multiple resources AND multiple tenants
        // Create additional tenants first
        var tenant3 = CreateTenant();
        var tenant4 = CreateTenant();

        // Create additional computers for this scenario
        var computer7 = CreateBlankComputer(tenantId: tenant3.Id, ctx: _dbContext); // Allowed tenant but denied resource
        var computer8 = CreateBlankComputer(tenantId: tenant3.Id, ctx: _dbContext); // Allowed tenant
        var computer9 = CreateBlankComputer(tenantId: tenant4.Id, ctx: _dbContext); // Allowed tenant

        identity = new ClaimsIdentity("test");
        var allTenantsClaimValue = claimsProvider.GetAllTenantAllowClaim(permission);
        var tenant1DenyClaimValue = claimsProvider.GetTenantDenyClaim(tenant1.Id, permission);
        var tenant2DenyClaimValue = claimsProvider.GetTenantDenyClaim(tenant2.Id, permission);
        var resourceDenyClaimValue3 = claimsProvider.GetResourceDenyClaim(computer3.Id.ToString(), permission);
        var resourceDenyClaimValue7 = claimsProvider.GetResourceDenyClaim(computer7.Id.ToString(), permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, allTenantsClaimValue));    // Allow all tenants
        identity.AddClaim(new Claim(permission.TenantClaimType, tenant1DenyClaimValue));   // Deny specific tenants
        identity.AddClaim(new Claim(permission.TenantClaimType, tenant2DenyClaimValue));
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceDenyClaimValue3));            // Deny specific resources
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceDenyClaimValue7));
        claimsPrincipal = new ClaimsPrincipal(identity);

        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for complex deny pattern
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("ComplexDenyPattern_ExcludeResourcesAndTenants");

        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        // Should only get computers from tenants 3,4 that aren't specifically denied resources
        Assert.Equal(2, filteredComputers.Count);
        Assert.Contains(filteredComputers, c => c.Id == computer8.Id && c.TenantId == tenant3.Id); // Allowed tenant, not denied resource
        Assert.Contains(filteredComputers, c => c.Id == computer9.Id && c.TenantId == tenant4.Id); // Allowed tenant, not denied resource
        Assert.DoesNotContain(filteredComputers, c => c.Id == computer1.Id); // Denied tenant
        Assert.DoesNotContain(filteredComputers, c => c.Id == computer2.Id); // Denied tenant
        Assert.DoesNotContain(filteredComputers, c => c.Id == computer3.Id); // Denied tenant
        Assert.DoesNotContain(filteredComputers, c => c.Id == computer4.Id); // Denied tenant
        Assert.DoesNotContain(filteredComputers, c => c.Id == computer7.Id); // Denied resource (despite allowed tenant)
    }

    [Fact]
    public async Task BuildFilterExpression_EdgeCases_TestsAuthenticationAndPermissions()
    {
        // ARRANGE
        var tenant = CreateTenant();
        var person = CreatePerson(tenant.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant.Id, person.Id);

        // Create computer for the test data
        CreateBlankComputer(tenantId: tenant.Id, ctx: _dbContext);

        // TEST 1: Unauthenticated user
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns((ClaimsPrincipal?)null);

        var filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for unauthenticated user
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("UnauthenticatedUser");

        var filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Empty(filteredComputers);

        // TEST 2: Authenticated user with no relevant permissions
        var identity = new ClaimsIdentity("test");
        identity.AddClaim(new Claim("some:other:claim", "true"));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        filter = _permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for user with no relevant permissions
        await VerifyGeneratedSql<Computer, IComputersViewPermission>("NoRelevantPermissions");
        filteredComputers = await _dbContext.Computers.Where(filter).ToListAsync();

        Assert.Empty(filteredComputers);
    }

    /// <summary>
    /// Tests RBAC filtering for Tag entities using join table tenant relationships.
    /// Validates that the TagSqlTranslatableTenantRelationshipProvider correctly generates
    /// SQL filters that check the TenantTagAuthorization join table for owned relationships.
    /// </summary>
    [Fact]
    public async Task BuildFilterExpression_ForTags_WithSpecificTenant_ShouldReturnTagsForTenant()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create tags with join table relationships
        var tag1 = CreateTagWithTenant("Tag1", tenant1.Id);
        var tag2 = CreateTagWithTenant("Tag2", tenant1.Id);
        var tag3 = CreateTagWithTenant("Tag3", tenant2.Id);

        // Give user permission to access tags in tenant 1
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<ITagsViewPermission>();
        var claimValue = claimsProvider.GetTenantAllowClaim(tenant1.Id, permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        // ACT
        var filter = _permissionFilterBuilder.BuildFilterExpression<Tag, ITagsViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for join table filtering
        await VerifyGeneratedSql<Tag, ITagsViewPermission>("TagJoinTableSingleTenant");

        var filteredTags = await _dbContext.Tags.Where(filter).ToListAsync();

        // ASSERT - Should only get tags from tenant 1
        Assert.Equal(2, filteredTags.Count);
        Assert.Contains(filteredTags, t => t.Id == tag1.Id && t.Name == "Tag1");
        Assert.Contains(filteredTags, t => t.Id == tag2.Id && t.Name == "Tag2");
        Assert.DoesNotContain(filteredTags, t => t.Id == tag3.Id); // Different tenant
    }

    /// <summary>
    /// Tests RBAC filtering for Media entities using join table tenant relationships.
    /// Validates that the MediaSqlTranslatableTenantRelationshipProvider correctly generates
    /// SQL filters that check the TenantMedia join table for owned relationships.
    /// </summary>
    [Fact]
    public async Task BuildFilterExpression_ForMedia_WithWildcardTenant_ShouldReturnAllMedia()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create media with join table relationships
        var media1 = CreateMediaWithTenant("Media1", "file1.jpg", tenant1.Id);
        var media2 = CreateMediaWithTenant("Media2", "file2.png", tenant1.Id);
        var media3 = CreateMediaWithTenant("Media3", "file3.gif", tenant2.Id);

        // Give user wildcard permission to access media across all tenants
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<IMediaViewPermission>();
        var claimValue = claimsProvider.GetAllTenantAllowClaim(permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        // ACT
        var filter = _permissionFilterBuilder.BuildFilterExpression<Media, IMediaViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for wildcard join table filtering
        await VerifyGeneratedSql<Media, IMediaViewPermission>("MediaJoinTableWildcardTenant");

        var filteredMedia = await _dbContext.Media.Where(filter).ToListAsync();

        // ASSERT - Should get all media regardless of tenant due to wildcard permission
        Assert.Equal(3, filteredMedia.Count);
        Assert.Contains(filteredMedia, m => m.Id == media1.Id && m.Name == "Media1");
        Assert.Contains(filteredMedia, m => m.Id == media2.Id && m.Name == "Media2");
        Assert.Contains(filteredMedia, m => m.Id == media3.Id && m.Name == "Media3");
    }

    /// <summary>
    /// Tests RBAC filtering for Tag entities with "my" tenant permission using join table relationships.
    /// Validates that the dynamic "my" tenant resolution works correctly with join table logic.
    /// </summary>
    [Fact]
    public async Task BuildFilterExpression_ForTags_WithMyTenant_ShouldReturnUserTenantTags()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create tags with join table relationships
        var tag1 = CreateTagWithTenant("MyTag1", tenant1.Id);
        var tag2 = CreateTagWithTenant("MyTag2", tenant1.Id);
        var tag3 = CreateTagWithTenant("OtherTag", tenant2.Id);

        // Give user "my" tenant permission - should resolve to tenant 100
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<ITagsViewPermission>();
        var claimValue = claimsProvider.GetMyTenantAllowClaim(permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        // ACT
        var filter = _permissionFilterBuilder.BuildFilterExpression<Tag, ITagsViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for "my" tenant join table filtering
        await VerifyGeneratedSql<Tag, ITagsViewPermission>("TagJoinTableMyTenant");

        var filteredTags = await _dbContext.Tags.Where(filter).ToListAsync();

        // ASSERT - Should only get tags from user's tenant
        Assert.Equal(2, filteredTags.Count);
        Assert.Contains(filteredTags, t => t.Id == tag1.Id && t.Name == "MyTag1");
        Assert.Contains(filteredTags, t => t.Id == tag2.Id && t.Name == "MyTag2");
        Assert.DoesNotContain(filteredTags, t => t.Id == tag3.Id); // Different tenant
    }


    /// <summary>
    /// Tests RBAC filtering for Script entities using join table tenant relationships.
    /// Validates that the ScriptSqlTranslatableTenantRelationshipProvider correctly generates
    /// SQL filters that check the TenantScript join table for owned relationships.
    /// </summary>
    [Fact]
    public async Task BuildFilterExpression_ForScripts_WithSpecificTenant_ShouldReturnScriptsForTenant()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create scripts with join table relationships
        var script1 = CreateScriptWithTenant("Script1", tenant1.Id);
        var script2 = CreateScriptWithTenant("Script2", tenant1.Id);
        var script3 = CreateScriptWithTenant("Script3", tenant2.Id);

        // Give user permission to access scripts in tenant1
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<IScriptsViewPermission>();
        var claimValue = claimsProvider.GetTenantAllowClaim(tenant1.Id, permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        // ACT
        var filter = _permissionFilterBuilder.BuildFilterExpression<Script, IScriptsViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for join table filtering
        await VerifyGeneratedSql<Script, IScriptsViewPermission>("ScriptJoinTableSingleTenant");

        var filteredScripts = await _dbContext.Scripts.Where(filter).ToListAsync();

        // ASSERT - Should only get scripts from tenant1
        Assert.Equal(2, filteredScripts.Count);
        Assert.Contains(filteredScripts, s => s.Id == script1.Id && s.Name == "Script1");
        Assert.Contains(filteredScripts, s => s.Id == script2.Id && s.Name == "Script2");
        Assert.DoesNotContain(filteredScripts, s => s.Id == script3.Id); // Different tenant
    }

    /// <summary>
    /// Tests RBAC filtering for Tag entities with multiple tenant associations.
    /// This is the core test for many-to-many tenant relationships - where a single tag
    /// can be associated with multiple tenants through the join table.
    /// </summary>
    [Fact]
    public async Task BuildFilterExpression_TagWithMultipleTenants_ShouldIncludeWhenUserHasAccessToAnyTenant()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var tenant3 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create tags with multiple tenant associations
        var multiTenantTag1 = CreateTagWithMultipleTenants("MultiTag1", tenant1.Id, tenant2.Id, tenant3.Id);
        var multiTenantTag2 = CreateTagWithMultipleTenants("MultiTag2", tenant2.Id, tenant3.Id);
        var singleTenantTag = CreateTagWithTenant("SingleTag", tenant1.Id);
        var noAccessTag = CreateTagWithTenant("NoAccessTag", tenant3.Id);

        // Give user permission to access tags in tenant1 and tenant2 only
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<ITagsViewPermission>();
        var claimValue1 = claimsProvider.GetTenantAllowClaim(tenant1.Id, permission);
        var claimValue2 = claimsProvider.GetTenantAllowClaim(tenant2.Id, permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue1));
        identity.AddClaim(new Claim(permission.TenantClaimType, claimValue2));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        // ACT
        var filter = _permissionFilterBuilder.BuildFilterExpression<Tag, ITagsViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for multiple tenant associations
        await VerifyGeneratedSql<Tag, ITagsViewPermission>("TagJoinTableMultipleTenants");

        var filteredTags = await _dbContext.Tags.Where(filter).ToListAsync();

        // ASSERT - Should get tags where user has access to at least one associated tenant
        Assert.Equal(3, filteredTags.Count);
        Assert.Contains(filteredTags, t => t.Id == multiTenantTag1.Id); // Has tenant1,2,3 - user has access to 1,2
        Assert.Contains(filteredTags, t => t.Id == multiTenantTag2.Id); // Has tenant2,3 - user has access to 2
        Assert.Contains(filteredTags, t => t.Id == singleTenantTag.Id); // Has tenant1 - user has access to 1
        Assert.DoesNotContain(filteredTags, t => t.Id == noAccessTag.Id); // Has tenant3 only - user has no access
    }

    /// <summary>
    /// Tests RBAC filtering for Media entities with multiple tenant associations and deny scenarios.
    /// Validates that deny rules work correctly when entities span multiple tenants.
    /// </summary>
    [Fact]
    public async Task BuildFilterExpression_MediaWithMultipleTenants_DenyScenarios()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var tenant3 = CreateTenant();
        var tenant4 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create media with various tenant associations
        var media1 = CreateMediaWithMultipleTenants("Media1", "file1.jpg", tenant1.Id, tenant2.Id);
        var media2 = CreateMediaWithMultipleTenants("Media2", "file2.png", tenant2.Id, tenant3.Id);
        var media3 = CreateMediaWithMultipleTenants("Media3", "file3.gif", tenant3.Id, tenant4.Id);
        var media4 = CreateMediaWithTenant("Media4", "file4.jpg", tenant1.Id);

        // Give user wildcard access but deny specific tenants
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<IMediaViewPermission>();
        var wildcardClaimValue = claimsProvider.GetAllTenantAllowClaim(permission);
        var tenant2DenyClaimValue = claimsProvider.GetTenantDenyClaim(tenant2.Id, permission);
        var tenant3DenyClaimValue = claimsProvider.GetTenantDenyClaim(tenant3.Id, permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, wildcardClaimValue));
        identity.AddClaim(new Claim(permission.TenantClaimType, tenant2DenyClaimValue));
        identity.AddClaim(new Claim(permission.TenantClaimType, tenant3DenyClaimValue));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        // ACT
        var filter = _permissionFilterBuilder.BuildFilterExpression<Media, IMediaViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for multi-tenant deny scenarios
        await VerifyGeneratedSql<Media, IMediaViewPermission>("MediaJoinTableMultiTenantDeny");

        var filteredMedia = await _dbContext.Media.Where(filter).ToListAsync();

        // ASSERT - Current deny logic excludes media with ANY denied tenant association (conservative approach)
        Assert.Single(filteredMedia);
        Assert.DoesNotContain(filteredMedia, m => m.Id == media1.Id); // Has tenant1,2 - excluded because tenant2 is denied
        Assert.DoesNotContain(filteredMedia, m => m.Id == media2.Id); // Has tenant2,3 - both denied, excluded
        Assert.DoesNotContain(filteredMedia, m => m.Id == media3.Id); // Has tenant3,4 - excluded because tenant3 is denied
        Assert.Contains(filteredMedia, m => m.Id == media4.Id); // Has only tenant1 - allowed, no denied tenant associations
    }

    /// <summary>
    /// Tests RBAC filtering for Script entities with resource-specific permissions on multi-tenant entities.
    /// Validates that resource-specific allow/deny works correctly when entities span multiple tenants.
    /// </summary>
    [Fact]
    public async Task BuildFilterExpression_ScriptWithMultipleTenants_ResourceSpecificPermissions()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var tenant3 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create scripts with multiple tenant associations
        var script1 = CreateScriptWithMultipleTenants("Script1", tenant1.Id, tenant2.Id);
        var script2 = CreateScriptWithMultipleTenants("Script2", tenant2.Id, tenant3.Id);
        var script3 = CreateScriptWithTenant("Script3", tenant1.Id);
        var script4 = CreateScriptWithTenant("Script4", tenant3.Id);

        // Give user resource-specific permissions (no tenant-level permissions)
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<IScriptsViewPermission>();
        var resourceAllowClaim1 = claimsProvider.GetResourceAllowClaim(script1.Id.ToString(), permission);
        var resourceAllowClaim2 = claimsProvider.GetResourceAllowClaim(script2.Id.ToString(), permission);
        var resourceAllowClaim4 = claimsProvider.GetResourceAllowClaim(script4.Id.ToString(), permission);
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceAllowClaim1));
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceAllowClaim2));
        identity.AddClaim(new Claim(permission.ResourceClaimType, resourceAllowClaim4));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        // ACT
        var filter = _permissionFilterBuilder.BuildFilterExpression<Script, IScriptsViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for resource-specific permissions on multi-tenant entities
        await VerifyGeneratedSql<Script, IScriptsViewPermission>("ScriptJoinTableMultiTenantResourceSpecific");

        var filteredScripts = await _dbContext.Scripts.Where(filter).ToListAsync();

        // ASSERT - Should get only specifically allowed scripts regardless of their tenant associations
        Assert.Equal(3, filteredScripts.Count);
        Assert.Contains(filteredScripts, s => s.Id == script1.Id); // Resource-specific allow
        Assert.Contains(filteredScripts, s => s.Id == script2.Id); // Resource-specific allow
        Assert.Contains(filteredScripts, s => s.Id == script4.Id); // Resource-specific allow
        Assert.DoesNotContain(filteredScripts, s => s.Id == script3.Id); // No permission
    }

    /// <summary>
    /// Tests complex mixed permissions with multiple tenant entities - tenant allows plus resource denies.
    /// This validates the most complex scenario where entities span multiple tenants and we have
    /// both tenant-level and resource-level permissions in play.
    /// </summary>
    [Fact]
    public async Task BuildFilterExpression_TagWithMultipleTenants_MixedTenantAndResourcePermissions()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var tenant3 = CreateTenant();
        var tenant4 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create tags with various tenant associations
        var tag1 = CreateTagWithMultipleTenants("Tag1", tenant1.Id, tenant2.Id); // Allowed via tenant1,2
        var tag2 = CreateTagWithMultipleTenants("Tag2", tenant1.Id, tenant3.Id); // Allowed via tenant1, but resource denied
        var tag3 = CreateTagWithMultipleTenants("Tag3", tenant2.Id, tenant4.Id); // Allowed via tenant2
        var tag4 = CreateTagWithTenant("Tag4", tenant3.Id); // No tenant access, but resource allowed
        var tag5 = CreateTagWithTenant("Tag5", tenant4.Id); // No access at all

        // Complex permission setup: allow tenant1,2 + resource deny tag2 + resource allow tag4
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<ITagsViewPermission>();
        var tenant1AllowClaim = claimsProvider.GetTenantAllowClaim(tenant1.Id, permission);
        var tenant2AllowClaim = claimsProvider.GetTenantAllowClaim(tenant2.Id, permission);
        var tag2DenyClaim = claimsProvider.GetResourceDenyClaim(tag2.Id.ToString(), permission);
        var tag4AllowClaim = claimsProvider.GetResourceAllowClaim(tag4.Id.ToString(), permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, tenant1AllowClaim));
        identity.AddClaim(new Claim(permission.TenantClaimType, tenant2AllowClaim));
        identity.AddClaim(new Claim(permission.ResourceClaimType, tag2DenyClaim));
        identity.AddClaim(new Claim(permission.ResourceClaimType, tag4AllowClaim));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        // ACT
        var filter = _permissionFilterBuilder.BuildFilterExpression<Tag, ITagsViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for complex mixed permissions
        await VerifyGeneratedSql<Tag, ITagsViewPermission>("TagJoinTableMixedTenantAndResourcePermissions");

        var filteredTags = await _dbContext.Tags.Where(filter).ToListAsync();

        // ASSERT - Complex permission evaluation
        Assert.Equal(3, filteredTags.Count);
        Assert.Contains(filteredTags, t => t.Id == tag1.Id); // Allowed via tenant1,2
        Assert.DoesNotContain(filteredTags, t => t.Id == tag2.Id); // Resource denied despite tenant1 access
        Assert.Contains(filteredTags, t => t.Id == tag3.Id); // Allowed via tenant2
        Assert.Contains(filteredTags, t => t.Id == tag4.Id); // Resource allowed despite no tenant access
        Assert.DoesNotContain(filteredTags, t => t.Id == tag5.Id); // No access
    }

    /// <summary>
    /// Tests edge case where entities have overlapping tenant associations with different permission levels.
    /// This validates the SQL generation for complex IN clauses with multiple tenant relationships.
    /// </summary>
    [Fact]
    public async Task BuildFilterExpression_MediaWithOverlappingTenants_ComplexTenantPermissions()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var tenant3 = CreateTenant();
        var tenant4 = CreateTenant();
        var tenant5 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create media with overlapping tenant associations to test complex scenarios
        var media1 = CreateMediaWithMultipleTenants("Media1", "file1.jpg", tenant1.Id);
        var media2 = CreateMediaWithMultipleTenants("Media2", "file2.png", tenant1.Id, tenant2.Id);
        var media3 = CreateMediaWithMultipleTenants("Media3", "file3.gif", tenant2.Id, tenant3.Id);
        var media4 = CreateMediaWithMultipleTenants("Media4", "file4.jpg", tenant3.Id, tenant4.Id, tenant5.Id);
        var media5 = CreateMediaWithMultipleTenants("Media5", "file5.png", tenant4.Id, tenant5.Id);
        var media6 = CreateMediaWithTenant("Media6", "file6.gif", tenant5.Id);

        // User has access to tenant1, tenant2, and tenant4 (non-contiguous access pattern)
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var permission = _serviceProvider.GetRequiredService<IMediaViewPermission>();
        var tenant1AllowClaim = claimsProvider.GetTenantAllowClaim(tenant1.Id, permission);
        var tenant2AllowClaim = claimsProvider.GetTenantAllowClaim(tenant2.Id, permission);
        var tenant4AllowClaim = claimsProvider.GetTenantAllowClaim(tenant4.Id, permission);
        identity.AddClaim(new Claim(permission.TenantClaimType, tenant1AllowClaim));
        identity.AddClaim(new Claim(permission.TenantClaimType, tenant2AllowClaim));
        identity.AddClaim(new Claim(permission.TenantClaimType, tenant4AllowClaim));
        var claimsPrincipal = new ClaimsPrincipal(identity);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(claimsPrincipal);
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        // ACT
        var filter = _permissionFilterBuilder.BuildFilterExpression<Media, IMediaViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query for overlapping tenant scenarios
        await VerifyGeneratedSql<Media, IMediaViewPermission>("MediaJoinTableOverlappingTenants");

        var filteredMedia = await _dbContext.Media.Where(filter).ToListAsync();

        // ASSERT - Should get media where user has access to at least one associated tenant
        Assert.Equal(5, filteredMedia.Count);
        Assert.Contains(filteredMedia, m => m.Id == media1.Id); // tenant1 - allowed
        Assert.Contains(filteredMedia, m => m.Id == media2.Id); // tenant1,2 - both allowed
        Assert.Contains(filteredMedia, m => m.Id == media3.Id); // tenant2,3 - tenant2 allowed
        Assert.Contains(filteredMedia, m => m.Id == media4.Id); // tenant3,4,5 - tenant4 allowed
        Assert.Contains(filteredMedia, m => m.Id == media5.Id); // tenant4,5 - tenant4 allowed
        Assert.DoesNotContain(filteredMedia, m => m.Id == media6.Id); // tenant5 only - not allowed
    }

    /// <summary>
    /// Tests that manage permissions correctly filter by Relationship = Owned (0) while view permissions include all relationships.
    /// This validates the core difference between view and manage permissions in the relationship filtering logic.
    /// </summary>
    [Fact]
    public async Task BuildFilterExpression_ForTags_WithManagePermission_ShouldIncludeRelationshipFilter()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create tags with different relationships using existing base class method
        var ownedTag = CreateTagWithTenant("OwnedTag", tenant1.Id);
        var assignedTag = CreateTagWithTenant("AssignedTag", tenant1.Id);

        // Update the assigned tag to have Relationship = Assigned (1)
        var assignedTagAuth = await _dbContext.TenantTagAuthorizations
            .FirstAsync(tta => tta.TagId == assignedTag.Id && tta.TenantId == tenant1.Id);
        assignedTagAuth.Relationship = Relationship.Assigned;
        await _dbContext.SaveChangesAsync();

        // Setup user with manage permission for tenant1
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var managePermission = _serviceProvider.GetRequiredService<ITagsManagePermission>();
        var viewPermission = _serviceProvider.GetRequiredService<ITagsViewPermission>();
        var manageClaimValue = claimsProvider.GetTenantAllowClaim(tenant1.Id, managePermission);
        var viewClaimValue = claimsProvider.GetTenantAllowClaim(tenant1.Id, viewPermission);

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(new ClaimsPrincipal(identity));
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        // TEST 1: Manage permissions should only return owned tags
        identity.AddClaim(new Claim(managePermission.TenantClaimType, manageClaimValue));
        var manageFilter = _permissionFilterBuilder.BuildFilterExpression<Tag, ITagsManagePermission>();

        // SNAPSHOT TEST: Verify the generated SQL query includes Relationship = 0
        await VerifyGeneratedSql<Tag, ITagsManagePermission>("TagJoinTableManagePermissionWithRelationshipFilter");

        var managedTags = await _dbContext.Tags.Where(manageFilter).ToListAsync();

        // ASSERT - Should only get the owned tag
        Assert.Single(managedTags);
        Assert.Equal(ownedTag.Id, managedTags[0].Id);
        Assert.Equal("OwnedTag", managedTags[0].Name);

        // TEST 2: View permissions should return all tags (owned and assigned)
        identity.RemoveClaim(identity.Claims.First(c => c.Type == managePermission.TenantClaimType));
        identity.AddClaim(new Claim(viewPermission.TenantClaimType, viewClaimValue));
        var viewFilter = _permissionFilterBuilder.BuildFilterExpression<Tag, ITagsViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query does NOT include Relationship filter
        await VerifyGeneratedSql<Tag, ITagsViewPermission>("TagJoinTableViewPermissionWithoutRelationshipFilter");

        var viewedTags = await _dbContext.Tags.Where(viewFilter).ToListAsync();

        // ASSERT - Should get both tags (owned and assigned)
        Assert.Equal(2, viewedTags.Count);
        Assert.Contains(viewedTags, t => t.Id == ownedTag.Id && t.Name == "OwnedTag");
        Assert.Contains(viewedTags, t => t.Id == assignedTag.Id && t.Name == "AssignedTag");
    }

    /// <summary>
    /// Tests RBAC filtering for Tag entities with multiple tenant permissions.
    /// Validates that the SQL query correctly generates IN clauses for multiple tenants
    /// with both manage and view permissions.
    /// </summary>
    [Fact]
    public async Task BuildFilterExpression_ForTags_WithMultipleTenants_ShouldGenerateInClause()
    {
        // ARRANGE
        var tenant1 = CreateTenant();
        var tenant2 = CreateTenant();
        var tenant3 = CreateTenant();
        var tenant4 = CreateTenant();
        var person = CreatePerson(tenant1.Id, "<EMAIL>", "Test", "User");
        var user = CreateUser(tenant1.Id, person.Id);

        // Create tags across multiple tenants
        var tag1 = CreateTagWithTenant("Tag1", tenant1.Id);
        var tag2 = CreateTagWithTenant("Tag2", tenant2.Id);
        var tag3 = CreateTagWithTenant("Tag3", tenant3.Id);
        var tag4 = CreateTagWithTenant("Tag4", tenant4.Id);

        // Update one tag to have Relationship = Assigned (1) for manage permission testing
        var assignedTagAuth = await _dbContext.TenantTagAuthorizations
            .FirstAsync(tta => tta.TagId == tag2.Id && tta.TenantId == tenant2.Id);
        assignedTagAuth.Relationship = Relationship.Assigned;
        await _dbContext.SaveChangesAsync();

        // Setup user with permissions for multiple tenants
        var identity = new ClaimsIdentity("test");
        var claimsProvider = _serviceProvider.GetRequiredService<IResourceClaimProvider>();
        var managePermission = _serviceProvider.GetRequiredService<ITagsManagePermission>();
        var viewPermission = _serviceProvider.GetRequiredService<ITagsViewPermission>();

        var authUser = GetAuthUser(user);
        _mockUserService.Setup(s => s.GetCurrentPrincipal(false)).Returns(new ClaimsPrincipal(identity));
        _mockUserService.Setup(s => s.TryGetCurrentUser(out authUser)).Returns(true);

        // TEST 1: Manage permissions for multiple tenants (should include Relationship = 0)
        var manageClaimValue1 = claimsProvider.GetTenantAllowClaim(tenant1.Id, managePermission);
        var manageClaimValue2 = claimsProvider.GetTenantAllowClaim(tenant2.Id, managePermission);
        var manageClaimValue3 = claimsProvider.GetTenantAllowClaim(tenant3.Id, managePermission);
        identity.AddClaim(new Claim(managePermission.TenantClaimType, manageClaimValue1));
        identity.AddClaim(new Claim(managePermission.TenantClaimType, manageClaimValue2));
        identity.AddClaim(new Claim(managePermission.TenantClaimType, manageClaimValue3));

        var manageFilter = _permissionFilterBuilder.BuildFilterExpression<Tag, ITagsManagePermission>();

        // SNAPSHOT TEST: Verify the generated SQL query includes IN clause and Relationship = 0
        await VerifyGeneratedSql<Tag, ITagsManagePermission>("TagJoinTableManagePermissionMultipleTenantsWithRelationshipFilter");

        var managedTags = await _dbContext.Tags.Where(manageFilter).ToListAsync();

        // ASSERT - Should only get owned tags from tenants 1 and 3 (tag2 is assigned, so excluded)
        Assert.Equal(2, managedTags.Count);
        Assert.Contains(managedTags, t => t.Id == tag1.Id && t.Name == "Tag1");
        Assert.Contains(managedTags, t => t.Id == tag3.Id && t.Name == "Tag3");
        Assert.DoesNotContain(managedTags, t => t.Id == tag2.Id); // Assigned relationship, should be excluded
        Assert.DoesNotContain(managedTags, t => t.Id == tag4.Id); // No permission for tenant4

        // TEST 2: View permissions for multiple tenants (should NOT include Relationship filter)
        // Clear previous claims
        foreach (var claim in identity.Claims.Where(c => c.Type == managePermission.TenantClaimType).ToList())
        {
            identity.RemoveClaim(claim);
        }

        var viewClaimValue1 = claimsProvider.GetTenantAllowClaim(tenant1.Id, viewPermission);
        var viewClaimValue2 = claimsProvider.GetTenantAllowClaim(tenant2.Id, viewPermission);
        var viewClaimValue3 = claimsProvider.GetTenantAllowClaim(tenant3.Id, viewPermission);
        identity.AddClaim(new Claim(viewPermission.TenantClaimType, viewClaimValue1));
        identity.AddClaim(new Claim(viewPermission.TenantClaimType, viewClaimValue2));
        identity.AddClaim(new Claim(viewPermission.TenantClaimType, viewClaimValue3));

        var viewFilter = _permissionFilterBuilder.BuildFilterExpression<Tag, ITagsViewPermission>();

        // SNAPSHOT TEST: Verify the generated SQL query includes IN clause but NO Relationship filter
        await VerifyGeneratedSql<Tag, ITagsViewPermission>("TagJoinTableViewPermissionMultipleTenantsWithoutRelationshipFilter");

        var viewedTags = await _dbContext.Tags.Where(viewFilter).ToListAsync();

        // ASSERT - Should get all tags from tenants 1, 2, and 3 (regardless of relationship)
        Assert.Equal(3, viewedTags.Count);
        Assert.Contains(viewedTags, t => t.Id == tag1.Id && t.Name == "Tag1");
        Assert.Contains(viewedTags, t => t.Id == tag2.Id && t.Name == "Tag2"); // Assigned relationship, but view permission includes it
        Assert.Contains(viewedTags, t => t.Id == tag3.Id && t.Name == "Tag3");
        Assert.DoesNotContain(viewedTags, t => t.Id == tag4.Id); // No permission for tenant4
    }
}
