function HA {
    param ($p)
    try {
      [BitConverter]::ToString([System.Security.Cryptography.HashAlgorithm]::Create('SHA256').ComputeHash([System.IO.File]::ReadAllBytes($p))) -replace '-', '';
    }
    catch {""}
}
function WD {
    param ($u, $f)
    "WD $u => $f"
    $w = New-Object System.Net.WebClient;
    $w.UseDefaultCredentials = $true;
    $w.Proxy = [System.Net.WebRequest]::GetSystemWebProxy();
    $w.DownloadFile($u, $f);
}
try {
    $ProgressPreference = 'SilentlyContinue';
    $IB = "ImmyBot";
    $IARGS = $input | % { $_ };
    $AC = $IARGS | measure | select -Expand Count;
    if ($AC -eq 1) {
        $Arguments = $IARGS | % { $_.Split(' ') };
    }
    elseif ($AC -gt 1) {
        $Arguments = $IARGS;
    }
    $Url = $Arguments[0];
    $Hash = $Arguments[1];
    $ISP = [System.Environment]::ExpandEnvironmentVariables($Arguments[2]);
    $BADDR = $Arguments[3];
    $SESID = $Arguments[4];
    $PAGTID = $Arguments[5];
    try {
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    }
    catch {
        [System.Net.ServicePointManager]::SecurityProtocol = 'Tls';
    }
    $GMN = "Global\$IB-$(($ISP -split '\\')[-1])";
    $GM = New-Object System.Threading.Mutex($false, $GMN);
    $GMWR = $false;
    $NIB = "Notify $IB";
    $MWT = Measure-Command {
        try {
            $GMWR = $GM.WaitOne(30000);
        }
        catch [System.Threading.AbandonedMutexException] {
            "$_ $NIB"
            $GMWR = $true;
        }
    }
    $tMs = $MWT.TotalMilliseconds;
    if (-not $GMWR) {
        "$GMN failed after $tMs ms; $NIB";
        return;
    }
    $E = "ephemeral";
    try {
        "$GMN got in $tMs ms";
        $A = "Agent";
        $FileName = "$IB.$A.Ephemeral.exe";
        $FilePath = Join-Path $ISP $FileName;
        mkdir -Force $ISP | Out-Null;
        $EXF = $FilePath.Replace(".zip", "");
        if (Test-Path $EXF) {
            $AHash = HA $EXF;
        }
        if ($AHash -ne $Hash) {
            try {
                ps | ? {
                    $_.Path -eq $FilePath -and $_.StartTime -lt (Get-Date).AddMinutes(-5)
                } | kill -Force
            }
            catch {
                $_
            }
            "DL $E";
            WD $Url $FilePath;
            if ($FilePath.EndsWith(".zip")) {
                $s = New-Object -com shell.application;
                $s.namespace($ISP).Copyhere($s.namespace($FilePath).items(), 0x14)
                rm $FilePath -Force | Out-Null
            }
        }

        $ArgumentList = "$E run --ImmyScriptPath $ISP --BackendAddress $BADDR --SessionId $SESID --ProviderAgentId $PAGTID";
        $Command = "$EXF $ArgumentList";
        "Running $Command";
        start $EXF $ArgumentList -passthru;
    }
    finally {
        "Released";
        $GM.Close();
    }
}
catch { $_ }
