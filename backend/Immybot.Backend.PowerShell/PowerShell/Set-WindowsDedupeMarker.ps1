if (!(Test-Path HKLM:\SOFTWARE\ImmenseNetworks)) {
  New-Item -Path HKLM:\SOFTWARE\ImmenseNetworks -ItemType Directory | Out-Null
}
if (!(Test-Path HKLM:\SOFTWARE\ImmenseNetworks\ImmyBot)) {
  New-Item HKLM:\SOFTWARE\ImmenseNetworks\ImmyBot -ItemType Directory | Out-Null
}
try {
  $prevMarker = Get-ItemProperty -ErrorAction Stop HKLM:\SOFTWARE\ImmenseNetworks\ImmyBot -Name ImmyBotDedupeMarker | % { $_.ImmyBotDedupeMarker }
  Remove-ItemProperty -Path HKLM:\SOFTWARE\ImmenseNetworks\ImmyBot -Name ImmyBotDedupeMarker | Out-Null
}
catch { }
$marker = [Guid]::NewGuid();
New-ItemProperty -Path HKLM:\SOFTWARE\ImmenseNetworks\ImmyBot -Name ImmyBotDedupeMarker -Value "$marker" | Out-Null
Write-Host "$marker"
