if (!(Test-Path HKLM:\SOFTWARE\ImmenseNetworks)) {
  New-Item -Path HKLM:\SOFTWARE\ImmenseNetworks -ItemType Directory | Out-Null
}
if (!(Test-Path HKLM:\SOFTWARE\ImmenseNetworks\ImmyBot)) {
  New-Item HKLM:\SOFTWARE\ImmenseNetworks\ImmyBot -ItemType Directory | Out-Null
}
try {
  $prevId = Get-ItemProperty -ErrorAction Stop HKLM:\SOFTWARE\ImmenseNetworks\ImmyBot -Name ImmyBotDeviceId | % { $_.ImmyBotDeviceId }
  Remove-ItemProperty -Path HKLM:\SOFTWARE\ImmenseNetworks\ImmyBot -Name ImmyBotDeviceId | Out-Null
}
catch { }
$deviceId = [Guid]::NewGuid();
New-ItemProperty -Path HKLM:\SOFTWARE\ImmenseNetworks\ImmyBot -Name ImmyBotDeviceId -Value "$deviceId" | Out-Null
Write-Host "$deviceId"
