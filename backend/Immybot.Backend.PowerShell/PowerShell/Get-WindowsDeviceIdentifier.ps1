Function IsGuid {
  param([string]$PossibleGuid)
  try {
    $Guid = New-Object "Guid" -ArgumentList $PossibleGuid
    return $true;
  }
  catch {
    return $false
  }
}
Function Get-AzureTenantId {
  $RegPath = "HKLM:/SYSTEM/CurrentControlSet/Control/CloudDomainJoin/JoinInfo"
  if ((Test-Path $RegPath)) {
    $subKey = Get-Item -Path $RegPath
    $subKey.GetSubKeyNames() | % { $subKey.OpenSubKey($_).GetValue("TenantId"); }
  }
  else {
    return $null
  }
}

if (!(Get-Command "gwmi" -ErrorAction SilentlyContinue)) {
  Set-Alias -Name gwmi -Value Get-CimInstance
}
$CS = gwmi Win32_ComputerSystem
Function Is-Sandbox {
  return ( (IsGuid $CS.DNSHostName) -and $CS.DNSHostName.Substring(0, 15) -like $CS.Name -and $CS.Manufacturer -eq "Microsoft Corporation" -and $CS.Model -eq "Virtual Machine")
}
$Domain = ""
if ($CS.Domain) {
  $Domain = $CS.Domain
}
$AzureTenantId = Get-AzureTenantId
$InstallDate = $null
$IsSandbox = Is-Sandbox -erroraction 'silentlycontinue'
if ($null -eq $isSandbox) { $isSandbox = $false }
$MachineID = Get-ItemProperty 'HKLM:\SOFTWARE\Microsoft\SQMClient' -ErrorAction SilentlyContinue -Name MachineID | ForEach-Object { [string][System.Guid]$_.MachineID }
if ($null -eq $machineID) { $machineID = '' }
try {
  $OSInfo = gwmi Win32_OperatingSystem -Property Caption, InstallDate
  $ChassisTypes = @()
  $ChassisTypes += gwmi Win32_SystemEnclosure -Property ChassisTypes | ForEach-Object { $_.ChassisTypes }
  $Caption = $OSInfo.Caption
  $installDate = ([WMI]'').ConvertToDateTime((gwmi Win32_OperatingSystem | select InstallDate).InstallDate)
}
catch {
  Write-Warning "Error $_"
}

$DeviceId = Get-ItemProperty 'HKLM:\SOFTWARE\ImmenseNetworks\ImmyBot' -ErrorAction SilentlyContinue -Name ImmyBotDeviceId | ForEach-Object { $_.ImmyBotDeviceId }
if (!$DeviceId) {
  $DeviceId = Get-CimInstance -ErrorAction SilentlyContinue Win32_ComputerSystemProduct -Property UUID | ForEach-Object { $_.UUID }
}
if (!$DeviceId) {
  $DeviceId = Get-WmiObject -ErrorAction SilentlyContinue Win32_ComputerSystemProduct -Property UUID | ForEach-Object { $_.UUID }
}

New-Object PSObject -Property @{
  ComputerName  = $env:ComputerName
  InstallDate   = $InstallDate
  DeviceId      = $DeviceId
  IsSandbox     = $IsSandbox
  MachineId     = $MachineID
  Domain        = $Domain
  AzureTenantId = $AzureTenantId
  OSCaption     = $Caption
  ChassisTypes  = $ChassisTypes
}
