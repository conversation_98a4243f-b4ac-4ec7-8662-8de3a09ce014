This directory contains PowerShell scripts, modules, formatting, and types used throughout ImmyBot's Backend.


## Format files

~~~PipeScript{
    # This regenerates a markdown table, so that the markdown stays up to date as files change.
    [PSCustomObject]@{
        Table = Get-ChildItem -Filter *format.ps1xml -Recurse | 
            Select Name, @{
                Name = 'Link'
                Expression = { "$($_.Fullname.Substring("$pwd".Length))" -replace '\\', '/'}
            } 
    }
}
~~~

## Type files

~~~PipeScript{
    # This regenerates a markdown table, so that the markdown stays up to date as files change.
    [PSCustomObject]@{
        Table = Get-ChildItem -Filter *types.ps1xml -Recurse |             
            Select Name, @{
                Name = 'Link'
                Expression = { "$($_.Fullname.Substring("$pwd".Length))" -replace '\\', '/'}
            } 
    }
}
~~~

## Scripts

~~~PipeScript{
    # This regenerates a markdown table, so that the markdown stays up to date as files change.
    [PSCustomObject]@{
        Table = Get-ChildItem -Filter *.ps1 | 
            Select Name, @{
                Name = 'Link'
                Expression = { $_.Name }
            } 
    }
}
~~~
