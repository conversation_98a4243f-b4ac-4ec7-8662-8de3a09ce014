param([switch]$WatchForFileChanges)
$psDirectory = Resolve-Path "$PSScriptRoot\PowerShell"
$startEphemeralScriptName = 'Start-EphemeralAgent.ps1'

function EnsurePSMinifierModule {
  if (Get-Module -Name PSMinifier) {
    Write-Host "PSMinifier module is already imported and ready to use" -ForegroundColor Green
  }
  elseif (Get-Module -ListAvailable -Name PSMinifier) {
    Write-Host "PSMinifier module found, importing..." -ForegroundColor Cyan
    Import-Module -Name PSMinifier
  }
  else {
    Write-Host "PSMinifier module not found, installing..." -ForegroundColor Yellow
    # this is the most expensive operation in the script, so only do it if we need to
    Install-Module -Name PSMinifier -Force
  }
}

function MinifyScripts {
  EnsurePSMinifierModule
  $commands = Get-ChildItem -LiteralPath $psDirectory -Include *.ps1,*.ps1.template -Recurse
  $commandsToMinify = $commands |
  Where-Object -FilterScript {
    if ($_.Name -like '*.min.*ps1' -or $_.Name -like '*.template') { return; }
    $fileBaseNameMinified = "$($_.BaseName).min"
    if (($commands | Where-Object { $_.BaseName -eq $fileBaseNameMinified } | measure).Count -gt 0) {
      return;
    }
    return $_
  } | Get-Command { $_.FullName }
  $minifiedCommands =
  @($commandsToMinify |
    Compress-ScriptBlock -OutputPath {
      $_.Source -replace '\.ps1$', '.min.ps1'
    } -PassThru)

  "Minified $($minifiedCommands.Length) Commands ($(($commands | measure).Count - ($commandsToMinify | measure).Count) scripts are already minified)" | Out-Host
  $minifiedCommands | Out-Host

  "Minification Summary:" | Out-Host
  for ($n = 0 ; $n -lt $commandsToMinify.Length; $n++) {
    $originalSize = ([IO.FileInfo]$($commandsToMinify[$n].Source)).Length
    $minifiedSize = $minifiedCommands[$n].Length
    $minifiedPercent = $minifiedCommands[$n].Length / $originalSize
    "$($commandsToMinify[$n].name) [$originalSize bytes] -> $($minifiedCommands[$n].Name) - $([Math]::Round($minifiedPercent * 100, 2))% [$minifiedSize bytes]" | Out-Host

    if ($commandsToMinify[$n].name -eq $startEphemeralScriptName) {
      #The actual max is 8191, but we pipe the script args in + cmd /c overhead, so this actually needs to be ~470 chars shorter to be safe
      $InputArgsAproxLength = 472
      $MaxLength = 8192 - $InputArgsAproxLength

      $rawContents = gc -raw $minifiedCommands[$n].FullName
      $base64Unicode = [Convert]::ToBase64String([Text.Encoding]::Unicode.Getbytes($rawContents))
      $overage = $base64Unicode.Length - $MaxLength

      if ($overage -gt 0) {
        $err = "$startEphemeralScriptName exceeds the maxiumum size of $MaxLength by $overage bytes when Unicode Base64'd"
        $err += ", ~$($overage / 4) characters must be trimmed from original script. Base64Unicode size: $($base64Unicode.Length) bytes"
        throw $err
      }
      Write-Information "$startEphemeralScriptName is under the max by $($overage * -1) bytes. Nice job."
    }
  }
}

if (-not $WatchForFileChanges) {
  MinifyScripts;
  return;
}
try {

  # create a filesystemwatcher object
  $watcher = New-Object IO.FileSystemWatcher $psDirectory, "*" -Property @{
    IncludeSubdirectories = $true
  }

  Write-Host "Monitoring $psDirectory for .ps1 changes. Press CTRL+C to stop."
  do {
    # wait for changes for the specified timeout since PoSH cannot be stopped while waiting, so we need to break preodically
    $result = $watcher.WaitForChanged([System.IO.WatcherChangeTypes]::All, 500)
    # if there was a timeout, continue monitoring:
    if ($result.TimedOut) { continue }
    try {
      Get-ChildItem -LiteralPath $psDirectory -Filter *.min.ps1 | Remove-Item -Force
      MinifyScripts;
    }
    catch {
      Write-Error $_
    }
    Write-Host "`r`n👀 Still looking for changes. Press CTRL+C to stop."
    # the loop runs forever until you hit CTRL+C
  } while ($true)
}
finally {
  # release the watcher and free its memory:
  $watcher.Dispose()
  Write-Host 'Goodbye.'
}
