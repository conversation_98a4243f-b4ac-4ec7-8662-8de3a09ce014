This directory contains PowerShell scripts, modules, formatting, and types used throughout ImmyBot's Backend.


## Format files


|Name                                                                                                                                       |
|-------------------------------------------------------------------------------------------------------------------------------------------|
|[PSReadLine.format.ps1xml](/EditorServicesExtracted/PSReadLine/2.2.6/PSReadLine.format.ps1xml)                                             |
|[ScriptAnalyzer.format.ps1xml](/EditorServicesExtracted/PSScriptAnalyzer/1.21.0/ScriptAnalyzer.format.ps1xml)                              |
|[DateTime.format.ps1xml](/Formats/DateTime.format.ps1xml)                                                                                  |
|[FileSystem.format.ps1xml](/Formats/FileSystem.format.ps1xml)                                                                              |
|[ImmyBot.Backend.Application.format.ps1xml](/Formats/ImmyBot.Backend.Application.format.ps1xml)                                            |
|[ImmyBot.format.ps1xml](/Formats/ImmyBot.format.ps1xml)                                                                                    |
|[Registry.format.ps1xml](/Formats/Registry.format.ps1xml)                                                                                  |
|[Version.format.ps1xml](/Formats/Version.format.ps1xml)                                                                                    |
|[AzureAD.Standard.Preview.Format.ps1xml](/Modules/AzureAD.Standard.Preview/AzureAD.Standard.Preview.Format.ps1xml)                         |
|[Microsoft.Graph.Authentication.format.ps1xml](/Modules/Microsoft.Graph.Authentication/1.23.0/Microsoft.Graph.Authentication.format.ps1xml)|
|[Microsoft.Graph.Groups.format.ps1xml](/Modules/Microsoft.Graph.Groups/1.23.0/Microsoft.Graph.Groups.format.ps1xml)                        |
|[Microsoft.Graph.Users.format.ps1xml](/Modules/Microsoft.Graph.Users/1.23.0/Microsoft.Graph.Users.format.ps1xml)                           |



## Type files


|Name                                                                                                                                                       |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------|
|[PowerShellEditorServices.Commands.types.ps1xml](/EditorServicesExtracted/PowerShellEditorServices/Commands/PowerShellEditorServices.Commands.types.ps1xml)|
|[ScriptAnalyzer.types.ps1xml](/EditorServicesExtracted/PSScriptAnalyzer/1.21.0/ScriptAnalyzer.types.ps1xml)                                                |



## Scripts


|Name                                                                      |
|--------------------------------------------------------------------------|
|[Complete-BitsTransfer.ps1](Complete-BitsTransfer.ps1)                    |
|[Compute-FileMd5.ps1](Compute-FileMd5.ps1)                                |
|[Download-FileToFolder.ps1](Download-FileToFolder.ps1)                    |
|[Download-WithBitsTransfer.ps1](Download-WithBitsTransfer.ps1)            |
|[Expand-ZipFile.ps1](Expand-ZipFile.ps1)                                  |
|[Get-BitsTransferAsJson.ps1](Get-BitsTransferAsJson.ps1)                  |
|[Get-InstalledChocolateySoftware.ps1](Get-InstalledChocolateySoftware.ps1)|
|[Get-InstalledChocolateyVersion.ps1](Get-InstalledChocolateyVersion.ps1)  |
|[Get-NameFromUpn.ps1](Get-NameFromUpn.ps1)                                |
|[Get-PowerShellVersion.ps1](Get-PowerShellVersion.ps1)                    |
|[Get-WindowsDedupeMarker.ps1](Get-WindowsDedupeMarker.ps1)                |
|[Get-WindowsDeviceIdentifier.ps1](Get-WindowsDeviceIdentifier.ps1)        |
|[ImmyBot.Backend.ezout.ps1](ImmyBot.Backend.ezout.ps1)                    |
|[Remove-BitsTransfer.ps1](Remove-BitsTransfer.ps1)                        |
|[Rename-Computer.ps1](Rename-Computer.ps1)                                |
|[Set-WindowsDedupeMarker.ps1](Set-WindowsDedupeMarker.ps1)                |
|[Set-WindowsDeviceIdentifier.ps1](Set-WindowsDeviceIdentifier.ps1)        |
|[Start-EphemeralAgent.ps1](Start-EphemeralAgent.ps1)                      |
|[Test-Path.ps1](Test-Path.ps1)                                            |



