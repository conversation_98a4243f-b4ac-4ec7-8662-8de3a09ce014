using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ModelBinding.Binders;
using System.Text.Json;

namespace Immybot.Backend.Web.Common.Lib;

public class JsonElementModelBinder : IModelBinder
{
  public Task BindModelAsync(ModelBindingContext bindingContext)
  {
    if (bindingContext == null)
      throw new ArgumentNullException(nameof(bindingContext));
    var modelName = bindingContext.ModelName;
    var valueProviderResult = bindingContext.ValueProvider.GetValue(modelName);
    if (valueProviderResult == ValueProviderResult.None) return Task.CompletedTask;
    bindingContext.ModelState.SetModelValue(modelName, valueProviderResult);

    var value = valueProviderResult.FirstValue;
    if (value == null)
    {
      if (bindingContext.ModelMetadata.ModelType == typeof(JsonElement?))
      {
        bindingContext.Result = ModelBindingResult.Success(null);
      }
      else
      {
        bindingContext.Result = ModelBindingResult.Success(default(JsonElement));
      }
      return Task.CompletedTask;
    }
    string toDeserialize;
    if (value == string.Empty)
      toDeserialize = "\"\"";
    else
      toDeserialize = value;
    var model = JsonSerializer.Deserialize<JsonElement>(toDeserialize);
    bindingContext.Result = ModelBindingResult.Success(model);
    return Task.CompletedTask;
  }

}
public class JsonElementModelBinderProvider : IModelBinderProvider
{
  public IModelBinder? GetBinder(ModelBinderProviderContext context)
  {
    if (context == null)
      throw new ArgumentNullException(nameof(context));
    if (context.Metadata.ModelType == typeof(JsonElement) || context.Metadata.ModelType == typeof(JsonElement?))
      return new BinderTypeModelBinder(typeof(JsonElementModelBinder));
    return null;
  }
}
