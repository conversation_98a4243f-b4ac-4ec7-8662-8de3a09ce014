using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ModelBinding.Binders;
using NuGet.Versioning;

namespace Immybot.Backend.Web.Common.Lib;

public class SemanticVersionModelBinder : IModelBinder
{
  public Task BindModelAsync(ModelBindingContext bindingContext)
  {
    if (bindingContext == null)
      throw new ArgumentNullException(nameof(bindingContext));
    var modelName = bindingContext.ModelName;
    var valueProviderResult = bindingContext.ValueProvider.GetValue(modelName);
    if (valueProviderResult == ValueProviderResult.None) return Task.CompletedTask;
    bindingContext.ModelState.SetModelValue(modelName, valueProviderResult);

    var value = valueProviderResult.FirstValue;
    if (string.IsNullOrEmpty(value)) return Task.CompletedTask;

    // First try to see if it's x.y.z (unless we're supposed to be parsing to a NuGetVersion)
    if (bindingContext.ModelMetadata.ModelType != typeof(NuGetVersion) && SemanticVersion.TryParse(value, out var sVer))
    {
      bindingContext.Result = ModelBindingResult.Success(sVer);
      return Task.CompletedTask;
    }
    // Now try to see if it's x.y.z.r
    if (!NuGetVersion.TryParse(value, out var model))
    {
      bindingContext.ModelState.TryAddModelError(modelName, "Must be a string representing a valid SemanticVersion");
      return Task.CompletedTask;
    }
    bindingContext.Result = ModelBindingResult.Success(model);
    return Task.CompletedTask;
  }
}
public class SemanticVersionModelBinderProvider : IModelBinderProvider
{
  public IModelBinder? GetBinder(ModelBinderProviderContext context)
  {
    if (context == null)
      throw new ArgumentNullException(nameof(context));
    if (context.Metadata.ModelType == typeof(SemanticVersion) || context.Metadata.ModelType == typeof(NuGetVersion))
      return new BinderTypeModelBinder(typeof(SemanticVersionModelBinder));
    return null;
  }
}
