using Newtonsoft.Json;
using OneOf;

namespace Immybot.Backend.Web.Common.Lib.JsonConverters;

public class OneOfConverter : JsonConverter
{
  public override bool CanConvert(Type objectType)
  {
    return objectType.IsAssignableTo(typeof(IOneOf)) || objectType.IsGenericType && objectType.GetGenericTypeDefinition() == typeof(Nullable<>) && objectType.GenericTypeArguments[0].IsAssignableTo(typeof(IOneOf));
  }

  public override object? Read<PERSON><PERSON>(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
  {
    throw new JsonException("Cannot be deserialized.");
  }

  public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
  {
    if (value is not IOneOf v)
    {
      writer.WriteNull();
      return;
    }

    if (v.Value is null)
    {
      writer.WriteNull();
      return;
    }
    serializer.Serialize(writer, v.Value, v.Value.GetType());
  }
}
