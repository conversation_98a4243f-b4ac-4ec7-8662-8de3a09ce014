using System.Text.Json;
using System.Text.Json.Serialization;
using Newtonsoft.Json.Linq;

namespace Immybot.Backend.Web.Common.Lib.JsonConverters;

public class JObjectConverter : JsonConverter<JObject>
{
  public override JObject Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
  {
    return JObject.Parse(reader.GetString() ?? string.Empty);
  }

  public override void Write(Utf8JsonWriter writer, JObject value, JsonSerializerOptions options)
  {
    using var document = JsonDocument.Parse(value.ToString());
    document.RootElement.WriteTo(writer);
  }
}
