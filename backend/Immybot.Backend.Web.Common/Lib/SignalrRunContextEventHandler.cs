using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib.SignalRHubs;
using Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources;
using Microsoft.AspNetCore.SignalR;

namespace Immybot.Backend.Web.Common.Lib;

public class SignalrRunContextEventHandler : IRunContextActions
{
  private readonly IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> _userHub;

  public SignalrRunContextEventHandler(
    IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> userHub)
  {
    _userHub = userHub;
  }

  public async Task CreateAction(IRunContext context, MaintenanceAction action)
  {
    await Task.Run(async () =>
    {
      await _userHub.Clients
        .Groups($"Session:{action.MaintenanceSessionId}:actions")
        .CreateMaintenanceAction(new CreateMaintenanceActionResource(action)
        {
          JoinedSessionGroupType = JoinedSessionGroupType.Session,
        });
    });
  }

  public async Task UpdateAction(IRunContext context, MaintenanceAction action)
  {
    await Task.Run(async () =>
    {
      await _userHub.Clients
        .Groups($"Session:{action.MaintenanceSessionId}:actions")
        .UpdateMaintenanceAction(new UpdateMaintenanceActionResource(action)
        {
          JoinedSessionGroupType = JoinedSessionGroupType.Session,
        });
    });
  }

  public async Task RemoveAction(IRunContext context, MaintenanceAction action)
  {
    await Task.Run(async () =>
    {
      await _userHub.Clients
        .Groups($"Session:{action.MaintenanceSessionId}:actions")
        .RemoveMaintenanceAction(new RemoveMaintenanceActionResource(action)
        {
          JoinedSessionGroupType = JoinedSessionGroupType.Session,
        });
    });
  }

  public async Task UpdateStage(IRunContext context, MaintenanceSessionStage stage)
  {
    await Task.Run(async () =>
    {
      await _userHub.Clients
        .Groups($"Session:{stage.MaintenanceSessionId}:stages")
        .UpdateMaintenanceSessionStage(new UpdateMaintenanceSessionStageResource(stage)
        {
          JoinedSessionGroupType = JoinedSessionGroupType.Session,
        });
    });
  }

  public async Task UpdateSession(IRunContext context, MaintenanceSession session)
  {
    await Task.Run(async () =>
    {
      // emit session events to the computer group
      if (context.IsComputerTarget)
      {
        await _userHub.Clients
          .Groups($"Computer:{context.ComputerId}")
          .UpdateMaintenanceSession(new UpdateMaintenanceSessionResource(session)
          {
            JoinedSessionGroupType = JoinedSessionGroupType.Computer,
          });
      }

      if (context.IsTenantTarget)
      {
        await _userHub.Clients
          .Groups($"Tenant:{context.TenantId}")
          .UpdateMaintenanceSession(new UpdateMaintenanceSessionResource(session)
          {
            JoinedSessionGroupType = JoinedSessionGroupType.Tenant,
          });
      }

      // emit session events to the session group
      await _userHub.Clients
        .Groups($"Session:{session.Id}")
        .UpdateMaintenanceSession(new UpdateMaintenanceSessionResource(session)
        {
          JoinedSessionGroupType = JoinedSessionGroupType.Session,
        });

    });
  }

  public Task UpdateComputer(IRunContext context, Computer computer)
  {
    return Task.CompletedTask;
  }

  public Task AddSessionLog(IRunContext context, SessionLog log)
  {
    return Task.Run(async () =>
    {
      if (log.MaintenanceSessionId != 0)
      {
        await _userHub.Clients
          .Groups($"Session:{log.MaintenanceSessionId}:logs")
          .AddLog(new AddLogResource()
          {
            PersonId = context.PersonId,
            ComputerId = context.ComputerId,
            TenantId = context.TenantId,
            Log = new GetMaintenanceSessionLogResponse(log),
            JoinedSessionGroupType = JoinedSessionGroupType.Session,
          });
      }
    });
  }

  public async Task CreatePhase(IRunContext runContext, SessionPhase phase)
  {
    await Task.Run(async () =>
    {
      await _userHub.Clients
        .Groups($"Session:{phase.MaintenanceSessionId}:logs")
        .CreateSessionPhase(new CreateSessionPhaseResource(phase)
        {
          JoinedSessionGroupType = JoinedSessionGroupType.Session,
        });
    });
  }

  public async Task UpdatePhase(IRunContext runContext, SessionPhase phase)
  {
    await Task.Run(async () =>
    {
      await _userHub.Clients
        .Groups($"Session:{phase.MaintenanceSessionId}:logs")
        .UpdateSessionPhase(new CreateSessionPhaseResource(phase)
        {
          JoinedSessionGroupType = JoinedSessionGroupType.Session,
        });
    });
  }

  public async Task AddMaintenanceActionActivity(IRunContext runContext, MaintenanceActionActivity activity)
  {
    await Task.Run(async () =>
    {
      await _userHub.Clients
        .Groups($"Session:{activity.MaintenanceSessionId}:activities")
        .AddMaintenanceActionActivity(new MaintenanceActionActivityResponse(
          activity.Id,
          activity.MaintenanceSessionId,
          activity.MaintenanceActionId,
          activity.Activity,
          activity.CurrentOperation,
          activity.ActivityId,
          activity.ParentId,
          activity.PercentComplete,
          activity.SecondsRemaining,
          activity.SourceId,
          activity.Status,
          activity.Completed,
          activity.DateUtc,
          activity.ScriptName));
    });
  }
}
