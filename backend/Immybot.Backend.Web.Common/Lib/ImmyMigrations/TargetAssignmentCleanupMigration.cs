using Immybot.Backend.Application.Commands;
using Immybot.Backend.Web.Common.Infrastructure;

namespace Immybot.Backend.Web.Common.Lib.ImmyMigrations;

public class TargetAssignmentCleanupMigration : IImmyMigrator
{
  private readonly ITargetAssignmentCleanupMigrationCmd _cmd;
  public int SortOrder => 1;

  public TargetAssignmentCleanupMigration(ITargetAssignmentCleanupMigrationCmd cmd)
  {
    _cmd = cmd;
  }

  public Task Migrate(CancellationToken appStopping) => _cmd.ExecuteAsync(appStopping);
}
