using DotNext;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Lib;

public interface IVisibilityAssignmentResolver
{
  Task<List<TargetAssignmentResource>> GetTargetAssignmentsForVisibility(
    AuthUserDto triggeredBy,
    ResolveVisibilityTargetAssignmentsRequest request,
    CancellationToken token);
}

public class VisibilityAssignmentResolver(ITargetAssignmentResolver resolver, IRunContextFactory runContextFactory) : IVisibilityAssignmentResolver
{
  public async Task<List<TargetAssignmentResource>> GetTargetAssignmentsForVisibility(
    AuthUserDto triggeredBy,
    ResolveVisibilityTargetAssignmentsRequest request,
    CancellationToken token)
  {
    List<TargetAssignmentResource> resources = [];

    Func<IRunContext, Task<List<TargetAssignment>>> resolverToUse = request.Visibility switch
    {
      TargetVisibility.TechnicianTools => resolver.GetTargetAssignmentsForTechnicianPod,
      TargetVisibility.SelfService => resolver.GetTargetAssignmentsForSelfService,
      _ => throw new NotImplementedException()
    };

    // resolve cloud assignments
    if (request.TenantId.HasValue)
    {
      using var tenantContext = await runContextFactory.GenerateTenantRunContext(
        manuallyTriggeredBy: triggeredBy,
        tenantId: request.TenantId.Value,
        cancellationToken: token);

      FormatTargetAssignmentList(
        await resolverToUse(tenantContext),
        resources);
    }

    // resolve person assignments
    if (request.PersonId.HasValue)
    {
      using var personContext = await runContextFactory.GeneratePersonRunContext(
        triggeredBy,
        request.PersonId.Value,
        token);

      FormatTargetAssignmentList(
        await resolverToUse(personContext),
        resources);
    }

    // resolve computer assignments
    if (request.ComputerId.HasValue)
    {
      using var computerContext = await runContextFactory.GenerateComputerOneOffRunContext(
        request.ComputerId.Value,
        token,
        manuallyTriggeredBy: triggeredBy);

      FormatTargetAssignmentList(
        await resolverToUse(computerContext),
        resources);
    }

    return resources;
  }

  private static void FormatTargetAssignmentList(List<TargetAssignment> assignmentList,  List<TargetAssignmentResource> resources)
  {
    var local = assignmentList.Where(a => a.DatabaseType == DatabaseType.Local)
      .Select(a => new LocalTargetAssignmentResource(a));
    var global = assignmentList.Where(a => a.DatabaseType == DatabaseType.Global)
      .Select(a => new GlobalTargetAssignmentResource(a));
    var combined = local.OfType<TargetAssignmentResource>().Concat(global).ToList();
    resources.AddRange(combined);
  }
}
