using Immybot.Backend.Domain.Models;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;

namespace Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources;

public class CreateSessionPhaseResource : GetMaintenanceSessionPhaseResponse, IJoinedSessionGroupResource
{
  public CreateSessionPhaseResource(SessionPhase sessionPhase) : base(sessionPhase) { }

  public JoinedSessionGroupType JoinedSessionGroupType { get; set; } = JoinedSessionGroupType.None;
}
