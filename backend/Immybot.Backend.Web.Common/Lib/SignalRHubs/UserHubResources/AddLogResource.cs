using Immybot.Backend.Web.Common.Contracts.V1.Responses;

namespace Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources;

public class AddLogResource : IJoinedSessionGroupResource
{
  public int? PersonId { get; set; }
  public int? ComputerId { get; set; }
  public int? TenantId { get; set; }
  public required GetMaintenanceSessionLogResponse Log { get; set; }
  public JoinedSessionGroupType JoinedSessionGroupType { get; set; } = JoinedSessionGroupType.None;
}
