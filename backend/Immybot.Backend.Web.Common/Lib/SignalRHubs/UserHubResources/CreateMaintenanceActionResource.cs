using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources;

public class CreateMaintenanceActionResource : IJoinedSessionGroupResource
{
  public int Id { get; set; }
  public int? ParentId { get; set; }
  public int? ComputerId { get; set; }
  public int? PersonId { get; set; }
  public int? TenantId { get; set; }
  public int MaintenanceSessionId { get; set; }
  public string MaintenanceDisplayName { get; set; } = string.Empty;
  public int? AssignmentId { get; set; }
  public DatabaseType? AssignmentType { get; set; }
  public string MaintenanceIdentifier { get; set; } = string.Empty;
  public MaintenanceType MaintenanceType { get; set; }
  public string MaintenanceTypeName => MaintenanceType.ToString();
  public string DetectedVersionString { get; set; } = string.Empty;
  public string DesiredVersionString { get; set; } = string.Empty;
  public MaintenanceActionType ActionType { get; set; }
  public string ActionTypeName => ActionType.ToString();
  public DateTime StartTime { get; set; }
  public DateTime EndTime { get; set; }
  public MaintenanceActionReason Reason { get; set; }
  public MaintenanceActionResult Result { get; set; }
  public string TestResult { get; set; } = string.Empty;
  public string PostMaintenanceTest { get; set; } = string.Empty;
  public int? PostMaintenanceTestType { get; set; }
  public bool? PostMaintenanceTestResult { get; set; }
  public string PostMaintenanceTestResultMessage { get; set; } = string.Empty;
  public MaintenanceActionStatus Status { get; set; }
  public string ResultReasonMessage { get; set; } = string.Empty;
  public MaintenanceActionResultReason? ResultReason { get; set; }
  public MaintenanceTaskMode? MaintenanceTaskMode { get; set; }
  public DesiredSoftwareState? DesiredSoftwareState { get; set; }
  public string MaintenanceTaskGetResult { get; set; } = string.Empty;
  public string SoftwareTableRegexString { get; set; } = string.Empty;
  public int? SoftwareActionIdForConfigurationTask { get; set; }
  public string? PolicyDescription { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public SoftwareProviderType SoftwareProviderType { get; set; }

  public SoftwareType? SoftwareType
  {
    get
    {
      switch (MaintenanceType)
      {
        case MaintenanceType.GlobalSoftware:
          return Domain.Models.SoftwareType.GlobalSoftware;
        case MaintenanceType.LocalSoftware:
          return Domain.Models.SoftwareType.LocalSoftware;
        case MaintenanceType.NiniteSoftware:
          return Domain.Models.SoftwareType.Ninite;
        case MaintenanceType.ChocolateySoftware:
          return Domain.Models.SoftwareType.Chocolatey;
        default:
          return null;
      }
    }
  }

  public DatabaseType? MaintenanceTaskType
  {
    get
    {
      switch (MaintenanceType)
      {
        case MaintenanceType.GlobalMaintenanceTask:
          return DatabaseType.Global;
        case MaintenanceType.LocalMaintenanceTask:
          return DatabaseType.Local;
        default:
          return null;
      }
    }
  }

  public JoinedSessionGroupType JoinedSessionGroupType { get; set; } = JoinedSessionGroupType.None;

  public CreateMaintenanceActionResource() { }

  public CreateMaintenanceActionResource(MaintenanceAction action)
  {
    if (action == null) throw new ArgumentNullException(nameof(action));

    Id = action.Id;
    ParentId = action.ParentId;
    ComputerId = action.ComputerId;
    PersonId = action.PersonId;
    TenantId = action.TenantId;
    MaintenanceSessionId = action.MaintenanceSessionId;
    MaintenanceDisplayName = action.MaintenanceDisplayName ?? string.Empty;
    AssignmentId = action.AssignmentId;
    AssignmentType = action.AssignmentType;
    MaintenanceIdentifier = action.MaintenanceIdentifier;
    MaintenanceType = action.MaintenanceType;
    DetectedVersionString = action.DetectedVersion?.ToNormalizedString() ?? string.Empty;
    DesiredVersionString = action.DesiredVersion?.ToNormalizedString() ?? string.Empty;
    ActionType = action.ActionType;
    StartTime = action.StartTime;
    EndTime = action.EndTime;
    Reason = action.ActionReason;
    Result = action.ActionResult;
    PostMaintenanceTest = action.PostMaintenanceTest ?? string.Empty;
    PostMaintenanceTestType = action.PostMaintenanceTestType;
    PostMaintenanceTestResult = action.PostMaintenanceTestResult;
    PostMaintenanceTestResultMessage = action.PostMaintenanceTestResultMessage ?? string.Empty;
    Status = action.ActionStatus;
    ResultReasonMessage = action.ActionResultReasonMessage ?? string.Empty;
    ResultReason = action.ActionResultReason;
    MaintenanceTaskMode = action.MaintenanceTaskMode;
    MaintenanceTaskGetResult = action.MaintenanceTaskGetResult ?? string.Empty;
    SoftwareTableRegexString = action.SoftwareTableRegexString ?? string.Empty;
    DesiredSoftwareState = action.DesiredSoftwareState;
    SoftwareActionIdForConfigurationTask = action.SoftwareActionIdForConfigurationTask;
    PolicyDescription = action.PolicyDescription;
    CreatedDateUTC = action.CreatedDate;
    SoftwareProviderType = action.SoftwareProviderType;
  }
}
