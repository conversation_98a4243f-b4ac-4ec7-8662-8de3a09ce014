using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources;

public record AgentIdentificationLogResource(int Id, int ProviderAgentId, string Message, AgentIdentificationLogType LogType, DateTime TimeUtc, string deviceName) : IAgentIdentificationLogProperties
{
  public AgentIdentificationLogResource(AgentIdentificationLogAddedEvent ev) : this(ev.Log.Id, ev.Log.ProviderAgentId, ev.Log.Message, ev.Log.LogType, ev.Log.TimeUtc, ev.DeviceName) { }
}
