namespace Immybot.Backend.Web.Common.Lib;

public static class UploadHelper
{
  public static void CheckFileExtensionValid(string fileName)
  {
    fileName = fileName.ToLower();
    string[] fileExtensions = [ ".zip",
        ".exe",
        ".msi",
        ".iso",
        ".msp",
        ".msix",
        ".appx",
        ".msixbundle",
        ".appxbundle"
    ];

    var isValidExtenstion = Array.Exists(fileExtensions, ext => fileName.LastIndexOf(ext) > -1);
    if (!isValidExtenstion)
      throw new InvalidOperationException("Not allowed file extension");
  }

  public static void CheckMaxFileSize(FileStream stream)
  {
    // 10 gb
    if (stream.Length > 10000000000)
      throw new InvalidOperationException("File is too large");
  }
}
