using System.Net.Mime;
using System.Text;
using Immybot.Backend.Domain.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Immybot.Backend.Web.Common.Lib.Middleware;

/*
 * Middleware to catch all uncaught exceptions and return a formal HttpProblem response
 */
public class HttpProblemExceptionMiddleware
{
  private readonly RequestDelegate _next;

  public HttpProblemExceptionMiddleware(RequestDelegate next)
  {
    _next = next;
  }

  public async Task InvokeAsync(
    HttpContext context,
    ILogger<HttpProblemExceptionMiddleware> logger,
    HttpProblemFormatter httpProblemFormatter)
  {
    try
    {
      await _next(context);
    }
    catch (Exception ex)
    {
      var hasAcceptHeader = context.Request.Headers.TryGetValue(HeaderNames.Accept, out var acceptHeader);
      var acceptsJson = !hasAcceptHeader || acceptHeader.Any(h => h != null && (h.Contains(MediaTypeNames.Application.Json) || h.Contains(MediaTypeNames.Text.Plain) || h.Contains("*/*")));
      var acceptsHtml = !hasAcceptHeader || acceptHeader.Any(h => h != null && (h.Contains(MediaTypeNames.Text.Html) || h.Contains(MediaTypeNames.Text.Plain) || h.Contains("*/*")));
      var requestPath = context.Request.Path;
      var requestMethod = context.Request.Method;
      var responseProblem = httpProblemFormatter.GetResponseProblemFromException(ex,
        requestPath: requestPath,
        currentUser: context.Items["CurrentUser"] as User,
        currentPrincipal: context.User,
        logger: logger);

      int? statusCode = null;
      string? responseContentType = null;
      string? responseBody = null;

      if (responseProblem != null)
      {
        if (acceptsJson)
        {
          responseContentType = MediaTypeNames.Application.Json;

          responseBody = SerializeToJson(responseProblem);
        }
        else if (acceptsHtml)
        {
          responseContentType = MediaTypeNames.Text.Html;
          var extrasPart = string.Empty;
          if (responseProblem.Extras != null && responseProblem.Extras.Count != 0)
          {
            var sb = new StringBuilder();
            sb.Append("<p>Extra Details:</p><dl>");
            foreach (var kvp in responseProblem.Extras)
            {
              sb.Append($"<dt>{kvp.Key}</dt><dd>{(kvp.Value == null ? "" : SerializeToJson(kvp.Value))}</dd>");
            }
            sb.Append("</dl>");
            extrasPart = sb.ToString();
          }

          responseBody =
            "<html lang=\"en\"><body>" +
            $"<h1>{(int?)responseProblem.Status} {responseProblem.Status} - {responseProblem.Title}</h1>" +
            $"<p>{responseProblem.Detail}</p>" +
            extrasPart +
            "</body></html>" +
            new string(' ', 512); // IE padding
        }
        else
        {
          // Don't know how to respond to request. Just use a status code and log the error
          var errorJson = SerializeToJson(responseProblem);
          logger.LogError(
            "Unable to respond to request with error details because content type is not json or html\n\n{ErrorJson}\n\n",
            errorJson);
        }
        statusCode = (int?)responseProblem.Status;
      }

      if (context.Response.HasStarted)
      {
        // We can't modify the response at all because it already started
        // So let's just log the body we would have sent and return
        logger.LogError(
          "An error occurred during a request, but the response was already started\n\n" +
          "REQUEST: {RequestMethod} {RequestPath}\n\n" +
          "ERROR RESPONSE:\nstatus: {ResponseStatusCode}\n" +
          "Content-Type: {ResponseContentType}\n" +
          "body: {ResponseBody}\n\n",
            requestMethod,
            requestPath,
            statusCode ?? 500,
            responseContentType,
            responseBody);
      }
      else
      {
        // we're good to modify the response's headers/body
        context.Response.StatusCode = statusCode ?? 500;
        context.Response.ContentType = responseContentType ?? "";
        if (responseBody != null)
          await context.Response.WriteAsync(responseBody);
      }
    }
  }

  private static string SerializeToJson(object value)
  {
    if (value == null) return "";
    return JsonConvert.SerializeObject(value, new JsonSerializerSettings()
    {
      ContractResolver = new CamelCasePropertyNamesContractResolver(),
      ReferenceLoopHandling = ReferenceLoopHandling.Ignore
    });
  }
}
