using Hangfire.Annotations;
using Hangfire.Dashboard;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Lib.Middleware;

/// <summary>
/// Filter than gets called after a request goes through our
/// middleware pipeline to authenticate a user to see the hangfire dashboard
/// </summary>
public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter
{
  public bool Authorize([NotNull] DashboardContext context)
  {
    var httpContext = context.GetHttpContext();
    var currentUser = httpContext.Items["CurrentUser"] as User;

    // only authenticated MSP users are allowed to see the dashboard
    if (currentUser == null || currentUser.OwnerTenant is { IsMsp: false }) return false;

    return true;
  }
}
