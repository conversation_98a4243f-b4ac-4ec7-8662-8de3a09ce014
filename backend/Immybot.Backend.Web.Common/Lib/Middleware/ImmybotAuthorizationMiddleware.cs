using Immybot.Backend.Application.DbContextExtensions.PersonExtensions;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Manager.Domain;
using Immybot.Backend.Persistence;
using Immybot.Backend.Web.Common.Contracts;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Lib.UserAuthentication;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly.Registry;

namespace Immybot.Backend.Web.Common.Lib.Middleware;

/// <summary>
/// 401 Unauthorized => Should be used when the service principal is missing.  e.g. The user has not been authenticated
/// 403 Forbidden    => Should be used when the service principal is present, but the user does not have permission for the requested resource.
///                  => e.g. the user is not authorized to access immybot.
/// </summary>
public class ImmybotAuthorizationMiddleware(RequestDelegate next)
{
  public async Task InvokeAsync(
    HttpContext context,
    Func<ImmybotDbContext> dbContextFactory,
    ICachedSingleton<ApplicationPreferences> cachedAppPrefs,
    IPolicyRegistry<string> policyRegistry,
    IManagerProvidedSettings mgrSettings,
    ILogger<ImmybotAuthorizationMiddleware> logger)
  {
    if (context.Request.Path.StartsWithSegments("/plugins/api"))
    {
      // MVC automatically reads the request body even if you don't specify [FromBody],
      // so we need to enable buffering so we can re-read the request body and send it to the provider.
      // By default, you can only read the request body once
      context.Request.EnableBuffering();
    }

    // Send all /api calls through the auth middleware
    if (
      context.Request.Path.StartsWithSegments("/api", StringComparison.OrdinalIgnoreCase)
      || context.Request.Path.StartsWithSegments("/UserHub", StringComparison.OrdinalIgnoreCase)
      || context.Request.Path.StartsWithSegments("/swagger", StringComparison.OrdinalIgnoreCase)
      || context.Request.Path.StartsWithSegments("/RemoteControl", StringComparison.OrdinalIgnoreCase)
      || context.Request.Path.StartsWithSegments("/hubs/viewer", StringComparison.OrdinalIgnoreCase))
    {
      if (
        // old public routes for v1 emails (kept for compatibility)
        context.Request.Path.StartsWithSegments("/api/v1/emails", StringComparison.OrdinalIgnoreCase)
        // api route for emails
        || context.Request.Path.StartsWithSegments("/api/v1/maintenance-emails", StringComparison.OrdinalIgnoreCase)
        // Allow EphemeralAgent routes to bypass middleware auth
        || context.Request.Path.StartsWithSegments("/api/v1/ephemeral-session", StringComparison.OrdinalIgnoreCase)
        // Allow agent to get metadata for update checks.
        || context.Request.Path.StartsWithSegments($"/{ImmyAgentMetadataRoutes.Base}",
          StringComparison.OrdinalIgnoreCase)
        // skip for requesting access to ImmyBot
        || context.Request.Path.StartsWithSegments("/api/v1/auth/request-access", StringComparison.OrdinalIgnoreCase)
        // skip for webhook requests
        || context.Request.Path.StartsWithSegments("/api/v1/webhooks", StringComparison.OrdinalIgnoreCase))
      {
        await next(context);
        return;
      }

      if (context.Features.Get<AuthenticateResult>()?.Properties
            ?.GetParameter<AuthenticationResult>(nameof(AuthenticationResult)) is not { } authResult)
      {
        throw new InvalidOperationException(
          $"{nameof(AuthenticationResult)} was expected to be in the HttpContext.");
      }

      switch (authResult)
      {
        case AuthenticationResult.Success:
          await next(context);
          return;
        case AuthenticationResult.PrincipalIdMissing:
          {
            const string msg = "No principal id was provided with the request.";
            logger.LogTrace(msg);
            var authDetail = new AuthDetail(AuthCode.PrincipalMissing, msg);
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            await context.Response.WriteAsync(JsonConvert.SerializeObject(authDetail),
              cancellationToken: context.RequestAborted);
          }
          return;
        case AuthenticationResult.PrincipalObjectIdMissing:
          {
            const string msg = "The object id from the auth principal is missing.";
            logger.LogTrace(msg);
            var authDetail = new AuthDetail(AuthCode.PrincipalObjectIdMissing, msg);
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            await context.Response.WriteAsync(JsonConvert.SerializeObject(authDetail),
              cancellationToken: context.RequestAborted);
          }
          return;
        case AuthenticationResult.AuthenticationExpired:
          {
            const string msg = "Authentication has expired.";
            logger.LogTrace(msg);
            var authDetail = new AuthDetail(AuthCode.AuthenticationExpired, msg);
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            await context.Response.WriteAsync(JsonConvert.SerializeObject(authDetail),
              cancellationToken: context.RequestAborted);
          }
          return;
        case AuthenticationResult.ExpectedImmyBotManager:
          {
            const string msg = "Expected ImmyBot Manager authentication.";
            logger.LogError(msg);
            context.Response.StatusCode = StatusCodes.Status403Forbidden;
            await context.Response.WriteAsync(msg, cancellationToken: context.RequestAborted);
          }
          return;
        case AuthenticationResult.KnownTenantUserMissing:
          {
            var objectId = context.User.GetObjectId();
            await using var dbContext = dbContextFactory();
            var accessRequested = objectId != null && dbContext.HasPersonWithPrincipalIdRequestedAccess(objectId);
            context.Response.StatusCode = StatusCodes.Status403Forbidden;
            const string msg = "You are not authorized to login.";
            var authDetail = new AuthDetail(AuthCode.KnownTenantUserMissing,
              msg,
              accessRequested: accessRequested,
              enableRequestAccess: cachedAppPrefs.Value.EnableRequestAccess);
            logger.LogTrace(msg);
            await context.Response.WriteAsync(JsonConvert.SerializeObject(authDetail),
              cancellationToken: context.RequestAborted);
          }
          return;
        case AuthenticationResult.UnknownTenantUserMissing:
          {
            var objectId = context.User.GetObjectId();
            await using var dbContext = dbContextFactory();
            var accessRequested = objectId != null && dbContext.HasPersonWithPrincipalIdRequestedAccess(objectId);
            context.Response.StatusCode = StatusCodes.Status403Forbidden;
            const string msg = "Your tenant is not authorized to login.";
            var authDetail = new AuthDetail(AuthCode.UnknownTenantUserMissing,
              msg,
              accessRequested: accessRequested,
              enableRequestAccess: cachedAppPrefs.Value.EnableRequestAccess);
            logger.LogTrace(msg);
            await context.Response.WriteAsync(JsonConvert.SerializeObject(authDetail),
              cancellationToken: context.RequestAborted);
          }
          return;
        case AuthenticationResult.DefaultRootTenantNotFound:
          {
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            const string msg = "Default MSP tenant could not be found.";
            logger.LogError(msg);
            await context.Response.WriteAsync(msg, cancellationToken: context.RequestAborted);
          }
          return;
        case AuthenticationResult.UserIsNotMsp:
          {
            var userTenantId = context.User.GetTenantId();
            context.Response.StatusCode = StatusCodes.Status403Forbidden;
            var msg = $"You are not authorized to login.  Your tenant {userTenantId} is not an MSP.";
            var authDetail = new AuthDetail(AuthCode.UnknownTenantUserMissing, msg);
            await context.Response.WriteAsync(
              JsonConvert.SerializeObject(authDetail),
              cancellationToken: context.RequestAborted);
          }
          return;
        case AuthenticationResult.AccessExpired:
          {
            var objectId = context.User.GetObjectId();
            await using var dbContext = dbContextFactory();
            var accessRequested = objectId != null && dbContext.HasPersonWithPrincipalIdRequestedAccess(objectId);
            const string msg = "Your access has expired.  Please request access again.";
            var authDetail = new AuthDetail(AuthCode.AccessExpired,
              msg,
              accessRequested: accessRequested,
              enableRequestAccess: cachedAppPrefs.Value.EnableRequestAccess);
            context.Response.StatusCode = StatusCodes.Status403Forbidden;
            await context.Response.WriteAsync(JsonConvert.SerializeObject(authDetail),
              cancellationToken: context.RequestAborted);
          }
          return;
        case AuthenticationResult.Exception:
          {
            const string msg = "An exception occurred while authenticating the user.";
            logger.LogError(msg);
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            await context.Response.WriteAsync(msg, cancellationToken: context.RequestAborted);
          }
          return;
        case AuthenticationResult.SupportAccessDisabled:
          {
            var authDetail = new AuthDetail(AuthCode.ImmyTechnicianSupportAccessDisabled,
              "Immy technician support is disabled in preferences.");
            context.Response.StatusCode = StatusCodes.Status403Forbidden;
            await context.Response.WriteAsync(
              JsonConvert.SerializeObject(authDetail),
              cancellationToken: context.RequestAborted);
          }
          return;
        case AuthenticationResult.None:
        default:
          throw new ArgumentException($"Unexpected authentication result value: {authResult}");
      }
    }
    else
    {
      await next(context);
    }
  }
}
