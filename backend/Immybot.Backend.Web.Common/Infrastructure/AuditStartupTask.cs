using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Persistence;
using Immybot.Shared.Services.Startup;

namespace Immybot.Backend.Web.Common.Infrastructure;

public class AuditStartupTask : IStartupTask
{
  private readonly Func<ImmybotDbContext> _dbFactory;

  public AuditStartupTask(Func<ImmybotDbContext> dbFactory)
  {
    _dbFactory = dbFactory;
  }

  public async Task ExecuteAsync(CancellationToken cancellationToken = default)
  {
    await using var ctx = _dbFactory();
    var authDetails = new AuditUserDetails(null, null, "System");
    await ctx.CreateAudit(new CreateAuditRequest(
      authDetails,
      AuditType.None,
      AuditObjectType.Startup,
      null,
      null,
      [],
      "Application started",
      ObjectName: "Application Started"));
  }
}
