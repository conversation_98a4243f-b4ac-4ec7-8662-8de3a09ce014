namespace Immybot.Backend.Web.Common.Infrastructure;

/// <summary>
/// Implement this interface and add it to DI to run a migration on startup.
/// Migrations are run in order of SortOrder.
/// After a migration is run, it will not be run again.
/// </summary>
public interface IImmyMigrator
{
  int SortOrder { get; }
  /// <summary>
  /// Run the migration. This method should be idempotent.
  /// </summary>
  Task Migrate(CancellationToken appStopping);
}