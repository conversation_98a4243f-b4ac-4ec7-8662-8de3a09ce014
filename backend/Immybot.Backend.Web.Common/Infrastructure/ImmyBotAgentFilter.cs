using System.Text.Json;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Immybot.Shared.Services.Startup;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Web.Common.Infrastructure;

public class ImmyBotAgentFilter : IStartupTask
{
  private readonly ILogger<ImmyBotAgentFilter> _logger;
  private readonly Func<ImmybotDbContext> _dbFactory;
  private readonly IOptions<ImmyAgentOptions> _agentOptions;

  public ImmyBotAgentFilter(
    ILogger<ImmyBotAgentFilter> logger,
    Func<ImmybotDbContext> dbFactory,
    IOptions<ImmyAgentOptions> agentOptions)
  {
    _logger = logger;
    _dbFactory = dbFactory;
    _agentOptions = agentOptions;
  }

  public async Task ExecuteAsync(CancellationToken cancellationToken = default)
  {
    // Always run this if we are in local dev mode.
    // Otherwise, only run it if we are if UseOptionsFromEnvironment is set to true.
    if (!_agentOptions.Value.UseDevTestAgents && !_agentOptions.Value.UseOptionsFromEnvironment) return;

    try
    {
      // ensure the immy agent integration is present
      await using var ctx = _dbFactory.Invoke();
      var immyAgentProviderId = Guid.Parse(Providers.Shared.Constants.ImmyProviderTypeId);
      var exists = await ctx.ProviderLinks
        .AsNoTracking()
        .AnyAsync(a => a.ProviderTypeId == immyAgentProviderId, cancellationToken);
      if (exists) return;

      var tenantId = ctx.GetRootTenant()?.Id ?? throw new NotSupportedException("A msp tenant is required in order to setup the integration");

      var emptyJson = JsonSerializer.Deserialize<JsonElement>("{}");
      var providerLink = new ProviderLink()
      {
        ProviderTypeId = immyAgentProviderId,
        Name = "ImmyBot Agent",
        TenantId = tenantId,
        ProviderTypeFormData = emptyJson,
        Disabled = false,
        HealthStatus = Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Healthy,
        RunScriptPriority = 1,
        ProviderInternalData = new()
        {
          InternalData = emptyJson
        }
      };
      ctx.ProviderLinks.Add(providerLink);
      await ctx.SaveChangesAsync(cancellationToken);
    }
    catch (Exception ex) when (!ex.IsCancellationException(cancellationToken))
    {
      _logger.LogError(ex, "Error while ensuring immy agent integration is present");
    }
  }
}
