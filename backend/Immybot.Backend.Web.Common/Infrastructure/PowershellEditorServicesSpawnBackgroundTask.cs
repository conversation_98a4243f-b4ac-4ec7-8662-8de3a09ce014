using System.Diagnostics;
using Immybot.Backend.Application.Interface.Language;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Web.Common.Infrastructure;

public class PowershellEditorServicesSpawnBackgroundTask : BackgroundService
{
  private readonly IOptions<PowershellEditorServicesOptions> _editorServicesSettings;
  private readonly ILanguageService _languageService;
  private readonly IHostApplicationLifetime _hostApplicationLifetime;
  private readonly IImmyCancellationManager _immyCancellationManager;
  private readonly ILogger<PowershellEditorServicesSpawnBackgroundTask> _logger;

  public PowershellEditorServicesSpawnBackgroundTask(
    IOptions<PowershellEditorServicesOptions> editorServicesSettings,
    ILanguageService languageService,
    IHostApplicationLifetime hostApplicationLifetime,
    IImmyCancellationManager immyCancellationManager,
    ILogger<PowershellEditorServicesSpawnBackgroundTask> logger)
  {
    _editorServicesSettings = editorServicesSettings;
    _languageService = languageService;
    _hostApplicationLifetime = hostApplicationLifetime;
    _immyCancellationManager = immyCancellationManager;
    _logger = logger;
  }
  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    var settings = _editorServicesSettings.Value;
    var parentProcess = Process.GetProcessById(settings.ParentProcessPid);

    try
    {
      if (_languageService is not ILanguageServiceSpawner spawner)
        throw new InvalidOperationException($"The currently injected {nameof(ILanguageService)} does not support spawning editor services via {nameof(ILanguageServiceSpawner)} interface!");


      var cancellationId = await spawner.StartEditorServices(
        settings.LanguagePipeName,
        settings.SessionInfoFilePath,
        terminalId: Guid.Empty,
        settings.ScriptId,
        settings.ScriptType,
        settings.ScriptCategory,
        settings.ScriptExecutionContext,
        stoppingToken);

      if (cancellationId is null)
        throw new InvalidOperationException("Executing PSES script failed to return a cancellationId!");

      using var timer = new PeriodicTimer(TimeSpan.FromSeconds(1));

      // Typically, we expect the parent process to kill us, but in the event the parent hard
      // crashes/dies w/o giving us SIGINT, we want to be able to shutdown and not hang around.
      while (await timer.WaitForNextTickAsync(stoppingToken))
      {
        parentProcess.Refresh();
        if (parentProcess.HasExited)
        {
          _logger.LogError("Parent process has exited. Gracefully killing PSES & exiting.");
          _immyCancellationManager.CancelScript(cancellationId.Value);
          break;
        }
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "An unexpected error occurred while starting Editor Services sub-process.");
    }
    finally
    {
      _hostApplicationLifetime.StopApplication();
      await Task.Delay(TimeSpan.FromSeconds(4));
      Environment.Exit(0);
    }
  }
}
