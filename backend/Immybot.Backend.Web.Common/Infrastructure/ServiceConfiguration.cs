using App.Metrics;
using App.Metrics.Filtering;
using App.Metrics.Formatters;
using App.Metrics.Infrastructure;
using App.Metrics.Internal.Infrastructure;
using App.Metrics.Reporting;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Interface.Events.RunImmyServiceEvents;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Notifications;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Manager;
using Immybot.Backend.Manager.Domain;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.UserCacheManagement.Implementations;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.Lib.ImmyMigrations;
using Immybot.Backend.Web.Common.Lib.SignalRHubs;
using Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Web.Common.Infrastructure;

internal static class ServiceConfiguration
{
  public static void AddMetricsServices(
    this IServiceCollection services)
  {
    // This is basically the same as AppMetrics's own AddMetrics extension method
    // (https://github.com/AppMetrics/AppMetrics/blob/e937e84b9d7b65b2d5f73ad81c51d8f176c9a1df/src/Extensions/src/App.Metrics.Extensions.DependencyInjection/AppMetricsServiceCollectionExtensions.cs)
    // except we delay all the singleton services resolutions until they're requested so that
    // we can construct the manager reporter using DI-resolved IManagerProvidedSettings class
    services.TryAddSingleton(sp =>
    {
      var mgrSettings = sp.GetRequiredService<IManagerProvidedSettings>();
      var logger = sp.GetRequiredService<ILogger<ManagerMetricsReporter>>();
      var mgrMetricsReporter = new ManagerMetricsReporter(
        logger,
        mgrSettings,

        // NB: Change the next line to alter how frequently metrics are sent to the manager
        TimeSpan.FromMinutes(30), // send metrics to the manager every 30 minutes

        // NB: Change the next line to alter what metrics are sent to the manager
        new MetricsFilter().WhereContext((_) => true) // send all metrics
      );
      var builder = new MetricsBuilder();
      builder.Configuration.Configure(opts =>
      {
        // Remove global tags from metrics
        opts.GlobalTags.Clear();
      });
      builder.Report.Using(mgrMetricsReporter);
      var metrics = builder.Build();
      return metrics;
    });
    services.TryAddSingleton<IMetrics>(sp => sp.GetRequiredService<IMetricsRoot>());
    services.TryAddSingleton(sp => sp.GetRequiredService<IMetricsRoot>().Clock);
    services.TryAddSingleton(sp => sp.GetRequiredService<IMetricsRoot>().Filter);
    services.TryAddSingleton(sp => sp.GetRequiredService<IMetricsRoot>().DefaultOutputMetricsFormatter);
    services.TryAddSingleton<IReadOnlyCollection<IMetricsOutputFormatter>>(sp => sp.GetRequiredService<IMetricsRoot>().OutputMetricsFormatters);
    services.TryAddSingleton(sp => sp.GetRequiredService<IMetricsRoot>().DefaultOutputEnvFormatter);
    services.TryAddSingleton<IReadOnlyCollection<IEnvOutputFormatter>>(sp => sp.GetRequiredService<IMetricsRoot>().OutputEnvFormatters);
    services.TryAddSingleton(new EnvironmentInfoProvider());
    services.TryAddSingleton(sp => sp.GetRequiredService<IMetricsRoot>().Options);
    services.TryAddSingleton<IReadOnlyCollection<IReportMetrics>>(sp => sp.GetRequiredService<IMetricsRoot>().Reporters);
    services.TryAddSingleton(sp => sp.GetRequiredService<IMetricsRoot>().ReportRunner);
    services.TryAddSingleton<AppMetricsMarkerService, AppMetricsMarkerService>();
    services.AddTransient<ITargetAssignmentEmitter, TargetAssignmentEmitter>();
    services.AddTransient<IExportToCsv, ExportToCsv>();
    services.AddTransient<IVisibilityAssignmentResolver, VisibilityAssignmentResolver>();

    // Add the metrics-reporting background service, so that the metric reporters configured above
    // will automatically report according to their configured schedules
    services.AddMetricsReportingHostedService(
      // this event is fired whenever any reporter throws an exception. If we don't "observe" the
      // event, it will crash the application
      (sender, exceptionEvent) =>
      {
        exceptionEvent.SetObserved(); // keep metrics report failures from crashing the application
        // TODO: find a way to log these exceptions
      });
  }

  public static void AddWebServices(
    this IServiceCollection services)
  {
    // Enables automatic case-insensitive filtering for DevExtreme server-side filtering
    DevExtreme.AspNet.Data.DataSourceLoadOptionsBase.StringToLowerDefault = true;

    services.AddSingleton<HttpProblemFormatter>();
    services.AddHttpContextAccessor();
    services.AddOutputCache();
    services.AddTransient<IUserService, HttpUserService>();
    services.AddTransient<IRunContextActions, SignalrRunContextEventHandler>();
    services.AddTransient<IMetascriptMessageHandler, SignalrMetascriptMessageHandler>();
    // Registration needed for RBAC transition functionality in SubjectPermissionAuthorizationHandler
    services.AddTransient<IExcelExporterFactory, ExcelExporterFactory>();
    services.AddTransient<IImmyMigrator, TargetAssignmentCleanupMigration>();
    // IImmyMigrator services
  }

  public static void InitializeWebServices(this IApplicationBuilder app)
  {
    var hubContext = app.ApplicationServices.GetRequiredService<IHubContext<ImmyBotUserHub, IImmyBotUserHubClient>>();
    var ctxFactory = app.ApplicationServices.GetRequiredService<Func<ImmybotDbContext>>();
    var domainEvents = app.ApplicationServices.GetRequiredService<IDomainEventBroker>();

    domainEvents.OnNumOnboardingComputersChangedForTenant += (tenantId, numComputers)
      => Task.Run(async () => await hubContext.Clients.Group($"Tenant:{tenantId}")
        .UpdateTenantNumOnboarding(tenantId, numComputers)).Forget();
    domainEvents.OnNumComputersChangedForTenant += (tenantId, numComputers)
      => Task.Run(async () => await hubContext.Clients.Group($"Tenant:{tenantId}")
      .UpdateTenantNumComputers(tenantId, numComputers)).Forget();
    domainEvents.OnNumOnboardingComputersChanged += (numComputers)
      => Task.Run(async () => await hubContext.Clients.Group($"MspTenant").UpdateNumOnboarding(numComputers)).Forget();
    domainEvents.OnNumComputersChanged += (numComputers)
      => Task.Run(async () => await hubContext.Clients.Group($"MspTenant").UpdateNumComputers(numComputers)).Forget();
    domainEvents.OnComputerNameUpdated += (computerId, computerName) =>
      Task.Run(async () => await hubContext.Clients.Group($"Computer:{computerId}")
      .UpdateComputer(new UpdateComputerResource(computerId, computerName))).Forget();

    // Setup IEvent-typed events that need to be routed to signalr
    var domainEventReceiver = app.ApplicationServices.GetRequiredService<IDomainEventReceiver>();
    domainEventReceiver
      .AddEventHandler<CorrelatedOnboardingPendingAgentCreatedEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"CorrelatedOnboarding:{ev.OnboardingCorrelationId}")
          .CorrelatedOnboardingPendingAgentCreated(new GetPendingAgentResponse(ev.Agent, ev.ProviderLinkName, ev.ExternalClientName))).Forget());
    domainEventReceiver
      .AddEventHandler<CorrelatedOnboardingComputerCreatedEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"CorrelatedOnboarding:{ev.OnboardingCorrelationId}")
          .CorrelatedOnboardingComputerCreated(ev.ComputerId)).Forget());
    domainEventReceiver
      .AddEventHandler<CorrelatedOnboardingExistingComputerDeterminedEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"CorrelatedOnboarding:{ev.OnboardingCorrelationId}").CorrelatedOnboardingExistingComputerDetermined(ev.ComputerId)).Forget());
    domainEventReceiver
      .AddEventHandler<CorrelatedOnboardingAgentIdentificationFailedEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"CorrelatedOnboarding:{ev.OnboardingCorrelationId}").CorrelatedOnboardingAgentIdentificationFailed(new GetAgentIdentificationFailureResponse(ev.AgentIdentificationFailure))).Forget());
    domainEventReceiver
      .AddEventHandler<CorrelatedOnboardingSessionCreatedEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"CorrelatedOnboarding:{ev.OnboardingCorrelationId}").CorrelatedOnboardingSessionCreated(ev)).Forget());
    domainEventReceiver
     .AddEventHandler<ComputerOnboardingStatusChangedEvent>(ev =>
       Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").UpdateComputer(new UpdateComputerResource(ev.ComputerId, onboardingStatus: ev.OnboardingStatus))).Forget());
    domainEventReceiver
      .AddEventHandler<ReplaceExistingAgentIdentificationResolutionEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").AgentIdentificationResolved(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<ReplaceExistingComputerIdentificationResolutionEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").AgentIdentificationResolved(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<KeepBothAgentsIdentificationResolutionEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").AgentIdentificationResolved(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<AgentIdentificationResolutionFailedEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").AgentIdentificationFailed(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<ComputerInventoryKeyUpdatingEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").ComputerInventoryKeyUpdating(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<ComputerInventoryKeyUpdatedEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").ComputerInventoryKeyUpdated(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<ComputerInventoryKeyUpdateFailedEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").ComputerInventoryKeyUpdateFailed(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<EphemeralAgentCircuitBrokenEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").EphemeralAgentCircuitBroken(ev.ComputerId)).Forget());
    domainEventReceiver
      .AddEventHandler<TimelineAppEventForComputer>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").TimelineEventAdded(ev.ComputerId, ev.TimelineEvent)).Forget());
    domainEventReceiver
      .AddEventHandler<AccessRequestedEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"MspTenant").AccessRequested(ev.Data)).Forget());
    domainEventReceiver
      .AddEventHandler<ProviderClientSyncProgressEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"ProviderLink:{ev.providerLinkId}").ProviderClientSyncProgress(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<MaintenanceSessionCancelledEvent>(ev =>
        HandleComputerMaintenanceSessionCancelledEvent(ctxFactory, hubContext, ev));
    domainEventReceiver
      .AddEventHandler<AgentIdentificationLogAddedEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"AgentIdentificationLogs").AddAgentIdentificationLog(new AgentIdentificationLogResource(ev))).Forget());
    domainEventReceiver
      .AddEventHandler<EphemeralAgentEvent>(ev =>
      {
        if (ev.TimelineEventType is TimelineEventType.EphemeralAgentSessionConnected)
        {
          Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").EphemeralAgentConnected(ev.ComputerId)).Forget();
        }
        else if (ev.TimelineEventType is TimelineEventType.EphemeralAgentSessionDisconnected)
        {
          Task.Run(async () => await hubContext.Clients.Group($"Computer:{ev.ComputerId}").EphemeralAgentDisconnected(ev.ComputerId)).Forget();
        }
      });
    domainEventReceiver
      .AddEventHandler<SessionGroupEvent>(ev =>
        Task.Run(async () => await hubContext.Clients.Group($"SessionGroup:{ev.SessionGroupId}").SessionGroupEvent(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<ProviderAuditLogAddedEvent>(ev =>
        Task.Run(async () =>
          await hubContext.Clients.Group($"ProviderLink:{ev.Log.ProviderLinkId}").ProviderAuditLogAdded(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<OauthHookSucceededEvent>(ev =>
        Task.Run(async () =>
          await hubContext.Clients.Group($"OauthHook:{ev.Hook.Id}").OauthHookSucceeded(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<OauthHookFailedEvent>(ev =>
        Task.Run(async () =>
          await hubContext.Clients.Group($"OauthHook:{ev.Hook.Id}").OauthHookFailed(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<AzureCustomerPreconsentStartedEvent>(ev => Task.Run(async () => await hubContext.Clients.Group($"AzureMultiCustomerPreconsent:{ev.PartnerTenantPrincipalId}").AzureCustomerPreconsentStarted(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<AzureCustomerPreconsentFinishedEvent>(ev => Task.Run(async () => await hubContext.Clients.Group($"AzureMultiCustomerPreconsent:{ev.PartnerTenantPrincipalId}").AzureCustomerPreconsentFinished(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<AzureCustomerPreconsentProgressMessageAddedEvent>(ev => Task.Run(async () => await hubContext.Clients.Group($"AzureMultiCustomerPreconsent:{ev.PartnerTenantPrincipalId}").AzureCustomerPreconsentProgressMessageAdded(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<AzureMultiCustomerPreconsentFailedEvent>(ev => Task.Run(async () => await hubContext.Clients.Group($"AzureMultiCustomerPreconsent:{ev.PartnerTenantPrincipalId}").AzureMultiCustomerPreconsentFailed(ev)).Forget());
    domainEventReceiver
      .AddEventHandler<AzureMultiCustomerPreconsentFinishedEvent>(ev => Task.Run(async () => await hubContext.Clients.Group($"AzureMultiCustomerPreconsent:{ev.PartnerTenantPrincipalId}").AzureMultiCustomerPreconsentFinished(ev)).Forget());

    domainEventReceiver
      .AddEventHandler<NotificationSavedEvent>(ev =>
        Task.Run(async () =>
        {
          if (ev.Notification.OnlyForUserId is { } userId)
          {
            await hubContext.Clients.Group($"User:{userId}").NotificationAdded(ev.Notification);
            return;
          }
          if (ev.Notification.TenantId is { } tenantId)
          {
            await hubContext.Clients.Group($"Tenant:{tenantId}").NotificationAdded(ev.Notification);
            return;
          }

          await hubContext.Clients.Group($"MspTenant").NotificationAdded(ev.Notification);
        }).Forget());
  }

  private static void HandleComputerMaintenanceSessionCancelledEvent(Func<ImmybotDbContext> ctxFactory,
    IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    MaintenanceSessionCancelledEvent ev)
  {
    using var ctx = ctxFactory();
    var session =
      ctx.MaintenanceSessions
      .AsNoTracking()
      .Include(s => s.Stages)
      .Include(s => s.Computer)
      .First(s => s.Id == ev.SessionId);

    session.SessionStatus = SessionStatus.Cancelled;

    foreach (var stage in session.Stages.Where(s => s.StageStatus != SessionStatus.Passed))
      stage.StageStatus = SessionStatus.Cancelled;

    Task.Run(async () => await hubContext.Clients
      .Groups($"Session:{ev.SessionId}")
      .UpdateMaintenanceSession(new UpdateMaintenanceSessionResource(session))).Forget();
  }
}
