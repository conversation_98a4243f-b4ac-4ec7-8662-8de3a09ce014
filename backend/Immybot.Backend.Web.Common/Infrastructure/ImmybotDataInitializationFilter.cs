using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.Preferences;
using Immybot.Backend.Application.DbContextExtensions.UserExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.KeyVaultRepositories;
using Immybot.Backend.Domain.Events;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Manager.Domain;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Manager.Shared;
using Immybot.Shared.Services.Startup;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Web.Common.Infrastructure;

public class ImmybotDataInitializationFilter(
  IServiceProvider serviceProvider,
  IOptions<AzureActiveDirectoryAuthOptions> azAdOpts,
  IOptions<DatabaseOptions> dbOpts,
  IMspAdminBuiltinRole mspAdminBuiltinRole,
  IFeatureTracker featureTracker)
  : IStartupTask
{
  private readonly DatabaseOptions _dbOpts = dbOpts.Value;
  private readonly AzureActiveDirectoryAuthOptions _azAdOpts = azAdOpts.Value;

  public async Task ExecuteAsync(CancellationToken cancellationToken = default)
  {
    using var scope = serviceProvider.CreateScope();

    await InitializeDb(scope, cancellationToken);
    MigrateGlobalAssignmentApprovals(scope);

    await MigrateAzureTenantAuthDetailsToKeyVault(scope);

    // Run non-EF migrations that haven't been run yet
    await RunImmyMigrations(scope);
  }

  private static async Task RunImmyMigrations(IServiceScope scope)
  {
    var appStopping = scope.ServiceProvider.GetRequiredService<IHostApplicationLifetime>().ApplicationStopping;
    var migrators = scope.ServiceProvider.GetServices<IImmyMigrator>().OrderBy(m => m.SortOrder).ToList();

    await using var immybotDb = scope.ServiceProvider.GetRequiredService<ImmybotDbContext>();
    var migrationsRan = await immybotDb.ImmyMigrations.Select(m => m.Name).ToListAsync();

    foreach (var migrator in migrators)
    {
      var migrationName = migrator.GetType().Name;
      if (migrationsRan.Contains(migrationName)) continue;

      await migrator.Migrate(appStopping);

      immybotDb.ImmyMigrations.Add(new ImmyMigration { Name = migrationName, DateRanUtc = DateTime.UtcNow });
      await immybotDb.SaveChangesAsync(appStopping);
    }
  }

  private static void MigrateGlobalAssignmentApprovals(IServiceScope scope)
  {
    using var immybotDb = scope.ServiceProvider.GetRequiredService<ImmybotDbContext>();
    // automatically approve all global assignments and initialize the maintenance item ordering
    // for new instances
    if (immybotDb.NeedsGlobalAssignmentApprovalInitialization())
    {
      using var softwareDbContext = scope.ServiceProvider
        .GetRequiredService<SoftwareDbContext>();

      var globalAssignments = softwareDbContext.TargetAssignments
        .AsNoTracking()
        .Where(a => a.AutoApprove)
        .Where(a => a.IntegrationTypeId == null)
        .ToList();

      var approvals = globalAssignments.Select(a => new RecommendedTargetAssignmentApproval
      {
        GlobalTargetAssignmentId = a.Id,
        Approved = true,
      });

      if (approvals.Any())
      {
        immybotDb.RecommendedTargetAssignmentApprovals.AddRange(approvals);
        immybotDb.SaveChanges();

        var updateOrderCmd = scope.ServiceProvider
          .GetRequiredService<IUpdateMaintenanceItemOrderCmd>();
        var retrieveOrderCmd = scope.ServiceProvider
          .GetRequiredService<IRetrieveMaintenanceItemOrdersCmd>();

        var orders = retrieveOrderCmd.Run();

        var ind = 0;
        foreach (var ass in globalAssignments.OrderBy(a => a.SortOrder))
        {
          var order = orders.Find(a =>
            a.MaintenanceIdentifier == ass.MaintenanceIdentifier && a.MaintenanceType == ass.MaintenanceType);
          if (order == null) continue;

          updateOrderCmd.Run(
            new UpdateMaintenanceItemOrderPayload(order.Id, ind, MaintenanceItemOrderLocation.Beginning));
          ind++;
        }
      }
    }
  }

  private async Task InitializeDb(IServiceScope scope, CancellationToken cancellationToken)
  {
    await using var immybotDb = scope.ServiceProvider.GetRequiredService<ImmybotDbContext>();
    if (immybotDb.NeedsInitialization())
    {
      var createTenantCmd = scope.ServiceProvider.GetRequiredService<ICreateTenantCmd>();
      var mspTenantPrincipalId = _azAdOpts.MasterTenantId;
      var mspTenantName = _azAdOpts.MasterTenantName;
      var preloadUserPrincipalIds = _dbOpts.PreloadUserPrincipalIds;

      // populate the tenant preferences cache before creating the msp tenant
      var tenantPrefs = scope.ServiceProvider
        .GetRequiredService<ICachedCollection<TenantPreferences>>();
      tenantPrefs.Value = immybotDb.GetTenantPreferences();

      // init msp tenant in db
      var tenant = await immybotDb.GetAllTenantsLinkedToAzure(principalId: mspTenantPrincipalId)
        .FirstOrDefaultAsync(cancellationToken) ?? await createTenantCmd.CreateTenant(
        new CreateTenantPayload(
          mspTenantName,
          Active: true,
          PrincipalId: mspTenantPrincipalId,
          IsMsp: true),
        ownerTenantId: null,
        cancellationToken);

      var preloadUsers = (preloadUserPrincipalIds ?? "").Split(";");
      var domainEventEmitter = scope.ServiceProvider
        .GetRequiredService<IDomainEventEmitter>();
      foreach (var preloadId in preloadUsers)
      {
        if (string.IsNullOrEmpty(preloadId)) continue;
        // if rbac is enabled, then assign the admin role to the preload user
        var u = new User
        {
          // todo; remove once rbac feature flag is removed
          IsAdmin = true,
          TenantId = tenant.Id,
          Person = new Person
          {
            FirstName = "",
            LastName = "",
            EmailAddress = "",
            TenantId = tenant.Id,
            AzurePrincipalId = preloadId,
          },
        };

        if (featureTracker.IsEnabled(FeatureEnum.RBACFeature))
        {
          u.UserRoles.Add(new UserRole { RoleId = mspAdminBuiltinRole.Role.Id });
        }

        var user = immybotDb.CreateUser(u);
        domainEventEmitter.EmitEvent(new UserCreatedEvent(user));
      }

      // init branding
      if (!await immybotDb.GetAllBrandings().AnyAsync(cancellationToken))
      {
        immybotDb.CreateBranding(new CreateBrandingRequestBody()
        {
          Description = "Global Default Brand",
          FromAddress = "<EMAIL>",
          MascotImgUri = "https://i3.createsend1.com/ti/d/4E/B4A/5A1/eblogo/Immybot_Maintenance2.png",
          MascotName = "ImmyBot",
          LogoUri = "http://i4.cmail20.com/ei/d/7b/e7c/48d/044444/immense-logo.png",
          LogoAltText = tenant.Name,
          BackgroundColor = "#222222",
          ForegroundColor = "#f7f5f2",
          TableHeaderColor = "#3d238a",
          TenantId = tenant.Id,
        });
      }

      // init application preferences
      immybotDb.InitPreferences();
      var appPreferences = immybotDb.GetApplicationPreferences();
      if (appPreferences.DefaultBrandingId == null)
      {
        var branding = await immybotDb.GetAllBrandings().FirstAsync(cancellationToken);
        appPreferences.DefaultBrandingId = branding.Id;
        immybotDb.UpdateApplicationPreferences(appPreferences);
      }
    }

    // populate the ApplicationPreferences cache after we know that it exists
    var cachedAppPrefs = scope.ServiceProvider
      .GetRequiredService<ICachedSingleton<ApplicationPreferences>>();
    cachedAppPrefs.Value = immybotDb.GetApplicationPreferences();

    // re-populate the tenant preferences cache
    var cachedTenantPrefs = scope.ServiceProvider
      .GetRequiredService<ICachedCollection<TenantPreferences>>();
    cachedTenantPrefs.Value = immybotDb.GetTenantPreferences();
  }

  private static async Task MigrateAzureTenantAuthDetailsToKeyVault(IServiceScope scope)
  {
    await using var immybotDb = scope.ServiceProvider.GetRequiredService<ImmybotDbContext>();
    var mgr = scope.ServiceProvider.GetRequiredService<IManagerProvidedSettings>();
    if (mgr.AzurePermissionLevel == AzurePermissionLevel.Me) return;

    var rootTenant = immybotDb.GetRootTenant();
    if (rootTenant?.AzureTenantLink?.AzureTenant is not { } azTenant) return;

    var azureTenantAuthDetailsRepository = scope.ServiceProvider
      .GetRequiredService<IAzureTenantAuthDetailsRepository>();
    var appLifetime = scope.ServiceProvider
      .GetRequiredService<IHostApplicationLifetime>();

    var authDetails = await azureTenantAuthDetailsRepository
      .GetAzureTenantAuthDetailsByAzurePrincipalId(azTenant.PrincipalId, appLifetime.ApplicationStopping);
    if (authDetails != null) return; // already migrated

    var updateAzureTenantAuthDetailsCmd = scope.ServiceProvider
      .GetRequiredService<IUpdateAzureTenantAuthDetailsCmd>();
    await updateAzureTenantAuthDetailsCmd.UpdateAzureTenantAuthDetails(new UpdateAzureTenantAuthDetailsCmdPayload(
        new AzureTenantAuthDetails(azTenant.PrincipalId,
          mgr.AzurePermissionLevel switch
          {
            AzurePermissionLevel.MyCustomers or AzurePermissionLevel.CustomAppRegistrationWithGdap when
              mgr.CspPreconsentAppRegistrationAppId is not null && mgr.CspPreconsentAppRegistrationSecret is not null
             => AzurePermissionLevel2.CustomAppRegistration,
            _ => AzurePermissionLevel2.DefaultAppRegistration,
          },
          mgr.CspPreconsentAppRegistrationAppId,
          mgr.CspPreconsentAppRegistrationSecret)),
      cancellationToken: appLifetime.ApplicationStopping);
  }
}
