using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Shared.Services.Startup;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Web.Common.Infrastructure;

/// <summary> Responsible for setting the Z.EntityFramework.Extensions license. </summary>
internal class ZEntityFrameworkInitializer(IOptionsMonitor<ZEntityFrameworkExtensionsOptions> zOpts) : IStartupTask
{
  public Task ExecuteAsync(CancellationToken cancellationToken = default)
  {
    zOpts.OnChange(InitializeLicenseManager);
    InitializeLicenseManager(zOpts.CurrentValue);
    return Task.CompletedTask;
  }

  private static void InitializeLicenseManager(ZEntityFrameworkExtensionsOptions zOptions)
  {
    if (string.IsNullOrEmpty(zOptions.LicenseName) || string.IsNullOrEmpty(zOptions.LicenseKey))
      return;

    Z.EntityFramework.Extensions.LicenseManager.AddLicense(zOptions.LicenseName, zOptions.LicenseKey);

    if (!Z.EntityFramework.Extensions.LicenseManager.ValidateLicense(out var licenseErrorMessage))
    {
      throw new InvalidOperationException(licenseErrorMessage);
    }
  }
}
