namespace Immybot.Backend.Web.Common.Controllers;

public static class Helpers
{
  public static bool ParseIncluders(string? include, string relationshipName)
  {
    var includers = (include ?? string.Empty).Split(",");
    return includers.Contains(relationshipName);
  }

  public static (bool, bool) ParseIncluders(string? include, string r1, string r2)
  {
  bool i1 = false, i2 = false;
    foreach (var r in (include ?? string.Empty).Split(","))
    {
      if (r == r1) i1 = true;
      else if (r == r2) i2 = true;
    }
    return (i1, i2);
  }

  public static (bool, bool, bool) ParseIncluders(string? include, string r1, string r2, string r3)
  {
    bool i1 = false, i2 = false, i3 = false;
    foreach (var r in (include ?? string.Empty).Split(","))
    {
      if (r == r1) i1 = true;
      else if (r == r2) i2 = true;
      else if (r == r3) i3 = true;
    }
    return (i1, i2, i3);
  }
}
