using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.MaintenanceActionExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Extensions;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NuGet.Versioning;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class MaintenanceActionsController(
  IPermissionFilterBuilder permissionFilterBuilder,
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IResourceAuthorizerFlow resourceAuthorizerFlow)
  : Controller
{
  private static IEnumerable<GetMaintenanceActionResponse> MergeComputerInfoIntoActions(
    IQueryable<GetMaintenanceActionResponse> actions,
    IEnumerable<ComputerInfo> computers)
  {
    var dict = computers.ToDictionary(c => c.Id, c => new { c.Name, c.IsOnline });
    foreach (var a in actions)
    {
      if (a.ComputerId is { } i && dict.TryGetValue(i, out var info))
      {
        a.ComputerName = info.Name;
        a.IsComputerOnline = info.IsOnline;
      }
      yield return a;
    }
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceActionApiRoutes.DxGetAll)]
  public ActionResult<GetMaintenanceActionResponse[]> DxGetAll(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    DataSourceLoadOptions loadOptions,
    [FromQuery] AppSieveModel? sieveModel = null)
  {
    var filter = permissionFilterBuilder
      .BuildFilterExpression<MaintenanceSession, IMaintenanceSessionsViewPermission>();
    var q = ctx.MaintenanceSessions
      .AsNoTracking()
      .AsSplitQuery()
      .Where(filter)
      .SelectMany(a => a.MaintenanceActions);

    if (sieveModel != null) q = sieveProcessor.Apply(sieveModel, q);
    var actions = q.Select(GetMaintenanceActionResponse.Projection);
    return Ok(DataSourceLoader.Load(actions, loadOptions));
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceActionApiRoutes.GetLatestNonCompliantMaintenanceActionsForTenant)]
  public async Task<ActionResult<object[]>> GetLatestNonCompliantMaintenanceActionsForTenant(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int tenantId,
    [FromQuery] DateTime? dateUtc = null)
  {
    // new DateTime is 01-01-0001
    var date = dateUtc ?? DateTime.MinValue;

    if (tenantId == 0) return BadRequest("TenantId is required");

    await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IMaintenanceSessionsViewPermission>(
      User,
      tenantId,
      strict: true);

    var q = from i in ctx.MaintenanceActions
            join o in
              from a in ctx.MaintenanceActions
              where a.TenantId == tenantId && a.MaintenanceType != MaintenanceType.WindowsUpdate
              group a by new { a.MaintenanceType, a.MaintenanceIdentifier, a.ComputerId } into g
              select new
              {
                g.Key,
                LastActionId = g.Max(aa => aa.Id),
              }
             on i.Id equals o.LastActionId
            where i.ActionResult == MaintenanceActionResult.Failed && i.CreatedDate >= date
            group i by new { i.MaintenanceType, i.MaintenanceIdentifier } into g2
            select new
            {
              g2.Key.MaintenanceType,
              g2.Key.MaintenanceIdentifier,
              Count = g2.Count()
            };

    return Ok(q.OrderByDescending(a => a.Count));
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpPost(MaintenanceActionApiRoutes.GetLatestActionForComputers)]
  public async Task<ActionResult<GetMaintenanceActionResponse[]>> GetLatestActionForComputers(
    [FromServices] ImmybotDbContext ctx,
    [FromBody] GetLatestActionForComputersRequestBody body)
  {
    foreach (var computerId in body.ComputerIds)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
        new DefaultKeyParameters(computerId),
        true);
    }

    // new DateTime is 01-01-0001
    var date = body.DateUtc ?? DateTime.MinValue;

    var q = from i in ctx.MaintenanceActions
            join o in
              from a in ctx.MaintenanceActions
              where a.ComputerId != null && body != null && body.ComputerIds.Any(c => c == a.ComputerId) && a.MaintenanceType == body.MaintenanceType && a.MaintenanceIdentifier == body.MaintenanceIdentifier && (a.ActionResult == MaintenanceActionResult.Success || a.ActionResult == MaintenanceActionResult.Failed)
              group a by new { a.MaintenanceType, a.MaintenanceIdentifier, a.ComputerId } into g
              select new
              {
                g.Key,
                LastActionId = g.Max(aa => aa.Id),
              }
             on i.Id equals o.LastActionId
            where i.CreatedDate >= date
            select new
            {
              i.Id,
              i.MaintenanceSessionId,
              i.ComputerId,
              i.MaintenanceDisplayName,
              i.AssignmentId,
              i.AssignmentType,
              i.MaintenanceIdentifier,
              i.MaintenanceType,
              i.DetectedVersionString,
              i.DesiredVersionString,
              i.ActionType,
              i.StartTime,
              i.EndTime,
              i.MaintenanceTaskMode,
              i.DesiredSoftwareState,
              i.SoftwareType,
              i.MaintenanceTaskType,
              i.MaintenanceTaskGetResult,
              Reason = i.ActionReason,
              Result = i.ActionResult,
              Status = i.ActionStatus,
              ResultReason = i.ActionResultReason,
              ResultReasonMessage = i.ActionResultReasonMessage,
              CreatedDateUTC = i.CreatedDate,
              UpdatedDateUTC = i.UpdatedDate,
              i.PolicyDescription,
            };
    return Ok(q);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpPost(MaintenanceActionApiRoutes.GetLatestActionForTenants)]
  public async Task<ActionResult<object[]>> GetLatestActionForTenants(
    [FromServices] ImmybotDbContext ctx,
    [FromBody] GetLatestActionForTenantsRequestBody body)
  {
    foreach (var tenantId in body.TenantIds)
    {
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IMaintenanceSessionsViewPermission>(
        User,
        tenantId,
        strict: true);
    }

    // new DateTime is 01-01-0001
    var date = body.DateUtc ?? DateTime.MinValue;

    var q = from i in ctx.MaintenanceActions
            join o in
              from a in ctx.MaintenanceActions
              where a.TenantId != null && body != null && body.TenantIds.Contains(a.TenantId.Value) && a.ComputerId == null && a.MaintenanceType == body.MaintenanceType && a.MaintenanceIdentifier == body.MaintenanceIdentifier
              group a by new { a.MaintenanceType, a.MaintenanceIdentifier, a.TenantId } into g
              select new
              {
                g.Key,
                LastActionId = g.Max(aa => aa.Id),
              }
             on i.Id equals o.LastActionId
            where i.CreatedDate >= date && (i.ActionResult == MaintenanceActionResult.Success || i.ActionResult == MaintenanceActionResult.Failed)
            select new
            {
              i.Id,
              i.ComputerId,
              i.TenantId,
              i.MaintenanceSessionId,
              i.MaintenanceDisplayName,
              i.AssignmentId,
              i.AssignmentType,
              i.MaintenanceIdentifier,
              i.MaintenanceType,
              i.DetectedVersionString,
              i.DesiredVersionString,
              i.ActionType,
              i.StartTime,
              i.EndTime,
              i.MaintenanceTaskMode,
              i.DesiredSoftwareState,
              i.SoftwareType,
              i.MaintenanceTaskType,
              i.MaintenanceTaskGetResult,
              Reason = i.ActionReason,
              Result = i.ActionResult,
              Status = i.ActionStatus,
              CreatedDateUTC = i.CreatedDate,
              UpdatedDateUTC = i.UpdatedDate,
              ResultReason = i.ActionResultReason,
              ResultReasonMessage = i.ActionResultReasonMessage,
              i.PolicyDescription
            };
    return Ok(q);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceActionApiRoutes.GetLatestActionsForComputer)]
  public async Task<ActionResult<GetLatestActionForComputer[]>> GetLatestActionsForComputer(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);
    var computer = ctx.GetComputerById(computerId);
    if (computer == null) return NotFound();
    var q = from i in ctx.MaintenanceActions
            join o in
              from a in ctx.MaintenanceActions
              where a.ComputerId == computerId
                && a.MaintenanceType != MaintenanceType.WindowsUpdate
                && a.AssignmentId != null
                && (a.ActionResult == MaintenanceActionResult.Success || a.ActionResult == MaintenanceActionResult.Failed)
              group a by new { a.MaintenanceType, a.MaintenanceIdentifier } into g
              select new
              {
                g.Key,
                LastActionId = g.Max(aa => aa.Id),
              }
             on i.Id equals o.LastActionId
            select new
            {
              i.Id,
              i.MaintenanceSessionId,
              i.ActionType,
              Result = i.ActionResult,
              Reason = i.ActionReason,
              Status = i.ActionStatus,
              ResultReason = i.ActionResultReason,
              DetectedVersion = i.DetectedVersionString,
              DesiredVersion = i.DesiredVersionString,
              i.MaintenanceDisplayName,
              i.MaintenanceIdentifier,
              i.MaintenanceType,
              i.DesiredSoftwareState,
              i.MaintenanceTaskMode,
              i.AssignmentId,
              i.AssignmentType,
              i.SoftwareTableRegexString,
              i.StartTime,
              i.EndTime,
              i.CreatedDate,
              i.SoftwareActionIdForConfigurationTask,
              i.PolicyDescription
            };
    return Ok(q);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceActionApiRoutes.GetLatestActionsForTenant)]
  public async Task<ActionResult<object[]>> GetLatestActionsForTenant(
    [FromServices] ImmybotDbContext ctx,
    DataSourceLoadOptions loadOptions,
    [FromRoute] int tenantId,
    [FromQuery] DateTime? createdDateUtc = null,
    [FromQuery] SessionType sessionType = SessionType.Computer)
  {
    await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IMaintenanceSessionsViewPermission>(
      User,
      tenantId,
      strict: true);

    var tenant = ctx.GetTenantById(tenantId);
    if (tenant is null) return NotFound();

    // fetch all maintenance actions for the tenant. The data source load options will be applied later
    var q = ctx.MaintenanceActions
      .AsNoTracking()
      .Where(a =>
        a.TenantId == tenantId &&
        a.MaintenanceType != MaintenanceType.WindowsUpdate);

    // filter to cloud or computer session types
    q = sessionType is SessionType.Cloud ?
      q.Where(a => a.ComputerId == null) :
      q.Where(a => a.ComputerId != null);

    // filter by created date if provided
    if (createdDateUtc.HasValue)
    {
      q = q.Where(a => a.CreatedDate >= createdDateUtc.Value);
    }

    // select the fields we want to return and execute the query
    var actions = await q
      .Select(i => new
      {
        i.MaintenanceSessionId,
        i.Id,
        i.TenantId,
        i.ComputerId,
        i.MaintenanceSession!.Computer!.ComputerName,
        i.ActionType,
        Result = i.ActionResult,
        Reason = i.ActionReason,
        Status = i.ActionStatus,
        ResultReason = i.ActionResultReason,
        i.DetectedVersion,
        i.DesiredVersion,
        i.MaintenanceDisplayName,
        i.DesiredSoftwareState,
        i.MaintenanceIdentifier,
        i.MaintenanceTaskMode,
        i.MaintenanceType,
        i.AssignmentId,
        i.AssignmentType,
        i.SoftwareTableRegexString,
        i.StartTime,
        i.EndTime,
        i.CreatedDate,
        i.SoftwareActionIdForConfigurationTask,
        i.PolicyDescription
      })
      .ToListAsync();

    // in-memory group by maintenance type, maintenance identifier, and computer id
    var groupedActions = actions
      .GroupBy(a => new { a.MaintenanceType, a.MaintenanceIdentifier, a.ComputerId })
      .Select(g => new
      {
        g.Key,
        LastActionId = g.Max(aa => aa.Id),
      });

    // join the grouped actions with the original actions to get the latest list of full action details
    var latestActions = groupedActions
      .Join(actions, o => o.LastActionId, i => i.Id, (_, i) => i);

    // apply the data source load options and return the result (date filtering is done in the query and not here)
    return Ok(DataSourceLoader.Load(latestActions, loadOptions));
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceActionApiRoutes.DxGetActionsForComputer)]
  public async Task<ActionResult<LoadResult>> GetActionsForComputer(
    [FromServices] ImmybotDbContext ctx,
    DataSourceLoadOptions loadOptions,
    [FromRoute] int computerId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var computer = ctx.GetComputerById(computerId);
    if (computer == null) return NotFound();

    var actions = ctx.GetMaintenanceActions()
      .Where(a => a.ComputerId == computerId)
      .Select(GetMaintenanceActionResponse.Projection);
    if (loadOptions.Group?.Any() ?? false)
    {
      // this fixes a strange issue with the data loader throwing an error when grouping
      // https://github.com/DevExpress/DevExtreme.AspNet.Data/issues/428
      return Ok(DataSourceLoader.Load(actions.ToNonAsyncEnumerable(), loadOptions));
    }

    return Ok(await DataSourceLoader.LoadAsync(actions, loadOptions));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(MaintenanceActionApiRoutes.GetActionsForMaintenanceItem)]
  public ActionResult<GetMaintenanceActionResponse[]> GetActionsForMaintenanceItem(
    [FromServices] ImmybotDbContext ctx,
    [FromQuery] MaintenanceType maintenanceType,
    [FromQuery] string maintenanceIdentifier)
  {
    var filter = permissionFilterBuilder
      .BuildFilterExpression<MaintenanceSession, IMaintenanceSessionsViewPermission>();
    var q = ctx.MaintenanceSessions
      .AsNoTracking()
      .AsSplitQuery()
      .Where(filter)
      .SelectMany(a => a.MaintenanceActions)
      .Where(a => a.MaintenanceType == maintenanceType && a.MaintenanceIdentifier == maintenanceIdentifier);
    var sevenDaysAgo = DateTime.UtcNow.AddDays(-7);
    var actions = q.Where(a => a.CreatedDate >= sevenDaysAgo).Select(GetMaintenanceActionResponse.Projection);
    var computerIds = q.Where(a => a.ComputerId != null)
      .Select(a => a.ComputerId!.Value)
      .ToHashSet();
    var computers = ctx.GetComputerInfoForComputerIds(computerIds);
    return Ok(MergeComputerInfoIntoActions(actions, computers));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(MaintenanceActionApiRoutes.GetActionsForVersion)]
  public async Task<ActionResult<GetMaintenanceActionResponse[]>> GetActionsForSoftwareVersion(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ISoftwareActions softwareActions,
    [FromQuery] SoftwareType softwareType,
    [FromQuery] string softwareIdentifier,
    [FromQuery] SemanticVersion version,
    CancellationToken token)
  {
    if (softwareType is SoftwareType.LocalSoftware)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
        new DefaultKeyParameters(Convert.ToInt32(softwareIdentifier)),
        true);
    }
    var (software, softwareVersion) = await softwareActions
      .GetSoftwareAndVersion(softwareType, softwareIdentifier, version, token);
    if (software == null || softwareVersion == null) return NotFound();

    var filter = permissionFilterBuilder
      .BuildFilterExpression<MaintenanceSession, IMaintenanceSessionsViewPermission>();
    var q = ctx.MaintenanceSessions
      .AsNoTracking()
      .AsSplitQuery()
      .Where(filter)
      .SelectMany(a => a.MaintenanceActions)
      .Where(a => a.MaintenanceType == softwareVersion.MaintenanceType &&
                  a.MaintenanceIdentifier == softwareVersion.SoftwareIdentifier &&
                  a.DesiredVersion == softwareVersion.SemanticVersion);

    var sevenDaysAgo = DateTime.UtcNow.AddDays(-7);
    var actions = q.Where(a => a.CreatedDate >= sevenDaysAgo).Select(GetMaintenanceActionResponse.Projection);
    var computerIds = await q
      .Where(a => a.ComputerId != null)
      .Select(a => a.ComputerId!.Value)
      .ToHashSetAsync(cancellationToken: token);
    var computers = ctx.GetComputerInfoForComputerIds(computerIds);
    return Ok(MergeComputerInfoIntoActions(actions, computers));
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceActionApiRoutes.GetLogsForAction)]
  public ActionResult<GetMaintenanceSessionLogResponse[]> GetLogsForAction(
    [FromRoute] int actionId,
    [FromServices] ImmybotDbContext ctx)
  {
    var filter = permissionFilterBuilder
      .BuildFilterExpression<MaintenanceSession, IMaintenanceSessionsViewPermission>();
    var logs = ctx.MaintenanceSessions
      .AsNoTracking()
      .AsSplitQuery()
      .TagForTelemetry()
      .Where(filter)
      .SelectMany(a => a.MaintenanceActions)
      .Where(a => a.Id == actionId)
      .SelectMany(a => a.Logs)
      .ToList();

    return Ok(logs.Select(log => new GetMaintenanceSessionLogResponse(log)).OrderBy(a => a.Id));
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(MaintenanceActionApiRoutes.GetActionsNeedingAttentionForComputer)]
  public async Task<ActionResult<GetMaintenanceActionResponse[]>> GetActionsNeedingAttentionForComputer(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var computer = ctx.GetComputerById(
      computerId);
    if (computer is null) return NotFound();

    var actions = ctx.GetActionsNeedingAttentionForComputer(computerId);
    return Ok(actions.Select(GetMaintenanceActionResponse.Projection));
  }
}
