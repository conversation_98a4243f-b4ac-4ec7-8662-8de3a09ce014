using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using Immybot.Backend.Application.DbContextExtensions.LicenseExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class LicensesController(
  IPermissionFilterBuilder permissionFilterBuilder,
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IResourceAuthorizerFlow authorizer,
  IUserService userService)
  : Controller
{
  [SubjectPermissionAuthorize(typeof(ILicensesViewPermission))]
  [HttpGet(LicenseApiRoutes.Dx)]
  public ActionResult<LoadResult> DxGet(
    DataSourceLoadOptions loadOptions,
    [FromServices] ImmybotDbContext ctx)
  {
    var filter = permissionFilterBuilder.BuildFilterExpression<License, ILicensesViewPermission>();
    var q = ctx
      .GetLicenses()
      .Where(filter)
      .Select(GetLicenseResponse.Projection);
    return Ok(DataSourceLoader.Load(q, loadOptions));
  }

  [SubjectPermissionAuthorize(typeof(ILicensesViewPermission))]
  [HttpGet(LicenseApiRoutes.GetAll)]
  public ActionResult<IEnumerable<GetLicenseResponse>> GetAll(
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromServices] ImmybotDbContext ctx,
    [FromQuery] string? filters = null)
  {
    var filter = permissionFilterBuilder.BuildFilterExpression<License, ILicensesViewPermission>();
    var q = ctx
      .GetLicenses()
      .Where(filter);

    if (!string.IsNullOrWhiteSpace(filters))
    {
      var sieveModel = new AppSieveModel() { Filters = filters };
      q = sieveProcessor.Apply(sieveModel, q);
    }

    var licenses = q.Select(GetLicenseResponse.Projection);
    return Ok(licenses);
  }

  [SubjectPermissionAuthorize(typeof(ILicensesViewPermission))]
  [HttpGet(LicenseApiRoutes.Get)]
  public async Task<ActionResult<GetLicenseResponse>> Get(
    [FromRoute] int licenseId,
    [FromServices] ImmybotDbContext ctx)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<License, ILicensesViewPermission>(
      new DefaultKeyParameters(licenseId),
      true);
    var license = ctx.GetLicense(licenseId);
    if (license == null) return NotFound();
    return Ok(new GetLicenseResponse(license));
  }

  [SubjectPermissionAuthorize(typeof(ILicensesManagePermission))]
  [HttpPut(LicenseApiRoutes.Update)]
  public async Task<ActionResult<GetLicenseResponse>> Update(
    [FromRoute] int licenseId,
    [FromBody] UpdateLicenseRequestBody body,
    [FromServices] ISoftwareActions softwareActions,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IHostApplicationLifetime lifetime)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<License, ILicensesViewPermission>(
      new DefaultKeyParameters(body.Id),
      true);
    await using var ctx = dbFactory();
    var license = ctx.GetLicense(body.Id);
    if (license == null) return NotFound();

    if (body.SoftwareType is SoftwareType.LocalSoftware)
    {
      await authorizer.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
        new DefaultKeyParameters(Convert.ToInt32(body.SoftwareIdentifier)),
        true);
    }

    var software = await softwareActions
      .GetSoftware(body.SoftwareType, body.SoftwareIdentifier, lifetime.ApplicationStopping);

    if (software is not null)
      body.SoftwareName = software.Name;

    if (body.TenantId != license.TenantId)
    {
      if (body.TenantId.HasValue)
      {
        await subjectPermissionAuthorizationService
          .AuthorizeTenantAsync<ILicensesManagePermission>(
            User,
            body.TenantId.Value,
            strict: true);
      }
      else
      {
        var canManageLicenseForAllTenants = await subjectPermissionAuthorizationService
          .AuthorizeGlobalAsync<ILicensesManagePermission>(
            User,
            strict: false);

        if (!canManageLicenseForAllTenants)
        {
          var currentUser = userService.GetCurrentUser();
          await subjectPermissionAuthorizationService
            .AuthorizeTenantAsync<ILicensesManagePermission>(
              User,
              currentUser.TenantId,
              strict: true);
          body.TenantId = currentUser.TenantId;
        }
      }
    }

    var updated = ctx.UpdateLicense(body);
    if (updated is null) return BadRequest("Failed to update license");
    return Ok(new GetLicenseResponse(updated));
  }

  [SubjectPermissionAuthorize(typeof(ILicensesManagePermission))]
  [HttpPost(LicenseApiRoutes.Create)]
  public async Task<ActionResult<GetLicenseResponse>> Create(
    [FromBody] CreateLicenseRequestBody body,
    [FromServices] ISoftwareActions softwareActions,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IHostApplicationLifetime lifetime)
  {
    // can the user create a license for the given tenant
    if (body.TenantId.HasValue)
    {
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<ILicensesManagePermission>(
        User,
        body.TenantId.Value,
        strict: true);
    }
    // if they can't create a license at the global scope, then default to creating a license for their own tenant
    else if (!await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<ILicensesManagePermission>(User,
               strict: false))
    {
      // can the user create a license for their own tenant
      var tenantId = userService.GetTenantId();
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<ILicensesManagePermission>(
        User,
        tenantId,
        strict: true);
      body.TenantId = tenantId;
    }

    if (body.SoftwareType is SoftwareType.LocalSoftware)
    {
      await authorizer.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
        new DefaultKeyParameters(Convert.ToInt32(body.SoftwareIdentifier)),
        true);
    }
    await using var ctx = dbFactory();
    var software = await softwareActions
      .GetSoftware(body.SoftwareType, body.SoftwareIdentifier, lifetime.ApplicationStopping);

    if (software is { Name: not null })
      body.SoftwareName = software.Name;


    var created = ctx.CreateLicense(body);
    return Ok(new GetLicenseResponse(created));
  }

  [SubjectPermissionAuthorize(typeof(ILicensesManagePermission))]
  [HttpDelete(LicenseApiRoutes.Delete)]
  public async Task<ActionResult> Delete(
    [FromRoute] int licenseId,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<License, ILicensesViewPermission>(
      new DefaultKeyParameters(licenseId),
      true);

    await using var ctx = dbFactory();
    var license = ctx.GetLicense(licenseId);
    if (license == null) return NotFound();

    ctx.DeleteLicense(licenseId);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ILicensesManagePermission))]
  [HttpPost(LicenseApiRoutes.Upload)]
  public async Task<ActionResult> Upload(
    [FromServices] IAzureBlobStorageUploadService azureBlobStorageService,
    IFormFile? file = null,
    [FromForm] string? chunkMetadata = null)
  {
    if (file == null || string.IsNullOrEmpty(chunkMetadata)) return NoContent();

    var metaDataObject = JsonConvert.DeserializeObject<ChunkMetadata>(chunkMetadata);
    if (metaDataObject is null) return BadRequest();
    var identifier = $"{metaDataObject.FileGuid}-{metaDataObject.FileName}";
    var licenseData = new AzureLicenseUploadData
    {
      FileGuid = new Guid(metaDataObject.FileGuid), FileName = metaDataObject.FileName
    };

    // ensure reference to blob is good and upload chunk
    await azureBlobStorageService.PrepareCloudBlockBlobForLicense(identifier, licenseData);
    await azureBlobStorageService.UploadChunkForBlob(identifier, file);

    // once all chunks are uploaded, commit chunks and return uri to resource
    if (metaDataObject.Index == metaDataObject.TotalCount - 1)
    {
      var blobName = await azureBlobStorageService.CommitBlocks(identifier);
      return Ok(blobName);
    }

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ILicensesDownloadPermission))]
  [HttpGet(LicenseApiRoutes.GetDownloadUrl)]
  public async Task<ActionResult<string?>> GetDownloadUrl(
    [FromRoute] int licenseId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IAzureBlobStorageSasService azureBlobStorageSasService)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<License, ILicensesDownloadPermission>(
      new DefaultKeyParameters(licenseId),
      true);
    var license = ctx.GetLicense(licenseId);
    if(license is null) return NotFound();
    var uri = azureBlobStorageSasService.GetLicenseDownloadUrl(license.LicenseValue);
    return Ok(uri);
  }
}
