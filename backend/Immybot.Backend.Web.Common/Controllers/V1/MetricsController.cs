using Microsoft.AspNetCore.Mvc;
using System.Text;
using App.Metrics;
using App.Metrics.Formatters.Json.Extensions;
using Polly.Registry;
using Immybot.Backend.Application.Lib.Policies;
using App.Metrics.Formatters.Json;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class MetricsController : Controller
{
  [SubjectPermissionAuthorize(typeof(IMetricsViewPermission))]
  [ApiExplorerSettings(IgnoreApi = true)]
  [HttpGet(MetricApiRoutes.GetAppMetrics)]
  public ActionResult<MetricData> GetAppMetrics([FromServices] IMetrics metrics)
  {
    var snap = metrics.Snapshot.Get();
    return Ok(snap.ToMetric());
  }

  [SubjectPermissionAuthorize(typeof(IMetricsViewPermission))]
  [HttpGet(MetricApiRoutes.GetCircuitBreakers)]
  public ActionResult<IEnumerable<CircuitBreakerState>> GetCircuitBreakers(
    [FromServices] IPolicyRegistry<string> policyRegistry)
  {
    var circuitBreakers = policyRegistry.GetAllCircuitBreakerPolicies();
    return Ok(circuitBreakers.Select(b => new CircuitBreakerState(b.Key, b.Value.CircuitState.ToString(), b.Value.LastException)));
  }

  [SubjectPermissionAuthorize(typeof(IMetricsManagePermission))]
  [HttpPost(MetricApiRoutes.IsolateCircuitBreaker)]
  public IActionResult IsolateCircuitBreaker(
    [FromServices] IPolicyRegistry<string> policyRegistry,
    [FromQuery] string policyName)
  {
    var circuitBreakers = policyRegistry.GetAllCircuitBreakerPolicies();
    if (!circuitBreakers.TryGetValue(policyName, out var breaker))
      return NotFound("No circuit breaker found with that policy name");
    breaker.Isolate();
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IMetricsManagePermission))]
  [HttpPost(MetricApiRoutes.ResetCircuitBreaker)]
  public IActionResult ResetCircuitBreaker(
    [FromServices] IPolicyRegistry<string> policyRegistry,
    [FromQuery] string policyName)
  {
    var circuitBreakers = policyRegistry.GetAllCircuitBreakerPolicies();
    if (!circuitBreakers.TryGetValue(policyName, out var breaker))
      return NotFound("No circuit breaker found with that policy name");
    breaker.Reset();
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IMetricsViewPermission))]
  [HttpGet(MetricApiRoutes.GetAllProviderLinkMetrics)]
  public async Task GetAllProviderLinkMetrics(
    [FromServices] IProviderMetrics providerMetrics,
    CancellationToken token)
  {
    var metrics = providerMetrics.GetMetrics();
    using var streamerLock = new SemaphoreSlim(1, 1);
    var tasks = new List<Task>(metrics.Count);
    Response.ContentType = "application/json";
    await Response.Body.WriteAsync(Encoding.UTF8.GetBytes("{"));
    var didWriteFirst = false;
    foreach (var m in metrics)
    {
      tasks.Add(Task.Run(async () =>
      {
        var snap = m.Value.Snapshot.Get(); // multiple tasks spun off, whichever ones finish this command first get to write first
        await streamerLock.WaitAsync(); // lock before we write to prevent writing at the same time as another thread
        try
        {
          if (didWriteFirst)
          {
            await Response.Body.WriteAsync(Encoding.UTF8.GetBytes(","));
          }
          await Response.Body.WriteAsync(Encoding.UTF8.GetBytes($"\"{m.Key}\":"));
          await m.Value.DefaultOutputMetricsFormatter.WriteAsync(Response.Body, snap, token);
          didWriteFirst = true;
        }
        finally
        {
          streamerLock.Release();
        }
      }));
    }
    await Task.WhenAll(tasks);
    await Response.Body.WriteAsync(Encoding.UTF8.GetBytes("}"));
  }
}
