using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class DevLabController : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(IDevLabManagePermission))]
  [HttpPost(DevLabApiRoutes.UnclaimVm)]
  public async Task<IActionResult> UnclaimVm(
    [FromServices] IDevLabActions devLabActions,
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId,
    CancellationToken token)
  {
    var computer = ctx.GetComputerById(computerId, asNoTracking: true);
    if (computer is null) return NotFound();

    if (computer.DevLabVmName is null) return BadRequest("This computer is not a dev lab machine");

    await devLabActions.UnclaimVm(computer.Id, computer.DevLabVmName, token);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IDevLabManagePermission))]
  [HttpGet(DevLabApiRoutes.DownloadRdpFile)]
  public async Task<IActionResult> DownloadRdpFile(
    [FromServices] IDevLabActions devLabActions,
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId)
  {
    var computer = ctx.GetComputerById(computerId, asNoTracking: true);
    var now = DateTime.UtcNow;
    if (computer is null ||
      computer.DevLabVmName is null ||
      computer.DevLabVmClaimExpirationDateUtc < now) return NotFound();

    var res = await devLabActions.GetRdpInfo(computer.DevLabVmName);
    return Ok(res);
  }
}
