using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions.MaintenanceSessionExtensions;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Manager.Domain;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Manager.Shared;
using Immybot.Manager.SitesApi;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using UpdateReleaseChannelRequest = Immybot.Manager.SitesApi.UpdateReleaseChannelRequest;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class SystemController(
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IUserService userService) : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(ISystemOperationsGetReleasesPermission))]
  [HttpGet(SystemApiRoutes.GetReleases)]
  public ActionResult<GetReleasesResponse> GetReleases(
    [FromServices] IManagerProvidedSettings manager)
  {
    var res = new GetReleasesResponse
    {
      CurrentRelease = manager.CurrentRelease,
      LatestReleases = manager.GetLatestReleases()
    };
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(ISystemOperationsPullUpdatesPermission))]
  [HttpPost(SystemApiRoutes.PullUpdate)]
  public async Task<IActionResult> UpdateInstance(
    [FromServices] IImmyBotUpgradeActions upgradeActions)
  {
    var user = userService.GetCurrentUser();
    await upgradeActions.UpdateNow(requester: user);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(SystemApiRoutes.GetTimezones)]
  public ActionResult<TimeZoneResource[]> GetTimezones()
  {
    return Ok(TimeZoneInfo.GetSystemTimeZones().Select(a => new TimeZoneResource(a.Id, a.DisplayName)));
  }

  [SubjectPermissionAuthorize(typeof(ISystemOperationsRestartBackendPermission))]
  [HttpPost(SystemApiRoutes.RestartBackend)]
  public async Task<IActionResult> RestartBackend(
    [FromServices] IManagerProvidedSettings manager)
  {
    await manager.RestartBackend();
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ISystemOperationsPullUpdatesPermission))]
  [HttpPost(SystemApiRoutes.UpdateReleaseChannel)]
  public async Task<IActionResult> UpdateReleaseChannel(
    [FromServices] IManagerProvidedSettings manager,
    [FromBody] UpdateReleaseChannelRequest req)
  {
    await manager.UpdateReleaseChannel(req.ReleaseChannel);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ISystemOperationsManageSupportAccessPermission))]
  [HttpGet(SystemApiRoutes.GetImmySupportAccessGrantDetails)]
  public ActionResult<MspInstanceImmySupportAccessGrantDetails?> GetImmySupportAccessGrantDetails(
  [FromServices] IManagerProvidedSettings mgrSettings)
  {
    return Ok(mgrSettings.ImmySupportAccessGrantDetails);
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(SystemApiRoutes.RequestSessionSupport)]
  public async Task<IActionResult> RequestSessionSupport(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ISupportRequestGenerator supportRequestGenerator,
    [FromBody] RequestSessionSupportRequestBody body,
    CancellationToken token)
  {
    var session = await ctx.GetMaintenanceSessionByIdAsync(body.SessionId);
    if (session == null) return NotFound();

    var user = userService.GetCurrentUser();

    var allowTechnicianAccess =
      await subjectPermissionAuthorizationService.AuthorizeAsync<ISystemOperationsManageSupportAccessPermission>(User,
        strict: false)
    ? body.AllowTechnicianAccess
    : null;

    await supportRequestGenerator.GenerateSessionSupportTicket(
        user,
          new(
            body.SessionId,
            body.TicketSubject,
            body.TicketNotes,
            allowTechnicianAccess),
          token
          );

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(SystemApiRoutes.RequestFormSupport)]
  public async Task<IActionResult> RequestFormSupport(
    [FromServices] ISupportRequestGenerator supportRequestGenerator,
    [FromBody] RequestFormSupportBody body,
    CancellationToken token)
  {
    var user = userService.GetCurrentUser();

    var allowTechnicianAccess =
      await subjectPermissionAuthorizationService.AuthorizeAsync<ISystemOperationsManageSupportAccessPermission>(User,
        strict: false)
    ? body.AllowTechnicianAccess
    : null;

    await supportRequestGenerator.GenerateFormSupportTicket(
      user,
       new(
        body.RequesterEmail,
        body.Subject,
        body.Notes,
        allowTechnicianAccess,
        body.BlobNames
      ),
      token
      );

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(SystemApiRoutes.IsImmySupportAccessGranted)]
  public ActionResult<bool> IsImmySupportAccessGranted(
    [FromServices] IManagerProvidedSettings mgrSettings)
  {
    return Ok(mgrSettings.IsImmySupportAccessEnabled);
  }

  [SubjectPermissionAuthorize(typeof(ISystemOperationsManageSupportAccessPermission))]
  [HttpPost(SystemApiRoutes.EnableImmySupportAccess)]
  public async Task<IActionResult> EnableImmySupportAccess(
    [FromServices] IManagerProvidedSettings mgrSettings)
  {
    var user = userService.GetCurrentUser();
    if (mgrSettings.IsImmySupportAccessEnabled)
      return BadRequest("Immy Support access is already enabled");

    await mgrSettings.EnableImmySupportTechnicianAccess(
      new ToggleImmySupportTechnicianAccessRequestBody(
        user.DisplayName ?? String.Empty,
        user.Email ?? String.Empty));
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ISystemOperationsManageSupportAccessPermission))]
  [HttpPost(SystemApiRoutes.DisableImmySupportAccess)]
  public async Task<IActionResult> DisableImmySupportAccess(
    [FromServices] IManagerProvidedSettings mgrSettings)
  {
    var user = userService.GetCurrentUser();
    if (!mgrSettings.IsImmySupportAccessEnabled)
      return BadRequest("Immy Support access is not enabled");

    await mgrSettings.DisableImmySupportTechnicianAccess(
      new ToggleImmySupportTechnicianAccessRequestBody(
        user.DisplayName ?? String.Empty,
        user.Email ?? String.Empty));
    return NoContent();
  }

  /// <summary>
  /// THIS ROUTE SHOULD ONLY EVER BE USED FOR TESTING PURPOSES AND IN CI/CD PIPELINES FOR E2E TESTS
  /// </summary>
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(SystemApiRoutes.Reset)]
  public async Task<IActionResult> Reset(
    [FromServices] IOptions<AppSettingsOptions> appSettingsOptions,
    [FromServices] IResetInstanceCmd resetInstanceCmd)
  {
    if (!appSettingsOptions.Value.AllowReset) return BadRequest();
    await resetInstanceCmd.Run();
    return NoContent();
  }
}
