using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.MaintenanceTaskExtensions;
using Immybot.Backend.Application.DbContextExtensions.ScriptExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands.Payloads.MaintenanceTasks;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.SoftwareDbContextExtensions;
using Immybot.Backend.Application.SoftwareDbContextExtensions.MaintenanceTaskExtensions;
using Immybot.Backend.Application.SoftwareDbContextExtensions.ScriptExtensions;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Requests.MaintenanceTasks;
using Immybot.Backend.Web.Common.Contracts.V1.Responses.MaintenanceTasks;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.Lib.SignalRHubs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class MaintenanceTasksController(
  IPermissionFilterBuilder permissionFilterBuilder,
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IResourceAuthorizerFlow resourceAuthorizerFlow,
  IUserService userService,
  IPendoEventManagementService pendoEventManagement)
  : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksViewPermission))]
  [HttpGet(MaintenanceTaskApiRoutes.GetAllGlobalMaintenanceTasks)]
  public IActionResult GetAllGlobalMaintenanceTasks(
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromServices] SoftwareDbContext ctx,
    [FromQuery] AppSieveModel? sieveModel = null)
  {
    var q = ctx.GetAllMaintenanceTasks();
    if (sieveModel != null) q = sieveProcessor.Apply(sieveModel, q);

    var tasks = q.Select(GetGlobalMaintenanceTaskResponse.Projection);
    return Ok(tasks);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksViewPermission))]
  [HttpGet(MaintenanceTaskApiRoutes.GetAllLocalMaintenanceTasks)]
  public IActionResult GetAllLocalMaintenanceTasks(
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromServices] ImmybotDbContext ctx,
    [FromQuery] AppSieveModel? sieveModel = null)
  {
    // Apply RBAC filtering based on TenantMaintenanceTask join table relationships.
    // The permission filter evaluates tenant ownership and relationship types to determine access.
    // View permissions: Users can see maintenance tasks assigned to their tenant or globally available tasks.
    // MSP users have elevated privileges and can access maintenance tasks across tenant boundaries.
    var maintenanceTaskFilter =
      permissionFilterBuilder.BuildFilterExpression<MaintenanceTask, IMaintenanceTasksViewPermission>();
    var q = ctx.GetAllMaintenanceTasks()
      .Where(maintenanceTaskFilter);

    if (sieveModel != null) q = sieveProcessor.Apply(sieveModel, q);

    var tasks = q.Select(GetLocalMaintenanceTaskResponse.Projection);
    return Ok(tasks);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksViewPermission))]
  [HttpGet(MaintenanceTaskApiRoutes.GetLocalMaintenanceTask)]
  public async Task<IActionResult> GetLocalMaintenanceTask(
    [FromServices] IMaintenanceTaskActions actions,
    [FromRoute] int id,
    CancellationToken token)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<MaintenanceTask, IMaintenanceTasksViewPermission>(
      new DefaultKeyParameters(id),
      true);

    var task = await actions.GetMaintenanceTask(
      MaintenanceType.LocalMaintenanceTask,
      id, null, null, token);
    if (task == null) return NotFound();

    return Ok(new GetLocalMaintenanceTaskResponse(task));
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksViewPermission))]
  [HttpGet(MaintenanceTaskApiRoutes.GetGlobalMaintenanceTask)]
  public IActionResult GetGlobalMaintenanceTask(
    [FromServices] SoftwareDbContext ctx,
    [FromRoute] int id)
  {
    var task = ctx.GetMaintenanceTask(id);
    if (task == null) return NotFound();
    return Ok(new GetGlobalMaintenanceTaskResponse(task));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(MaintenanceTaskApiRoutes.CreateGlobalMaintenanceTask)]
  public IActionResult CreateGlobalMaintenanceTask(
    [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] IEntityValidator entityValidator,
    [FromBody] CreateGlobalMaintenanceTaskPayload body)
  {
    using var ctx = dbFactory();

    var errors = entityValidator.ValidateMaintenanceTask(body);
    if (errors.HasErrors) return new UnprocessableEntityObjectResult(errors);

    var created = ctx.CreateMaintenanceTask(body);
    var resp = new GetGlobalMaintenanceTaskResponse(created);
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").GlobalMaintenanceTaskCreated(resp);
    }).Forget();
    return Ok(resp);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksManagePermission))]
  [HttpPost(MaintenanceTaskApiRoutes.CreateLocalMaintenanceTask)]
  public async Task<IActionResult> CreateLocalMaintenanceTask(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] IEntityValidator entityValidator,
    [FromBody] CreateLocalMaintenanceTaskPayload body)
  {
    await using var ctx = dbFactory();

    var errors = entityValidator.ValidateMaintenanceTask(body);
    if (errors.HasErrors) return new UnprocessableEntityObjectResult(errors);

    var currentUser = userService.GetCurrentUser();

    var tenantRelationshipResult = await subjectPermissionAuthorizationService
      .AuthorizeTenantRelationshipsAsync<IMaintenanceTasksManagePermission>(
        User,
        relationships: body.Tenants.OfType<ITenantRelationship>().ToList());

    if (tenantRelationshipResult.RequiresOwnedRelationship)
    {
      body.Tenants.Add(new LocalMaintenanceTaskTenantPayload(currentUser.TenantId, Relationship.Owned));
    }

    var created = ctx.CreateMaintenanceTask(body);
    created = ctx.GetMaintenanceTask(created.Id);
    if (created == null) return NotFound();
    var resp = new GetLocalMaintenanceTaskResponse(created);
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").LocalMaintenanceTaskCreated(resp);
    }).Forget();

    pendoEventManagement.Enqueue(
      PendoEventManagementService.PendoEvents.ImmyNewLocalTaskAddedEvent()
    );

    return Ok(resp);
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(MaintenanceTaskApiRoutes.UpdateGlobalMaintenanceTask)]
  public IActionResult UpdateGlobalMaintenanceTask(
    [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] IEntityValidator entityValidator,
    [FromBody] UpdateGlobalMaintenanceTaskPayload body,
    [FromRoute] int id)
  {
    using var ctx = dbFactory();
    var existing = ctx.GetMaintenanceTask(id);
    if (existing == null) return NotFound();

    var errors = entityValidator.ValidateMaintenanceTask(body, existing);
    if (errors.HasErrors) return new UnprocessableEntityObjectResult(errors);

    body.Id = id;
    var updated = ctx.UpdateMaintenanceTask(body);
    if (updated == null) return BadRequest("Global maintenance task was not updated");
    var resp = new GetGlobalMaintenanceTaskResponse(updated);
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").GlobalMaintenanceTaskUpdated(resp);
    }).Forget();
    return Ok(resp);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksManagePermission))]
  [HttpPost(MaintenanceTaskApiRoutes.UpdateLocalMaintenanceTask)]
  public async Task<IActionResult> UpdateLocalMaintenanceTask(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] IEntityValidator entityValidator,
    [FromBody] UpdateLocalMaintenanceTaskPayload body,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<MaintenanceTask, IMaintenanceTasksManagePermission>(
      new DefaultKeyParameters(id),
      true);
    await using var ctx = dbFactory();
    var existing = ctx.GetMaintenanceTask(id);
    if (existing == null) return NotFound();

    var errors = entityValidator.ValidateMaintenanceTask(body, existing);
    if (errors.HasErrors) return new UnprocessableEntityObjectResult(errors);

    var currentUser = userService.GetCurrentUser();
    var tenantRelationshipAuthorizationResult = await subjectPermissionAuthorizationService
      .AuthorizeTenantRelationshipsAsync<IMaintenanceTasksManagePermission>(
        User,
        relationships: body.Tenants.OfType<ITenantRelationship>().ToList());

    if (tenantRelationshipAuthorizationResult.RequiresOwnedRelationship)
    {
      body.Tenants.Add(new LocalMaintenanceTaskTenantPayload(currentUser.TenantId, Relationship.Owned));
    }

    body.Id = id;
    var updated = ctx.UpdateMaintenanceTask(body);
    if (updated == null) return BadRequest("Local maintenance task was not updated");
    updated = ctx.GetMaintenanceTask(updated.Id);
    if (updated == null) return NotFound();
    var resp = new GetLocalMaintenanceTaskResponse(updated);
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").LocalMaintenanceTaskUpdated(resp);
    }).Forget();
    return Ok(resp);
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpDelete(MaintenanceTaskApiRoutes.DeleteGlobalMaintenanceTask)]
  public IActionResult DeleteGlobalMaintenanceTask(
    [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory,
    [FromRoute] int id,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext)
  {
    using var ctx = dbFactory();
    var task = ctx.GetMaintenanceTask(id);
    if (task == null) return NotFound();
    ctx.DeleteMaintenanceTask(task);
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").GlobalMaintenanceTaskDeleted(id);
    }).Forget();
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksManagePermission))]
  [HttpDelete(MaintenanceTaskApiRoutes.DeleteLocalMaintenanceTask)]
  public async Task<IActionResult> DeleteLocalMaintenanceTask(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromRoute] int id,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<MaintenanceTask, IMaintenanceTasksManagePermission>(
      new DefaultKeyParameters(id),
      true);
    await using var ctx = dbFactory();
    var task = ctx.GetMaintenanceTask(id);
    if (task == null) return NotFound();

    var deploymentRefCount = await ctx.GetAllTargetAssignments()
      .Where(a => a.MaintenanceType == MaintenanceType.LocalMaintenanceTask && a.MaintenanceIdentifier == id.ToString())
      .CountAsync();

    if (deploymentRefCount > 0) return BadRequest($"Maintenance task is referenced in {deploymentRefCount} deployments");

    ctx.DeleteMaintenanceTask(task);
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").LocalMaintenanceTaskDeleted(id);
    }).Forget();
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksViewPermission))]
  [HttpGet(MaintenanceTaskApiRoutes.GetReferenceCount)]
  public async Task<IActionResult> GetReferenceCount(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] SoftwareDbContext softwareCtx,
    [FromQuery] DatabaseType? maintenanceTaskType,
    [FromQuery] int? maintenanceTaskId)
  {
    if (!maintenanceTaskType.HasValue || !maintenanceTaskId.HasValue) return BadRequest();

    MaintenanceTask? task;
    MaintenanceType maintenanceType;
    if (maintenanceTaskType.Value == DatabaseType.Global)
    {
      maintenanceType = MaintenanceType.GlobalMaintenanceTask;
      task = softwareCtx.GetMaintenanceTask(maintenanceTaskId.Value);
      if (task == null) return NotFound();
    }
    else
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<MaintenanceTask, IMaintenanceTasksViewPermission>(
        new DefaultKeyParameters(maintenanceTaskId.Value),
        true);
      maintenanceType = MaintenanceType.LocalMaintenanceTask;
      task = ctx.GetMaintenanceTask(maintenanceTaskId.Value);
      if (task == null) return NotFound();
    }

    var filter = permissionFilterBuilder
      .BuildFilterExpression<TargetAssignment, IDeploymentsManageSingleTenantPermission>();

    var assignments = ctx.GetAllTargetAssignments()
      .Where(filter);

    var count = await assignments.Where(a =>
      a.MaintenanceIdentifier == maintenanceTaskId.ToString() && a.MaintenanceType == maintenanceType).CountAsync();
    return Ok(count);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksManagePermission))]
  [HttpPost(MaintenanceTaskApiRoutes.DuplicateMaintenanceTask)]
  public async Task<IActionResult> DuplicateMaintenanceTask(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] SoftwareDbContext softwareCtx,
    [FromBody] DuplicateMaintenanceTaskRequestbody body)
  {
    using var ctx = dbFactory();
    MaintenanceTask? task;
    if (body.MaintenanceTaskType == DatabaseType.Global)
    {
      task = softwareCtx.GetMaintenanceTask(body.MaintenanceTaskId);
      if (task == null) return NotFound();

      // wipe out the icon media id
      task.IconMediaId = null;
    }
    else
    {
      await resourceAuthorizerFlow
        .PerformAuthorizationWorkflowAsync<MaintenanceTask, IMaintenanceTasksManagePermission>(
        new DefaultKeyParameters(body.MaintenanceTaskId),
        true);
      task = ctx.GetMaintenanceTask(body.MaintenanceTaskId);
      if (task == null) return NotFound();
    }

    var createRequest = new CreateLocalMaintenanceTaskPayload(task);
    createRequest.Name += " [DUPLICATED]";
    var currentUser = userService.GetCurrentUser();

    var tenantRelationshipAuthorizationResult = await subjectPermissionAuthorizationService
      .AuthorizeTenantRelationshipsAsync<IMaintenanceTasksManagePermission>(
        User,
        relationships: createRequest.Tenants.OfType<ITenantRelationship>().ToList());

    if (tenantRelationshipAuthorizationResult.RequiresOwnedRelationship)
    {
      createRequest.Tenants.Add(new LocalMaintenanceTaskTenantPayload(currentUser.TenantId, Relationship.Owned));
    }

    var duplicate = ctx.CreateMaintenanceTask(createRequest);

    pendoEventManagement.Enqueue(
      PendoEventManagementService.PendoEvents.ImmyLocalTaskDuplicatedEvent()
    );

    return Ok(new GetLocalMaintenanceTaskResponse(duplicate));
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksManagePermission))]
  [HttpGet(MaintenanceTaskApiRoutes.Search)]
  public IActionResult Search(
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromServices] ImmybotDbContext localCtx,
    [FromServices] SoftwareDbContext globalCtx,
    [FromQuery] AppSieveModel? sieveModel = null,
    [FromQuery] bool globalOnly = false,
    [FromQuery] bool configurationTaskOnly = false)
  {
    IQueryable<MaintenanceTask>? localMaintenanceTask = null;

    if (!globalOnly)
    {
      var filter = permissionFilterBuilder.BuildFilterExpression<MaintenanceTask, IMaintenanceTasksViewPermission>();
      localMaintenanceTask = localCtx.GetAllMaintenanceTasks()
        .Where(filter);
      if (sieveModel != null) localMaintenanceTask = sieveProcessor.Apply(sieveModel, localMaintenanceTask);
    }

    var globalMaintenanceTask = globalCtx.GetAllMaintenanceTasks();
    if (sieveModel != null) globalMaintenanceTask = sieveProcessor.Apply(sieveModel, globalMaintenanceTask);

    if (configurationTaskOnly)
    {
      localMaintenanceTask = localMaintenanceTask?.Where(a => a.IsConfigurationTask);
      globalMaintenanceTask = globalMaintenanceTask.Where(a => a.IsConfigurationTask);
    }

    var localRes = localMaintenanceTask?.Select(a => new MaintenanceTaskSearchResult(a)).ToList() ?? [];
    var globalRes = globalMaintenanceTask.Select(a => new MaintenanceTaskSearchResult(a)).ToList();

    return Ok(localRes.Concat(globalRes));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpGet(MaintenanceTaskApiRoutes.MigrateLocalToGlobalWhatIf)]
  public IActionResult MigrateLocalToGlobalWhatIf(
    [FromRoute] int id,
    [FromServices] ILocalToGlobalMigrator localToGlobalMigrator
    )
  {
    var data = localToGlobalMigrator.MigrateLocalTaskToGlobalTaskWhatIf(id);
    return Ok(data);
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(MaintenanceTaskApiRoutes.MigrateLocalToGlobal)]
  public IActionResult MigrateLocalToGlobal(
    [FromRoute] int id,
    [FromServices] ILocalToGlobalMigrator localToGlobalMigrator
    )
  {
    var globalTaskId = localToGlobalMigrator.MigrateLocalTaskToGlobalTask(id, userService.GetCurrentUser());
    return Ok(globalTaskId);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksViewPermission))]
  [HttpPost(MaintenanceTaskApiRoutes.GetParamBlockFromGlobalMaintenanceTaskParameters)]
  public IActionResult GetParamBlockFromGlobalMaintenanceTaskParameters(
    [FromServices] SoftwareDbContext ctx,
    [FromRoute] int id)
  {
    var task = ctx.GetMaintenanceTask(id);
    if (task == null) return NotFound();

    var paramBlock = ParameterConverter.GetParamBlockFromMaintenanceTaskParameters(task.Parameters.ToList());
    return Ok(paramBlock);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksViewPermission))]
  [HttpPost(MaintenanceTaskApiRoutes.GetParamBlockFromLocalMaintenanceTaskParameters)]
  public async Task<IActionResult> GetParamBlockFromLocalMaintenanceTaskParameters(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<MaintenanceTask, IMaintenanceTasksViewPermission>(
      new DefaultKeyParameters(id),
      true);
    var task = ctx.GetMaintenanceTask(id);
    if (task == null) return NotFound();

    var paramBlock = ParameterConverter.GetParamBlockFromMaintenanceTaskParameters(task.Parameters.ToList());
    return Ok(paramBlock);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceTasksViewPermission))]
  [HttpPost(MaintenanceTaskApiRoutes.ValidateParamBlockParameters)]
  public async Task<IActionResult> ValidateParamBlockParameters(
    [FromServices] ImmybotDbContext localCtx,
    [FromServices] SoftwareDbContext globalCtx,
    [FromServices] IDynamicFormService dynamicFormService,
    [FromServices] IRunContextFactory runContextFactory,
    [FromBody] ValidateParamBlockParametersFromTaskRequest request,
    CancellationToken cancellationToken)
  {
    MaintenanceTask? task = null;
    string? scriptAction = null;
    DatabaseType scriptType;

    if (request.DatabaseType is DatabaseType.Local)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<MaintenanceTask, IMaintenanceTasksViewPermission>(
        new DefaultKeyParameters(request.MaintenanceTaskId),
        true);
      task = localCtx.GetMaintenanceTask(request.MaintenanceTaskId);
      if (task is null) return NotFound();
    }
    else
    {
      task = globalCtx.GetMaintenanceTask(request.MaintenanceTaskId);
      if (task is null) return NotFound();
    }

    if (task.UseScriptParamBlock)
    {
      if (task.SetScriptId is null || task.SetScriptType is null)
        return BadRequest(
          "Cannot get parameters for a maintenance task that using a script param block because it does not have a set script");

      Script? script = null;
      // fetch from set script
      if (task.SetScriptType is DatabaseType.Local)
      {
        script = localCtx.GetScriptById(task.SetScriptId.Value);
      }
      else
      {
        script = globalCtx.GetScriptById(task.SetScriptId.Value);
      }

      if (script is null) return NotFound("Set script for the task was not found");

      scriptType = script.ScriptType;
      scriptAction = script.Action;
    }
    else
    {
      scriptAction = ParameterConverter.GetParamBlockFromMaintenanceTaskParameters(task.Parameters.ToList());
      scriptType = task.DatabaseType;
    }

    var tenantId = userService.GetTenantId();


    if (request.TenantId.HasValue)
    {
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IMaintenanceTasksViewPermission>(
        User,
        request.TenantId.Value,
        strict: true);
      tenantId = request.TenantId.Value;
    }

    IRunContext? runContext = null;
    try
    {
      if (request.MaintenanceSessionId is { } maintenanceSessionId)
      {
        var sessionRunContext = await runContextFactory.GenerateSessionRunContext(
          maintenanceSessionId,
          actionIdToRerun: null,
          cancellationToken);
        runContext = sessionRunContext;
      }
      else if (request.ComputerId is { } computerId)
      {
        await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
          new DefaultKeyParameters(computerId),
          true);

        runContext = await runContextFactory.GenerateComputerOneOffRunContext(
          computerId,
          cancellationToken,
          manuallyTriggeredBy: userService.GetCurrentUser());
      }
      else
      {
        runContext = await runContextFactory.GenerateTenantRunContext(
          userService.GetCurrentUser(),
          tenantId,
          cancellationToken);
      }

      if (request.TerminalId.HasValue)
      {
        runContext.Args.MetascriptInvoker.SetTerminalId(request.TerminalId.Value);
      }

      // if we were provided a deployment id and database type, then assign deployment specified parameters
      var parameterValues = new Dictionary<string, ParameterValue>(request.ParameterValues ?? []);

      if (request is { DeploymentDatabaseType: { } deploymentDatabaseType, DeploymentId: { } deploymentId })
      {
        var deployment = deploymentDatabaseType is DatabaseType.Global ?
          globalCtx.GetTargetAssignmentById(deploymentId) :
          localCtx.GetTargetAssignmentById(deploymentId);

        if (deployment?.TaskParameterValues is { } deploymentParameterValues)
        {
          foreach (var p in deploymentParameterValues)
          {
            if (p.Value is null || p.Value.RequiresOverride is true) continue;

            // add deployment only parameters
            if (p.Value.AllowOverride is not true || request.ParameterValues?.ContainsKey(p.Key) is not true)
            {
              parameterValues[p.Key] = p.Value;
            }
          }
        }
      }

      var isMsp = userService.IsMspUser();
      var canAccessParentTenant = runContext.CanAccessParentTenant();

      var ret = await dynamicFormService.BindParameters(
        canAccessMspResources: isMsp,
        canAccessParentTenant: canAccessParentTenant,
        runContext,
        scriptAction,
        scriptType,
        cancellationToken,
        parameterValues,
        ignoreCache: request.ForceRebind);

      return Ok(ret);
    }
    finally
    {
      runContext?.Dispose();
    }
  }
}
