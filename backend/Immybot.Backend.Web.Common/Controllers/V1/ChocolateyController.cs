using Immybot.Backend.Application.Lib.ChocolateyApi;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Controllers.V1;

/// <summary>
/// Api to proxy requests to chocolatey since we chocolatey.org does not set CORS headers
/// </summary>
public class ChocolateyController : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(ChocolateyApiRoutes.Search)]
  public async Task<IActionResult> Search(
    [FromQuery] string searchTerm,
    [FromServices] IChocolateyApi chocolateyApi,
    CancellationToken token)
  {
    var results = await chocolateyApi.Search(searchTerm, token);
    if (results == null) return Conflict("The Chocolatey API is not currently operational");
    return Ok(results.GroupBy(a => a.Id).Select(a => a.First()));
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(ChocolateyApiRoutes.FindPackagesById)]
  public async Task<IActionResult> FindPackagesById(
    [FromQuery] string packageId,
    [FromServices] IChocolateyApi chocolateyApi,
    CancellationToken token)
  {
    var results = await chocolateyApi.FindPackagesById(packageId, token);
    if (results == null) return Conflict("The Chocolatey API is not currently operational");
    return Ok(results.OrderByDescending(a => a.SemanticVersion));
  }
}
