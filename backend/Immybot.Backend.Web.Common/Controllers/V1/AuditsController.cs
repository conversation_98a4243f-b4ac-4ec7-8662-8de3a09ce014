using DevExtreme.AspNet.Data;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class AuditsController : Controller
{
  [SubjectPermissionAuthorize(typeof(IAuditViewPermission))]
  [HttpGet(AuditsApiRoutes.GetLocalDx)]
  public ActionResult<ICollection<Audit>> GetLocalDx(
    [FromServices] IAuditStore auditStore,
    DataSourceLoadOptions loadOptions)
  {
    var auditsDisposable = auditStore.GetLocalAudits();
    Response.RegisterForDispose(auditsDisposable);
    return Ok(DataSourceLoader.Load(auditsDisposable.Value, loadOptions));
  }

  // everyone should be allowed to see global audit changes
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(AuditsApiRoutes.GetGlobalDx)]
  public ActionResult<ICollection<Audit>> GetGlobalDx(
    [FromServices] IAuditStore auditStore,
    DataSourceLoadOptions loadOptions)
  {
    var auditsDisposable = auditStore.GetGlobalAudits();
    Response.RegisterForDispose(auditsDisposable);
    return Ok(DataSourceLoader.Load(auditsDisposable.Value, loadOptions));
  }
}
