using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class BrandingController(IUserService userService) : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(IBrandingViewPermission))]
  [HttpGet(BrandingApiRoutes.GetAll)]
  public IActionResult Get(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx)
  {
    var brandings = ctx.GetAllBrandings();
    var filter = permissionFilterBuilder.BuildFilterExpression<Branding, IBrandingViewPermission>();
    var res = brandings.Where(filter).Select(GetBrandingResponse.Projection);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(IBrandingViewPermission))]
  [HttpGet(BrandingApiRoutes.Get)]
  public async Task<IActionResult> GetById(
    [FromRoute] int id,
    [FromServices] IResourceAuthorizerFlow resourceAuthorizerFlow,
    [FromServices] ImmybotDbContext ctx)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Branding, IBrandingViewPermission>(
      new DefaultKeyParameters(id),
      true);
    var branding = ctx.GetBrandingById(id);
    if (branding == null) return NotFound();
    return Ok(new GetBrandingResponse(branding));
  }

  [SubjectPermissionAuthorize(typeof(IBrandingManagePermission))]
  [HttpPut(BrandingApiRoutes.Update)]
  public async Task<IActionResult> Update(
    [FromServices] IResourceAuthorizerFlow resourceAuthorizerFlow,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromRoute] int id,
    [FromBody] UpdateBrandingRequestBody body,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Branding, IBrandingManagePermission>(
      new DefaultKeyParameters(id),
      true);

    if (body.TenantId is null)
      await subjectPermissionAuthorizationService.AuthorizeAsync<IBrandingGlobalManagePermission>(User, true);
    else
    {
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IBrandingManagePermission>(User,
        body.TenantId.Value,
        strict: true);
    }

    await using var ctx = dbFactory();
    var branding = ctx.GetBrandingById(id);
    if (branding == null) return NotFound();

    body.Id = id;
    if (!String.IsNullOrEmpty(branding.TimeFormat))
    {
      var isFormatValid = BrandingExtensions.IsBrandingTimeFormatValid(body.TimeFormat!);
      if (!isFormatValid) return BadRequest("Invalid time format");
    }
    var update = ctx.UpdateBranding(body);
    if (update is null) return BadRequest("Failed to update branding");
    return Ok(new GetBrandingResponse(update));
  }

  [SubjectPermissionAuthorize(typeof(IBrandingManagePermission))]
  [HttpPost(BrandingApiRoutes.Create)]
  public async Task<IActionResult> Create(
    [FromBody] CreateBrandingRequestBody body,
    [FromServices] IResourceAuthorizerFlow resourceAuthorizerFlow,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory)
  {
    // only users with global manage permission can create brandings without a tenant or for other tenants
    if (body.TenantId is null)
      await subjectPermissionAuthorizationService.AuthorizeAsync<IBrandingGlobalManagePermission>(User, true);
    else
    {
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IBrandingManagePermission>(User,
        body.TenantId.Value,
        strict: true);
    }

    await using var ctx = dbFactory();
    var creation = ctx.CreateBranding(body);
    if (!String.IsNullOrEmpty(creation.TimeFormat))
    {
      var isFormatValid = BrandingExtensions.IsBrandingTimeFormatValid(creation.TimeFormat!);
      if (!isFormatValid) return BadRequest("Invalid time format");
    }
    return Ok(new GetBrandingResponse(creation));
  }

  [SubjectPermissionAuthorize(typeof(IBrandingManagePermission))]
  [HttpDelete(BrandingApiRoutes.Delete)]
  public async Task<IActionResult> Delete(
    [FromServices] IResourceAuthorizerFlow resourceAuthorizerFlow,
    [FromRoute] int id,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Branding, IBrandingManagePermission>(
      new DefaultKeyParameters(id),
      true);

    await using var ctx = dbFactory();
    var branding = ctx.GetBrandingById(id);
    if (branding == null) return NotFound();

    try
    {
      ctx.DeleteBranding(branding);
      return NoContent();
    }
    catch (DeleteDefaultBrandingException)
    {
      throw new ImmyWebException(new HttpProblem()
      {
        Title = "Branding Error",
        Type = "Branding",
        Instance = $"/branding/{id}",
        Detail = "Cannot delete the default branding.",
        Status = System.Net.HttpStatusCode.BadRequest,
      });
    }
  }

  [SubjectPermissionAuthorize(typeof(IBrandingGlobalManagePermission))]
  [HttpPost(BrandingApiRoutes.SetDefaultBranding)]
  public IActionResult SetGlobalDefaultBranding(
    [FromRoute] int id,
    [FromServices] ImmybotDbContext ctx)
  {
    ctx.SetGlobalDefaultBranding(id);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IBrandingManagePermission))]
  [HttpPost(BrandingApiRoutes.SendTestBrandingEmail)]
  public async Task<IActionResult> SendTestBrandingEmail(
    [FromBody] SendTestBrandingEmailRequest req,
    [FromServices] ISendTestBrandingEmailCmd cmd
    )
  {
    await cmd.Run(
      new Branding
      {
        MascotImgUri = req.Branding.MascotImgUri,
        MascotName = req.Branding.MascotName,
        TimeFormat = req.Branding.TimeFormat,
        BackgroundColor = req.Branding.BackgroundColor ?? string.Empty,
        ForegroundColor = req.Branding.ForegroundColor ?? string.Empty,
        LogoAltText = req.Branding.LogoAltText,
        LogoUri = req.Branding.LogoUri,
        TableHeaderColor = req.Branding.TableHeaderColor ?? string.Empty,
        Description = req.Branding.Description,
        FromAddress = req.Branding.FromAddress ?? string.Empty,
        TextColor = req.Branding.TextColor ?? string.Empty,
        TableHeaderTextColor = req.Branding.TableHeaderTextColor ?? string.Empty,
      },
      req.To,
      userService.GetTenantId());

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IBrandingManagePermission))]
  [HttpPost(BrandingApiRoutes.ValidateTimeFormat)]
  public IActionResult ValidateTimeFormat(
    [FromRoute] string timeFormat)
  {
    try
    {
      if(String.IsNullOrEmpty(timeFormat)) timeFormat = "HH:mm";
      var formattedTime = DateTime.Now.ToString(timeFormat);
      return Ok(formattedTime);
    }
    catch (Exception e)
    {
      return BadRequest(e.Message);
    }
  }

  /// <summary>
  /// Fetches support related branding changes to be used in the Support Sidebar, Session Support Request, or other Support related UI.
  /// These branding changes can be specified by Dynamic Providers implementing 'ISupportsSupportTicketDetailOverride'
  /// </summary>
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(BrandingApiRoutes.GetSupportBranding)]
  public async Task<IActionResult> GetSupportBranding(
    [FromServices] IProviderActions providerActions)
  {
    var supportBranding = await providerActions.GetSupportBrandingDetails(CancellationToken.None);
    return Ok(supportBranding.ValueOrDefault);
  }
}
