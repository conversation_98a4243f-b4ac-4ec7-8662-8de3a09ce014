using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Lib.UserAuthentication;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class UserAuthController : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(UserAuthRoutes.Me)]
  public ActionResult<UserAuthInfo> Me([FromServices] IUserService userService)
  {
    var principal = userService.GetCurrentPrincipal(false);
    var expiration = HttpContext.Features.Get<AuthenticateResult>()?.Properties?.ExpiresUtc;

    // we want to return user details even if the full authentication failed for places like the landing page
    if (principal != null && expiration.HasValue)
    {
      return Ok(new UserAuthInfo
      {
        UserEmailAddress = principal.GetEmailAddress(),
        UserDisplayName = principal.GetClaimValue(ClaimConstants.Name),
        ExpirationSecondsSinceEpoch = expiration.Value.ToUnixTimeMilliseconds(),
        ExpirationDate = expiration.Value,
      });
    }

    return Unauthorized();
  }

  // TODO remove from here and the frontend once EasyAuth isn't using it -- either when we remove EasyAuth or if we can change EasyAuth to use the sliding cookies
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(UserAuthRoutes.Refresh)]
  public async Task<IActionResult> Refresh() =>
    Challenge(new AuthenticationProperties
    {
      Parameters = { { nameof(AuthenticationChallengeType), AuthenticationChallengeType.Refresh } },
      AllowRefresh = true,
    });

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(UserAuthRoutes.Login)]
  public IActionResult Login([FromServices] IUserService userService, [FromQuery] string redirectUrl, [FromQuery] string? prompt = null) =>
    Challenge(new AuthenticationProperties
    {
      Parameters =
      {
        { OpenIdConnectParameterNames.Prompt, prompt },
        { OpenIdConnectParameterNames.LoginHint, userService.GetCurrentPrincipal(false)?.GetEmailAddress() }, // provide login hint mainly to avoid Microsoft's account selector screen which has `X-Frame-Options: DENY` and refuses to load within an iframe
        { nameof(AuthenticationChallengeType), AuthenticationChallengeType.Login },
      },
      RedirectUri = Url.IsLocalUrl(redirectUrl) ? redirectUrl : "/",
      AllowRefresh = true,
    });

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(UserAuthRoutes.Logout)]
  public IActionResult Logout() =>
    SignOut(new AuthenticationProperties { RedirectUri = "/" });
}
