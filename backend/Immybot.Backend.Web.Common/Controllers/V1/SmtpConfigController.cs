using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.DbContextExtensions.SmtpConfigExtensions;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Microsoft.AspNetCore.Mvc;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class SmtpConfigController(
  IUserService userService,
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IResourceAuthorizerFlow resourceAuthorizerFlow)
  : Controller
{
  [SubjectPermissionAuthorize(typeof(ISmtpConfigurationsViewPermission))]
  [HttpGet(SmtpConfigApiRoutes.GetAll)]
  public ActionResult<IEnumerable<GetSmtpConfigResponse>> GetAll(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromQuery] AppSieveModel? appSieveModel = null)
  {
    var filter = permissionFilterBuilder.BuildFilterExpression<SmtpConfig, ISmtpConfigurationsViewPermission>();
    var q = ctx.GetAllSmtpConfigs()
      .Where(filter);

    if (appSieveModel != null) q = sieveProcessor.Apply(appSieveModel, q);
    var configs = q.Select(GetSmtpConfigResponse.Projection);
    return Ok(configs);
  }

  [SubjectPermissionAuthorize(typeof(ISmtpConfigurationsViewPermission))]
  [HttpGet(SmtpConfigApiRoutes.Get)]
  public async Task<ActionResult<GetSmtpConfigResponse>> Get(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int tenantId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<SmtpConfig, ISmtpConfigurationsViewPermission>(
      new DefaultKeyParameters(tenantId),
      strict: true
    );

    var smtp = ctx.GetSmtpConfigByTenantId(tenantId);
    if (smtp == null) return NotFound();
    return Ok(new GetSmtpConfigResponse(smtp));
  }

  [SubjectPermissionAuthorize(typeof(ISmtpConfigurationsManagePermission))]
  [HttpPost(SmtpConfigApiRoutes.Create)]
  public async Task<ActionResult<GetSmtpConfigResponse>> Create(
    [FromServices] ISmtpConfigActions smtpActions,
    [FromBody] CreateSmtpRequest request)
  {
    var smtp = new SmtpConfig
    {
      EnableSSL = request.EnableSSL,
      Host = request.Host,
      Port = request.Port,
      Timeout = request.Timeout,
      Username = request.UseAuthentication ? request.Username : null,
      Enabled = request.Enabled,
      UseAuthentication = request.UseAuthentication,
    };

    if (request.TenantId is { } tenantId)
    {
      // smtp is for a specific tenant
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<ISmtpConfigurationsManagePermission>(User,
        tenantId,
        strict: true);
      smtp.TenantId = tenantId;
    }
    else
    {
      // smtp is for the current user's tenant if no tenantId is provided
      smtp.TenantId = userService.GetTenantId();
    }

    var password = request.UseAuthentication ? request.Password : null;
    var createdSmtp = smtpActions.Create(smtp, password);
    if (createdSmtp == null) return BadRequest("Failed to create SMTP config");
    return Ok(new GetSmtpConfigResponse(createdSmtp));
  }

  [SubjectPermissionAuthorize(typeof(ISmtpConfigurationsManagePermission))]
  [HttpPost(SmtpConfigApiRoutes.Update)]
  public async Task<ActionResult<GetSmtpConfigResponse>> Update(
    [FromServices] ISmtpConfigActions smtpActions,
    [FromBody] CreateSmtpRequest request,
    [FromRoute] int tenantId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<SmtpConfig, ISmtpConfigurationsManagePermission>(
      new DefaultKeyParameters(tenantId),
      strict: true
    );

    var smtp = new SmtpConfig()
    {
      EnableSSL = request.EnableSSL,
      Host = request.Host,
      Port = request.Port,
      Timeout = request.Timeout,
      Username = request.UseAuthentication ? request.Username : null,
      Enabled = request.Enabled,
      UseAuthentication = request.UseAuthentication,
      TenantId = tenantId
    };

    var password = request.UseAuthentication ? request.Password : null;
    var updated = smtpActions.Update(smtp, password);
    if (updated == null) return BadRequest("Failed to update SMTP config");
    return Ok(new GetSmtpConfigResponse(updated));
  }

  [SubjectPermissionAuthorize(typeof(ISmtpConfigurationsManagePermission))]
  [HttpDelete(SmtpConfigApiRoutes.Delete)]
  public async Task<ActionResult> Delete(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int tenantId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<SmtpConfig, ISmtpConfigurationsManagePermission>(
      new DefaultKeyParameters(tenantId),
      strict: true
    );

    var smtp = ctx.GetSmtpConfigByTenantId(tenantId);
    if (smtp == null) return NotFound();
    ctx.DeleteSmtpConfig(smtp);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ISmtpConfigurationsManagePermission))]
  [HttpPost(SmtpConfigApiRoutes.SendTestEmail)]
  public async Task<ActionResult<string>> SendTestEmail(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ISmtpConfigActions smtpActions,
    [FromBody] SendTestEmailRequest request)
  {
    // if tenantId is provided and no password was provided in the request that set the password hash
    string? passwordToUse = null;
    if (request.UseAuthentication)
    {
      if (!string.IsNullOrEmpty(request.Password))
      {
        passwordToUse = request.Password;
      }
      else if (request.TenantId != null)
      {
        await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<SmtpConfig, ISmtpConfigurationsManagePermission>(
          new DefaultKeyParameters(request.TenantId.Value),
          strict: true
        );
        var existingSmtp = ctx.GetSmtpConfigByTenantId(request.TenantId.Value);
        if (existingSmtp != null && !string.IsNullOrEmpty(existingSmtp.PasswordHash))
          passwordToUse = smtpActions.GetPassword(existingSmtp.PasswordHash);
      }
    }

    var smtp = new SmtpConfig()
    {
      EnableSSL = request.EnableSSL,
      Host = request.Host,
      Port = request.Port,
      Timeout = request.Timeout,
      Username = request.Username,
      Enabled = request.Enabled,
      UseAuthentication = request.UseAuthentication,
    };

    if (request.UseAuthentication && passwordToUse == null) return BadRequest("Password is required for sending test email");
    string passwordParam = request.UseAuthentication ? passwordToUse! : string.Empty;
    return Ok(smtpActions.SendTestEmail(smtp, passwordParam, request.To, request.From));
  }
}
