using System.Net;
using Immybot.Backend.Application.Interface.Actions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;

namespace Immybot.Backend.Web.Common.Controllers.V1;

[ApiController]
public class EmailController : ControllerBase
{
  private readonly IScheduledEmailActions _emailActions;
  public EmailController(
    IScheduledEmailActions emailActions)
  {
    _emailActions = emailActions;
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(EmailApiRoutes.Postpone)]
  public async Task<IActionResult> Postpone([FromRoute] string emailGuid)
  {
    return new ContentResult
    {
      ContentType = "text/html",
      StatusCode = (int)HttpStatusCode.OK,
      Content = await _emailActions.Postpone(emailGuid)
    };
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(EmailApiRoutes.RebootNow)]
  public async Task<IActionResult> RebootNow(
    [FromServices] IHostApplicationLifetime lifetime,
    [FromRoute] string emailGuid)
  {
    return new ContentResult
    {
      ContentType = "text/html",
      StatusCode = (int)HttpStatusCode.OK,
      Content = await _emailActions.RebootNow(emailGuid, lifetime.ApplicationStopping),
    };
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(EmailApiRoutes.UpdateNow)]
  public async Task<IActionResult> UpdateNow([FromRoute] string emailGuid)
  {
    return new ContentResult
    {
      ContentType = "text/html",
      StatusCode = (int)HttpStatusCode.OK,
      Content = await _emailActions.UpdateNow(emailGuid)
    };
  }
}



