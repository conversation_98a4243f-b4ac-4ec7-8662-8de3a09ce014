using Immybot.Backend.Application.DbContextExtensions.ScheduleExtensions;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Manager.Domain;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class DevInstanceManagementController : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(DevInstanceManagementApiRoutes.StartHangfireServer)]
  public async Task<IActionResult> StartHangfireServer(
    [FromServices] IManagerProvidedSettings mgr,
    [FromServices] IOptions<AppSettingsOptions> appOpts,
    [FromServices] IOptions<HangfireSettings> hangfireOpts,
    [FromServices] ImmybotDbContext dbContext)
  {
    if (!appOpts.Value.IsDevInstance) return NotFound();
    if (hangfireOpts.Value.RunHangfireServer) return BadRequest();

    // disable all the schedules in the system before starting the hangfire
    // server, so we don't end up running production maintenance in dev
    await dbContext.DisableAllSchedules();

    // reach out to manager to update this instance's hangfire server config
    await mgr.RunHangfireServer(true);
    return Ok(new { Success = true });
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(DevInstanceManagementApiRoutes.StopHangfireServer)]
  public async Task<IActionResult> StopHangfireServer(
    [FromServices] IManagerProvidedSettings mgr,
    [FromServices] IOptions<AppSettingsOptions> appOpts,
    [FromServices] IOptions<HangfireSettings> hangfireOpts)
  {
    if (!appOpts.Value.IsDevInstance) return NotFound();
    if (!hangfireOpts.Value.RunHangfireServer) return BadRequest();
    // reach out to manager to update this instance's hangfire server config
    await mgr.RunHangfireServer(false);
    return Ok(new { Success = true });
  }
}
