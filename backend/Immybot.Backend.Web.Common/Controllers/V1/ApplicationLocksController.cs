using System.Text.Json;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Contracts.V1.Responses.ResponseCollectionTypes;
using Immybot.Shared.Primitives;
using MessagePipe;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class ApplicationLocksController(KeyedLocker keyedLocker)
  : Controller
{
  private static readonly JsonSerializerOptions _jsonSerializerOptions = new(JsonSerializerDefaults.Web);

  [SubjectPermissionAuthorize(typeof(IApplicationLocksViewPermission))]
  [HttpGet(ApplicationLocksRoutes.EndpointBase)]
  public ActionResult<IEnumerable<ApplicationLocksResponse>> GetAll()
  {
    var locksAndHolders = keyedLocker.GetActiveLocks();

    return Ok(locksAndHolders.Select(x =>
      ApplicationLocksResponse.From(x.Key, x.Value, keyedLocker.GetLockWaitersForKey(x.Key))));
  }

  [SubjectPermissionAuthorize(typeof(IApplicationLocksViewPermission))]
  [HttpGet(ApplicationLocksRoutes.EventStream)]
  public async Task<ActionResult<EventStream<ApplicationLockEvent>>> EventStreamAsync(
    [FromServices] IHostEnvironment hostEnvironment,
    CancellationToken token)
  {
    // short-circuit if test environment so we don't have to deal with a long-running request
    if (hostEnvironment.IsEnvironment("Test"))
    {
      return new EmptyResult();
    }


    Response.Headers["Content-Type"] = "text/event-stream";
    Response.Headers["Cache-Control"] = "no-cache";
    Response.Headers["Connection"] = "keep-alive";
    try
    {
      var events = new AsyncQueue<KeyedLockEvent>();
      using var _ = keyedLocker.GlobalLockEventSubscriber.Subscribe(events.Enqueue);


      await Response.WriteAsync(":KEEPALIVE");
      await Response.WriteAsync("\n\n");
      await Response.Body.FlushAsync();

      while (!token.IsCancellationRequested)
      {
        var evt = await events.DequeueAsync(token);

        await Response.WriteAsync("data: ");

        await JsonSerializer.SerializeAsync(Response.Body, ApplicationLockEvent.From(evt), options: _jsonSerializerOptions, cancellationToken: token);

        await Response.WriteAsync("\n\n");
        await Response.Body.FlushAsync();
      }

      return new EmptyResult();
    }
    catch (OperationCanceledException)
    {
      return new EmptyResult();
    }
  }

  [SubjectPermissionAuthorize(typeof(IApplicationLocksManagePermission))]
  [HttpPost(ApplicationLocksRoutes.RequestCancellation)]
  public ActionResult<OpResult> RequestKeyCancellation(
    [FromBody] CancelApplicationLockRequest request)
  {
    var res = keyedLocker.RequestKeyRelease(request.Key, cancelReason: request.CancelReason);

    return Ok(res);
  }
}
