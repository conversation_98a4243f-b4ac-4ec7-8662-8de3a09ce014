using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.Lib.SignalRHubs;
using Immybot.Shared.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class ChangeRequestsController(
  IUserService userService) : Controller
{
  [SubjectPermissionAuthorize([
    typeof(IChangeRequestManagePermission),
    typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission)
  ])]
  [HttpDelete(ChangeRequestApiRoutes.DeleteChangeRequest)]
  public async Task<IActionResult> DeleteChangeRequest(
    [FromRoute] int id,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequest = changeRequestStore.GetChangeRequest(id);
    if (changeRequest is null) return NotFound();

    await changeRequestStore.DeleteChangeRequest(userService.GetCurrentUser(), changeRequest, CancellationToken.None);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IChangeRequestManagePermission))]
  [HttpPost(ChangeRequestApiRoutes.ApproveChangeRequest)]
  public async Task<IActionResult> ApproveChangeRequest(
    [FromRoute] int id,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] ITargetAssignmentEmitter targetAssignmentEmitter,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequest = changeRequestStore.GetChangeRequest(id);
    if (changeRequest is null) return NotFound();

    var entityId =
      await changeRequestStore.ApproveChangeRequest(userService.GetCurrentUser(),
        changeRequest,
        CancellationToken.None);
    switch (changeRequest.ObjectType)
    {
      case ChangeRequestObjectType.TargetAssignment:
        // emit created/updated entity over signalr
        var query = ctx.TargetAssignments
          .AsNoTracking()
          .Where(a => a.Id == entityId);
        var resources = query.Select(LocalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
        var populatedAssignment = (await targetPopulator.Populate(resources, CancellationToken.None)).First();
        targetAssignmentEmitter.Emit(populatedAssignment);
        break;
      case ChangeRequestObjectType.Script:
      // todo: implement script change requests
      default: break;
    }

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IChangeRequestManagePermission))]
  [HttpPost(ChangeRequestApiRoutes.DenyChangeRequest)]
  public async Task<IActionResult> DenyChangeRequest(
    [FromRoute] int id,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequest = changeRequestStore.GetChangeRequest(id);
    if (changeRequest is null) return NotFound();

    await changeRequestStore.DenyChangeRequest(userService.GetCurrentUser(), changeRequest, CancellationToken.None);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IChangeRequestManagePermission))]
  [HttpPost(ChangeRequestApiRoutes.RequireChanges)]
  public async Task<IActionResult> RequireChanges(
    [FromRoute] int id,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequest = changeRequestStore.GetChangeRequest(id);
    if (changeRequest is null) return NotFound();

    await changeRequestStore.RequireChanges(userService.GetCurrentUser(), changeRequest, CancellationToken.None);

    return NoContent();
  }

  [SubjectPermissionAuthorize([
    typeof(IChangeRequestManagePermission),
    typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission),
  ])]
  [HttpPost(ChangeRequestApiRoutes.CommentOnChangeRequest)]
  public async Task<ActionResult<ChangeRequestCommentResponse>> CommentOnChangeRequest(
    [FromRoute] int id,
    [FromBody] AddChangeRequestCommentBody body,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequest = changeRequestStore.GetChangeRequest(id);
    if (changeRequest is null) return NotFound();

    var comment = await changeRequestStore.CommentOnChangeRequest(userService.GetCurrentUser(),
      changeRequest,
      body.Comment,
      CancellationToken.None);

    var commentResponse = ChangeRequestCommentResponse.Projection.Compile().Invoke(comment);

    return Ok(commentResponse);
  }

  [SubjectPermissionAuthorize([
    typeof(IChangeRequestManagePermission),
    typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission)
  ])]
  [HttpGet(ChangeRequestApiRoutes.GetAllDx)]
  public async Task<ActionResult<LoadResult>> GetAllDx(
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromServices] IChangeRequestStore changeRequestStore,
    DataSourceLoadOptions loadOptions,
    CancellationToken token)
  {
    var changeRequestsDisposable = changeRequestStore
      .GetAllChangeRequests(token);
    Response.RegisterForDispose(changeRequestsDisposable);

    var q = changeRequestsDisposable.Value;
    var currentUserId = userService.GetCurrentUser().Id;
    var canManageChangeRequests =
      await subjectPermissionAuthorizationService.AuthorizeAsync<IChangeRequestManagePermission>(User, strict: false);
    q = canManageChangeRequests ? q : q.Where(a => a.CreatedBy == currentUserId);

    var changeRequests = q
      .AsSplitQuery()
      .Select(ChangeRequestResponse.Projection);
    return Ok(await DataSourceLoader.LoadAsync(changeRequests, loadOptions, token));
  }

  [SubjectPermissionAuthorize([
    typeof(IChangeRequestManagePermission),
    typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission)
  ])]
  [HttpGet(ChangeRequestApiRoutes.GetOpenCount)]
  public async Task<ActionResult<int>> GetOpenCount(
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromServices] IChangeRequestStore changeRequestStore,
    CancellationToken token)
  {
    var currentUserId = userService.GetCurrentUser().Id;
    var canManageChangeRequests = await subjectPermissionAuthorizationService
      .AuthorizeAsync<IChangeRequestManagePermission>(User, strict: false);

    var changeRequestsDisposable = changeRequestStore.GetOpenChangeRequests(token);
    Response.RegisterForDispose(changeRequestsDisposable);
    var count = await changeRequestsDisposable.Value
      .PipeIf(!canManageChangeRequests, q => q.Where(a => a.CreatedBy == currentUserId))
      .CountAsync(token);
    return Ok(count);
  }
}
