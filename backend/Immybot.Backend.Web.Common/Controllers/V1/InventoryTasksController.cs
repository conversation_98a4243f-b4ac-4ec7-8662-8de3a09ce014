using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class InventoryTasksController(IUserService userService) : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(IInventoryTasksViewPermission))]
  [HttpGet(InventoryTaskApiRoutes.GetAll)]
  public async Task<ActionResult<IEnumerable<InventoryTaskResource>>> GetAll(
    [FromServices] IInventoryTaskActions inventoryTaskActions)
  {
    var tasks = await inventoryTaskActions.GetAllInventoryTasks(HttpContext.RequestAborted);
    return Ok(tasks.Select(t => new InventoryTaskResource(t)));
  }

  [SubjectPermissionAuthorize(typeof(IInventoryTasksManagePermission))]
  [HttpPost(InventoryTaskApiRoutes.CreateLocal)]
  public async Task<ActionResult<InventoryTaskResource>> CreateLocal(
    [FromServices] ILocalDbRepository repo,
    [FromServices] IInventoryTaskActions inventoryTaskActions,
    [FromBody] InventoryTaskPayload payload)
  {
    var user = userService.GetCurrentUser();
    var created = await repo.Create<InventoryTask, InventoryTaskPayload>(payload, user);
    if (created is null) return BadRequest("Failed to create inventory task");
    inventoryTaskActions.RebuildCache();
    return Ok(new InventoryTaskResource(created));
  }

  [SubjectPermissionAuthorize(typeof(IInventoryTasksManagePermission))]
  [HttpPost(InventoryTaskApiRoutes.UpdateLocal)]
  public async Task<ActionResult<InventoryTaskResource>> UpdateLocal(
    [FromServices] ILocalDbRepository repo,
    [FromServices] IInventoryTaskActions inventoryTaskActions,
    [FromRoute] int id,
    [FromBody] InventoryTaskPayload payload)
  {
    var user = userService.GetCurrentUser();
    var updated = await repo.Update<InventoryTask, InventoryTaskPayload>(id, payload, user);
    if (updated is null) return NotFound();
    inventoryTaskActions.RebuildCache();
    return Ok(new InventoryTaskResource(updated));
  }

  [SubjectPermissionAuthorize(typeof(IInventoryTasksManagePermission))]
  [HttpDelete(InventoryTaskApiRoutes.DeleteLocal)]
  public async Task<ActionResult<InventoryTaskResource>> DeleteLocal(
    [FromServices] ILocalDbRepository repo,
    [FromServices] IInventoryTaskActions inventoryTaskActions,
    [FromRoute] int id)
  {
    var user = userService.GetCurrentUser();
    await repo.Delete<InventoryTask>(id, user);
    inventoryTaskActions.RebuildCache();
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IInventoryTasksManagePermission))]
  [HttpPost(InventoryTaskApiRoutes.AddScriptToLocalInventoryTask)]
  public async Task<ActionResult<InventoryTaskScriptResource>> AddScriptToLocalInventoryTask(
    [FromServices] ILocalDbRepository repo,
    [FromServices] IInventoryTaskActions inventoryTaskActions,
    [FromRoute] int id,
    [FromBody] InventoryTaskScriptPayload payload)
  {
    var user = userService.GetCurrentUser();
    var p = payload with { InventoryTaskId = id };
    var res = await repo.Create<InventoryTaskScript, InventoryTaskScriptPayload>(
      p,
      q => q.Where(a => a.InventoryKey == payload.InventoryKey && a.InventoryTaskId == payload.InventoryTaskId),
      user);
    if (res is null) return NotFound();

    inventoryTaskActions.RebuildCache();
    return Ok(new InventoryTaskScriptResource(res));
  }

  [SubjectPermissionAuthorize(typeof(IInventoryTasksManagePermission))]
  [HttpDelete(InventoryTaskApiRoutes.DeleteScriptFromLocalInventoryTask)]
  public async Task<ActionResult<InventoryTaskResource>> DeleteScriptFromLocalInventoryTask(
    [FromServices] ILocalDbRepository repo,
    [FromServices] IInventoryTaskActions inventoryTaskActions,
    [FromRoute] int taskId,
    [FromRoute] string inventoryKey)
  {
    var user = userService.GetCurrentUser();
    await repo.Delete<InventoryTaskScript>(
      q => q.Where(a => a.InventoryKey == inventoryKey && a.InventoryTaskId == taskId),
      user);
    inventoryTaskActions.RebuildCache();
    return NoContent();
  }
}
