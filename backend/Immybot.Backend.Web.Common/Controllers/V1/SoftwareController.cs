using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Microsoft.EntityFrameworkCore;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NuGet.Versioning;
using static Immybot.Backend.Web.Common.Controllers.Helpers;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class SoftwareController(
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IResourceAuthorizerFlow resourceAuthorizerFlow,
  IUserService userService)
  : Controller
{
  #region Local Software

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpGet(SoftwareApiRoutes.MigrateLocalToGlobalWhatIf)]
  public ActionResult<MigrationPreviewResponse> MigrateLocalToGlobalWhatIf(
    [FromRoute] int softwareIdentifier,
    [FromServices] ILocalToGlobalMigrator localToGlobalMigrator
    )
  {
    var data = localToGlobalMigrator.MigrateLocalSoftwareToGlobalSoftwareWhatIf(softwareIdentifier);
    return Ok(data);
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(SoftwareApiRoutes.MigrateLocalToGlobal)]
  public async Task<ActionResult<int>> MigrateLocalToGlobal(
    [FromRoute] int softwareIdentifier,
    [FromServices] ILocalToGlobalMigrator localToGlobalMigrator
    )
  {
    // this could take a while
    var globalSoftwareId =
      await localToGlobalMigrator.MigrateLocalSoftwareToGlobalSoftware(softwareIdentifier,
        userService.GetCurrentUser());
    return Ok(globalSoftwareId);
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetAllLocalSoftware)]
  public async Task<ActionResult<GetLocalSoftwareResponse[]>> GetAllLocalSoftware(
    [FromServices] ILocalSoftwareActions localSoftwareActions,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromQuery] string? include = null)
  {
    var (includeVersions, includeSoftwarePrereqs, includeTenantSoftware) =
      ParseIncluders(include, "softwareVersions", "softwarePrerequisites", "tenantSoftware");

    // Build permission filter that evaluates tenant relationships through TenantSoftware join table.
    // For View permissions: User sees software owned by their tenant OR globally available software.
    // For Manage permissions: User sees only software owned by their tenant.
    // MSP users have elevated permissions and see software across all tenants.
    var softwareFilter = permissionFilterBuilder.BuildFilterExpression<LocalSoftware, ISoftwareViewPermission>();

    // Execute query with permission filtering applied at SQL level for optimal performance.
    // The queryable approach allows EF Core to translate the permission logic into efficient EXISTS clauses.
    var localSoftwareQueryable = localSoftwareActions.GetAllSoftwareQueryable(
      withVersions: includeVersions,
      withPrerequisites: includeSoftwarePrereqs,
      withTenantSoftware: includeTenantSoftware,
      withSoftwareIcon: true,
      withUpdatedByUser: true);

    Response?.RegisterForDispose(localSoftwareQueryable);

    // todo: break out the tenant software data into a different api method
    if (!await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<ISoftwareViewPermission>(User, strict: false))
    {
      includeTenantSoftware = false;
    }

    var software = localSoftwareQueryable.Value
      .TagForTelemetry()
      .Where(softwareFilter)
      .Where(s => !string.IsNullOrEmpty(s.Name))
      .OrderBy(s => s.Name)
      .AsNoTracking()
      .AsEnumerable()  // Switch to LINQ to Objects here
      .Select(s => new GetLocalSoftwareResponse(s,
        includeVersions,
        includeSoftwarePrereqs,
        includeTenantSoftware,
        includeSoftwareIcon: true))
      .ToList();

    return Ok(software);
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetLocalSoftware)]
  public async Task<ActionResult<GetLocalSoftwareResponse>> GetLocalSoftware(
    [FromServices] ILocalSoftwareActions localSoftwareActions,
    [FromRoute] string softwareIdentifier,
    [FromQuery] string? include = null)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
      new DefaultKeyParameters(Convert.ToInt32(softwareIdentifier)),
      true);

    var (includeVersions, includeSoftwarePrereqs, includeTenantSoftware) =
      ParseIncluders(include, "softwareVersions", "softwarePrerequisites", "tenantSoftware");

    // todo: break out the tenant software data into a different api method
    if (!await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<ISoftwareViewPermission>(User, strict: false))
    {
      includeTenantSoftware = false;
    }

    var software = localSoftwareActions.GetById(softwareIdentifier,
      withVersions: includeVersions,
      withTenantSoftware: includeTenantSoftware,
      withPrerequisites: includeSoftwarePrereqs,
      withSoftwareIcon: true,
      withUpdatedByUser: true);

    if (software is null) return NotFound();

    return Ok(new GetLocalSoftwareResponse(software,
      includeTenantSoftware: includeTenantSoftware,
      includeSoftwareVersions: includeVersions,
      includeSoftwarePrerequisites: includeSoftwarePrereqs,
      includeSoftwareIcon: true));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareManagePermission))]
  [HttpPost(SoftwareApiRoutes.CreateLocalSoftware)]
  public async Task<ActionResult<GetLocalSoftwareResponse>> CreateLocalSoftware(
    [FromServices] ILocalSoftwareActions localSoftwareActions,
    [FromBody] CreateLocalSoftwareRequestBody requestBody)
  {
    await ValidateAccessLevelChanges(requestBody);

    // todo: validate user can see scripts, tasks, and prereq software...

    var created = localSoftwareActions.CreateSoftware(requestBody, userService.GetCurrentUser());
    return Ok(new GetLocalSoftwareResponse(created, includeSoftwarePrerequisites: true, includeTenantSoftware: true));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareManagePermission))]
  [HttpPatch(SoftwareApiRoutes.UpdateLocalSoftware)]
  public async Task<ActionResult<GetLocalSoftwareResponse>> UpdateLocalSoftware(
    [FromServices] ILocalSoftwareActions localSoftwareActions,
    [FromRoute] string softwareIdentifier,
    [FromBody] UpdateLocalSoftwareRequestBody requestBody)
  {
    if (!int.TryParse(softwareIdentifier, out var softwareId)) return BadRequest();
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareManagePermission>(
      new DefaultKeyParameters(softwareId),
      true);

    requestBody.SoftwareId = softwareId;

    await ValidateAccessLevelChanges(requestBody);

    // todo: validate user can see scripts, tasks, and prereq software...

    var updated = localSoftwareActions.UpdateSoftware(requestBody, userService.GetCurrentUser());
    if (updated == null) return NotFound();
    return Ok(new GetLocalSoftwareResponse(updated, includeSoftwarePrerequisites: true, includeTenantSoftware: true));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareManagePermission))]
  [HttpDelete(SoftwareApiRoutes.DeleteLocalSoftware)]
  public async Task<IActionResult> DeleteLocalSoftware(
    [FromServices] ILocalSoftwareActions localSoftwareActions,
    [FromRoute] string softwareIdentifier)
  {
    if (!int.TryParse(softwareIdentifier, out var softwareId)) return BadRequest();
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareManagePermission>(
      new DefaultKeyParameters(softwareId),
      true);
    var software = localSoftwareActions.GetById(softwareIdentifier);
    if (software == null) return NotFound();
    localSoftwareActions.DeleteSoftware(software, userService.GetCurrentUser());
    return NoContent();
  }

  #endregion Local Software

  #region Global Software

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetAllGlobalSoftware)]
  public ActionResult<GetGlobalSoftwareResponse[]> GetAllGlobalSoftware(
    [FromServices] IGlobalSoftwareActions globalSoftwareActions,
    [FromQuery] string? include = null)
  {
    var (includeVersions, includeSoftwarePrereqs) =
      ParseIncluders(include, "softwareVersions", "softwarePrerequisites");
    var software = globalSoftwareActions
      .GetAllSoftware(withVersions: includeVersions, withPrerequisites: includeSoftwarePrereqs, withSoftwareIcon: true)
      .Where(s => !string.IsNullOrEmpty(s.Name))
      .OrderBy(s => s.Name)
      .Select(s => new GetGlobalSoftwareResponse(s,
        includeSoftwareVersions: includeVersions,
        includeSoftwarePrerequisites: includeSoftwarePrereqs,
        includeSoftwareIcon: true))
      .ToList();
    return Ok(software);
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetGlobalSoftware)]
  public ActionResult<GetGlobalSoftwareResponse> GetGlobalSoftware(
    [FromServices] IGlobalSoftwareActions globalSoftwareActions,
    [FromRoute] string softwareIdentifier,
    [FromQuery] string? include = null)
  {
    var (includeVersions, includeSoftwarePrereqs) =
      ParseIncluders(include, "softwareVersions", "softwarePrerequisites");
    var software = globalSoftwareActions.GetById(softwareIdentifier,
      withVersions: includeVersions,
      withPrerequisites: includeSoftwarePrereqs,
      withSoftwareIcon: true);
    if (software == null) return NotFound();
    return Ok(new GetGlobalSoftwareResponse(software,
      includeSoftwareVersions: includeVersions,
      includeSoftwarePrerequisites: includeSoftwarePrereqs,
      includeSoftwareIcon: true));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(SoftwareApiRoutes.CreateGlobalSoftware)]
  public ActionResult<GetGlobalSoftwareResponse> CreateGlobalSoftware(
    [FromServices] IGlobalSoftwareActions globalSoftwareActions,
    [FromBody] CreateGlobalSoftwareRequestBody requestBody)
  {
    var created = globalSoftwareActions.CreateSoftware(requestBody, userService.GetCurrentUser());
    return Ok(new GetGlobalSoftwareResponse(created, includeSoftwarePrerequisites: true));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPatch(SoftwareApiRoutes.UpdateGlobalSoftware)]
  public ActionResult<GetGlobalSoftwareResponse> UpdateGlobalSoftware(
    [FromServices] IGlobalSoftwareActions globalSoftwareActions,
    [FromRoute] string softwareIdentifier,
    [FromBody] UpdateGlobalSoftwareRequestBody requestBody)
  {
    if (!int.TryParse(softwareIdentifier, out var softwareId)) return BadRequest();

    requestBody.SoftwareId = softwareId;
    var updated = globalSoftwareActions.UpdateSoftware(requestBody, userService.GetCurrentUser());
    if (updated == null) return NotFound();
    return Ok(new GetGlobalSoftwareResponse(updated, includeSoftwarePrerequisites: true));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpDelete(SoftwareApiRoutes.DeleteGlobalSoftware)]
  public IActionResult DeleteGlobalSoftware(
    [FromServices] IGlobalSoftwareActions globalSoftwareActions,
    [FromRoute] string softwareIdentifier)
  {
    var software = globalSoftwareActions.GetById(softwareIdentifier);
    if (software == null) return NotFound();
    globalSoftwareActions.DeleteSoftware(software, userService.GetCurrentUser());
    return NoContent();
  }

  #endregion Global Software

  #region Local Software Versions

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetAllLocalSoftwareVersions)]
  public async Task<ActionResult<GetLocalSoftwareVersionResponse[]>> GetLocalSoftwareVersions(
    [FromServices] ILocalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
      new DefaultKeyParameters(Convert.ToInt32(softwareIdentifier)),
      true);

    var software = softwareActions.GetById(softwareIdentifier, withVersions: true);
    if (software == null) return NotFound();
    return Ok(software.SoftwareVersions.Select(v => new GetLocalSoftwareVersionResponse(v, includeSoftware: false)));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetLocalSoftwareVersion)]
  public async Task<ActionResult<GetLocalSoftwareVersionResponse>> GetLocalSoftwareVersion(
    [FromServices] ILocalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier,
    [FromRoute] SemanticVersion semanticVersion)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
      new DefaultKeyParameters(Convert.ToInt32(softwareIdentifier)),
      true);
    var softwareVersion = softwareActions.GetSoftwareVersion(softwareIdentifier, semanticVersion, populateSoftware: true);
    if (softwareVersion == null) return NotFound();
    return Ok(new GetLocalSoftwareVersionResponse(softwareVersion));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetLatestVersionForLocalSoftware)]
  public async Task<ActionResult<GetLocalSoftwareVersionResponse>> GetLatestVersionForLocalSoftware(
    [FromServices] ILocalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
      new DefaultKeyParameters(Convert.ToInt32(softwareIdentifier)),
      true);

    var software = softwareActions.GetById(softwareIdentifier, withVersions: true);
    if (software == null) return NotFound();
    if (software.SoftwareVersions.Count == 0) return NoContent();
    var maxSemanticVersion = software.SoftwareVersions.Max(a => a.SemanticVersion);
    var version = software.SoftwareVersions.First(a => a.SemanticVersion == maxSemanticVersion);
    version.Software = null;
    return Ok(new GetLocalSoftwareVersionResponse(version));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareManagePermission))]
  [HttpPost(SoftwareApiRoutes.CreateLocalSoftwareVersion)]
  public async Task<ActionResult<GetLocalSoftwareVersionResponse>> CreateLocalSoftwareVersion(
    [FromServices] ILocalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier,
    [FromBody] CreateLocalSoftwareVersionRequestBody requestBody)
  {
    if (!int.TryParse(softwareIdentifier, out var softwareId)) return BadRequest();
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareManagePermission>(
      new DefaultKeyParameters(Convert.ToInt32(softwareId)),
      true);

    var software = softwareActions.GetById(softwareIdentifier);
    if (software is null) return NotFound();
    requestBody.SoftwareId = software.Id;
    AddCacheSourcePathIfMissing(requestBody);
    var created = softwareActions.CreateSoftwareVersion(requestBody, userService.GetCurrentUser());
    return Ok(new GetLocalSoftwareVersionResponse(created));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareManagePermission))]
  [HttpPatch(SoftwareApiRoutes.UpdateLocalSoftwareVersion)]
  public async Task<ActionResult<GetLocalSoftwareVersionResponse>> UpdateLocalSoftwareVersion(
    [FromServices] ILocalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier,
    [FromRoute] SemanticVersion semanticVersion,
    [FromBody] UpdateLocalSoftwareVersionRequestBody requestBody)
  {
    if (!int.TryParse(softwareIdentifier, out var softwareId)) return BadRequest();
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareManagePermission>(
      new DefaultKeyParameters(Convert.ToInt32(softwareId)),
      true);

    var software = softwareActions.GetById(softwareIdentifier);
    if (software is null) return NotFound();

    requestBody.SoftwareId = softwareId;
    requestBody.CurrentSemanticVersion = semanticVersion;
    AddCacheSourcePathIfMissing(requestBody);

    var updated = softwareActions.UpdateSoftwareVersion(requestBody, userService.GetCurrentUser());
    if (updated == null) return NotFound();
    return Ok(new GetLocalSoftwareVersionResponse(updated));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareManagePermission))]
  [HttpPost(SoftwareApiRoutes.UploadLocalSoftwareVersionFile)]
  public async Task<ActionResult<SoftwareFileUploadData?>> UploadLocalSoftwareVersionFile(
    [FromServices] IAzureBlobStorageUploadService azureBlobStorageService,
    IFormFile? file,
    [FromForm] string chunkMetadata)
  {
    if (file is null || string.IsNullOrEmpty(chunkMetadata)) return NoContent();

    var metaDataObject = JsonConvert.DeserializeObject<ChunkMetadata>(chunkMetadata);
    if(metaDataObject == null) return BadRequest("Failed to parse file, file may be invalid");
    UploadHelper.CheckFileExtensionValid(metaDataObject.FileName);

    var identifier = $"{metaDataObject.FileGuid}-{metaDataObject.FileName}";
    var packageData = new AzureUploadData()
    {
      FileGuid = Guid.Parse(metaDataObject.FileGuid),
      FileName = metaDataObject.FileName,
      PackageType = metaDataObject.FileName.EndsWith(".zip", StringComparison.InvariantCultureIgnoreCase) ? PackageType.EntireFolder : PackageType.InstallerFile,
    };

    // ensure reference to blob is good and upload chunk
    await azureBlobStorageService.PrepareCloudBlockBlobForSoftware(DatabaseType.Local, identifier, packageData);
    await azureBlobStorageService.UploadChunkForBlob(identifier, file);

    // once all chunks are uploaded, commit chunks and return version meta data
    if (metaDataObject.Index == metaDataObject.TotalCount - 1)
    {
      var hash = azureBlobStorageService.GetMD5Hash(identifier);
      var blobName = await azureBlobStorageService.CommitBlocks(identifier);
      var fileUploadData = new SoftwareFileUploadData()
      {
        RelativeCacheSourcePath = packageData.FileGuid,
        InstallerType = SoftwareVersionInstallerType.File,
        PackageType = packageData.PackageType,
        InstallerFile = packageData.PackageType == PackageType.InstallerFile ? packageData.FileName : null,
        FileBlobName = blobName,
        Md5Hash = hash,
      };
      return Ok(fileUploadData);
    }

    // if not the last chunk then return no content
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetDownloadUrlForLocalSoftwareVersion)]
  public async Task<ActionResult<string>> GetDownloadUrlForLocalSoftwareVersion(
    [FromServices] ILocalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier,
    [FromRoute] SemanticVersion semanticVersion,
    [FromServices] IAzureBlobStorageSasService azureBlobStorageSasService)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
      new DefaultKeyParameters(Convert.ToInt32(softwareIdentifier)),
      true);
    var softwareVersion = softwareActions.GetSoftwareVersion(softwareIdentifier, semanticVersion);
    if (softwareVersion == null) return NotFound();

    // throw if software does not have a File InstallerType
    if (softwareVersion.InstallerType != SoftwareVersionInstallerType.File)
    {
      throw new ImmyWebException(new HttpProblem()
      {
        Title = "Software Download Problem",
        Detail = "Software does not have an installer type of 'File' so a file can not be downloaded",
        Status = System.Net.HttpStatusCode.UnprocessableEntity,
      });
    }

    if (softwareVersion.BlobName == null) return NotFound();
    var uri = azureBlobStorageSasService.GetLocalSoftwareDownloadUrl(softwareVersion.BlobName);
    return Ok(uri);
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareManagePermission))]
  [HttpPost(SoftwareApiRoutes.FastCreateLocalSoftwareVersion)]
  public async Task<ActionResult<GetLocalSoftwareResponse>> FastCreateLocalVersion(
    [FromServices] ILocalSoftwareActions localSoftwareActions,
    [FromBody] FastCreateLocalVersionRequestBody body)
  {
    if (body.SoftwareId is null && body.Software is null) return BadRequest("SoftwareId or Software must be provided");
    if (body.SoftwareVersion is null && body.Software?.UseDynamicVersions is false)
      return BadRequest("SoftwareVersion must be provided if software does not use dynamic versions");


    LocalSoftware? software;
    if (body.SoftwareId != null)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareManagePermission>(
        new DefaultKeyParameters(body.SoftwareId.Value),
        true);
      // if existing software id is provided, make sure it exists and user can create a version for it
      var existingId = body.SoftwareId.Value;
      software = localSoftwareActions.GetById(existingId);
      if (software == null) return NotFound("Software was not found");
    }
    else
    {
      // existing software id is not provided, create it and set the owner tenant id if user is non msp
      var softwareCreation = body.Software;

      if(softwareCreation is null)
        return BadRequest("Invalid software creation request");

      var currentUser = userService.GetCurrentUser();
      if (!currentUser.IsMsp)
        softwareCreation.OwnerTenantId = currentUser.TenantId;

      software = localSoftwareActions.CreateSoftware(softwareCreation, userService.GetCurrentUser());
    }

    if (!software.UseDynamicVersions && body.SoftwareVersion is not null)
    {
      // create version
      var existingVersion = localSoftwareActions
        .GetSoftwareVersion(software.Identifier, body.SoftwareVersion.SemanticVersion);
      if (existingVersion != null)
      {
        throw new ImmyWebException(
          new EntityAlreadyExistsProblem(
            $"This software already has an existing version {body.SoftwareVersion.SemanticVersion}.",
            Request.Path));
      }

      var versionCreation = body.SoftwareVersion;
      versionCreation.SoftwareId = software.Id;
      AddCacheSourcePathIfMissing(body.SoftwareVersion);
      localSoftwareActions.CreateSoftwareVersion(versionCreation, userService.GetCurrentUser());
    }
    var updatedSoftware = localSoftwareActions.GetById(software.Identifier, withVersions: true);
    if (updatedSoftware == null) return NotFound();
    return Ok(new GetLocalSoftwareResponse(updatedSoftware, includeSoftwareVersions: true));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareManagePermission))]
  [HttpDelete(SoftwareApiRoutes.DeleteLocalSoftwareVersion)]
  public async Task<IActionResult> DeleteLocalSoftwareVersion(
    [FromServices] ILocalSoftwareActions localSoftwareActions,
    [FromRoute] string softwareIdentifier,
    [FromRoute] SemanticVersion semanticVersion)
  {
    if (!int.TryParse(softwareIdentifier, out var softwareId)) return BadRequest();
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareManagePermission>(
      new DefaultKeyParameters(Convert.ToInt32(softwareIdentifier)),
      true);
    localSoftwareActions.DeleteSoftwareVersion(new DeleteLocalSoftwareVersionPayload(softwareId, semanticVersion),
      userService.GetCurrentUser());
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareManagePermission))]
  [HttpPost(SoftwareApiRoutes.AnalyzeLocalSoftwarePackage)]
  public async Task<ActionResult<AnalyzeLocalSoftwarePackageResponse>> AnalyzeLocalPackage(
    [FromBody] AnalyzePackageParams packageParams,
    [FromServices] IPackageAnalyzer packageAnalyzer,
    [FromServices] IAzureBlobStorageSasService azureBlobStorageSasService)
  {
    if (string.IsNullOrEmpty(packageParams.Url) && string.IsNullOrEmpty(packageParams.BlobName))
    {
      return BadRequest("Url or BlobName must be provided");
    }

    var url = packageParams.Url ?? azureBlobStorageSasService.GetLocalSoftwareDownloadUrl(packageParams.BlobName!);
    var analyzerResponse = await packageAnalyzer.Run(new Uri(url), packageParams.FileName);
    return Ok(new AnalyzeLocalSoftwarePackageResponse(analyzerResponse));
  }

  #endregion Local Software Versions

  #region Global Software Versions

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetAllGlobalSoftwareVersions)]
  public ActionResult<GetGlobalSoftwareVersionResponse> GetGlobalSoftwareVersions(
    [FromServices] IGlobalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier)
  {
    var software = softwareActions.GetById(softwareIdentifier, withVersions: true);
    if (software == null) return NotFound();
    return Ok(software.SoftwareVersions.Select(v => new GetGlobalSoftwareVersionResponse(v, includeSoftware: false)));
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetGlobalSoftwareVersion)]
  public ActionResult<GetGlobalSoftwareVersionResponse> GetGlobalSoftwareVersion(
    [FromServices] IGlobalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier,
    [FromRoute] SemanticVersion semanticVersion)
  {
    var softwareVersion = softwareActions
      .GetSoftwareVersion(softwareIdentifier, semanticVersion, populateSoftware: true);
    if (softwareVersion == null) return NotFound();
    return Ok(new GetGlobalSoftwareVersionResponse(softwareVersion));

  }

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetLatestVersionForGlobalSoftware)]
  public ActionResult<GetGlobalSoftwareVersionResponse> GetLatestVersionForGlobalSoftware(
    [FromServices] IGlobalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier)
  {
    var software = softwareActions.GetById(softwareIdentifier, withVersions: true);
    if (software == null) return NotFound();
    if (software.SoftwareVersions.Count == 0) return NoContent();
    var maxSemanticVersion = software.SoftwareVersions.Max(a => a.SemanticVersion);
    var version = software.SoftwareVersions.First(a => a.SemanticVersion == maxSemanticVersion);
    return Ok(new GetGlobalSoftwareVersionResponse(version));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(SoftwareApiRoutes.CreateGlobalSoftwareVersion)]
  public ActionResult<GetGlobalSoftwareVersionResponse> CreateGlobalSoftwareVersion(
    [FromServices] IGlobalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier,
    [FromBody] CreateGlobalSoftwareVersionRequestBody requestBody)
  {
    var software = softwareActions.GetById(softwareIdentifier);
    if (software is null) return NotFound();

    requestBody.SoftwareId = software.Id;
    AddCacheSourcePathIfMissing(requestBody);

    var created = softwareActions.CreateSoftwareVersion(requestBody, userService.GetCurrentUser());
    return Ok(new GetGlobalSoftwareVersionResponse(created));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPatch(SoftwareApiRoutes.UpdateGlobalSoftwareVersion)]
  public ActionResult<GetGlobalSoftwareVersionResponse> UpdateGlobalSoftwareVersion(
    [FromServices] IGlobalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier,
    [FromRoute] SemanticVersion semanticVersion,
    [FromBody] UpdateGlobalSoftwareVersionRequestBody requestBody)
  {
    if (!int.TryParse(softwareIdentifier, out var softwareId)) return BadRequest();
    requestBody.SoftwareId = softwareId;
    requestBody.CurrentSemanticVersion = semanticVersion;
    AddCacheSourcePathIfMissing(requestBody);

    var updated = softwareActions.UpdateSoftwareVersion(requestBody, userService.GetCurrentUser());
    if (updated == null) return NotFound();
    return Ok(new GetGlobalSoftwareVersionResponse(updated));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(SoftwareApiRoutes.UploadGlobalSoftwareVersionFile)]
  public async Task<ActionResult<SoftwareFileUploadData?>> UploadGlobalSoftwareVersionFile(
    [FromServices] IAzureBlobStorageUploadService azureBlobStorageService,
    IFormFile? file,
    [FromForm] string chunkMetadata)
  {
    if (file is null || string.IsNullOrEmpty(chunkMetadata)) return NoContent();

    var metaDataObject = JsonConvert.DeserializeObject<ChunkMetadata>(chunkMetadata);
    if (metaDataObject == null) return BadRequest("Invalid chunk metadata");
    UploadHelper.CheckFileExtensionValid(metaDataObject.FileName);

    var identifier = $"{metaDataObject.FileGuid}-{metaDataObject.FileName}";
    var packageData = new AzureUploadData()
    {
      FileGuid = Guid.Parse(metaDataObject.FileGuid),
      FileName = metaDataObject.FileName,
      PackageType = metaDataObject.FileName.EndsWith(".zip", StringComparison.InvariantCultureIgnoreCase) ? PackageType.EntireFolder : PackageType.InstallerFile,
    };

    // ensure reference to blob is good and upload chunk
    await azureBlobStorageService.PrepareCloudBlockBlobForSoftware(DatabaseType.Global, identifier, packageData);
    await azureBlobStorageService.UploadChunkForBlob(identifier, file);

    // if not the last chunk then return no content
    if (metaDataObject.Index != metaDataObject.TotalCount - 1) return NoContent();

    // once all chunks are uploaded, commit chunks and return version meta data
    var hash = azureBlobStorageService.GetMD5Hash(identifier);
    var blobName = await azureBlobStorageService.CommitBlocks(identifier);
    var fileUploadData = new SoftwareFileUploadData()
    {
      RelativeCacheSourcePath = packageData.FileGuid,
      InstallerType = SoftwareVersionInstallerType.File,
      PackageType = packageData.PackageType,
      InstallerFile = packageData.PackageType == PackageType.InstallerFile ? packageData.FileName : null,
      FileBlobName = blobName,
      Md5Hash = hash,
    };
    return Ok(fileUploadData);
  }

  [SubjectPermissionAuthorize(typeof(ISoftwareViewPermission))]
  [HttpGet(SoftwareApiRoutes.GetDownloadUrlForGlobalSoftwareVersion)]
  public async Task<ActionResult<string>> GetDownloadUrlForGlobalSoftwareVersion(
    [FromServices] IGlobalSoftwareActions softwareActions,
    [FromRoute] string softwareIdentifier,
    [FromRoute] SemanticVersion semanticVersion,
    [FromServices] IAzureBlobStorageSasService azureBlobStorageSasService)
  {
    var softwareVersion = softwareActions.GetSoftwareVersion(softwareIdentifier, semanticVersion, populateSoftware: true);
    if (softwareVersion == null) return NotFound();

    // throw if software does not have a File InstallerType
    if (softwareVersion.InstallerType != SoftwareVersionInstallerType.File)
    {
      throw new ImmyWebException(new HttpProblem()
      {
        Title = "Software Download Problem",
        Detail = "Software does not have an installer type of 'File' so a file can not be downloaded",
        Status = System.Net.HttpStatusCode.UnprocessableEntity,
      });
    }

    if (softwareVersion.BlobName == null) return NotFound();
    var uri = azureBlobStorageSasService.GetGlobalSoftwareDownloadUrl(softwareVersion.BlobName);
    return Ok(uri);
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(SoftwareApiRoutes.FastCreateGlobalSoftwareVersion)]
  public ActionResult<GetGlobalSoftwareResponse> FastCreateGlobalVersion(
    [FromServices] IGlobalSoftwareActions globalSoftwareActions,
    [FromBody] FastCreateGlobalVersionRequestBody body)
  {
    // allow creation without a version if the software uses dynamic versions
    if (body.SoftwareId is null && body.Software is null) return BadRequest("SoftwareId or Software must be provided");
    if (body.SoftwareVersion is null && body.Software?.UseDynamicVersions is false)
      return BadRequest("SoftwareVersion must be provided if software does not use dynamic versions");

    GlobalSoftware? software;
    if (body.SoftwareId != null)
    {
      // if existing software id is provided, make sure it exists and user can create a version for it
      var existingId = body.SoftwareId.Value;
      software = globalSoftwareActions.GetById(existingId);
      if (software == null) return BadRequest();
    }
    else
    {
      // existing software id is not provided, create it
      var softwareCreation = body.Software;
      if (softwareCreation is null) return NotFound();
      software = globalSoftwareActions.CreateSoftware(softwareCreation, userService.GetCurrentUser());
    }

    // create version
    if (!software.UseDynamicVersions && body.SoftwareVersion is not null)
    {
      var existingVersion = globalSoftwareActions
        .GetSoftwareVersion(software.Identifier, body.SoftwareVersion.SemanticVersion);
      if (existingVersion != null)
      {
        throw new ImmyWebException(
          new EntityAlreadyExistsProblem(
            $"This software already has an existing version {body.SoftwareVersion.SemanticVersion}.",
            Request.Path));
      }

      var versionCreation = body.SoftwareVersion;
      versionCreation.SoftwareId = software.Id;
      AddCacheSourcePathIfMissing(body.SoftwareVersion);
      globalSoftwareActions.CreateSoftwareVersion(versionCreation, userService.GetCurrentUser());
    }

    var updatedSoftware = globalSoftwareActions.GetById(software.Identifier, withVersions: true);
    if (updatedSoftware is null) return NotFound("Failed to find software after creation");
    return Ok(new GetGlobalSoftwareResponse(updatedSoftware, includeSoftwareVersions: true));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpDelete(SoftwareApiRoutes.DeleteGlobalSoftwareVersion)]
  public IActionResult DeleteGlobalSoftwareVersion(
    [FromServices] IGlobalSoftwareActions globalSoftwareActions,
    [FromRoute] string softwareIdentifier,
    [FromRoute] SemanticVersion semanticVersion)
  {
    if (!int.TryParse(softwareIdentifier, out var softwareId)) return BadRequest();
    globalSoftwareActions.DeleteSoftwareVersion(new DeleteGlobalSoftwareVersionPayload(softwareId, semanticVersion),
      userService.GetCurrentUser());
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(SoftwareApiRoutes.AnalyzeGlobalSoftwarePackage)]
  public async Task<ActionResult<AnalyzeGlobalSoftwarePackageResponse>> AnalyzeGlobalPackage(
    [FromBody] AnalyzePackageParams packageParams,
    [FromServices] IPackageAnalyzer packageAnalyzer,
    [FromServices] IAzureBlobStorageSasService azureBlobStorageSasService)
  {
    if (string.IsNullOrEmpty(packageParams.Url) && string.IsNullOrEmpty(packageParams.BlobName))
    {
      return BadRequest("Url or BlobName must be provided");
    }
    var url = packageParams.Url ?? azureBlobStorageSasService.GetGlobalSoftwareDownloadUrl(packageParams.BlobName!);
    var packageAnalyzerResponse = await packageAnalyzer.Run(new Uri(url), packageParams.FileName);

    // set suggest script types to global
    if (packageAnalyzerResponse.SuggestedBatchInstallScript != null)
      packageAnalyzerResponse.SuggestedBatchInstallScript.ScriptType = DatabaseType.Global;
    if (packageAnalyzerResponse.SuggestedPowerShellInstallScript != null)
      packageAnalyzerResponse.SuggestedPowerShellInstallScript.ScriptType = DatabaseType.Global;
    if (packageAnalyzerResponse.SuggestedDetectionScript != null)
      packageAnalyzerResponse.SuggestedDetectionScript.ScriptType = DatabaseType.Global;
    if (packageAnalyzerResponse.SuggestedPowerShellUninstallScript != null)
      packageAnalyzerResponse.SuggestedPowerShellUninstallScript.ScriptType = DatabaseType.Global;
    return Ok(new AnalyzeGlobalSoftwarePackageResponse(packageAnalyzerResponse));
  }
  #endregion Global Software Versions

  private static void AddCacheSourcePathIfMissing(SoftwareVersionRequestBodyBase body)
  {
    if (string.IsNullOrWhiteSpace(body.RelativeCacheSourcePath))
    {
      body.RelativeCacheSourcePath = Guid.NewGuid().ToString();
    }
  }

  private async Task ValidateAccessLevelChanges(ISoftwareAccessLevel accessLevel)
  {
    // if the request doesn't contain any tenant relationships, then the user must have the ability to create software for all tenants
    if (accessLevel.TenantSoftware.Count is 0 && !accessLevel.OwnerTenantId.HasValue &&
        await subjectPermissionAuthorizationService
          .AuthorizeGlobalAsync<ISoftwareManagePermission>(User, strict: false))
    {
      accessLevel.OwnerTenantId = userService.GetTenantId();
      return;
    }

    // ensure the user has permission to create software for each specified tenant
    if (accessLevel.TenantSoftware.Count > 0)
    {
      foreach (var tenantId in accessLevel.TenantSoftware)
      {
        await subjectPermissionAuthorizationService.AuthorizeTenantAsync<ISoftwareManagePermission>(
          User,
          tenantId,
          strict: true);
      }

      return;
    }

    // ensure the user has permission to create software for the specified owner
    if (accessLevel.OwnerTenantId.HasValue)
    {
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<ISoftwareManagePermission>(
        User,
        accessLevel.OwnerTenantId.Value,
        strict: true);
    }
  }
}
