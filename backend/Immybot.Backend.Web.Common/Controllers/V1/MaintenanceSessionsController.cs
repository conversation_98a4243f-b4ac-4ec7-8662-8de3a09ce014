using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using Immybot.Backend.Application.DbContextExtensions.MaintenanceSessionExtensions;
using Immybot.Backend.Application.DbContextExtensions.ScheduleExtensions;
using Immybot.Backend.Application.DbContextExtensions.SessionLogExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Application.Lib.SessionLogs;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class MaintenanceSessionsController(
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IPermissionFilterBuilder permissionFilterBuilder,
  IResourceAuthorizerFlow resourceAuthorizerFlow,
  IUserService userService,
  IInitializationStatus initStatus)
  : Controller
{
  private const string _errorMessageAppIsInitializing = "Application is still initializing. Maintenance sessions cannot be triggered until initialization is completed.";

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceSessionApiRoutes.DxGetAll)]
  public ActionResult<LoadResult> DxGetAll(
    [FromServices] ImmybotDbContext ctx,
    DataSourceLoadOptions loadOptions,
    [FromQuery] int? computerId = null,
    [FromQuery] int? tenantId = null,
    [FromQuery] int? personId = null,
    [FromQuery] SessionType sessionType = SessionType.Computer)
  {
    var filter = permissionFilterBuilder
      .BuildFilterExpression<MaintenanceSession, IMaintenanceSessionsViewPermission>();
    var q = ctx.GetAllMaintenanceSessions(includeComputers: true)
      .Where(filter)
      .AsSplitQuery();

    if (computerId.HasValue)
      q = q.Where(a => a.Computer != null && a.Computer.Id == computerId);

    if (tenantId.HasValue)
      q = q.Where(a => a.TenantId == tenantId);

    if (personId.HasValue)
      q = q.Where(a => a.PersonId == personId);

    if (sessionType == SessionType.Computer)
    {
      q = q.Where(a => a.ComputerId != null);
    }
    else if (sessionType == SessionType.Cloud)
    {
      q = q.Where(a => a.ComputerId == null && a.PersonId == null);
    }
    else if (sessionType is SessionType.Person)
    {
      q = q.Where(a => a.PersonId != null);
    }

    var sessions = q.Select(GetDxMaintenanceSessionResponse.Projection);
    return Ok(DataSourceLoader.Load(sessions, loadOptions));
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceSessionApiRoutes.Get)]
  public async Task<ActionResult<GetMaintenanceSessionResponse>> Get(
    int sessionId,
    [FromQuery] bool includeActions,
    [FromQuery] bool includeStages,
    [FromQuery] bool includeActionActivities,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ISessionObjectsUpdateHandler sessionObjectsUpdateHandler)
  {
    await resourceAuthorizerFlow
      .PerformAuthorizationWorkflowAsync<MaintenanceSession, IMaintenanceSessionsViewPermission>(
        new DefaultKeyParameters(sessionId),
        true);

    var query = ctx.MaintenanceSessions.AsNoTracking();

    if (includeActions)
    {
      query = query.Include(a => a.MaintenanceActions).ThenInclude(a => a.DependsOn).ThenInclude(a => a.DependsOn);
      query = query.Include(a => a.MaintenanceActions).ThenInclude(a => a.Dependents).ThenInclude(a => a.Dependent);
    }

    var session = await query.Select(GetMaintenanceSessionResponse.Projection(
      includeArgs: true,
      includeActions: includeActions,
      includeStages: includeStages))
    .AsSplitQuery()
    .FirstOrDefaultAsync(a => a.Id == sessionId);

    if (session == null) return NotFound();

    var sessionData = sessionObjectsUpdateHandler.GetSessionWorkQueue()
      .FirstOrDefault(a => a.Id == sessionId);

    if (sessionData is not null)
    {
      session.SessionStatus = sessionData.SessionStatus;
    }

    if (includeActions)
    {
      var actionData = sessionObjectsUpdateHandler.GetMaintenanceActionWorkQueue()
       .Where(a => a.MaintenanceSessionId == session.Id)
       .Select(a => new GetMaintenanceActionResponse(a))
       .ToList();

      foreach (var a in actionData)
      {
        var existing = session.MaintenanceActions?.FirstOrDefault(b => b.Id == a.Id);
        if (existing is not null)
        {
          session.MaintenanceActions?.Remove(existing);
        }

        session.MaintenanceActions?.Add(a);
      }
    }

    if (includeActionActivities)
    {
      var activities = await ctx.MaintenanceActionActivities
        .AsNoTracking()
        .Where(a => a.MaintenanceSessionId == sessionId)
        .OrderBy(a => a.Id)
        .ToListAsync();

      session.Activities = activities.Select(a => new MaintenanceActionActivityResponse(
        a.Id,
        a.MaintenanceSessionId,
        a.MaintenanceActionId,
        a.Activity,
        a.CurrentOperation,
        a.ActivityId,
        a.ParentId,
        a.PercentComplete,
        a.SecondsRemaining,
        a.SourceId,
        a.Status,
        a.Completed,
        a.DateUtc,
        a.ScriptName))
      .ToList();
    }

    if (includeStages)
    {
      var stageData = sessionObjectsUpdateHandler.GetMaintenanceSessionStageWorkQueue()
       .Where(a => a.MaintenanceSessionId == session.Id)
       .Select(a => new GetMaintenanceSessionStageResponse(a))
       .ToList();

      foreach (var s in stageData)
      {
        var existing = session.Stages?.FirstOrDefault(b => b.Id == s.Id);
        if (existing is not null)
        {
          session.Stages?.Remove(existing);
        }

        session.Stages?.Add(s);
      }

      if (stageData.Count != 0)
      {
        // reset the stage statuses
        session.ResolutionStageStatus = session.Stages?.FirstOrDefault(a => a.Type == SessionStageType.Resolution)?.StageStatus;
        session.OnboardingStageStatus = session.Stages?.FirstOrDefault(a => a.Type == SessionStageType.Onboarding)?.StageStatus;
        session.AgentUpdatesStageStatus = session.Stages?.FirstOrDefault(a => a.Type == SessionStageType.AgentUpdates)?.StageStatus;
        session.DetectionStageStatus = session.Stages?.FirstOrDefault(a => a.Type == SessionStageType.Detection)?.StageStatus;
        session.ExecutionStageStatus = session.Stages?.FirstOrDefault(a => a.Type == SessionStageType.Execution)?.StageStatus;
      }
    }

    return Ok(session);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceSessionApiRoutes.GetSessionLogs)]
  public async Task<ActionResult<IEnumerable<GetMaintenanceSessionLogResponse>>> GetSessionLogs(
    [FromRoute] int sessionId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ISessionLogUpdateQueue sessionLogUpdateQueue)
  {
    await resourceAuthorizerFlow
      .PerformAuthorizationWorkflowAsync<MaintenanceSession, IMaintenanceSessionsViewPermission>(
        new DefaultKeyParameters(sessionId),
        true);

    var q = await ctx.GetAllSessionLogsBySession(sessionId);

    // fetch logs potentially still in work queue that aren't saved to the database
#pragma warning disable S6966
    var workQueueLogs = sessionLogUpdateQueue.GetSessionLogWorkQueue()
      .Where(a => a.MaintenanceSessionId == sessionId)
      .AsQueryable()
      .Select(GetMaintenanceSessionLogResponse.Projection)
      // do not use ToListAsync here
      .ToList();
#pragma warning restore S6966

    var logs = q.Select(GetMaintenanceSessionLogResponse.Projection).ToNonAsyncEnumerable().OrderBy(a => a.Id);
    return Ok(logs.Concat(workQueueLogs));
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceSessionApiRoutes.GetOldSessionLogs)]
  public async Task<ActionResult<IEnumerable<GetMaintenanceSessionLogResponse>>> GetOldSessionLogs(
    [FromRoute] int sessionId,
    [FromServices] ImmybotDbContext ctx)
  {
    await resourceAuthorizerFlow
      .PerformAuthorizationWorkflowAsync<MaintenanceSession, IMaintenanceSessionsViewPermission>(
        new DefaultKeyParameters(sessionId),
        true);

    var q = ctx.GetOldSessionLogsBySession(sessionId);

    var logs = q.Select(GetMaintenanceSessionLogResponse.Projection).ToNonAsyncEnumerable().OrderBy(a => a.Id);
    return Ok(logs);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceSessionApiRoutes.GetSessionPhases)]
  public async Task<ActionResult<IEnumerable<GetMaintenanceSessionPhaseResponse>>> GetSessionPhases(
    [FromRoute] int sessionId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ISessionObjectsUpdateHandler sessionObjectsUpdateHandler)
  {
    await resourceAuthorizerFlow
      .PerformAuthorizationWorkflowAsync<MaintenanceSession, IMaintenanceSessionsViewPermission>(
        new DefaultKeyParameters(sessionId),
        true);

    var q = ctx.GetPhasesForSession(sessionId);

    var phases = q.Select(p => new GetMaintenanceSessionPhaseResponse(p)).ToNonAsyncEnumerable().ToList();

    var phaseData = sessionObjectsUpdateHandler.GetSessionPhaseWorkQueue()
      .Where(a => a.MaintenanceSessionId == sessionId)
      .Select(a => new GetMaintenanceSessionPhaseResponse(a))
      .ToList();

    foreach (var s in phaseData)
    {
      var existing = phases.Find(b => b.Id == s.Id);
      if (existing is not null)
      {
        phases.Remove(existing);
      }

      phases.Add(s);
    }

    return Ok(phases.OrderBy(a => a.Id));
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceSessionApiRoutes.GetLastSessionLog)]
  public async Task<ActionResult<GetMaintenanceSessionLogResponse>> GetLastSessionLog(
    [FromRoute] int sessionId,
    [FromServices] ImmybotDbContext ctx)
  {
    await resourceAuthorizerFlow
      .PerformAuthorizationWorkflowAsync<MaintenanceSession, IMaintenanceSessionsViewPermission>(
        new DefaultKeyParameters(sessionId),
        true);
    var log = ctx.GetLastLogBySession(sessionId);
    if (log is null) return NotFound();
    return Ok(new GetMaintenanceSessionLogResponse(log));
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsManagePermission))]
  [HttpGet(MaintenanceSessionApiRoutes.CancelSession)]
  public async Task<ActionResult> CancelSession(
    [FromRoute] int sessionId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IMaintenanceSessionActions sessionActions)
  {
    await resourceAuthorizerFlow
      .PerformAuthorizationWorkflowAsync<MaintenanceSession, IMaintenanceSessionsManagePermission>(
        new DefaultKeyParameters(sessionId),
        true);
    var session = await ctx.GetMaintenanceSessionByIdAsync(sessionId);
    if (session is null) return NotFound();

    if (!initStatus.IsInitialized) return Conflict(_errorMessageAppIsInitializing);

    // update the session status directly
    var sessions = ctx.GetAllActiveMaintenanceSessions().Where(a => a.Id == sessionId);
    await sessions.UpdateFromQueryAsync(a => new MaintenanceSession { SessionStatus = SessionStatus.Cancelled });

    await sessionActions.TryCancelSession(sessionId);

    return Ok();
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsManagePermission))]
  [HttpPost(MaintenanceSessionApiRoutes.CancelAllSessions)]
  public ActionResult CancelAllSessions(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IMaintenanceSessionActions sessionActions)
  {
    if (!initStatus.IsInitialized) return Conflict(_errorMessageAppIsInitializing);

    Response.OnCompleted(async () =>
    {
      var filter = permissionFilterBuilder
        .BuildFilterExpression<MaintenanceSession, IMaintenanceSessionsManagePermission>();
      var sessions = ctx.GetAllActiveMaintenanceSessions()
        .Where(filter);

      // cancel everything immediately
      await sessions.UpdateFromQueryAsync(a => new MaintenanceSession { SessionStatus = SessionStatus.Cancelled });

      var sessionIds = sessions.Select(a => a.Id).ToList();

      await sessionActions.TryCancelSessions(sessionIds);
    });

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsManagePermission))]
  [HttpPost(MaintenanceSessionApiRoutes.CancelSessions)]
  public async Task<ActionResult> CancelSessions(
    [FromBody] CancelSessionsRequestBody body,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IMaintenanceSessionActions sessionActions)
  {
    if (!initStatus.IsInitialized) return Conflict(_errorMessageAppIsInitializing);

    var filter = permissionFilterBuilder
      .BuildFilterExpression<MaintenanceSession, IMaintenanceSessionsManagePermission>();
    var sessions = ctx.GetMaintenanceSessionsByIds(body.SessionIds, includeStages: true, includeComputer: true)
      .Where(filter);

    var cancelableSessions = sessions.Where(s =>
        s.SessionStatus == SessionStatus.Running ||
        s.SessionStatus == SessionStatus.Pending ||
        s.SessionStatus == SessionStatus.PendingConnectivity ||
        s.SessionStatus == SessionStatus.PendingPreflight ||
        s.SessionStatus == SessionStatus.PendingEphemeralAgentConnection ||
        s.SessionStatus == SessionStatus.Created)
      .Select(a => new { a.Id, a.TenantId });

    await sessionActions.TryCancelSessions(await cancelableSessions.Select(a => a.Id).ToListAsync());

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsRerunPermission))]
  [HttpPost(MaintenanceSessionApiRoutes.RerunSession)]
  public async Task<ActionResult<int?>> RerunSession(
    [FromRoute] int sessionId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IFunctionScriptManager functionScriptManager,
    [FromServices] IRerunMaintenanceSessionCmd cmd)
  {
    if (!initStatus.IsInitialized) return Conflict(_errorMessageAppIsInitializing);

    await resourceAuthorizerFlow
      .PerformAuthorizationWorkflowAsync<MaintenanceSession, IMaintenanceSessionsRerunPermission>(
        new DefaultKeyParameters(sessionId),
        true);

    var session = await ctx.GetMaintenanceSessionByIdAsync(sessionId);
    if (session == null) return NotFound();

    functionScriptManager.InvalidateCache();
    var jobId = cmd.Run(userService.GetCurrentUser(), session);
    var newSession = ctx.GetMaintenanceSessionByJobId(jobId);
    return Ok(newSession?.Id);
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsRerunPermission))]
  [HttpPost(MaintenanceSessionApiRoutes.RerunSessions)]
  public ActionResult RerunSessions(
    [FromBody] RerunSessionsRequestBody body,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IFunctionScriptManager functionScriptManager,
    [FromServices] IRerunMaintenanceSessionCmd cmd)
  {
    if (!initStatus.IsInitialized) return Conflict(_errorMessageAppIsInitializing);

    var filter = permissionFilterBuilder
      .BuildFilterExpression<MaintenanceSession, IMaintenanceSessionsRerunPermission>();
    var sessions = ctx.GetMaintenanceSessionsByIds(body.SessionIds, includeStages: true, includeComputer: true)
      .Where(filter)
      .ToList();
    functionScriptManager.InvalidateCache();
    sessions.ForEach(s =>
    {
      cmd.Run(userService.GetCurrentUser(), s);
    });

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsResumePermission))]
  [HttpPost(MaintenanceSessionApiRoutes.ResumeSession)]
  public async Task<ActionResult> ResumeSession(
    [FromRoute] int sessionId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IResumeMaintenanceSessionCmd cmd)
  {
    await resourceAuthorizerFlow
      .PerformAuthorizationWorkflowAsync<MaintenanceSession, IMaintenanceSessionsResumePermission>(
        new DefaultKeyParameters(sessionId),
        true);
    var session = await ctx.GetMaintenanceSessionByIdAsync(sessionId, includeStages: true);
    if (session == null) return NotFound();

    if (!initStatus.IsInitialized) return Conflict(_errorMessageAppIsInitializing);
    var userPersonId = userService.GetCurrentUser().PersonId ?? 0;
    cmd.Run(session, userPersonId);
    return Ok();
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsManagePermission))]
  [HttpGet(MaintenanceSessionApiRoutes.CancelSessionsForSchedule)]
  public async Task<ActionResult> CancelSessionsForSchedule(
    [FromRoute] int scheduleId,
    [FromServices] IMaintenanceSessionActions sessionActions,
    [FromServices] ImmybotDbContext ctx)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Schedule, ISchedulesManagePermission>(
      new DefaultKeyParameters(scheduleId),
      strict: true);
    var schedule = ctx.GetScheduleById(scheduleId);
    if (schedule == null) return NotFound();
    if (!initStatus.IsInitialized) return Conflict(_errorMessageAppIsInitializing);
    await sessionActions.TryCancelSessionsForSchedule(scheduleId);
    return Ok();
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsViewPermission))]
  [HttpGet(MaintenanceSessionApiRoutes.GetSessionStatusCounts)]
  public async Task<ActionResult<GetSessionStatusCountsPayload>> GetSessionStatusCounts(
    [FromServices] IDashboardActions dashboardActions)
  {
    // todo: fix this to not require manually checking the global permission and then only
    // returning user's tenant data if the user does not have global permission
    if (await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<IMaintenanceSessionsViewPermission>(User,
          strict: false))
    {
      return Ok(dashboardActions.GetSessionData());
    }

    return Ok(dashboardActions.GetSessionData(userService.GetTenantId()));
  }

  [SubjectPermissionAuthorize(typeof(IMaintenanceSessionsRerunPermission))]
  [HttpPost(MaintenanceSessionApiRoutes.RerunAction)]
  public async Task<ActionResult> RerunAction(
    [FromRoute] int sessionId,
    [FromRoute] int actionId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IRerunMaintenanceActionCmd cmd,
    [FromServices] IHostApplicationLifetime lifetime)
  {
    await resourceAuthorizerFlow
      .PerformAuthorizationWorkflowAsync<MaintenanceSession, IMaintenanceSessionsRerunPermission>(
        new DefaultKeyParameters(sessionId),
        true);
    var t = lifetime.ApplicationStopping;
    var session = await ctx.GetMaintenanceSessionByIdAsync(sessionId, includeActions: true);
    t.ThrowIfCancellationRequested();
    if (session == null) return NotFound();

    var action = session.MaintenanceActions.FirstOrDefault(a => a.Id == actionId);
    t.ThrowIfCancellationRequested();
    if (action == null) return NotFound();

    t.ThrowIfCancellationRequested();

    if (!initStatus.IsInitialized) return Conflict(_errorMessageAppIsInitializing);

    // send cmd
    cmd.Run(action, triggeredByUserId: userService.GetCurrentUser().Id);

    return NoContent();
  }
}


