using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers.Attributes;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Shared.Primitives;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class DynamicIntegrationTypesController(
  IUserService userService,
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService) : Controller
{
  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesManagePermission))]
  [HttpPost(DynamicIntegrationTypeRoutes.SetupTestIntegration)]
  public async Task<ActionResult<OpResult<DynamicFormBindResult>>> SetupTestIntegration(
    [FromServices] IProviderRegistrationService providerIntegrationService,
    [FromBody] SetupTestIntegrationRequest request,
    [FromRoute] Guid terminalId)
  {
    // add the test registration
    var res = await providerIntegrationService.AddTestIntegration(request.Script, terminalId, HttpContext.RequestAborted);
    var resWithoutConvertedParameters = res.IsSuccess
      ? OpResult.Ok(res.Value.GetBaseResult())
      : OpResult.Fail<DynamicFormBindResult>(res.Exception, res.Reason);
    return Ok(resWithoutConvertedParameters);
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesManagePermission))]
  [HttpDelete(DynamicIntegrationTypeRoutes.RemoveTestIntegration)]
  public async Task<ActionResult> RemoveTestIntegration(
    [FromServices] IProviderRegistrationService providerIntegrationService,
    [FromRoute] Guid terminalId)
  {
    await providerIntegrationService.RemoveTestRegistration(terminalId, HttpContext.RequestAborted);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesManagePermission))]
  [HttpPost(DynamicIntegrationTypeRoutes.TestIntegrationMethod)]
  public async Task<ActionResult<OpResult<object?>>> TestIntegrationMethod(
    [FromServices] IProviderRegistrationService providerIntegrationService,
    [FromBody] TestIntegrationMethodRequest request,
    [FromRoute] Guid terminalId,
    [FromRoute] string method)
  {
    var providerResult = providerIntegrationService.GetTestProvider(terminalId);
    if (!providerResult.IsSuccess) return Ok(OpResult.Fail(providerResult.Reason));

    var provider = providerResult.Value;

    switch (method)
    {
      case "init":
        await provider.Init(-1, request.ProviderTypeFormData, HttpContext.RequestAborted);
        break;
    }

    return Ok(OpResult.Ok("ok"));
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesManagePermission))]
  [HttpPost(DynamicIntegrationTypeRoutes.TestIntegrationBindConfigurationForm)]
  public async Task<ActionResult<OpResult<DynamicFormBindResult>>> TestIntegrationBindConfigurationForm(
    [FromServices] IDynamicFormService dynamicFormService,
    [FromServices] IProviderRegistrationService providerIntegrationService,
    [FromServices] IScriptCanAccessParentTenantPermission scriptCanAccessParentTenantPermission,
    [FromBody] TestIntegrationBindConfigurationFormRequest request,
    [FromRoute] Guid terminalId)
  {
    var registrationResult = providerIntegrationService.GetTestRegistration(terminalId);
    if (!registrationResult.IsSuccess) return OpResult.Fail<DynamicFormBindResult>(registrationResult.Reason);

    var providerTypeAttr = registrationResult.Value.Metadata.ProviderAttribute as DynamicProviderAttribute;
    if (providerTypeAttr is null) return OpResult.Fail<DynamicFormBindResult>("Provider type is not dynamic");

    var isMsp = userService.IsMspUser();

    var canAccessParentTenant = User.HasClaim(a => a.Value == scriptCanAccessParentTenantPermission.AllowClaim);

    // bind the parameters to the form
    var result = await dynamicFormService.BindParameters(
      canAccessMspResources: isMsp,
      canAccessParentTenant: canAccessParentTenant,
      null,
      providerTypeAttr.ParamBlock,
      DatabaseType.Global,
      HttpContext.RequestAborted,
      specifiedParameters: request.ParameterValues,
      ignoreCache: true);

    var resWithoutConvertedParameters = OpResult.Ok(result.GetBaseResult());
    return Ok(resWithoutConvertedParameters);
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesManagePermission))]
  [HttpPost(DynamicIntegrationTypeRoutes.Reload)]
  public async Task<ActionResult> Reload(
    [FromServices] IProviderRegistrationService providerIntegrationService)
  {
    var isImmense =
      await subjectPermissionAuthorizationService.AuthorizeAsync<IGlobalManagePermission>(User, strict: false);
    await providerIntegrationService.ReloadRegistrations(updateGlobalErrorMessageOnFailure: isImmense, HttpContext.RequestAborted);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesManagePermission))]
  [HttpPost(DynamicIntegrationTypeRoutes.ReloadByGlobalId)]
  public async Task<ActionResult> ReloadByGlobalId(
    [FromServices] IProviderRegistrationService providerIntegrationService,
    [FromServices] IDynamicIntegrationsGlobalStore store,
    [FromRoute] int id)
  {
    var integrationType = store.GetDynamicIntegrationType(id);
    if (integrationType is null) return NotFound();
    await providerIntegrationService.ReloadRegistration(integrationType, HttpContext.RequestAborted);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesManagePermission))]
  [HttpPost(DynamicIntegrationTypeRoutes.ReloadByLocalId)]
  public async Task<ActionResult> ReloadByLocalId(
    [FromServices] IProviderRegistrationService providerIntegrationService,
    [FromServices] IDynamicIntegrationsLocalStore store,
    [FromRoute] int id)
  {
    var integrationType = store.GetDynamicIntegrationType(id);
    if (integrationType is null) return NotFound();
    await providerIntegrationService.ReloadRegistration(integrationType, HttpContext.RequestAborted);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesViewPermission))]
  [HttpGet(DynamicIntegrationTypeRoutes.GetAll)]
  public ActionResult<IEnumerable<DynamicIntegrationTypeResponse>> GetAll(
    [FromServices] IDynamicIntegrationsLocalStore localStore,
    [FromServices] IDynamicIntegrationsGlobalStore globalStore)
  {
    var globalTypes = globalStore
      .GetAllDynamicIntegrationTypes().Using(q => q
        .Select(DynamicIntegrationTypeResponse.Projection)
        .ToList());
    var localTypes = localStore
      .GetAllDynamicIntegrationTypes().Using(q => q
        .Select(DynamicIntegrationTypeResponse.Projection)
        .ToList());
    return Ok(globalTypes.Concat(localTypes));
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesViewPermission))]
  [HttpGet(DynamicIntegrationTypeRoutes.GetGlobal)]
  public ActionResult<DynamicIntegrationTypeResponse> GetGlobal(
    [FromServices] IDynamicIntegrationsGlobalStore store,
    [FromRoute] int id)
  {
    var type = store.GetDynamicIntegrationType(id);
    if (type is null) return NotFound();

    var res = new List<DynamicIntegrationType>
      {
        type
      }.AsQueryable().Select(DynamicIntegrationTypeResponse.Projection)
      .First();
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesViewPermission))]
  [HttpGet(DynamicIntegrationTypeRoutes.GetLocal)]
  public ActionResult<DynamicIntegrationTypeResponse> GetLocal(
    [FromServices] IDynamicIntegrationsLocalStore store,
    [FromRoute] int id)
  {
    var type = store.GetDynamicIntegrationType(id);
    if (type is null) return NotFound();

    var res = new List<DynamicIntegrationType>
      {
        type
      }.AsQueryable().Select(DynamicIntegrationTypeResponse.Projection)
      .First();
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(DynamicIntegrationTypeRoutes.CreateGlobal)]
  public async Task<ActionResult<DynamicIntegrationTypeResponse>> CreateGlobal(
    [FromServices] IDynamicIntegrationsGlobalStore store,
    [FromBody] CreateDynamicIntegrationTypePayload payload)
  {
    var type = await store.CreateDynamicIntegrationType(payload, userService.GetCurrentUser());
    if (type is null) return BadRequest("Failed to create global dynamic integration type");
    return Ok(new DynamicIntegrationTypeResponse(type));
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesManagePermission))]
  [HttpPost(DynamicIntegrationTypeRoutes.CreateLocal)]
  public async Task<ActionResult<DynamicIntegrationTypeResponse>> CreateLocal(
    [FromServices] IDynamicIntegrationsLocalStore store,
    [FromBody] CreateDynamicIntegrationTypePayload payload)
  {
    var type = await store.CreateDynamicIntegrationType(payload, userService.GetCurrentUser());
    if (type is null) return BadRequest("Failed to create local dynamic integration type");
    return Ok(new DynamicIntegrationTypeResponse(type));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(DynamicIntegrationTypeRoutes.UpdateGlobal)]
  public async Task<ActionResult<DynamicIntegrationTypeResponse>> UpdateGlobal(
    [FromServices] IDynamicIntegrationsGlobalStore store,
    [FromRoute] int id,
    [FromBody] UpdateDynamicIntegrationTypePayload payload)
  {
    var type = store.GetDynamicIntegrationType(id);
    if (type is null) return NotFound();

    var res = await store.UpdateDynamicIntegrationType(payload, userService.GetCurrentUser());
    if (res is null) return NotFound();

    return Ok(new DynamicIntegrationTypeResponse(res));
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesManagePermission))]
  [HttpPost(DynamicIntegrationTypeRoutes.UpdateLocal)]
  public async Task<ActionResult<DynamicIntegrationTypeResponse>> UpdateLocal(
    [FromServices] IDynamicIntegrationsLocalStore store,
    [FromRoute] int id,
    [FromBody] UpdateDynamicIntegrationTypePayload payload)
  {
    var type = store.GetDynamicIntegrationType(id);
    if (type is null) return NotFound();

    var res = await store.UpdateDynamicIntegrationType(payload, userService.GetCurrentUser());
    if (res is null) return NotFound();

    return Ok(new DynamicIntegrationTypeResponse(res));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpDelete(DynamicIntegrationTypeRoutes.DeleteGlobal)]
  public async Task<ActionResult> DeleteGlobal(
    [FromServices] IDynamicIntegrationsGlobalStore store,
    [FromRoute] int id)
  {
    var type = store.GetDynamicIntegrationType(id);
    if (type is null) return NotFound();

    await store.DeleteDynamicIntegrationType(type, userService.GetCurrentUser());
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IDynamicIntegrationTypesManagePermission))]
  [HttpDelete(DynamicIntegrationTypeRoutes.DeleteLocal)]
  public async Task<ActionResult> DeleteLocal(
    [FromServices] IDynamicIntegrationsLocalStore store,
    [FromRoute] int id)
  {
    var type = store.GetDynamicIntegrationType(id);
    if (type is null) return NotFound();

    await store.DeleteDynamicIntegrationType(type, userService.GetCurrentUser());
    return NoContent();
  }
}
