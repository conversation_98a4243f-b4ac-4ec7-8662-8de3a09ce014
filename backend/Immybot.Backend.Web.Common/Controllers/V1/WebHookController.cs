using Microsoft.AspNetCore.Mvc;
using Immybot.Backend.Application.Lib;
using Microsoft.Extensions.Logging;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class WebHookController : Controller
{
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(WebHookRoutes.EndpointBase)]
  [HttpPost(WebHookRoutes.EndpointBase)]
  public ActionResult HandleWebHookAsync(
    [FromServices] IWebHookService webHookService,
    [FromServices] IDomainEventEmitter emitter,
    [FromServices] ILogger<WebHookController> logger,
    [FromRoute] Guid id,
    [FromBody] object? body,
    CancellationToken ct)
  {
    var webhook = webHookService.GetWebHook(id);
    if (webhook is null)
    {
      logger.LogWarning("Non-existent webhook {id}.", id);
      return NotFound();
    }

    var queryParameters = HttpContext.Request.Query.ToDictionary(x => x.Key, x => (object?)x.Value);
    emitter.EmitEvent(new WebHookTriggeredEvent(id, QueryParameters: queryParameters, Body: body));
    return NoContent();
  }
}
