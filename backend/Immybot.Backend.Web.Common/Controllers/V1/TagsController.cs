using Immybot.Backend.Application.DbContextExtensions.TagExtensions;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class TagsController(
  IResourceAuthorizerFlow resourceAuthorizerFlow,
  IUserService userService,
  ISubjectPermissionAuthorizationService permissionAuthorizationService) : Controller
{
  [SubjectPermissionAuthorize(typeof(ITagsViewPermission))]
  [HttpGet(TagApiRoutes.GetAll)]
  public IActionResult GetAll(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx,
    [FromQuery] string? name = null,
    [FromQuery] bool orderByUpdatedDate = false,
    [FromQuery] int? pageSize = null)
  {
    var isMsp = userService.IsMspUser();
    // Use permission filter builder for tenant-aware filtering
    var tagFilter = permissionFilterBuilder.BuildFilterExpression<Tag, ITagsViewPermission>();
    var q = ctx.GetAllTags().TagForTelemetry().Where(tagFilter);

    if (!string.IsNullOrEmpty(name))
      q = q.Where(a => EF.Functions.ILike(a.Name, "%" + name + "%"));
    if (orderByUpdatedDate)
      q = q.OrderByDescending(a => a.UpdatedDate);
    else
      q = q.OrderByDescending(a => a.Name);

    if (pageSize is { } take)
      q = q.Take(take);

    return Ok(q.Select(GetTagResponse.Projection(includeAuthorizations: isMsp)));
  }

  [SubjectPermissionAuthorize(typeof(ITagsViewPermission))]
  [HttpGet(TagApiRoutes.Get)]
  public async Task<IActionResult> Get(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int tagId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tag, ITagsViewPermission>(
      new DefaultKeyParameters(tagId),
      true);

    var tag = ctx.GetTagById(tagId);
    if (tag is null) return NotFound();
    var isMsp = userService.IsMspUser();
    return Ok(GetTagResponse.CreateFromEntity(tag, includeAuthorizations: isMsp));
  }

  [SubjectPermissionAuthorize(typeof(ITagsManagePermission))]
  [HttpPost(TagApiRoutes.Create)]
  public async Task<IActionResult> Create(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromBody] CreateTagPayload payload)
  {
    await using var ctx = dbFactory();
    var currentUser = userService.GetCurrentUser();

    var tenantRelationshipAuthorizationResult =
      await permissionAuthorizationService.AuthorizeTenantRelationshipsAsync<ITagsManagePermission>(
      User,
      relationships: payload.TenantTagAuthorizations.OfType<ITenantRelationship>().ToList());

    if (tenantRelationshipAuthorizationResult.RequiresOwnedRelationship)
    {
      payload.TenantTagAuthorizations.Add(
        new CreateTenantTagAuthorizationPayload(currentUser.TenantId, Relationship.Owned));
    }

    var tag = ctx.CreateTag(payload);
    if (tag is null) return BadRequest();
    return Ok(GetTagResponse.CreateFromEntity(tag, includeAuthorizations: currentUser.IsMsp));
  }

  [SubjectPermissionAuthorize(typeof(ITagsManagePermission))]
  [HttpPost(TagApiRoutes.Update)]
  public async Task<IActionResult> Update(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromBody] UpdateTagPayload payload,
    [FromRoute] int tagId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tag, ITagsManagePermission>(
      new DefaultKeyParameters(tagId),
      true);
    await using var ctx = dbFactory();
    var tag = ctx.GetTagById(tagId);
    if (tag is null) return NotFound();

    var currentUser = userService.GetCurrentUser();

    var tenantRelationshipAuthorizationResult =
      await permissionAuthorizationService.AuthorizeTenantRelationshipsAsync<ITagsManagePermission>(
      User,
      relationships: payload.TenantTagAuthorizations.OfType<ITenantRelationship>().ToList());

    if (tenantRelationshipAuthorizationResult.RequiresOwnedRelationship)
    {
      payload.TenantTagAuthorizations.Add(
        new CreateTenantTagAuthorizationPayload(currentUser.TenantId, Relationship.Owned));
    }

    var updated = ctx.UpdateTag(payload with { Id = tagId });
    if (updated is null) return BadRequest();
    var isMsp = currentUser.IsMsp;
    return Ok(GetTagResponse.CreateFromEntity(updated, includeAuthorizations: isMsp));
  }

  [SubjectPermissionAuthorize(typeof(ITagsManagePermission))]
  [HttpDelete(TagApiRoutes.Delete)]
  public async Task<IActionResult> Delete(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromRoute] int tagId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tag, ITagsManagePermission>(
      new DefaultKeyParameters(tagId),
      true);
    await using var ctx = dbFactory();
    var tag = ctx.GetTagById(tagId);
    if (tag is null) return NotFound();
    ctx.DeleteTag(tag.Id);
    return NoContent();
  }
}
