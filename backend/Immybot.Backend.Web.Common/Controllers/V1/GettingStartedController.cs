using Immybot.Backend.Application.Services;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class GettingStartedController : Controller
{
  [SubjectPermissionAuthorize(typeof(IGettingStartedViewPermission))]
  [HttpGet(GettingStartedApiRoutes.Checklist)]
  public async Task<ActionResult<GettingStartedChecklist>> Checklist(
    [FromServices] IGettingStartedService gettingStartedService,
    CancellationToken token)
  {
    return Ok(await gettingStartedService.GetChecklist(token));
  }

  [SubjectPermissionAuthorize(typeof(IGettingStartedViewPermission))]
  [HttpPost(GettingStartedApiRoutes.ResetChecklist)]
  public async Task<IActionResult> ResetChecklist(
    [FromServices] IGettingStartedService gettingStartedService,
    CancellationToken token)
  {
    await gettingStartedService.ResetChecklist(token);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IGettingStartedViewPermission))]
  [HttpPost(GettingStartedApiRoutes.CompleteChecklist)]
  public async Task<IActionResult> CompleteChecklist(
    [FromServices] IGettingStartedService gettingStartedService,
    CancellationToken token)
  {
    await gettingStartedService.CompleteChecklist(token);
    return NoContent();
  }
}
