using Hangfire;
using Immybot.Backend.Application.Jobs;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class SyncsController : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(ITriggerUserAffinitySyncPermission))]
  [HttpPost(SyncsApiRoutes.TriggerUserAffinitySync)]
  public IActionResult TriggerUserAffinitySync(
    [FromServices] IRecurringJobManager manager)
  {
    manager.Trigger(UserAffinityJob.JobId);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ITriggerAzureUserSyncPermission))]
  [HttpPost(SyncsApiRoutes.TriggerAzureUserSync)]
  public IActionResult TriggerAzureUserSync(
    [FromServices] IRecurringJobManager manager)
  {
    manager.Trigger(SyncAzureTenantDataJob.JobId);
    return NoContent();
  }
}
