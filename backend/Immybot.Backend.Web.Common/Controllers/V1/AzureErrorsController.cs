using DevExtreme.AspNet.Data;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class AzureErrorsController : Controller
{
  [SubjectPermissionAuthorize(typeof(IAzureOperationsViewPermission))]
  [HttpGet(AzureErrorsApiRoutes.GetForTenantDx)]
  public ActionResult<ICollection<AzureErrorLogItem>> GetForTenantDx(
    [FromServices] IAzureErrorStore azureErrorStore,
    [FromRoute] string tenantPrincipalId,
    DataSourceLoadOptions loadOptions)
  {
    var azureErrorsDisposable = azureErrorStore.GetForTenant(tenantPrincipalId);
    loadOptions.DefaultSort = nameof(AzureErrorLogItem.CreatedDateUtc);
    Response.RegisterForDispose(azureErrorsDisposable);
    return Ok(DataSourceLoader.Load(azureErrorsDisposable.Value, loadOptions));
  }

  [SubjectPermissionAuthorize(typeof(IAzureOperationsViewPermission))]
  [HttpGet(AzureErrorsApiRoutes.GetAllDx)]
  public ActionResult<ICollection<AzureErrorLogItem>> GetAllDx(
    [FromServices] IAzureErrorStore azureErrorStore,
    DataSourceLoadOptions loadOptions)
  {
    var azureErrorsDisposable = azureErrorStore.GetAll();
    Response.RegisterForDispose(azureErrorsDisposable);
    return Ok(DataSourceLoader.Load(azureErrorsDisposable.Value, loadOptions));
  }
}
