using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.LicenseExtensions;
using Immybot.Backend.Application.DbContextExtensions.PersonExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Events.RunImmyServiceEvents;
using Immybot.Backend.Application.Interface.Jobs;
using Immybot.Backend.Application.Jobs;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Domain.Infrastructure;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class ImmyBotController(
  IPermissionFilterBuilder permissionFilterBuilder,
  IResourceAuthorizerFlow resourceAuthorizerFlow,
  IImmyServiceJob immyServiceJob,
  ISoftwareActions softwareActions,
  IUserService userService,
  IInitializationStatus initializationStatus)
  : ControllerBase
{
  private const string _errorMessageAppIsInitializing = "Application is still initializing. Maintenance sessions cannot be triggered until initialization is completed.";

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(ImmyBotApiRoutes.RunImmyService)]
  public async Task<IActionResult> RunImmyService(
    [FromBody] RunImmyServiceRequestBody body,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IMaintenanceTaskActions maintenanceTaskActions,
    [FromServices] ILogger<ImmyBotController> logger,
    [FromServices] IFeatureManager featureManager,
    [FromServices] IHostApplicationLifetime lifetime,
    [FromServices] IFunctionScriptManager functionScriptManager,
    [FromServices] ISessionObjectsUpdateHandler sessionObjectsUpdateHandler)
  {
    var cacheGroupId = Guid.NewGuid();

    logger.LogDebug("Attempting to run ImmyBot session");
    if (!featureManager.IsEnabled(FeatureEnum.SchedulesFeature) && (!string.IsNullOrEmpty(body.UpdateTime) || body.UseComputersTimezoneForExecution || body.ScheduleExecutionAfterActiveHours))
    {
      throw new FeatureNotEnabledException("This instance is not allowed to schedule maintenance to occur at a later date")
      {
        SubscriptionFeatureId = null,
      };
    }

    if (!featureManager.IsEnabled(FeatureEnum.PersonTasksFeature) && body.Persons.Count > 0)
    {
      throw new FeatureNotEnabledException("The feature to allow maintenance for persons is current disabled.")
      {
        SubscriptionFeatureId = null,
      };
    }

    if (body.MaintenanceParams != null)
    {
      if (body.MaintenanceParams.MaintenanceType == MaintenanceType.LocalSoftware)
      {
        if (body.MaintenanceParams is { SoftwareType: null } or { MaintenanceIdentifier: null })
          return BadRequest("Software type and maintenance identifier are required for local software deployment");

        var software = await softwareActions.GetSoftware(
          body.MaintenanceParams.SoftwareType.Value,
          body.MaintenanceParams.MaintenanceIdentifier,
          lifetime.ApplicationStopping);
        if (software is LocalSoftware localSoftware)
          await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
            new DefaultKeyParameters(localSoftware.Id),
            true);
      }
      else if (body.MaintenanceParams.MaintenanceType == MaintenanceType.LocalMaintenanceTask)
      {
        var task = await maintenanceTaskActions.GetMaintenanceTask(
          MaintenanceType.LocalMaintenanceTask,
          Convert.ToInt32(body.MaintenanceParams.MaintenanceIdentifier),
          null,
          null,
          lifetime.ApplicationStopping);
        if (task is null) return NotFound("Maintenance task not found");
        await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<MaintenanceTask, IMaintenanceTasksViewPermission>(
          new DefaultKeyParameters(task.Id),
          true);
      }
    }
    if (body.Computers.Count != 0)
    {
      var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
      var computers = await ctx
        .GetComputersByIds(body.Computers.Select(comp => comp.ComputerId).ToArray())
        .Where(filter)
        .ToListAsync();
      if (computers.Count != body.Computers.Count) return NotFound("One or more computers were not found");

      // if any computer is currently updating a session, then wait 4 seconds to see if it finishes
      if (sessionObjectsUpdateHandler.GetSessionWorkQueue().Any(a =>
            a.ComputerId.HasValue && computers.Exists(comp => comp.Id == a.ComputerId.Value)))
      {
        await Task.Delay(4000);
      }
    }
    else
    {
      var filter = permissionFilterBuilder.BuildFilterExpression<Tenant, ITenantsViewPermission>();
      var tenants = await ctx
        .GetTenantsByIds(body.Tenants.Select(tenant => tenant.TenantId).ToArray())
        .Where(filter)
        .ToListAsync();
      if (tenants.Count != body.Tenants.Count) return NotFound("One or more tenants were not found");
    }

    if (body.LicenseId.HasValue)
    {
      var license = ctx.GetLicense(body.LicenseId.Value);
      if (license is null) return NotFound("License not found");
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<License, ILicensesViewPermission>(
        new DefaultKeyParameters(license.Id),
        true);
    }

    if (!initializationStatus.IsInitialized) return Conflict(_errorMessageAppIsInitializing);

    // setup email configuration
    var maintenanceEmailConfiguration = new MaintenanceEmailConfiguration
    {
      SendDetectionEmail = body.SendDetectionEmail,
      SendDetectionEmailWhenAllActionsAreCompliant = body.SendDetectionEmailWhenAllActionsAreCompliant,
      SendFollowUpOnlyIfActionNeeded = body.SendFollowUpOnlyIfActionNeeded,
      SendFollowUpEmail = body.SendFollowUpEmail,
      ShowPostponeButton = body.ShowPostponeButton,
      ShowRunNowButton = body.ShowRunNowButton,
      ShowMaintenanceActions = body.ShowMaintenanceActions,
    };

    MaintenanceItem? maintenanceItem = null;

    if (
      body.MaintenanceParams?.MaintenanceIdentifier is { } maintId
      && !string.IsNullOrEmpty(maintId)
      && body.MaintenanceParams.MaintenanceType is { } maintType)
    {
      var maintParams = body.MaintenanceParams;
      var maintenanceTaskAssignmentDetails = maintParams?.MaintenanceTaskMode != null || maintParams?.TaskParameterValues?.Any() == true
        ? new MaintenanceTaskAssignmentDetails
        {
          MaintenanceTaskMode = maintParams.MaintenanceTaskMode,
          TaskParameterValues = maintParams.TaskParameterValues,
        }
        : null;

      if (MaintenanceSpecifier.IsSoftwareSpecifier(maintType))
      {
        if (maintParams?.DesiredSoftwareState.HasValue is false && !body.UseWinningDeployment)
          return BadRequest("Desired software state is required when deploying software");
        maintenanceItem = new SoftwareMaintenanceItem(maintId, maintType,
          softwareAssignmentDetails: body.UseWinningDeployment ? null : new SoftwareAssignmentDetails
          {
            LicenseId = body.LicenseId,
            DesiredSoftwareState = maintParams?.DesiredSoftwareState ?? DesiredSoftwareState.NoAction,
            SoftwareProviderType = maintParams?.SoftwareProviderType,
            SoftwareSemanticVersion = maintParams?.SemanticVersion
          },
          taskConfigurationDetails: maintenanceTaskAssignmentDetails);
      }
      else if (MaintenanceSpecifier.IsMaintenanceTaskSpecifier(maintType))
      {
        if (maintenanceTaskAssignmentDetails == null && !body.UseWinningDeployment)
          return BadRequest("Maintenance task mode is required when running a maintenance task");

        maintenanceItem = new TaskMaintenanceItem(maintId, maintType,
          details: body.UseWinningDeployment ? null : maintenanceTaskAssignmentDetails);
      }
    }

    if (body.Computers.Count is { } c and > 0)
    {
      var newComputerSessions = new Dictionary<int, int>(c);
      var alreadyRunningComputerSessions = new Dictionary<int, int>(c);

      using (logger.BeginScope(body))
      {
        logger.LogInformation("Starting user-trigger maintenance sessions");
      }

      MaintenanceAgentUpdatesConfiguration? maintenanceAgentUpdatesConfiguration = null;
      if (body.ProviderLinkIdForAgentUpdates is { } providerLinkIdForAgentUpdates)
      {
        maintenanceAgentUpdatesConfiguration = new MaintenanceAgentUpdatesConfiguration
        {
          ProviderLinkId = providerLinkIdForAgentUpdates,
        };
      }

      functionScriptManager.InvalidateCache(); // ensure we've loaded the latest function scripts

      foreach (var computer in body.Computers)
      {
        using var _ = logger.BeginScope(new { computer.ComputerId });

        // setup scheduling configuration
        var maintenanceSchedulingConfiguration = new MaintenanceSchedulingConfiguration
        {
          MaintenanceTime = body.UpdateTime,
          TimeZoneInfoId = body.TimeZoneInfoId,
          UseComputersTimezoneForExecution = body.UseComputersTimezoneForExecution,
          ScheduleExecutionAfterActiveHours = body.ScheduleExecutionAfterActiveHours,
        };

        try
        {
          var immybotParams = new SessionJobArgs()
          {
            UseWinningDeployment = body.UseWinningDeployment || body.ResolutionOnly,
            Repair = body.MaintenanceParams?.Repair ?? false,
            CacheOnly = body.CacheOnly,
            ComputerId = computer.ComputerId,
            InstallWindowsUpdates = body.FullMaintenance,
            RebootPreference = body.RebootPreference,
            PromptTimeoutAction = body.PromptTimeoutAction,
            AutoConsentToReboots = body.AutoConsentToReboots,
            PromptTimeoutMinutes = body.PromptTimeoutMinutes,
            PropagateToChildTenants = body.PropagateToChildTenants,
            AllowAccessToParentTenant = body.AllowAccessToParentTenant,
            ManuallyTriggeredByUserId = userService.GetCurrentUser().Id,
            DeploymentId = body.DeploymentId,
            DeploymentType = body.DeploymentType,
            CacheGroupId = cacheGroupId,
            DetectionOnly = body.DetectionOnly,
            ResolutionOnly = body.ResolutionOnly,
            RunInventoryInDetection = body.RunInventoryInDetection,
            MaintenanceItem = maintenanceItem,
            MaintenanceAgentUpdatesConfiguration = maintenanceAgentUpdatesConfiguration,
            MaintenanceEmailConfiguration = maintenanceEmailConfiguration,
            MaintenanceSchedulingConfiguration = maintenanceSchedulingConfiguration,
            MaintenanceOnboardingConfiguration = new MaintenanceOnboardingConfiguration
            {
              OnboardingOnlyParameterValueOverrides = computer.MaintenanceTaskParameterValueOverrides,
            },
            OfflineBehavior = body.OfflineBehavior,
            SuppressRebootsDuringBusinessHours = body.SuppressRebootsDuringBusinessHours
          };

          var session = body.SkipBackgroundJob
            ? immyServiceJob.RunNow(new EnqueueNewSessionPayload(immybotParams))
            : immyServiceJob.EnqueueAdhoc(new EnqueueNewSessionPayload(immybotParams));
          newComputerSessions.Add(computer.ComputerId, session.Id);
          logger.LogDebug("Successfully enqueued maintenance job");
        }
        catch (SessionAlreadyRunningException ex)
        {
          if (ex.ComputerId is { } computerId)
            alreadyRunningComputerSessions.Add(computerId, ex.SessionId);
        }
        catch (EnqueueSessionFailedException ex)
        {
          logger.LogError(ex, "Attempt to start session failed");
        }
      }

      var sessionsStarted = newComputerSessions
        .Select(kvp => new RunImmyServiceResponseSession
        {
          ComputerId = kvp.Key,
          SessionId = kvp.Value,
        })
        .ToList();
      var sessionsAlreadyInProgress = alreadyRunningComputerSessions
        .Select(kvp => new RunImmyServiceResponseSession
        {
          ComputerId = kvp.Key,
          SessionId = kvp.Value,
        })
        .ToList();
      return Ok(new RunImmyServiceResponseBody(sessionsStarted, sessionsAlreadyInProgress));
    }
    else if (body.Tenants.Count is { } t and > 0)
    {
      // run for tenant
      var newTenantSessions = new Dictionary<int, int>(t);
      var alreadyRunningTenantSessions = new Dictionary<int, int>(t);

      using (logger.BeginScope(body))
      {
        logger.LogInformation("Starting user-trigger maintenance sessions");
      }

      functionScriptManager.InvalidateCache(); // ensure we've loaded the latest function scripts

      foreach (var tenant in body.Tenants)
      {
        var maintenanceSchedulingConfiguration = new MaintenanceSchedulingConfiguration
        {
          MaintenanceTime = body.UpdateTime,
          TimeZoneInfoId = body.TimeZoneInfoId,
          UseComputersTimezoneForExecution = body.UseComputersTimezoneForExecution,
          ScheduleExecutionAfterActiveHours = body.ScheduleExecutionAfterActiveHours,
        };
        using var _ = logger.BeginScope(new { tenant.TenantId });
        try
        {
          var immybotParams = new SessionJobArgs()
          {
            CacheOnly = body.CacheOnly,
            TenantId = tenant.TenantId,
            InstallWindowsUpdates = body.FullMaintenance,
            RebootPreference = body.RebootPreference,
            PromptTimeoutAction = body.PromptTimeoutAction,
            AutoConsentToReboots = body.AutoConsentToReboots,
            PromptTimeoutMinutes = body.PromptTimeoutMinutes,
            PropagateToChildTenants = body.PropagateToChildTenants,
            AllowAccessToParentTenant = body.AllowAccessToParentTenant,
            ManuallyTriggeredByUserId = userService.GetCurrentUser().Id,
            DeploymentId = body.DeploymentId,
            DeploymentType = body.DeploymentType,
            CacheGroupId = cacheGroupId,
            MaintenanceItem = maintenanceItem,
            MaintenanceEmailConfiguration = maintenanceEmailConfiguration,
            MaintenanceSchedulingConfiguration = maintenanceSchedulingConfiguration,
            MaintenanceOnboardingConfiguration = new MaintenanceOnboardingConfiguration
            {
              // todo: stop abusing onboarding configuration for tenant parameter overrides
              OnboardingOnlyParameterValueOverrides = tenant.MaintenanceTaskParameterValueOverrides,
            },
            DetectionOnly = body.DetectionOnly,
          };

          var session = immyServiceJob.EnqueueAdhoc(new EnqueueNewSessionPayload(immybotParams));
          newTenantSessions.Add(tenant.TenantId, session.Id);
          logger.LogDebug("Successfully enqueued maintenance job");
        }
        catch (SessionAlreadyRunningException ex)
        {
          if (ex.TenantId is { } tenantId)
            alreadyRunningTenantSessions.Add(tenantId, ex.SessionId);
        }
        catch (EnqueueSessionFailedException ex)
        {
          logger.LogError(ex, "Attempt to start session failed");
        }
      }
      var sessionsStarted = newTenantSessions
        .Select(kvp => new RunImmyServiceResponseSession
        {
          TenantId = kvp.Key,
          SessionId = kvp.Value,
        })
        .ToList();
      var sessionsAlreadyInProgress = alreadyRunningTenantSessions
        .Select(kvp => new RunImmyServiceResponseSession
        {
          TenantId = kvp.Key,
          SessionId = kvp.Value,
        })
        .ToList();
      return Ok(new RunImmyServiceResponseBody(sessionsStarted, sessionsAlreadyInProgress));
    }
    else if (body.Persons.Count is { } p and > 0)
    {
      // copied from tenant section above
      //will be modified for Persons
      var newPersonSessions = new Dictionary<int, int>(p);
      var alreadyRunningPersonSessions = new Dictionary<int, int>(p);

      using (logger.BeginScope(body))
      {
        logger.LogInformation("Starting user-trigger maintenance sessions");
      }

      functionScriptManager.InvalidateCache(); // ensure we've loaded the latest function scripts

      foreach (var person in body.Persons)
      {
        var maintenanceSchedulingConfiguration = new MaintenanceSchedulingConfiguration
        {
          MaintenanceTime = body.UpdateTime,
          TimeZoneInfoId = body.TimeZoneInfoId,
          UseComputersTimezoneForExecution = body.UseComputersTimezoneForExecution,
          ScheduleExecutionAfterActiveHours = body.ScheduleExecutionAfterActiveHours,
        };
        using var _ = logger.BeginScope(new { person.PersonId });
        try
        {
          var immybotParams = new SessionJobArgs()
          {
            CacheOnly = body.CacheOnly,
            PersonId = person.PersonId,
            InstallWindowsUpdates = body.FullMaintenance,
            RebootPreference = body.RebootPreference,
            ManuallyTriggeredByUserId = userService.GetCurrentUser().Id,
            DeploymentId = body.DeploymentId,
            DeploymentType = body.DeploymentType,
            CacheGroupId = cacheGroupId,
            MaintenanceItem = maintenanceItem,
            MaintenanceEmailConfiguration = maintenanceEmailConfiguration,
            MaintenanceSchedulingConfiguration = maintenanceSchedulingConfiguration,
            MaintenanceOnboardingConfiguration = new MaintenanceOnboardingConfiguration
            {
              // todo: stop abusing onboarding configuration for person parameter overrides
              OnboardingOnlyParameterValueOverrides = person.MaintenanceTaskParameterValueOverrides,
            },
            DetectionOnly = body.DetectionOnly,
          };

          var session = immyServiceJob.EnqueueAdhoc(new EnqueueNewSessionPayload(immybotParams));
          newPersonSessions.Add(person.PersonId, session.Id);
          logger.LogDebug("Successfully enqueued maintenance job");
        }
        catch (SessionAlreadyRunningException ex)
        {
          if (ex.PersonId is { } personId)
            alreadyRunningPersonSessions.Add(personId, ex.SessionId);
        }
        catch (EnqueueSessionFailedException ex)
        {
          logger.LogError(ex, "Attempt to start session failed");
        }
      }
      var sessionsStarted = newPersonSessions
        .Select(kvp => new RunImmyServiceResponseSession
        {
          PersonId = kvp.Key,
          SessionId = kvp.Value,
        })
        .ToList();
      var sessionsAlreadyInProgress = alreadyRunningPersonSessions
        .Select(kvp => new RunImmyServiceResponseSession
        {
          PersonId = kvp.Key,
          SessionId = kvp.Value,
        })
        .ToList();
      return Ok(new RunImmyServiceResponseBody(sessionsStarted, sessionsAlreadyInProgress));
    }
    else
    {
      return BadRequest("Must send either computers, tenants, or persons to run immy service on");
    }
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(ImmyBotApiRoutes.RunImmyServiceNew)]
  public async Task<IActionResult> RunImmyServiceNew(
    [FromBody] RunImmyServiceRequestBody body,
    [FromServices] IServiceScopeFactory serviceScopeFactory,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IMaintenanceTaskActions maintenanceTaskActions,
    [FromServices] IHostApplicationLifetime lifetime,
    [FromServices] IDomainEventEmitter domainEventEmitter,
    [FromServices] IFeatureManager featureManager)
  {
    if (!initializationStatus.IsInitialized) return Conflict(_errorMessageAppIsInitializing);

    if (!featureManager.IsEnabled(FeatureEnum.SchedulesFeature) && (!string.IsNullOrEmpty(body.UpdateTime) || body.UseComputersTimezoneForExecution || body.ScheduleExecutionAfterActiveHours))
    {
      throw new FeatureNotEnabledException("This instance is not allowed to schedule maintenance to occur at a later date")
      {
        SubscriptionFeatureId = null,
      };
    }

    using var scope = serviceScopeFactory.CreateScope();

    var cacheGroupId = Guid.NewGuid();

    if (body.MaintenanceParams != null)
    {
      if (body.MaintenanceParams.MaintenanceType == MaintenanceType.LocalSoftware)
      {
        if (body.MaintenanceParams is { SoftwareType: null } or { MaintenanceIdentifier: null })
          return BadRequest("Software type and maintenance identifier are required for local software deployment");

        var software = await softwareActions.GetSoftware(
          body.MaintenanceParams.SoftwareType.Value,
          body.MaintenanceParams.MaintenanceIdentifier,
          lifetime.ApplicationStopping);
        if (software is LocalSoftware localSoftware)
          await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
            new DefaultKeyParameters(localSoftware.Id),
            true);
      }
      else if (body.MaintenanceParams.MaintenanceType == MaintenanceType.LocalMaintenanceTask)
      {
        var task = await maintenanceTaskActions.GetMaintenanceTask(
          MaintenanceType.LocalMaintenanceTask,
          Convert.ToInt32(body.MaintenanceParams.MaintenanceIdentifier),
          null,
          null,
          lifetime.ApplicationStopping);
        if (task is null) return NotFound("Maintenance task not found");
        await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<MaintenanceTask, IMaintenanceTasksViewPermission>(
          new DefaultKeyParameters(task.Id),
          true);
      }
    }
    if (body.Computers.Count != 0)
    {
      var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
      var computers = await ctx
        .GetComputersByIds(body.Computers.Select(c => c.ComputerId).ToArray())
        .Where(filter)
        .ToListAsync();
      if (computers.Count != body.Computers.Count) return NotFound("One or more computers were not found");
    }
    else if (body.Persons.Count != 0)
    {
      var filter = permissionFilterBuilder.BuildFilterExpression<Person, IPersonsViewPermission>();
      var persons = await ctx
        .GetPersonsByIds(body.Persons.Select(c => c.PersonId).ToArray())
        .Where(filter)
        .ToListAsync();
      if (persons.Count != body.Persons.Count) return NotFound("One or more people were not found");
    }
    else
    {
      var filter = permissionFilterBuilder.BuildFilterExpression<Tenant, ITenantsViewPermission>();
      var tenants = await ctx
        .GetTenantsByIds(body.Tenants.Select(c => c.TenantId).ToArray())
        .Where(filter)
        .ToListAsync();
      if (tenants.Count != body.Tenants.Count) return NotFound("One or more tenants were not found");
    }

    if (body.LicenseId.HasValue)
    {
      var license = ctx.GetLicense(body.LicenseId.Value);
      if (license is null) return NotFound("License not found");
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<License, ILicensesViewPermission>(
        new DefaultKeyParameters(license.Id),
        true);
    }

    Task.Run(async () =>
    {
      // fetch all necessary transient services
      using var s = serviceScopeFactory.CreateScope();
      var logger = s.ServiceProvider.GetRequiredService<ILogger<ImmyBotController>>();
      try
      {
        // setup email configuration
        var maintenanceEmailConfiguration = new MaintenanceEmailConfiguration
        {
          SendDetectionEmail = body.SendDetectionEmail,
          SendDetectionEmailWhenAllActionsAreCompliant = body.SendDetectionEmailWhenAllActionsAreCompliant,
          SendFollowUpOnlyIfActionNeeded = body.SendFollowUpOnlyIfActionNeeded,
          SendFollowUpEmail = body.SendFollowUpEmail,
          ShowPostponeButton = body.ShowPostponeButton,
          ShowRunNowButton = body.ShowRunNowButton,
          ShowMaintenanceActions = body.ShowMaintenanceActions,
        };

        MaintenanceItem? maintenanceItem = null;

        if (
          body.MaintenanceParams?.MaintenanceIdentifier is { } maintId
          && !string.IsNullOrEmpty(maintId)
          && body.MaintenanceParams.MaintenanceType is { } maintType)
        {
          var maintParams = body.MaintenanceParams;
          var maintenanceTaskAssignmentDetails = maintParams?.MaintenanceTaskMode != null || maintParams?.TaskParameterValues?.Any() == true
            ? new MaintenanceTaskAssignmentDetails
            {
              MaintenanceTaskMode = maintParams.MaintenanceTaskMode,
              TaskParameterValues = maintParams.TaskParameterValues,
            }
            : null;

          if (MaintenanceSpecifier.IsSoftwareSpecifier(maintType))
          {
            if (maintParams?.DesiredSoftwareState.HasValue is false && !body.UseWinningDeployment)
            {
              if (body.SessionGroupId is not null)
              {
                domainEventEmitter.EmitEvent(
                  new SessionGroupEvent(
                    body.SessionGroupId.Value,
                    SessionGroupEventType.SessionFailedToBeCreated,
                    new SessionGroupData(
                      null,
                      null,
                      null,
                      null,
                      false,
                      ErrorMessage: "Desired software state is required when deploying software")));
              }
              return;
            }

            maintenanceItem = new SoftwareMaintenanceItem(maintId, maintType,
              providerLinkIdForMaintenanceItem: maintParams?.ProviderLinkIdForMaintenanceItem,
              softwareAssignmentDetails: body.UseWinningDeployment ? null : new SoftwareAssignmentDetails
              {
                LicenseId = body.LicenseId,
                DesiredSoftwareState = maintParams?.DesiredSoftwareState ?? DesiredSoftwareState.NoAction,
                SoftwareProviderType = maintParams?.SoftwareProviderType,
                SoftwareSemanticVersion = maintParams?.SemanticVersion
              },
              taskConfigurationDetails: maintenanceTaskAssignmentDetails);
          }
          else if (MaintenanceSpecifier.IsMaintenanceTaskSpecifier(maintType))
          {
            if (maintenanceTaskAssignmentDetails == null && !body.UseWinningDeployment)
            {
              if (body.SessionGroupId is not null)
              {
                domainEventEmitter.EmitEvent(
                  new SessionGroupEvent(
                    body.SessionGroupId.Value,
                    SessionGroupEventType.SessionFailedToBeCreated,
                    new SessionGroupData(
                      null,
                      null,
                      null,
                      null,
                      false,
                      ErrorMessage: "Maintenance task mode is required when running a maintenance task")));
              }
              return;
            }

            maintenanceItem = new TaskMaintenanceItem(maintId, maintType,
              details: body.UseWinningDeployment ? null : maintenanceTaskAssignmentDetails);
          }
        }

        if (body.Computers.Count is > 0)
        {
          using (logger.BeginScope(body))
          {
            logger.LogInformation("Starting user-trigger maintenance sessions");
          }

          // todo: refactor the api request.
          // just take the onboarding configuration from the first computer
          var onboardingConfiguration = new MaintenanceOnboardingConfiguration
          {
            OnboardingOnlyParameterValueOverrides = body.Computers[0].MaintenanceTaskParameterValueOverrides,
          };

          // setup scheduling configuration
          var maintenanceSchedulingConfiguration = new MaintenanceSchedulingConfiguration
          {
            MaintenanceTime = body.UpdateTime,
            TimeZoneInfoId = body.TimeZoneInfoId,
            UseComputersTimezoneForExecution = body.UseComputersTimezoneForExecution,
            ScheduleExecutionAfterActiveHours = body.ScheduleExecutionAfterActiveHours,
          };

          MaintenanceAgentUpdatesConfiguration? maintenanceAgentUpdatesConfiguration = null;
          if (body.ProviderLinkIdForAgentUpdates is { } providerLinkIdForAgentUpdates)
          {
            maintenanceAgentUpdatesConfiguration = new MaintenanceAgentUpdatesConfiguration
            {
              ProviderLinkId = providerLinkIdForAgentUpdates,
            };
          }

          var immybotParams = new SessionJobArgs()
          {
            SessionGroupId = body.SessionGroupId,
            UseWinningDeployment = body.UseWinningDeployment,
            Repair = body.MaintenanceParams?.Repair ?? false,
            CacheOnly = body.CacheOnly,
            InstallWindowsUpdates = body.FullMaintenance,
            RebootPreference = body.RebootPreference,
            PromptTimeoutAction = body.PromptTimeoutAction,
            AutoConsentToReboots = body.AutoConsentToReboots,
            PromptTimeoutMinutes = body.PromptTimeoutMinutes,
            PropagateToChildTenants = body.PropagateToChildTenants,
            AllowAccessToParentTenant = body.AllowAccessToParentTenant,
            ManuallyTriggeredByUserId = userService.GetCurrentUser().Id,
            DeploymentId = body.DeploymentId,
            DeploymentType = body.DeploymentType,
            CacheGroupId = cacheGroupId,
            DetectionOnly = body.DetectionOnly,
            ResolutionOnly = body.ResolutionOnly,
            InventoryOnly = body.InventoryOnly,
            RunInventoryInDetection = body.RunInventoryInDetection,
            MaintenanceItem = maintenanceItem,
            MaintenanceAgentUpdatesConfiguration = maintenanceAgentUpdatesConfiguration,
            MaintenanceEmailConfiguration = maintenanceEmailConfiguration,
            MaintenanceSchedulingConfiguration = maintenanceSchedulingConfiguration,
            MaintenanceOnboardingConfiguration = onboardingConfiguration,
            OfflineBehavior = body.OfflineBehavior,
            SuppressRebootsDuringBusinessHours = body.SuppressRebootsDuringBusinessHours
          };

          await immyServiceJob.EnqueueAdhocComputerSessions(
            body.Computers.Select(a => a.ComputerId).ToList(), new EnqueueNewSessionPayload(immybotParams));
        }
        else if (body.Persons.Count > 0)
        {
          using (logger.BeginScope(body))
          {
            logger.LogInformation("Starting user-trigger maintenance sessions");
          }

          // todo: refactor the api request.
          // just take the onboarding configuration from the first computer
          var onboardingConfiguration = new MaintenanceOnboardingConfiguration
          {
            OnboardingOnlyParameterValueOverrides = body.Persons[0].MaintenanceTaskParameterValueOverrides,
          };

          var maintenanceSchedulingConfiguration = new MaintenanceSchedulingConfiguration
          {
            MaintenanceTime = body.UpdateTime,
            TimeZoneInfoId = body.TimeZoneInfoId,
            UseComputersTimezoneForExecution = body.UseComputersTimezoneForExecution,
            ScheduleExecutionAfterActiveHours = body.ScheduleExecutionAfterActiveHours,
          };

          var immybotParams = new SessionJobArgs()
          {
            SessionGroupId = body.SessionGroupId,
            CacheOnly = body.CacheOnly,
            InstallWindowsUpdates = body.FullMaintenance,
            RebootPreference = body.RebootPreference,
            PromptTimeoutAction = body.PromptTimeoutAction,
            AutoConsentToReboots = body.AutoConsentToReboots,
            PromptTimeoutMinutes = body.PromptTimeoutMinutes,
            PropagateToChildTenants = body.PropagateToChildTenants,
            AllowAccessToParentTenant = body.AllowAccessToParentTenant,
            ManuallyTriggeredByUserId = userService.GetCurrentUser().Id,
            DeploymentId = body.DeploymentId,
            DeploymentType = body.DeploymentType,
            CacheGroupId = cacheGroupId,
            MaintenanceItem = maintenanceItem,
            MaintenanceEmailConfiguration = maintenanceEmailConfiguration,
            MaintenanceSchedulingConfiguration = maintenanceSchedulingConfiguration,
            MaintenanceOnboardingConfiguration = onboardingConfiguration,
            DetectionOnly = body.DetectionOnly,
          };

          await immyServiceJob.EnqueueAdhocPersonSessions(body.Persons.Select(a => a.PersonId).ToList(),
            new EnqueueNewSessionPayload(immybotParams));
          logger.LogDebug("Successfully enqueued person maintenance job");
        }
        else if (body.Tenants?.Count is > 0)
        {
          using (logger.BeginScope(body))
          {
            logger.LogInformation("Starting user-trigger maintenance sessions");
          }

          // todo: refactor the api request.
          // just take the onboarding configuration from the first computer
          var onboardingConfiguration = new MaintenanceOnboardingConfiguration
          {
            OnboardingOnlyParameterValueOverrides = body.Tenants[0].MaintenanceTaskParameterValueOverrides,
          };

          var maintenanceSchedulingConfiguration = new MaintenanceSchedulingConfiguration
          {
            MaintenanceTime = body.UpdateTime,
            TimeZoneInfoId = body.TimeZoneInfoId,
            UseComputersTimezoneForExecution = body.UseComputersTimezoneForExecution,
            ScheduleExecutionAfterActiveHours = body.ScheduleExecutionAfterActiveHours,
          };
          var immybotParams = new SessionJobArgs()
          {
            SessionGroupId = body.SessionGroupId,
            CacheOnly = body.CacheOnly,
            InstallWindowsUpdates = body.FullMaintenance,
            RebootPreference = body.RebootPreference,
            PromptTimeoutAction = body.PromptTimeoutAction,
            AutoConsentToReboots = body.AutoConsentToReboots,
            PromptTimeoutMinutes = body.PromptTimeoutMinutes,
            PropagateToChildTenants = body.PropagateToChildTenants,
            AllowAccessToParentTenant = body.AllowAccessToParentTenant,
            ManuallyTriggeredByUserId = userService.GetCurrentUser().Id,
            DeploymentId = body.DeploymentId,
            DeploymentType = body.DeploymentType,
            CacheGroupId = cacheGroupId,
            MaintenanceItem = maintenanceItem,
            MaintenanceEmailConfiguration = maintenanceEmailConfiguration,
            MaintenanceSchedulingConfiguration = maintenanceSchedulingConfiguration,
            MaintenanceOnboardingConfiguration = onboardingConfiguration,
            DetectionOnly = body.DetectionOnly,
          };

          await immyServiceJob.EnqueueAdhocTenantSessions(body.Tenants.Select(a => a.TenantId).ToList(),
            new EnqueueNewSessionPayload(immybotParams));
          logger.LogDebug("Successfully enqueued maintenance job");
        }
      }
      catch (Exception ex) when (!ex.IsCancellationException())
      {
        logger.LogError(ex, "Error while manually running maintenance session");
      }
    }).Forget();

    return Accepted();
  }
}
