using Immybot.Backend.Application.Lib;
using Immybot.Backend.Manager.Domain;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Manager.Shared;
using Immybot.Manager.SitesApi;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class BillingController : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(IBillingManagePermission))]
  [HttpPost(BillingApiRoutes.CreateCustomerPortalSession)]
  public async Task<ActionResult<GetCustomerPortalSessionResult>> CreateCustomerPortalSession(
    [FromServices] IManagerProvidedSettings mgrSettings)
  {
    var session = await mgrSettings.GetCustomerPortalSession();
    return Ok(session);
  }

  [SubjectPermissionAuthorize(typeof(IBillingViewPermission))]
  [HttpGet(BillingApiRoutes.GetSubscriptionDetails)]
  public ActionResult<GetSubscriptionDetailsResponse> GetSubscriptionDetails(
    [FromServices] IManagerProvidedSettings mgrSettings,
    [FromServices] IFeatureManager featureManager)
  {
    var enabledFeatures = featureManager.GetFeaturesEnabledFromSubscription();
    return Ok(new GetSubscriptionDetailsResponse(
      mgrSettings.SubscriptionPlanId,
      mgrSettings.SubscriptionStatus,
      mgrSettings.SubscriptionTrialStartUtc,
      mgrSettings.SubscriptionTrialEndUtc,
      mgrSettings.SubscriptionActivatedDateUtc,
      enabledFeatures));
  }

  [SubjectPermissionAuthorize(typeof(IBillingViewPermission))]
  [HttpGet(BillingApiRoutes.GetProductCatalogItems)]
  public async Task<ActionResult<GetProductCatalogItemsResponse>> GetProductCatalogItems(
    [FromServices] IManagerProvidedSettings mgrSettings)
  {
    return Ok(await mgrSettings.GetProductCatalogItems());
  }

  [SubjectPermissionAuthorize(typeof(IBillingViewPermission))]
  [HttpGet(BillingApiRoutes.GetBillingPlatformDetails)]
  public ActionResult<BillingPlatformDetails> GetBillingPlatformDetails(
    [FromServices] IManagerProvidedSettings mgrSettings)
  {
    return Ok(mgrSettings.BillingPlatformDetails);
  }
}
