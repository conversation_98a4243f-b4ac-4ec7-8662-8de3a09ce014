using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.RoleClaimMetadataService.Interfaces;
using Immybot.Backend.RBAC.Domain.RoleManagement.Interfaces;
using Immybot.Backend.RBAC.Domain.RoleManagement.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Implementations.ExceptionFactories;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;

namespace Immybot.Backend.Web.Common.Controllers.V1.RBAC;

public class RolesController(
  IFeatureTracker featureTracker,
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService) : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(IRbacManagePermission))]
  [HttpPost(RoleApiRoutes.CreateRole)]
  public async Task<ActionResult<GetRoleResponse>> CreateRole(
    [FromBody] CreateOrUpdateRoleRequest request,
    [FromServices] ICreateRoleOperation createRoleOperation,
    [FromServices] IGetRoleOperation getRoleOperation,
    CancellationToken cancellationToken)
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);
    featureTracker.IsEnabled(FeatureEnum.RBACCustomRolesFeature, strict: true);
    var newRole = await createRoleOperation.ExecuteAsync(request, cancellationToken: cancellationToken);
    var response = await getRoleOperation.ExecuteAsync(newRole.Id, cancellationToken);
    return Ok(response);
  }


  [SubjectPermissionAuthorize(typeof(IRbacManagePermission))]
  [HttpPut(RoleApiRoutes.UpdateRole)]
  public async Task<ActionResult<GetRoleResponse>> UpdateRole(
    [FromRoute] int roleId,
    [FromBody] CreateOrUpdateRoleRequest request,
    [FromServices] IUpdateRoleOperation updateRoleOperation,
    [FromServices] IGetRoleOperation getRoleOperation,
    CancellationToken cancellationToken)
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);
    featureTracker.IsEnabled(FeatureEnum.RBACCustomRolesFeature, strict: true);

    _ = await updateRoleOperation.ExecuteAsync(
      roleId,
      request,
      cancellationToken: cancellationToken);

    var updatedRole = await getRoleOperation.ExecuteAsync(roleId, cancellationToken);
    return Ok(updatedRole);
  }

  [SubjectPermissionAuthorize(typeof(IRbacManagePermission))]
  [HttpPost(RoleApiRoutes.CloneRole)]
  public async Task<ActionResult<GetRoleResponse>> CloneRole(
    [FromRoute] int roleId,
    [FromBody] CloneRoleRequest request,
    [FromServices] ICloneRoleOperation cloneRoleOperation,
    [FromServices] IGetRoleOperation getRoleOperation,
      CancellationToken cancellationToken)
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);
    featureTracker.IsEnabled(FeatureEnum.RBACCustomRolesFeature, strict: true);

    var clonedRole = await cloneRoleOperation.ExecuteAsync(
      roleId,
      request.NewName,
      cancellationToken);

    var response = await getRoleOperation.ExecuteAsync(clonedRole.Id, cancellationToken);
    return Ok(response);
  }

  [SubjectPermissionAuthorize(typeof(IRbacViewPermission))]
  [HttpGet(RoleApiRoutes.GetRoles)]
  public async Task<ActionResult<List<GetRoleResponse>>> GetRoles(
    [FromServices] IGetRolesOperation operation,
    [FromServices] IRoleClaimMetadataService roleClaimMetadataService,
    CancellationToken cancellationToken)
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);
    var roles = await operation.ExecuteAsync(cancellationToken);

    if (await CanAssignCrossTenantRoles()) return Ok(roles);

    // filter out roles with cross-tenant claims
    roles = roles.Where(a => !a.RoleClaims.Any(c => roleClaimMetadataService.IsCrossTenantRoleClaim(c.ClaimValue)))
      .ToList();

    return Ok(roles);
  }

  [SubjectPermissionAuthorize(typeof(IRbacViewPermission))]
  [HttpGet(RoleApiRoutes.GetRole)]
  public async Task<ActionResult<GetRoleResponse>> GetRole(
    [FromRoute] int roleId,
    [FromServices] IRoleClaimMetadataService roleClaimMetadataService,
    [FromServices] IGetRoleOperation operation,
    CancellationToken cancellationToken)
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);
    var role = await operation.ExecuteAsync(roleId, cancellationToken);
    var canAssignCrossTenantRoles = await CanAssignCrossTenantRoles();
    var isRoleCrossTenant = role.RoleClaims
      .Any(a => roleClaimMetadataService.IsCrossTenantRoleClaim(a.ClaimValue));

    if (!canAssignCrossTenantRoles && isRoleCrossTenant)
    {
      throw ResourceExceptionFactory.CreateResourceAuthorizationFailedForCrossTenantRole(role.Id);
    }

    return Ok(role);
  }

  [SubjectPermissionAuthorize(typeof(IRbacManagePermission))]
  [HttpDelete(RoleApiRoutes.DeleteRole)]
  public async Task<IActionResult> DeleteRole(
    [FromRoute] int roleId,
    [FromServices] IDeleteRoleOperation operation,
    CancellationToken cancellationToken)
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);
    featureTracker.IsEnabled(FeatureEnum.RBACCustomRolesFeature, strict: true);
    await operation.ExecuteAsync(roleId, cancellationToken);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IRbacViewPermission))]
  [HttpGet(RoleApiRoutes.GetPermissions)]
  public async Task<ActionResult<List<ISubjectMetadata>>> GetPermissions(
    [FromServices] IEnumerable<ISubjectMetadata> subjectDefinitions,
    CancellationToken cancellationToken)
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);
    return Ok(subjectDefinitions.Where(a => !a.IsSystemManaged));
  }

  private async Task<bool> CanAssignCrossTenantRoles() => await subjectPermissionAuthorizationService
    .AuthorizeAsync<IUsersAssignRolesWithCrossTenantPermissions>(User, strict: false);
}
