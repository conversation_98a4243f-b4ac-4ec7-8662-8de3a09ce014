### Get Initial State (Before Tests)
# Retrieves all roles to verify starting state
# Expected: 200 OK with array of built-in roles only
GET http://localhost:5000/api/v1/roles/get
Accept: application/json

### Get Role Template
# Retrieves template with all available subjects and permissions
# Expected: 200 OK with empty role template
GET http://localhost:5000/api/v1/roles/template
Accept: application/json

### Create Role with All Tenants Mode
# Creates role that applies to all tenants
# Expected: 200 OK with role details including user and tenant lists
POST http://localhost:5000/api/v1/roles/create
Content-Type: application/json

{
 "name": "AllTenantsRole",
 "description": "Role that applies to all tenants", 
 "tenantRestrictionMode": 0,
 "grantedPermissionIds": [1, 2],
 "tenantIds": [],
 "userIds": [1]
}

### Verify All Tenants Role Creation and User/Tenant Lists
# Expected: 200 OK with role details including empty tenant list and user list with ID 1
GET http://localhost:5000/api/v1/roles/get/role/5
Accept: application/json

### Create Test User for Role Assignment
# Creates user that will be assigned to roles during testing
POST http://localhost:5000/api/v1/users
Content-Type: application/json
{
 "tenantId": 1,
 "isAdmin": false,
 "hasManagementAccess": true,
 "servicePrincipalId": "test-user-2"
}

### Create Role with Include Tenants Mode
# Creates role that only applies to specific tenants
# Expected: 200 OK with role details including tenant and user lists
POST http://localhost:5000/api/v1/roles/create
Content-Type: application/json

{
 "name": "IncludeTenantsRole",
 "description": "Role that applies only to specified tenants",
 "tenantRestrictionMode": 1,
 "grantedPermissionIds": [1, 2],
 "tenantIds": [1],
 "userIds": [2]  
}

### Verify Include Tenants Role Creation and Lists
# Expected: 200 OK with role details including tenant ID 1 and user ID 2
GET http://localhost:5000/api/v1/roles/get/role/6
Accept: application/json

### Create Role with Exclude Tenants Mode
# Creates role that applies to all except specific tenants
# Expected: 200 OK with role details including tenant and user lists
POST http://localhost:5000/api/v1/roles/create
Content-Type: application/json
{
 "name": "ExcludeTenantsRole", 
 "description": "Role that excludes specified tenants",
 "tenantRestrictionMode": 2,
 "grantedPermissionIds": [1, 2],
 "tenantIds": [1],
 "userIds": []
}

### Verify Exclude Tenants Role Creation and Lists
# Expected: 200 OK with role details including tenant ID 1 and empty user list
GET http://localhost:5000/api/v1/roles/get/role/7
Accept: application/json

### Update Role Properties and Assignments
# Updates existing role with new properties, tenants and users
# Expected: 200 OK with updated role details including new lists
PUT http://localhost:5000/api/v1/roles/update/role/5
Content-Type: application/json

{
 "name": "UpdatedAllTenantsRole",
 "description": "Updated description",
 "tenantRestrictionMode": 0,
 "grantedPermissionIds": [1, 2, 3, 4],
 "tenantIds": [],
 "userIds": [1, 2]
}

### Verify Role Update Including User/Tenant Changes
# Expected: 200 OK with updated role details and new user/tenant lists
GET http://localhost:5000/api/v1/roles/get/role/5
Accept: application/json

### Clone Custom Role
# Creates a copy of an existing role including tenant assignments
# Expected: 200 OK with cloned role details including lists 
POST http://localhost:5000/api/v1/roles/clone/role/5
Content-Type: application/json

{
 "newName": "ClonedCustomRole"
}

### Clone Built-in Role
# Creates a copy of an existing role including tenant assignments
# Expected: 200 OK with cloned role details including lists 
POST http://localhost:5000/api/v1/roles/clone/role/1
Content-Type: application/json

{
 "newName": "ClonedBuiltinRole"
}

### Attempt to Update Built-in Role
# Should fail when trying to modify a built-in role
# Expected: 400 Bad Request
PUT http://localhost:5000/api/v1/roles/update/role/1  
Content-Type: application/json

{
 "name": "ModifiedBuiltIn",
 "description": "Should fail",
 "tenantRestrictionMode": 0,
 "grantedPermissionIds": [1],
 "tenantIds": [],
 "userIds": []
}

### Create Role for Deletion Test
# Creates role with no users for deletion test
POST http://localhost:5000/api/v1/roles/create
Content-Type: application/json

{
 "name": "RoleToDelete",
 "description": "Will be deleted",
 "tenantRestrictionMode": 0, 
 "grantedPermissionIds": [1],
 "tenantIds": [],
 "userIds": []
}

### Verify Deletion Test Role Creation Including Empty Lists
# Expected: 200 OK with role details and empty user/tenant lists
GET http://localhost:5000/api/v1/roles/get/role/9
Accept: application/json

### Delete Role with No Users 
# Deletes a role that has no assigned users
# Expected: 204 No Content
DELETE http://localhost:5000/api/v1/roles/delete/role/9
Accept: application/json

### Delete Role with Users
# Deletes role and automatically removes user assignments
# Expected: 204 No Content
DELETE http://localhost:5000/api/v1/roles/delete/role/5
Accept: application/json

### Attempt to Delete Built-in Role
# Should fail when trying to delete a built-in role  
# Expected: 400 Bad Request
DELETE http://localhost:5000/api/v1/roles/delete/role/1
Accept: application/json




### CLEAN UP BEGINS HERE




### Clean Up - Delete Test User
# Remove test user
DELETE http://localhost:5000/api/v1/users/2
Accept: application/json

### Clean Up - Delete Cloned Role
# Remove cloned role
DELETE http://localhost:5000/api/v1/roles/delete/role/8
Accept: application/json

### Clean Up - Delete Exclude Tenants Role  
# Remove exclude tenants role
DELETE http://localhost:5000/api/v1/roles/delete/role/7
Accept: application/json

### Clean Up - Delete Include Tenants Role
# Remove include tenants role
DELETE http://localhost:5000/api/v1/roles/delete/role/6
Accept: application/json

### Clean Up - Delete Cloned Built-in Role
# Remove include tenants role
DELETE http://localhost:5000/api/v1/roles/delete/role/10
Accept: application/json


### Reset Users Identity Sequence
# Resets users table identity sequence
POST http://localhost:5000/ResetUsers
Accept: application/json

### Reset Roles Identity Sequence  
# Resets roles table identity sequence
POST http://localhost:5000/ResetRoles
Accept: application/json

### Reset User Claims Identity Sequence
# Resets user claims table identity sequence
POST http://localhost:5000/ResetUserClaims
Accept: application/json

### Verify Final State
# Confirms return to initial state with only built-in roles
GET http://localhost:5000/api/v1/roles/get
Accept: application/json
