### Get All Roles (Initial State)
# Retrieves all roles including built-in roles
# Expected: 200 OK with array of built-in roles
GET http://localhost:5000/api/v1/roles/get
Accept: application/json

### Get User Roles (Empty)
# Retrieves roles for a user before any are assigned
# Expected: 200 OK with empty array
GET http://localhost:5000/api/v1/user-roles/get/user/1/roles
Accept: application/json

### Create Initial Role
# Creates first custom role for testing assignment
# Expected: 200 OK with role details including 0 users and custom role type
POST http://localhost:5000/api/v1/roles/create
Content-Type: application/json

{
   "name": "TestRole1"
}

### Assign Role to User
# Assigns first role to user using single assignment endpoint
# Expected: 204 No Content
POST http://localhost:5000/api/v1/user-roles/assign/user/1/role/5
Accept: application/json

### Verify User Has Role
# Checks that user has been assigned TestRole1
# Expected: 200 OK with array containing TestRole1
GET http://localhost:5000/api/v1/user-roles/get/user/1/roles
Accept: application/json

### Get Users In Role
# Retrieves all users assigned to a specific role
# Note: Using role ID 5 (TestRole1) from previous tests
# Expected: 200 OK with array of users in role
GET http://localhost:5000/api/v1/user-roles/get/role/5/users
Accept: application/json

### Get Users In Non-existent Role
# Attempts to get users from a role that doesn't exist
# Expected: 404 Not Found
GET http://localhost:5000/api/v1/user-roles/get/role/999/users
Accept: application/json

### Create Additional Roles
# Creates two more roles for testing bulk assignment
# Expected: 200 OK with role details including 0 users and custom role type
POST http://localhost:5000/api/v1/roles/create
Content-Type: application/json

{
   "name": "TestRole2"
}

###
POST http://localhost:5000/api/v1/roles/create
Content-Type: application/json

{
   "name": "TestRole3"
}

### Set User Roles (Replace Existing)
# Uses bulk set endpoint to replace existing role with two new roles
# Expected: 204 No Content
POST http://localhost:5000/api/v1/user-roles/set/user/1/roles
Content-Type: application/json

{
    "roleIds": [6, 7]
}

### Verify Role Update
# Confirms user now has only TestRole2 and TestRole3
# Expected: 200 OK with array containing TestRole2 and TestRole3
GET http://localhost:5000/api/v1/user-roles/get/user/1/roles
Accept: application/json

### Clean Up - Remove All Roles from User
# Removes all role assignments from user
# Expected: 204 No Content 
POST http://localhost:5000/api/v1/user-roles/set/user/1/roles
Content-Type: application/json

{
    "roleIds": []
}

### Clean Up - Delete Test Roles
# Deletes all test roles created during testing
# Expected: 204 No Content
DELETE http://localhost:5000/api/v1/roles/delete/role/5
Accept: application/json

###
DELETE http://localhost:5000/api/v1/roles/delete/role/6
Accept: application/json

###
DELETE http://localhost:5000/api/v1/roles/delete/role/7
Accept: application/json

### Verify Final State
# Confirms return to initial state with only built-in roles
# Expected: 200 OK with only built-in roles
GET http://localhost:5000/api/v1/roles/get
Accept: application/json

### Reset Identity Sequences
# Resets all identity sequences to prevent gaps in IDs
POST http://localhost:5000/ResetRoles
Accept: application/json
