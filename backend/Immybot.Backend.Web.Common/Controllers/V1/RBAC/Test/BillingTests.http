### Test Billing View Permission
# Tests access to subscription details endpoint
# Expected: 200 OK if user has billing view permission, 403 Forbidden otherwise
GET http://localhost:5000/api/v1/billing/subscription-details

### Test Billing Manage Permission
# Tests access to create customer portal session endpoint
# Expected: 200 OK if user has billing manage permission, 403 Forbidden otherwise
POST http://localhost:5000/api/v1/billing/create-customer-portal-session
Content-Type: application/json

{}
