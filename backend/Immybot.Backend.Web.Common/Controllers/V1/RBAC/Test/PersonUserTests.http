# These tests assume the following in the backend:
# - User ID 1 with Person ID 1
# - Built-in Roles with IDs 1-4 (MS<PERSON> Admin, MSP User, Tenant Admin, Tenant User)

### Test: Get Initial State 
# Description: Retrieves all users including admins and regular users before any changes
# Expected: 200 OK with array containing the original user (id=1) with no roles
GET http://localhost:5000/api/v1/person-user/get
Accept: application/json

### Test: Assign First Role to User
# Description: Assigns MSP Admin role to initial user
# Expected: 204 No Content with successful role assignment
POST http://localhost:5000/api/v1/user-roles/assign/user/1/role/1
Accept: application/json

### Test: Assign Second Role to Same User
# Description: Assigns MSP User role to same user, testing multiple role assignment
# Expected: 204 No Content with successful role assignment
POST http://localhost:5000/api/v1/user-roles/assign/user/1/role/2
Accept: application/json

### Test: Create Person Without User
# Description: Creates a person record without an associated user account (will be Person ID 2)
# Expected: 200 OK with created person details and empty roles array
POST http://localhost:5000/api/v1/persons
Content-Type: application/json

{
"firstName": "Person",
"lastName": "Only",
"emailAddress": "<EMAIL>",
"tenantId": 1,
"azurePrincipalId": "2f8ae350-4587-4ca6-b287-45fb4b93d54a"
}

### Test: Create Person For User Creation
# Description: Creates person record that will have an associated user (will be Person ID 3)
# Expected: 200 OK with created person details
POST http://localhost:5000/api/v1/persons 
Content-Type: application/json

{
"firstName": "Person",
"lastName": "WithUser",
"emailAddress": "<EMAIL>",
"tenantId": 1,
"azurePrincipalId": "e3ed42b7-668a-450f-b63d-306ceb8ff2aa"
}

### Test: Create User From Person
# Description: Creates user account linked to Person ID 3 (will be User ID 2)
# Expected: 200 OK with created user details
POST http://localhost:5000/api/v1/users/create-from-person
Content-Type: application/json

{
"personId": 3,
"hasManagementAccess": true
}

### Test: Assign First Role to New User
# Description: Assigns Tenant Admin role to User ID 2
# Expected: 204 No Content with successful role assignment
POST http://localhost:5000/api/v1/user-roles/assign/user/2/role/3
Accept: application/json

### Test: Assign Second Role to New User
# Description: Assigns Tenant User role to User ID 2, testing multiple role assignment
# Expected: 204 No Content with successful role assignment
POST http://localhost:5000/api/v1/user-roles/assign/user/2/role/4
Accept: application/json

### Test: Create Service Account
# Description: Creates a user without an associated person record (will be User ID 3)
# Expected: 200 OK with created service account details
POST http://localhost:5000/api/v1/users
Content-Type: application/json

{
 "servicePrincipalId": "78f8caf2-4c39-4df1-83c4-9382a7ce8a26",
 "tenantId": 1,
 "isAdmin": false,
 "hasManagementAccess": true
}

### Test: Assign First Role to Service Account
# Description: Assigns MSP User role to service account
# Expected: 204 No Content with successful role assignment
POST http://localhost:5000/api/v1/user-roles/assign/user/3/role/2
Accept: application/json

### Test: Assign Second Role to Service Account
# Description: Assigns Tenant User role to service account, testing multiple role assignment
# Expected: 204 No Content with successful role assignment
POST http://localhost:5000/api/v1/user-roles/assign/user/3/role/4
Accept: application/json

### Test: Get Current State
# Description: Verifies state after all creations and assignments
# Expected: 200 OK with array containing:
# - User ID 1 with Person ID 1 (has MSP Admin and MSP User roles)
# - Person ID 2 (person-only record, empty roles array)
# - Person ID 3 with User ID 2 (has Tenant Admin and Tenant User roles)
# - User ID 3 (service account with MSP User and Tenant User roles)
GET http://localhost:5000/api/v1/person-user/get
Accept: application/json

### Begin Cleanup in Order of Dependencies

### Test: Remove First Role From User 1
# Description: Removes MSP Admin role from initial user
# Expected: 204 No Content with successful role removal
DELETE http://localhost:5000/api/v1/user-roles/remove/user/1/role/1
Accept: application/json

### Test: Remove Second Role From User 1
# Description: Removes MSP User role from initial user
# Expected: 204 No Content with successful role removal
DELETE http://localhost:5000/api/v1/user-roles/remove/user/1/role/2
Accept: application/json

### Test: Remove First Role From User 2
# Description: Removes Tenant Admin role from User ID 2
# Expected: 204 No Content with successful role removal
DELETE http://localhost:5000/api/v1/user-roles/remove/user/2/role/3
Accept: application/json

### Test: Remove Second Role From User 2
# Description: Removes Tenant User role from User ID 2
# Expected: 204 No Content with successful role removal
DELETE http://localhost:5000/api/v1/user-roles/remove/user/2/role/4
Accept: application/json

### Test: Remove First Role From Service Account
# Description: Removes MSP User role from service account
# Expected: 204 No Content with successful role removal
DELETE http://localhost:5000/api/v1/user-roles/remove/user/3/role/2
Accept: application/json

### Test: Remove Second Role From Service Account
# Description: Removes Tenant User role from service account
# Expected: 204 No Content with successful role removal
DELETE http://localhost:5000/api/v1/user-roles/remove/user/3/role/4
Accept: application/json

### Test: Delete Service Account
# Description: Removes service account user
# Expected: 204 No Content with successful user deletion
DELETE http://localhost:5000/api/v1/users/3
Accept: application/json

### Test: Delete Person-with-User's User Account
# Description: Removes user account for Person ID 3
# Expected: 204 No Content with successful user deletion
DELETE http://localhost:5000/api/v1/users/2
Accept: application/json

### Test: Delete Person-with-User Record
# Description: Removes person record that had associated user
# Expected: 204 No Content with successful person deletion
DELETE http://localhost:5000/api/v1/persons/3
Accept: application/json

### Test: Delete Person-only Record
# Description: Removes person record that never had an associated user
# Expected: 204 No Content with successful person deletion
DELETE http://localhost:5000/api/v1/persons/2
Accept: application/json

### Test: Reset Users Identity Sequence
# Description: Resets users table identity sequence to prevent gaps
# Expected: 200 OK with confirmation message
POST http://localhost:5000/ResetUsers
Accept: application/json

### Test: Reset Persons Identity Sequence
# Description: Resets persons table identity sequence to prevent gaps
# Expected: 200 OK with confirmation message
POST http://localhost:5000/ResetPersons
Accept: application/json

### Test: Reset Roles Identity Sequence
# Description: Resets roles table identity sequence to prevent gaps
# Expected: 200 OK with confirmation message
POST http://localhost:5000/ResetRoles
Accept: application/json

### Test: Reset User Claims Identity Sequence
# Description: Resets user claims table identity sequence to prevent gaps
# Expected: 200 OK with confirmation message
POST http://localhost:5000/ResetUserClaims
Accept: application/json

### Test: Verify Final State
# Description: Confirms cleanup returned system to original state
# Expected: 200 OK with array containing only original User ID 1 with Person ID 1 and no roles
GET http://localhost:5000/api/v1/person-user/get
Accept: application/json
