### Get All Users (Initial State)
# Retrieves all users including admins and regular users
# Expected: 200 OK with array of users
GET http://localhost:5000/api/v1/users
Accept: application/json

### Get User Claims
# Retrieves the current user's role claims
# Expected: 200 OK with array of role claims
GET http://localhost:5001/api/v1/users/claims
Accept: application/json

### Get Single User
# Retrieves a specific user by ID - using ID 1 which exists 
# Expected: 200 OK with user details
GET http://localhost:5000/api/v1/users/1
Accept: application/json

### Get Non-existent User
# Attempts to retrieve user that doesn't exist
# Expected: 404 Not Found
GET http://localhost:5000/api/v1/users/999
Accept: application/json

### Create Person First
# Creates a new person record to use for user creation
# Expected: 200 OK with created person details
POST http://localhost:5000/api/v1/persons
Content-Type: application/json

{
   "firstName": "Test",
   "lastName": "Person",
   "emailAddress": "<EMAIL>",
   "tenantId": 1,
   "azurePrincipalId": "test-azure-id-123" 
}

### Create User From New Person
# Creates new user from newly created person record
# Note: Use the ID returned from the person creation above
POST http://localhost:5000/api/v1/users/create-from-person
Content-Type: application/json

{
    "personId": 2,
    "hasManagementAccess": true
}

### Create User From Non-existent Person
# Attempts to create user from non-existent person
# Expected: 404 Not Found 
POST http://localhost:5000/api/v1/users/create-from-person
Content-Type: application/json

{
    "personId": 999,
    "hasManagementAccess": true
}

### Update User
# Updates existing user's properties
# Note: Use ID from the created user above
POST http://localhost:5000/api/v1/users/2
Content-Type: application/json

{
    "id": 2,
    "tenantId": 1,
    "isAdmin": false,
    "hasManagementAccess": true,
    "canManageCrossTenantDeployments": false,
    "expirationDateUTC": "2025-01-01T00:00:00Z"
}

### Update Non-existent User
# Attempts to update user that doesn't exist
# Expected: 404 Not Found
POST http://localhost:5000/api/v1/users/999
Content-Type: application/json

{
    "id": 999,
    "isAdmin": false,
    "hasManagementAccess": true
}

### Delete User
# Deletes the user we created
# Note: Use ID from created user above
# Expected: 204 No Content
DELETE http://localhost:5000/api/v1/users/2
Accept: application/json

### Delete Person
# Deletes the person we created
# Note: Use ID from created person above
# Expected: 204 No Content  
DELETE http://localhost:5000/api/v1/persons/2
Accept: application/json

### Delete Non-existent User
# Attempts to delete user that doesn't exist
# Expected: 404 Not Found
DELETE http://localhost:5000/api/v1/users/999
Accept: application/json

### Submit User Feedback
# Submits user feedback with rating and details
# Expected: 204 No Content
POST http://localhost:5000/api/v1/users/submit-feedback
Content-Type: application/json

{
    "rating": 5,
    "details": "Great system improvements!"
}

### Get Final State
# Gets all users to verify final state after operations
# Expected: 200 OK with updated user list
GET http://localhost:5000/api/v1/users
Accept: application/json

### Reset Users Identity Sequence
# Resets users table identity sequence
POST http://localhost:5000/ResetUsers
Accept: application/json

### Reset Persons Identity Sequence
# Resets persons table identity sequence
POST http://localhost:5000/ResetPersons
Accept: application/json

### Reset User Claims Identity Sequence
# Resets user claims table identity sequence
POST http://localhost:5000/ResetUserClaims
Accept: application/json

