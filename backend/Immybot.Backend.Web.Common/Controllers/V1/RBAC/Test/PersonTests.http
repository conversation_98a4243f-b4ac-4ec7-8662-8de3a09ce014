### Note, like half these tests don't work or aren't setup correctly. I didn't spend a lot of time on these
### because they aren't the focus of RBAC and should already be working. There are no changes
### to person controller or table.

### Get All Persons (Initial State)
# Retrieves all persons including admins and regular users
# Expected: 200 OK with array of persons
GET http://localhost:5000/api/v1/persons/dx
Accept: application/json

### Get Single Person
# Retrieves a specific person by ID 
# Expected: 200 OK with person details
GET http://localhost:5000/api/v1/persons/1
Accept: application/json

### Get Non-existent Person
# Attempts to retrieve person that doesn't exist
# Expected: 404 Not Found
GET http://localhost:5000/api/v1/persons/999
Accept: application/json

### Create Person
# Creates a new person record
# Expected: 200 OK with created person details
POST http://localhost:5000/api/v1/persons
Content-Type: application/json

{
   "firstName": "Test",
   "lastName": "Person",
   "emailAddress": "<EMAIL>",
   "tenantId": 1
}

### WARNING: This one fails. Might try to fix this later, but it's a low priority.
### Create Person With Invalid Data
# Attempts to create person with missing required fields
# Expected: 400 Bad Request
POST http://localhost:5000/api/v1/persons
Content-Type: application/json

{
   "firstName": "",
   "lastName": "",
   "emailAddress": ""
}

### WARNING: Before you run this, make sure you know your own user id.
### If you update your own user id, the request below will set your azure principal to null.
### Consequently, you will not be able to log in or access endpoints.
### This test creates fake users who can't login!
### Update Person
# Updates existing person's properties
# Expected: 200 OK with updated person details
PUT http://localhost:5000/api/v1/persons/2
Content-Type: application/json

{
   "id": 2,
   "firstName": "Raymond",
   "lastName": "Holston",
   "emailAddress": "<EMAIL>",
   "tenantId": 1
}

### WARNING: This throws 400 bad request since email address is required
### Update Non-existent Person
# Attempts to update person that doesn't exist
# Expected: 404 Not Found
PUT http://localhost:5000/api/v1/persons/999
Content-Type: application/json

{
   "id": 999,
   "firstName": "NonExistent",
   "lastName": "Person"
}

### Delete Person
# Deletes an existing person
# Expected: 204 No Content
DELETE http://localhost:5000/api/v1/persons/2
Accept: application/json

### Delete Non-existent Person
# Attempts to delete person that doesn't exist
# Expected: 404 Not Found
DELETE http://localhost:5000/api/v1/persons/999
Accept: application/json

### WARNING: This test is not setup correctly... returning 404 not found.
### Low priority to fix.
### Add Tags to Person
# Adds tags to existing person
# Expected: 204 No Content
POST http://localhost:5000/api/v1/persons/add-tags
Content-Type: application/json

{
   "entityIds": [1],
   "tagIds": [1, 2]
}

### Remove Tags from Person
# Removes tags from existing person
# Expected: 204 No Content
POST http://localhost:5000/api/v1/persons/remove-tags
Content-Type: application/json

{
   "entityIds": [1],
   "tagIds": [1, 2]
}

### Get Persons Requesting Access
# Retrieves list of persons who have requested access
# Expected: 200 OK with array of access requests
GET http://localhost:5000/api/v1/persons/requesting-access
Accept: application/json

### WARNING: Got errors on this one.
### Grant Access to Person
# Grants system access to person with specified permissions
# Expected: 200 OK with operation result
POST http://localhost:5000/api/v1/persons/1/grant-access
Content-Type: application/json

{
   "isAdmin": false,
   "hasManagementAccess": true,
   "expirationTime": "2024-12-31T23:59:59Z"
}

### Deny Access to Person
# Denies system access to person
# Expected: 200 OK with operation result
POST http://localhost:5000/api/v1/persons/2/deny-access
Accept: application/json

### Get Person Self-Service Items
# Retrieves available self-service items for person
# Expected: 200 OK with array of self-service items
GET http://localhost:5000/api/v1/persons/1/self-service
Accept: application/json

### Verify Final State
# Gets all persons to verify final state after operations
# Expected: 200 OK with updated person list
GET http://localhost:5000/api/v1/persons/dx
Accept: application/json
