### Get Initial State (Before Tests)
# Retrieves all roles to verify starting state 
# Expected: 200 OK with array of built-in roles only
GET http://localhost:5000/api/v1/roles/get

### Create Role with No Permissions 
# Creates role without any permissions to verify access is denied
# Expected: 200 OK with created role details
POST http://localhost:5000/api/v1/roles/create
Content-Type: application/json

{
    "name": "Billing Viewer Role",
    "description": "Role for viewing billing information",
    "tenantRestrictionMode": 0,
    "grantedPermissionIds": [],
    "tenantIds": [],
    "userIds": [1]
}

### Test View Permission (Should Fail)
# Tests access to get subscription details endpoint
# Expected: 403 Forbidden
GET http://localhost:5000/api/v1/billing/subscription-details

### Update Role to Add View Permission
# Updates role to add billing view permission
# Expected: 200 OK with updated role details 
PUT http://localhost:5000/api/v1/roles/update/role/5
Content-Type: application/json

{
    "name": "Billing Viewer Role", 
    "description": "Role for viewing billing information",
    "tenantRestrictionMode": 0,
    "grantedPermissionIds": [40],
    "tenantIds": [],
    "userIds": [1]
}

### Test View Permission (Should Succeed)
# Tests access to get subscription details endpoint
# Expected: 200 OK with subscription details
GET http://localhost:5000/api/v1/billing/subscription-details

### Delete Role
# Deletes the test role
# Expected: 204 No Content  
DELETE http://localhost:5000/api/v1/roles/delete/role/5

### Get Final State (After Tests)
# Verifies role cleanup
# Expected: 200 OK with original roles only
GET http://localhost:5000/api/v1/roles/get

### Reset Users Identity Sequence
# Resets users table identity sequence
POST http://localhost:5000/ResetUsers
Accept: application/json

### Reset Roles Identity Sequence  
# Resets roles table identity sequence
POST http://localhost:5000/ResetRoles
Accept: application/json

### Reset User Claims Identity Sequence
# Resets user claims table identity sequence
POST http://localhost:5000/ResetUserClaims
Accept: application/json
