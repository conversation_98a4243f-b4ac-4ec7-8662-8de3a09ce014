using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Immybot.Backend.Persistence;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Authorization;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;

namespace Immybot.Backend.Web.Common.Controllers.V1.RBAC;

// Require authentication for all endpoints in this controller
// This controller is only meant for use in a testing capacity in a dev environment.
[Authorize]
[ApiController]
[Route("api/v1/identity-reseed")]
public class AspNetIdentityReseedController(
  Func<ImmybotDbContext> context,
  ILogger<AspNetIdentityReseedController> logger,
  IHostEnvironment environment,
  IFeatureTracker featureTracker)
  : ControllerBase
{
  // Reset identity sequence for roles table
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost("ResetRoles")]
  public async Task<IActionResult> ResetAspNetRoles()
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);

    // Early return if not in development environment
    if (!environment.IsDevelopment())
      return StatusCode(403, "This operation is only allowed in development environments.");

    try
    {
      // Create context once and ensure proper disposal with using statement
      using var context1 = context();

      int maxId = await context1.Roles.MaxAsync(r => (int?)r.Id) ?? 0;
      int nextVal = maxId + 1;

      using var transaction = await context1.Database.BeginTransactionAsync();
      await context1.Database.ExecuteSqlRawAsync(
          $"SELECT setval('\"roles_id_seq\"', {nextVal}, false)");
      await transaction.CommitAsync();

      return Ok($"Identity reset completed for roles. Next sequence value will be {nextVal}");
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "Error resetting sequence for roles");
      return StatusCode(500, $"An error occurred: {ex.Message}");
    }
  }

  // Reset identity sequence for role claims table
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost("ResetClaims")]
  public async Task<IActionResult> ResetAspNetRoleClaims()
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);

    // Early return if not in development environment
    if (!environment.IsDevelopment())
      return StatusCode(403, "This operation is only allowed in development environments.");

    try
    {
      // Create context once and ensure proper disposal with using statement
      using var context1 = context();

      int maxId = await context1.RoleClaims.MaxAsync(rc => (int?)rc.Id) ?? 0;
      int nextVal = maxId + 1;

      using var transaction = await context1.Database.BeginTransactionAsync();
      await context1.Database.ExecuteSqlRawAsync(
          $"SELECT setval('\"role_claims_id_seq\"', {nextVal}, false)");
      await transaction.CommitAsync();

      return Ok($"Identity reset completed for role claims. Next sequence value will be {nextVal}");
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "Error resetting sequence for role claims");
      return StatusCode(500, $"An error occurred: {ex.Message}");
    }
  }

  // Reset identity sequence for user claims table
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost("ResetUserClaims")]
  public async Task<IActionResult> ResetAspNetUserClaims()
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);

    // Early return if not in development environment
    if (!environment.IsDevelopment())
      return StatusCode(403, "This operation is only allowed in development environments.");

    try
    {
      // Create context once and ensure proper disposal with using statement
      using var context1 = context();

      int maxId = await context1.UserClaims.MaxAsync(uc => (int?)uc.Id) ?? 0;
      int nextVal = maxId + 1;

      using var transaction = await context1.Database.BeginTransactionAsync();
      await context1.Database.ExecuteSqlRawAsync(
          $"SELECT setval('\"user_claims_id_seq\"', {nextVal}, false)");
      await transaction.CommitAsync();

      return Ok($"Identity reset completed for user claims. Next sequence value will be {nextVal}");
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "Error resetting sequence for user claims");
      return StatusCode(500, $"An error occurred: {ex.Message}");
    }
  }

  // Reset identity sequence for users table
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost("ResetUsers")]
  public async Task<IActionResult> ResetUsers()
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);

    // Early return if not in development environment
    if (!environment.IsDevelopment())
      return StatusCode(403, "This operation is only allowed in development environments.");

    try
    {
      // Create context once and ensure proper disposal with using statement
      using var context1 = context();

      int maxId = await context1.Users.MaxAsync(u => (int?)u.Id) ?? 0;
      int nextVal = maxId + 1;

      using var transaction = await context1.Database.BeginTransactionAsync();
      await context1.Database.ExecuteSqlRawAsync(
          $"SELECT setval('\"users_id_seq\"', {nextVal}, false)");
      await transaction.CommitAsync();

      return Ok($"Identity reset completed for users. Next sequence value will be {nextVal}");
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "Error resetting sequence for users");
      return StatusCode(500, $"An error occurred: {ex.Message}");
    }
  }

  // Reset identity sequence for persons table
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost("ResetPersons")]
  public async Task<IActionResult> ResetPersons()
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);

    // Early return if not in development environment
    if (!environment.IsDevelopment())
      return StatusCode(403, "This operation is only allowed in development environments.");

    try
    {
      // Create context once and ensure proper disposal with using statement
      using var context1 = context();

      int maxId = await context1.Persons.MaxAsync(p => (int?)p.Id) ?? 0;
      int nextVal = maxId + 1;

      using var transaction = await context1.Database.BeginTransactionAsync();
      await context1.Database.ExecuteSqlRawAsync(
          $"SELECT setval('\"persons_id_seq\"', {nextVal}, false)");
      await transaction.CommitAsync();

      return Ok($"Identity reset completed for persons. Next sequence value will be {nextVal}");
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "Error resetting sequence for persons");
      return StatusCode(500, $"An error occurred: {ex.Message}");
    }
  }
}
