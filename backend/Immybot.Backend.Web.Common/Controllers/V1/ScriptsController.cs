using System.Management.Automation;
using System.Management.Automation.Language;
using System.Text.Json;
using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.MaintenanceActionExtensions;
using Immybot.Backend.Application.DbContextExtensions.MaintenanceSessionExtensions;
using Immybot.Backend.Application.DbContextExtensions.MaintenanceTaskExtensions;
using Immybot.Backend.Application.DbContextExtensions.ScriptExtensions;
using Immybot.Backend.Application.DbContextExtensions.SessionLogExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Commands.Payloads.Scripts;
using Immybot.Backend.Application.Interface.Language;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Application.Lib.SyntaxChecker;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.SoftwareDbContextExtensions;
using Immybot.Backend.Application.SoftwareDbContextExtensions.MaintenanceTaskExtensions;
using Immybot.Backend.Application.SoftwareDbContextExtensions.ScriptExtensions;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Exceptions;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Requests.Scripts;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Contracts.V1.Responses.Scripts;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Shared.Primitives;
using Immybot.Shared.Scripts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class ScriptsController : Controller
{
  private readonly IResourceAuthorizerFlow _resourceAuthorizerFlow;
  private readonly IUserService _userService;
  public ScriptsController(
    IResourceAuthorizerFlow resourceAuthorizerFlow,
    IUserService userService)
  {
    _resourceAuthorizerFlow = resourceAuthorizerFlow;
    _userService = userService;
  }

  [SubjectPermissionAuthorize(typeof(IScriptsRunArbitraryPermission))]
  [HttpPost(ScriptApiRoutes.CancelScript)]
  public ActionResult CancelScript(
    [FromServices] IImmyCancellationManager immyCancellationManager,
    [FromRoute] Guid cancellationId)
  {
    // TODO: add user ability check to ensure the current user either was the one who ran the
    // debug script or that they have permission to cancel any debug script.
    // It's currently "security through obscurity", which is not actually terrible since the
    // cancellationId is generated by the client to begin with
    immyCancellationManager.CancelScript(cancellationId);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpPost(ScriptApiRoutes.StartEditorServices)]
  public async Task<ActionResult> StartEditorServices(
    [FromBody] StartEditorServicesRequest payload,
    [FromServices] ILanguageService debugSessionHandler,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    CancellationToken cancellationToken)
  {
    var user = _userService.GetCurrentUser();
    var userCanViewAllOpenScriptEditors = await subjectPermissionAuthorizationService
      .AuthorizeAsync<ISystemOperationsViewOpenScriptEditorsPermission>(User, strict: false);

    // The RunDebugSession task will complete when the debugger session is finished or canceled
    await debugSessionHandler.StartEditorServices(payload.TerminalId, payload.ScriptId, payload.ScriptType, payload.ScriptCategory, payload.ScriptExecutionContext, user, userCanViewAllOpenScriptEditors, cancellationToken);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.ConnectLanguageService)]
  public async Task<ActionResult> ConnectLanguageService(
    [FromRoute] Guid terminalId,
    [FromServices] IHttpContextAccessor httpContextAccessor,
    [FromServices] ILanguageService debugSessionHandler)
  {
    if (httpContextAccessor.HttpContext is not HttpContext context || !context.WebSockets.IsWebSocketRequest) return BadRequest();
    using var webSocket = await context.WebSockets.AcceptWebSocketAsync();
    await debugSessionHandler.ConnectLanguageServices(webSocket, terminalId, context.RequestAborted);
    // Don't return Ok() or NoContent() because that will throw a InvalidOperationException 'StatusCode cannot be set because the response has already started.'
    // https://stackoverflow.com/questions/45675102/asp-net-core-middleware-cannot-set-status-code-on-exception-because-response-ha
    return new EmptyResult();
  }

  //TODO: This method needs to be refactored to be less-permissive. Currently any user can view a script as long as it isn't a global/integration script.
  [SubjectPermissionAuthorize(typeof(IScriptsRunArbitraryPermission))]
  [HttpPost(ScriptApiRoutes.RunScript)]
  public async Task<ActionResult> RunScript(
      [FromBody] RunScriptRequestBody body,
      [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
      [FromServices] IResourceAuthorizerFlow resourceAuthorizerFlow,
      [FromServices] ImmybotDbContext ctx,
      [FromServices] SoftwareDbContext globalCtx,
      [FromServices] IMaintenanceTaskActions maintenanceTaskActions,
      [FromServices] InteractiveScriptExecutionService interactiveScriptExecutionService,
      [FromServices] IFunctionScriptManager functionScriptManager,
      [FromServices] ITargetAssignmentActions targetAssignmentActions,
      CancellationToken cancellationToken)
  {
    var triggeredBy = _userService.GetCurrentUser();

    if (body.Script.ScriptExecutionContext is not ScriptExecutionContext.CloudScript
        && body.ComputerId is null
        && body.MaintenanceSessionId is null)
      return BadRequest("Computer id or maintenance session id is required. Neither were specified");

    if (body.Script is { ScriptCategory: ScriptCategory.Integration, ScriptType: DatabaseType.Global })
    {
      await subjectPermissionAuthorizationService.AuthorizeAsync<IGlobalManagePermission>(User, strict: true);
    }

    var scriptContextParameters = new ScriptContextParameters();

    // if the user is an msp, then use the tenantId from the body.
    // otherwise use the current user's tenantId
    if (body.TenantId is { } tenantId)
    {
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IScriptsRunArbitraryPermission>(
        User,
        tenantId,
        strict: true);
      scriptContextParameters.Tenant = ctx.GetTenantById(tenantId);
      if (scriptContextParameters.Tenant == null) return NotFound($"Unable to find tenant with id {tenantId}");
    }
    else
    {
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IScriptsRunArbitraryPermission>(
        User,
        triggeredBy.TenantId,
        strict: true);
      scriptContextParameters.Tenant = ctx.GetTenantById(triggeredBy.TenantId);
    }

    if (body.ComputerId is { } computerId)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
        new DefaultKeyParameters(computerId),
        strict: true);
      var computer = ctx.GetComputerById(computerId, includeAgents: true);
      if (computer == null) return NotFound("Computer not found");
      scriptContextParameters.Computer = computer;
    }

    if (body.MaintenanceSessionId is { } sessionId)
    {
      var session = await ctx.GetMaintenanceSessionByIdAsync(sessionId, includeStages: true);
      if (session?.TenantId is null) return NotFound("Maintenance Session not found");

      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IMaintenanceSessionsViewPermission>(
        User,
        session.TenantId.Value,
        strict: true);

      scriptContextParameters.Session = session;

      if (session.ComputerId is not null)
      {
        var computer = ctx.GetComputerById(session.ComputerId.Value, includeAgents: true);
        scriptContextParameters.Computer = computer;
      }

      if (session.TenantId is not null)
      {
        var tenant = ctx.GetTenantById(session.TenantId.Value);
        scriptContextParameters.Tenant = tenant;
      }
    }

    if (body.MaintenanceActionId is { } actionId)
    {
      var action = ctx.GetMaintenanceActionById(actionId);
      scriptContextParameters.Action = action;

      if (action is { AssignmentId: { } assignmentId, AssignmentType: { } assignmentType })
      {
        var assignment =
          await targetAssignmentActions.GetTargetAssignmentById(assignmentId,
            assignmentType,
            cancellationToken);

        // this is needed if we are debugging an integration script from a maintenance session
        body.Script.ProviderLinkIdForMaintenanceItem = assignment?.ProviderLinkIdForMaintenanceItem;
      }
    }

    // if we have a maintenance task, then load it as part of the script context parameters
    // so we can perform validation against the task's parameters
    if (body is { MaintenanceTaskId: { } taskId, MaintenanceTaskType: { } taskType })
    {
      scriptContextParameters.MaintenanceTask = await maintenanceTaskActions.GetMaintenanceTask(taskType is DatabaseType.Global ? MaintenanceType.GlobalMaintenanceTask : MaintenanceType.LocalMaintenanceTask, taskId, null, null, cancellationToken);

      if (scriptContextParameters.MaintenanceTask is not null && taskType is DatabaseType.Local)
      {
        _ = await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<MaintenanceTask, IMaintenanceTasksViewPermission>(
          new DefaultKeyParameters(taskId),
          strict: true);
      }

      if (body.Script.ParameterOverrides.Count > 0)
      {
        scriptContextParameters.TargetAssignment = new TargetAssignment
        {
          MaintenanceIdentifier = taskId.ToString(),
          MaintenanceType = taskType is DatabaseType.Global
            ? MaintenanceType.GlobalMaintenanceTask
            : MaintenanceType.LocalMaintenanceTask,
          TaskParameterValues = body.Script.ParameterOverrides.ToDeploymentParameterValueDictionary(),
        };
      }
    }

    // assert permissions if this is an existing script
    var script = body.Script;
    Script? existingScript = null;
    if (script.Id is not 0 && script.ScriptType is DatabaseType.Local)
    {
      // Use RBAC resource authorization - retrieves and authorizes in one step
      existingScript = await _resourceAuthorizerFlow.GetAuthorizedResourceOrFailAsync<Script, IScriptsViewPermission>(
        new DefaultKeyParameters(script.Id));
    }
    else if (script.Id is not 0 && script.ScriptType is DatabaseType.Global)
    {
      existingScript = globalCtx.GetScriptById(script.Id);
      if (existingScript == null) return NotFound($"Script with id {script.Id} not found");
      // Global scripts use subject permissions only - authorization handled by method-level attribute
    }

    if (!(await subjectPermissionAuthorizationService.AuthorizeAsync<IScriptsRunArbitraryPermission>(User,
          strict: false)))
    {
      // Don't allow user to overwrite any of the parameters for this script run
      script = existingScript ??
               throw new SubjectPermissionAuthorizationFailedException(
                 "User does not have permission to run arbitrary scripts");

      if (body.SessionLogId is int sessionLogId)
      {
        // Allow non-msp-admin users to run scripts from session logs. Don't trust the params
        // they send in alongside the script - get the params from the session log itself
        var sessionLog = ctx.GetSessionLogById(sessionLogId);
        if (sessionLog == null) return NotFound("Session Log not found");
        if (sessionLog.ScriptId != script.Id)
          return BadRequest("Specified script is not the one used by the specified session log");
        if (sessionLog.MaintenanceActionId != body.MaintenanceActionId)
          return BadRequest("Specified session log does not belong to the specified maintenance action");
        if (sessionLog.MaintenanceSessionId != body.MaintenanceSessionId)
          return BadRequest("Specified session log does not belong to the specified maintenance session");

        script.Variables = sessionLog.ScriptParameters;
        script.Parameters = sessionLog.ParamBlockParameters;
      }
      else if (body.MaintenanceActionId != null || body.MaintenanceSessionId != null)
      {
        // keep non-msp-admin users from trying to run scripts in inappropriate contexts
        return BadRequest("To run a script in the context of a maintenance session or action, a session log must be specified");
      }
    }

    var scriptNamePrefix = string.IsNullOrEmpty(script.Name) ? "" : script.Name + " - ";
    script.Name = scriptNamePrefix + triggeredBy.DisplayName + " - Script Editor";

    // always bust function script cache when debugging scripts from the editor.
    functionScriptManager.InvalidateCache();

    return Ok(await interactiveScriptExecutionService.RunInteractive(
      script,
      scriptContextParameters,
      body.CancellationId,
      triggeredBy,
      cancellationToken,
      terminalId: body.TerminalId));
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.DxGetAll)]
  public async Task<ActionResult<LoadResult>> DxGetAll(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] SoftwareDbContext globalCtx,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    DataSourceLoadOptions loadOptions,
    [FromQuery] DatabaseType? databaseType = null)
  {
    var localScripts = new List<ScriptResource>().AsQueryable();
    var globalScripts = new List<ScriptResource>().AsQueryable();

    if (databaseType is DatabaseType.Local or null)
    {
      // Use RBAC query filtering - automatically handles tenant scoping at database level
      var filterExpression = permissionFilterBuilder.BuildFilterExpression<Script, IScriptsViewPermission>();

      // Result will be formatted differently for users with global tenant scope.
      // Check if user has tenant:* scoped claim (effectively an MSP user)
      // Note the use of false here since this is a non-blocking authorization check that only changes the results presentation
      if (await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<IScriptsViewPermission>(User, strict: false))
        localScripts = ctx.GetAllScripts().Where(filterExpression).Select(GetLocalScriptResponseForMsp.Projection);
      else
        localScripts = ctx.GetAllScripts().Where(filterExpression).Select(GetLocalScriptResponse.Projection);
    }

    if (databaseType is DatabaseType.Global or null)
    {
      globalScripts = globalCtx.GetAllScripts().Select(GetGlobalScriptResponse.Projection);
    }

    return Ok(DataSourceLoader.Load(localScripts.ToNonAsyncEnumerable().Concat(globalScripts.ToNonAsyncEnumerable()), loadOptions));
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetAllGlobalScriptNames)]
  public ActionResult<IOrderedEnumerable<GetScriptNameResponse>> GetAllGlobalScriptNames(
    [FromServices] SoftwareDbContext ctx,
    [FromQuery] string? searchFilter = null,
    [FromQuery] ScriptCategory? scriptCategory = null,
    [FromQuery] string searchType = "Name and Content")
  {
    var scripts = ctx.Scripts.AsNoTracking();

    if (scriptCategory != null)
    {
      scripts = scripts.Where(a => a.ScriptCategory == scriptCategory);
    }

    if (!string.IsNullOrEmpty(searchFilter))
    {
      if (searchType == "Name and Content")
      {
        scripts = scripts
          .Where(a => EF.Functions.ILike(a.Name + " " + a.Action, $"%{searchFilter}%"));
      }
      else if (searchType == "Name")
      {
        scripts = scripts
          .Where(a => EF.Functions.ILike(a.Name, $"%{searchFilter}%"));
      }
      else if (searchType == "Content")
      {
        scripts = scripts
          .Where(a => EF.Functions.ILike(a.Action, $"%{searchFilter}%"));
      }
    }

    IQueryable<GetScriptNameResponse> res;


    if (string.IsNullOrEmpty(searchFilter) || searchType == "Name")
    {
      // if we don't have a search filter, do not include the script contents
      res = scripts.Select(a => new GetScriptNameResponse
      {
        Id = a.Id,
        Name = a.Name,
        Category = a.ScriptCategory,
        UpdatedDateUtc = a.UpdatedDate
      });
    }
    else
    {
      // if we do have a search filter, include the script contents so we can render occurence highlighting
      res = scripts.Select(a => new GetScriptNameResponse
      {
        Id = a.Id,
        Name = a.Name,
        Category = a.ScriptCategory,
        UpdatedDateUtc = a.UpdatedDate,
        Action = a.Action
      });
    }

    return Ok(res.OrderBy(a => a.Name));
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetAllLocalScriptNames)]
  public ActionResult<IQueryable<GetScriptNameResponse>> GetAllLocalScriptNames(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromQuery] string? searchFilter = null,
    [FromQuery] ScriptCategory? scriptCategory = null,
    [FromQuery] string searchType = "Name and Content")
  {
    // Use RBAC query filtering - automatically handles tenant scoping at database level
    var filterExpression = permissionFilterBuilder.BuildFilterExpression<Script, IScriptsViewPermission>();
    var scripts = ctx.GetAllScripts().Where(filterExpression);

    if (scriptCategory != null)
    {
      scripts = scripts.Where(a => a.ScriptCategory == scriptCategory);
    }

    if (!string.IsNullOrEmpty(searchFilter))
    {
      if (searchType == "Name and Content")
      {
        scripts = scripts
          .Where(a => EF.Functions.ILike(a.Name + " " + a.Action, $"%{searchFilter}%"));
      }
      else if (searchType == "Name")
      {
        scripts = scripts
          .Where(a => EF.Functions.ILike(a.Name, $"%{searchFilter}%"));
      }
      else if (searchType == "Content")
      {
        scripts = scripts
          .Where(a => EF.Functions.ILike(a.Action, $"%{searchFilter}%"));
      }
    }

    IQueryable<GetScriptNameResponse> res;


    if (string.IsNullOrEmpty(searchFilter) || searchType == "Name")
    {
      // if we don't have a search filter, do not include the script contents
      res = scripts.Select(a => new GetScriptNameResponse
      {
        Id = a.Id,
        Name = a.Name,
        Category = a.ScriptCategory,
        UpdatedDateUtc = a.UpdatedDate,
        UpdatedBy = a.UpdatedByUser != null && a.UpdatedByUser.Person != null ? a.UpdatedByUser.Person.DisplayName : null
      });
    }
    else
    {
      // if we do have a search filter, include the script contents so we can render occurence highlighting
      res = scripts.Select(a => new GetScriptNameResponse
      {
        Id = a.Id,
        Name = a.Name,
        Category = a.ScriptCategory,
        UpdatedDateUtc = a.UpdatedDate,
        UpdatedBy = a.UpdatedByUser != null && a.UpdatedByUser.Person != null ? a.UpdatedByUser.Person.DisplayName : null,
        Action = a.Action
      });
    }

    return Ok(res.OrderBy(a => a.Name));
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.Search)]
  public ActionResult<List<ScriptSearchResult>> Search(
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromServices] ImmybotDbContext localCtx,
    [FromServices] SoftwareDbContext globalCtx,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromQuery] bool globalOnly = true,
    [FromQuery] AppSieveModel? sieveModel = null,
    [FromQuery] bool localOnly = false)
  {
    IQueryable<Script>? localScripts = null;
    IQueryable<Script>? globalScripts = null;

    if (!globalOnly)
    {
      // Use RBAC query filtering - automatically handles tenant scoping at database level
      var filterExpression = permissionFilterBuilder.BuildFilterExpression<Script, IScriptsViewPermission>();
      localScripts = localCtx.GetAllScripts().Where(filterExpression);

      if (sieveModel is not null) localScripts = sieveProcessor.Apply(sieveModel, localScripts);
    }

    if (!localOnly)
    {
      globalScripts = globalCtx.GetAllScripts();
      if (sieveModel != null) globalScripts = sieveProcessor.Apply(sieveModel, globalScripts);
    }

    var localRes =
      localScripts?.OrderByDescending(a => a.UpdatedDate).Select(a => new ScriptSearchResult(a)).ToList() ?? [];
    var globalRes =
      globalScripts?.OrderByDescending(a => a.UpdatedDate).Select(a => new ScriptSearchResult(a)).ToList() ??
      [];

    return Ok(localRes.Concat(globalRes));
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetAllLocalScripts)]
  public async Task<ActionResult<IQueryable<GetLocalScriptResponse>>> GetAllLocalScripts(
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromQuery] AppSieveModel? sieveModel = null)
  {
    // Use RBAC query filtering - automatically handles tenant scoping at database level
    var filterExpression = permissionFilterBuilder.BuildFilterExpression<Script, IScriptsViewPermission>();
    var q = ctx.GetAllScripts().Where(filterExpression);
    if (sieveModel != null) q = sieveProcessor.Apply(sieveModel, q);

    // Check if user has global tenant scope (tenant:* scoped claim) to determine response type
    if (await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<IScriptsViewPermission>(User, strict: false))
    {
      var scripts = q.Select(GetLocalScriptResponseForMsp.Projection).OrderByDescending(a => a.UpdatedDateUTC);
      return Ok(scripts);
    }
    else
    {
      var scripts = q.Select(GetLocalScriptResponse.Projection).OrderByDescending(a => a.UpdatedDateUTC);
      return Ok(scripts);
    }
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetAllGlobalScripts)]
  public ActionResult<IQueryable<GetGlobalScriptResponse>> GetAllGlobalScripts(
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromServices] SoftwareDbContext ctx,
    [FromQuery] AppSieveModel? sieveModel = null)
  {
    var q = ctx.GetAllScripts();
    if (sieveModel != null) q = sieveProcessor.Apply(sieveModel, q);
    var scripts = q.Select(GetGlobalScriptResponse.Projection).OrderByDescending(a => a.UpdatedDateUTC);
    return Ok(scripts);
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetLocalScript)]
  public async Task<ActionResult<GetLocalScriptResponse>> GetLocalScript(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromRoute] int scriptId)
  {
    // Use RBAC resource authorization - retrieves and authorizes in one step
    var script = await _resourceAuthorizerFlow.GetAuthorizedResourceOrFailAsync<Script, IScriptsViewPermission>(
      new DefaultKeyParameters(scriptId));

    // TODO: Revisit this logic to handle custom roles where users can have permissions
    // to view other tenants but aren't MSP users. Future custom roles may grant users
    // access to view any number of other tenants. Consider using RBAC claims to determine
    // response type rather than simple MSP check.
    if (await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<IScriptsViewPermission>(User, strict: false))
      return Ok(new GetLocalScriptResponseForMsp(script));
    else
      return Ok(new GetLocalScriptResponse(script, _userService.GetTenantId()));
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetGlobalScript)]
  public ActionResult<GetGlobalScriptResponse> GetGlobalScript(
    [FromServices] SoftwareDbContext ctx,
    [FromRoute] int scriptId)
  {
    // Normally this would be retrieved by a ResourceAccessor<Script> in the resource auth flow.
    // However, global script changes are secred by IGlobalManagePermission and do not have resource tenant scopes.
    var script = ctx.GetScriptById(scriptId);
    if (script == null) return NotFound();

    return Ok(new GetGlobalScriptResponse(script));
  }

  [SubjectPermissionAuthorize(typeof(IScriptsManagePermission))]
  [HttpPost(ScriptApiRoutes.UpdateLocalScript)]
  public async Task<ActionResult<GetLocalScriptResponse>> UpdateLocalScript(
    [FromRoute] int scriptId,
    [FromServices] IEntityValidator entityValidator,
    [FromServices] IProviderRegistrationService providerRegistrationService,
    [FromServices] IProviderActions providerActions,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> localDbFactory,
    [FromServices] IPublicScriptsContainerService publicScripts,
    [FromServices] IFunctionScriptManager functionScriptManager,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromBody] UpdateLocalScriptRequestBody body,
    [FromQuery] bool ignoreSyntaxErrors = false)
  {
    await using var ctx = localDbFactory();
    // Use RBAC resource authorization - retrieves and authorizes in one step
    var existing = await _resourceAuthorizerFlow.GetAuthorizedResourceOrFailAsync<Script, IScriptsManagePermission>(
      new DefaultKeyParameters(scriptId));

    var errors = entityValidator.ValidateScript(body, existing, ignoreSyntaxErrors: ignoreSyntaxErrors);
    if (errors.HasErrors) return new UnprocessableEntityObjectResult(errors);

    ScrubScript(body);

    var tenantRelationshipAuthorizationResult = await subjectPermissionAuthorizationService
      .AuthorizeTenantRelationshipsAsync<IScriptsManagePermission>(
      User,
      relationships: body.Tenants.OfType<ITenantRelationship>().ToList());

    if (tenantRelationshipAuthorizationResult.RequiresOwnedRelationship)
    {
      var currentUser = _userService.GetCurrentUser();
      body.Tenants.Add(new TenantScript { TenantId = currentUser.TenantId, Relationship = Relationship.Owned });
    }

    if (existing is { ScriptCacheName: { Length: > 0 } existingCacheName, PublicStorageDownloadUrl: { Length: > 0 }}
        && existing.ScriptCacheName != body.ScriptCacheName)
    {
      publicScripts.RemovePublicScript(existingCacheName, CancellationToken.None);
    }
    if (body is { ScriptCategory: ScriptCategory.DeviceInventory, ScriptCacheName: { Length: > 0 } cacheName })
    {
      var (url, hash) = publicScripts.UploadOrCreatePublicScript(
        cacheName,
        body.Action,
        CancellationToken.None,
        signScript: true);
      body.PublicStorageDownloadUrl = url;
      body.ScriptHash = hash;
    }

    var updated = ctx.UpdateScript(scriptId, body);
    if (updated is null) return NotFound();

    // if this is a function script then bust the function script cache
    if (updated.ScriptCategory is ScriptCategory.Function)
    {
      functionScriptManager.InvalidateCache();
    }

    // also update integration types and reload integrations if this is an integration script
    if (updated.ScriptCategory is ScriptCategory.Integration)
    {
      var integrationType = await ctx.GetAllDynamicIntegrationTypes()
        .Include(a => a.Script)
        .Include(a => a.Logo)
        .FirstOrDefaultAsync(a => a.ScriptId == updated.Id);

      if (integrationType is not null)
      {
        await providerRegistrationService.ReloadRegistration(integrationType, HttpContext.RequestAborted);
        var integrations = await ctx.GetProviderLinks()
          .Where(a => a.ProviderTypeId == integrationType.IntegrationTypeId)
          .ToListAsync();
        foreach (var integration in integrations)
        {
          await providerActions.ReloadProvider(integration, HttpContext.RequestAborted);
        }
      }
    }


    if (await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<IScriptsManagePermission>(User, strict: false))
      return Ok(new GetLocalScriptResponseForMsp(updated));
    else
      // Question: For custom roles, can a user update a script for a tenant other than their own?
      // Probably need to see what this looks like from the front end perspective...
      return Ok(new GetLocalScriptResponse(updated, _userService.GetTenantId()));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(ScriptApiRoutes.UpdateGlobalScript)]
  public async Task<ActionResult<GetGlobalScriptResponse>> UpdateGlobalScript(
    [FromRoute] int scriptId,
    [FromServices] IEntityValidator entityValidator,
    [FromServices] IProviderRegistrationService providerRegistrationService,
    [FromServices] IProviderActions providerActions,
    [FromServices] UserBearingDbFactory<SoftwareDbContext> globalDbFactory,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] IFunctionScriptManager functionScriptManager,
    [FromBody] UpdateGlobalScriptRequestBody body,
    [FromQuery] bool ignoreSyntaxErrors = false)
  {
    await using var ctx = globalDbFactory();
    var existing = ctx.GetScriptById(scriptId);
    if (existing is null || existing.ReadOnly) return BadRequest(new InvalidOperationException());
    // Authorization handled by [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]

    var errors = entityValidator.ValidateScript(body, existing, ignoreSyntaxErrors: ignoreSyntaxErrors);
    if (errors.HasErrors) return new UnprocessableEntityObjectResult(errors);

    ScrubScript(body);

    var updated = ctx.UpdateScript(scriptId, body);
    if (updated is null) return NotFound();

    // if this is a function script then bust the function script cache
    if (updated.ScriptCategory is ScriptCategory.Function)
    {
      functionScriptManager.InvalidateCache();
    }

    // also update integration types and reload integrations if this is an integration script
    if (updated.ScriptCategory != ScriptCategory.Integration)
    {
      return Ok(new GetGlobalScriptResponse(updated));
    }

    var integrationType = await ctx.GetAllDynamicIntegrationTypes()
      .Include(a => a.Script)
      .Include(a => a.Logo)
      .FirstOrDefaultAsync(a => a.ScriptId == updated.Id);

    if (integrationType == null)
    {
      return Ok(new GetGlobalScriptResponse(updated));
    }

    await providerRegistrationService.ReloadRegistration(integrationType, HttpContext.RequestAborted);
    await using var localCtx = ctxFactory();
    var integrations = await localCtx.GetProviderLinks()
      .Where(a => a.ProviderTypeId == integrationType.IntegrationTypeId)
      .ToListAsync();
    foreach (var integration in integrations)
    {
      await providerActions.ReloadProvider(integration, HttpContext.RequestAborted);
    }

    return Ok(new GetGlobalScriptResponse(updated));
  }

  [SubjectPermissionAuthorize(typeof(IScriptsManagePermission))]
  [HttpPost(ScriptApiRoutes.CreateLocalScript)]
  public async Task<ActionResult<GetLocalScriptResponse>> CreateLocalScript(
    [FromServices] IEntityValidator entityValidator,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> localDbFactory,
    [FromBody] CreateLocalScriptRequestBody body,
    [FromServices] IFunctionScriptManager functionScriptManager,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromQuery] bool ignoreSyntaxErrors = false)
  {
    await using var ctx = localDbFactory();

    var errors = entityValidator.ValidateScript(body, null, ignoreSyntaxErrors);
    if (errors.HasErrors) return new UnprocessableEntityObjectResult(errors);

    ScrubScript(body);

    var tenantRelationshipAuthorizationResult = await subjectPermissionAuthorizationService
      .AuthorizeTenantRelationshipsAsync<IScriptsManagePermission>(
        User,
        relationships: body.Tenants.OfType<ITenantRelationship>().ToList());

    if (tenantRelationshipAuthorizationResult.RequiresOwnedRelationship)
    {
      var currentUser = _userService.GetCurrentUser();
      body.Tenants.Add(new TenantScript { TenantId = currentUser.TenantId, Relationship = Relationship.Owned });
    }

    var created = ctx.CreateScript(body);

    // if this is a function script then bust the function script cache
    if (created.ScriptCategory is ScriptCategory.Function)
    {
      functionScriptManager.InvalidateCache();
    }

    // Check if user has global tenant scope (tenant:* scoped claim) to determine response type
    if (await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<IScriptsManagePermission>(User, strict: false))
      return Ok(new GetLocalScriptResponseForMsp(created));
    else
      return Ok(new GetLocalScriptResponse(created, _userService.GetTenantId()));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(ScriptApiRoutes.CreateGlobalScript)]
  public ActionResult<GetGlobalScriptResponse> CreateGlobalScript(
    [FromServices] UserBearingDbFactory<SoftwareDbContext> globalDbFactory,
    [FromServices] IEntityValidator entityValidator,
    [FromServices] IFunctionScriptManager functionScriptManager,
    [FromBody] CreateGlobalScriptRequestBody body,
    [FromQuery] bool ignoreSyntaxErrors = false)
  {
    using var ctx = globalDbFactory();

    var errors = entityValidator.ValidateScript(body, ignoreSyntaxErrors: ignoreSyntaxErrors);
    if (errors.HasErrors) return new UnprocessableEntityObjectResult(errors);

    ScrubScript(body);

    var created = ctx.CreateScript(body);

    // if this is a function script then bust the function script cache
    if (created.ScriptCategory is ScriptCategory.Function)
    {
      functionScriptManager.InvalidateCache();
    }
    return Ok(new GetGlobalScriptResponse(created));
  }

  [SubjectPermissionAuthorize(typeof(IScriptsManagePermission))]
  [HttpDelete(ScriptApiRoutes.DeleteLocalScript)]
  public async Task<ActionResult> DeleteLocalScript(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> localDbFactory,
    [FromServices] IFunctionScriptManager functionScriptManager,
    [FromRoute] int scriptId)
  {
    using var ctx = localDbFactory();
    // Use RBAC resource authorization - retrieves and authorizes in one step
    var script = await _resourceAuthorizerFlow.GetAuthorizedResourceOrFailAsync<Script, IScriptsManagePermission>(
      new DefaultKeyParameters(scriptId));
    if (script.ReadOnly) return BadRequest(new InvalidOperationException());
    ctx.DeleteScript(script);

    // if this is a function script then bust the function script cache
    if (script.ScriptCategory is ScriptCategory.Function)
    {
      functionScriptManager.InvalidateCache();
    }

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpDelete(ScriptApiRoutes.DeleteGlobalScript)]
  public ActionResult DeleteGlobalScript(
    [FromServices] UserBearingDbFactory<SoftwareDbContext> globalDbFactory,
    [FromServices] IFunctionScriptManager functionScriptManager,
    [FromRoute] int scriptId)
  {
    using var ctx = globalDbFactory();
    var script = ctx.GetScriptById(scriptId);
    if (script == null) return NotFound();
    if (script.ScriptCategory == ScriptCategory.ImmySystem)
      return BadRequest("Cannot delete immy system scripts");
    if (script.ReadOnly) return BadRequest(new InvalidOperationException());
    // Authorization handled by [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
    ctx.DeleteScript(script);

    // if this is a function script then bust the function script cache
    if (script.ScriptCategory is ScriptCategory.Function)
    {
      functionScriptManager.InvalidateCache();
    }

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpPost(ScriptApiRoutes.ScriptSyntaxCheck)]
  public ActionResult<SyntaxCheckerResult> SyntaxCheck(
    [FromBody] ScriptSyntaxCheckRequestBody body)
  {
    SyntaxCheckerResult? syntax = null;
    switch (body.ScriptLanguage)
    {
      case ScriptLanguage.PowerShell:
        syntax = PowershellSyntaxChecker.CheckSyntax(body.Script);
        break;
      default:
        throw new NotSupportedException("Only powershell and command line are supported");
    }
    return Ok(syntax);
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetScriptReferenceCounts)]
  public async Task<ActionResult<ScriptReferenceCounts>> GetScriptReferenceCounts(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] SoftwareDbContext softwareCtx,
    [FromServices] IScriptActions scriptActions,
    [FromQuery] DatabaseType scriptType,
    [FromQuery] int id)
  {
    Script? script;
    if (scriptType == DatabaseType.Global)
    {
      script = softwareCtx.GetScriptById(id);
      if (script == null) return NotFound();
      // Global scripts use subject permissions - authorization handled by method-level attribute
    }
    else
    {
      // Use RBAC resource authorization - retrieves and authorizes in one step
      script = await _resourceAuthorizerFlow.GetAuthorizedResourceOrFailAsync<Script, IScriptsViewPermission>(
        new DefaultKeyParameters(id));
    }

    var count = scriptActions.GetScriptReferenceCounts(script, localCtx: ctx, globalCtx: softwareCtx);
    return Ok(count);
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetGlobalScriptReferences)]
  public ActionResult<List<ScriptReference>> GetGlobalScriptReferences(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] SoftwareDbContext globalCtx,
    [FromServices] IScriptActions scriptActions,
    [FromRoute] int scriptId)
  {
    var script = globalCtx.GetScriptById(scriptId);
    if (script == null) return NotFound();

    // a global script can be referenced from both local and global items

    // fetch all software, versions, and tasks that utilize the provided script
    var references = scriptActions.GetScriptReferences(script, ctx, globalCtx);
    return Ok(references);
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetLocalScriptReferences)]
  public async Task<ActionResult<List<ScriptReference>>> GetLocalScriptReferences(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] SoftwareDbContext globalCtx,
    [FromServices] IScriptActions scriptActions,
    [FromRoute] int scriptId)
  {
    // Use RBAC resource authorization - retrieves and authorizes in one step
    var script = await _resourceAuthorizerFlow.GetAuthorizedResourceOrFailAsync<Script, IScriptsViewPermission>(
      new DefaultKeyParameters(scriptId));

    // fetch all software, versions, and tasks that utilize the provided script
    var references = scriptActions.GetScriptReferences(script, ctx, globalCtx);
    return Ok(references);
  }

  [SubjectPermissionAuthorize(typeof(IScriptsManagePermission))]
  [HttpPost(ScriptApiRoutes.DuplicateScript)]
  public async Task<ActionResult<GetLocalScriptResponse>> DuplicateScript(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> localDbFactory,
    [FromServices] UserBearingDbFactory<SoftwareDbContext> globalDbFactory,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromBody] DuplicateScriptRequestBody body)
  {
    using var localCtx = localDbFactory();
    using var globalCtx = globalDbFactory();
    Script? script;
    if (body.ScriptType == DatabaseType.Global)
    {
      script = globalCtx.GetScriptById(body.Id);
      if (script == null) return NotFound();
      // Global scripts use subject permissions - authorization handled by method-level attribute
    }
    else
    {
      // Use RBAC resource authorization - retrieves and authorizes in one step
      script = await _resourceAuthorizerFlow.GetAuthorizedResourceOrFailAsync<Script, IScriptsViewPermission>(
        new DefaultKeyParameters(body.Id));
      if (script.ScriptCategory == ScriptCategory.ImmySystem)
        return BadRequest("Cannot duplicate local immy system script - you must update the existing script");
    }

    // duplicate and save
    var createRequest = CreateLocalScriptRequestBody.FromScript(script);
    if (script.ScriptCategory == ScriptCategory.ImmySystem)
    {
      // TODO: Consider if system script duplication should be controlled by specific subject permissions
      // rather than MSP flag. Future RBAC permissions might grant non-MSP users system script access?
      if (!await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<IScriptsManagePermission>(User, strict: false))
        return BadRequest("Only MSP users can duplicate system scripts");

      if (await localCtx.Scripts.AnyAsync(s => s.Name == script.Name && s.ScriptCategory == script.ScriptCategory))
        return BadRequest("Cannot duplicate global immy system script - it has already been duplicated - you must update the existing script");
    }
    else
    {
      // Don't change the name of immy system scripts
      createRequest.Name += " - DUPLICATED";
    }

    // assign ownership for non-msps
    var currentUser = _userService.GetCurrentUser();
    if (!currentUser.IsMsp)
    {
      createRequest.Tenants.Clear();
      createRequest.Tenants.Add(new TenantScript
      {
        Relationship = Relationship.Owned, TenantId = currentUser.TenantId
      });
    }

    var duplicate = localCtx.CreateScript(createRequest);

    // TODO: Revisit this logic to handle custom roles where users can have permissions
    // to duplicate scripts but aren't MSP users. Future custom roles may grant users
    // access to duplicate scripts across multiple tenants. Consider using RBAC claims to determine
    // response type rather than simple MSP check.
    if (await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<IScriptsManagePermission>(User, strict: false))
      return Ok(new GetLocalScriptResponseForMsp(duplicate));
    else
      return Ok(new GetLocalScriptResponse(duplicate, currentUser.TenantId));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpGet(ScriptApiRoutes.MigrateLocalToGlobalWhatIf)]
  public ActionResult<MigrationPreviewResponse> MigrateLocalToGlobalWhatIf(
    [FromRoute] int scriptId,
    [FromServices] ILocalToGlobalMigrator localToGlobalMigrator
    )
  {
    try
    {
      var data = localToGlobalMigrator.MigrateLocalScriptToGlobalScriptWhatIf(scriptId);
      return Ok(data);
    }

    catch (LocalToGlobalMigrationFailedException ex)
    {
      throw new ImmyWebException(new HttpProblem
      {
        Title = "Local-to-global script migration failed",
        Detail = ex.Message,
        Status = System.Net.HttpStatusCode.BadRequest,
      });
    }
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(ScriptApiRoutes.MigrateLocalToGlobal)]
  public ActionResult<int> MigrateLocalToGlobal(
    [FromRoute] int scriptId,
    [FromServices] ILocalToGlobalMigrator localToGlobalMigrator
    )
  {
    try
    {
      var globalScriptId = localToGlobalMigrator.MigrateLocalScriptToGlobalScript(scriptId, _userService.GetCurrentUser());
      return Ok(globalScriptId);
    }
    catch (LocalToGlobalMigrationFailedException ex)
    {
      throw new ImmyWebException(new HttpProblem
      {
        Title = "Local-to-global script migration failed",
        Detail = ex.Message,
        Status = System.Net.HttpStatusCode.BadRequest,
      });
    }
  }

  [SubjectPermissionAuthorize(typeof(IScriptsManagePermission))]
  [HttpPost(ScriptApiRoutes.GetScriptVariablesAndParameters)]
  public async Task<ActionResult<GetScriptVariablesAndParametersResponse>> GetScriptVariablesAndParameters(
    [FromServices] IRunContextFactory runContextFactory,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] SoftwareDbContext globalCtx,
    [FromBody] GetScriptVariablesAndParametersRequest request)
  {
    IRunContext? runContext = null;
    try
    {
      var currentUser = _userService.GetCurrentUser();
      MaintenanceTask? task = null;
      Software? software = null;

      // computer
      if (request.ComputerId is int computerId
        && request.ScriptExecutionContext is not ScriptExecutionContext.CloudScript)
      {
        // Use RBAC resource authorization for computer access
        await _resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
          new DefaultKeyParameters(computerId), strict: true);
        runContext = await runContextFactory.GenerateComputerOneOffRunContext(computerId,
          CancellationToken.None,
          manuallyTriggeredBy: currentUser);
      }
      // tenant
      else if (request.ScriptCategory is ScriptCategory.FilterScriptDeploymentTarget
        || request.ScriptExecutionContext is ScriptExecutionContext.CloudScript)
      {
        var tenantId = request.TenantId ?? _userService.GetTenantId();
        if (request.TenantId.HasValue)
        {
          await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IScriptsManagePermission>(
            User,
            tenantId,
            strict: true);
        }

        runContext = await runContextFactory.GenerateTenantRunContext(currentUser, tenantId, CancellationToken.None);
      }
      else
      {
        return BadRequest("ComputerId or TenantId is required");
      }

      // software
      if (request.SoftwareId is int softwareId
          && request.SoftwareType is SoftwareType softwareType
          && request.ScriptCategory is
            ScriptCategory.DynamicVersions
            or ScriptCategory.DownloadInstaller
            or ScriptCategory.SoftwareDetection
            or ScriptCategory.SoftwareVersionAction)
      {
        if (softwareType is SoftwareType.LocalSoftware)
        {
          await _resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
            new DefaultKeyParameters(softwareId),
            true);
        }

        switch (softwareType)
        {
          case SoftwareType.LocalSoftware:
            software = await ctx.Software.AsNoTracking().FirstOrDefaultAsync(a => a.Id == softwareId);
            break;
          case SoftwareType.GlobalSoftware:
            software = await globalCtx.Software.AsNoTracking().FirstOrDefaultAsync(a => a.Id == softwareId);
            break;
          default:
            break;
        }
      }

      // task
      if (request.TaskId is int taskId
        && request.TaskType is DatabaseType taskType
        && request.ScriptCategory is ScriptCategory.MaintenanceTaskSetter)
      {
        switch (taskType)
        {
          case DatabaseType.Local:
            task = ctx.GetMaintenanceTask(taskId);
            break;
          case DatabaseType.Global:
            task = globalCtx.GetMaintenanceTask(taskId);
            break;
          default:
            break;
        }
      }

      if (task is { DatabaseType: DatabaseType.Local })
      {
        // Use RBAC resource authorization for local maintenance task access
        await _resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<MaintenanceTask, IMaintenanceTasksViewPermission>(
          new DefaultKeyParameters(task.Id), strict: true);
      }
      else if (task is { DatabaseType: DatabaseType.Global })
      {
        // Global maintenance tasks use subject permissions - authorization handled by method-level attribute
      }

      var contextParameters = new ScriptContextParameters
      {
        MaintenanceTask = task,
        Software = software,
      };

      var script = new Script { Name = "" };
      script = await script.SetRunContextParameters(runContext, contextParameters, CancellationToken.None);
      return Ok(
        new GetScriptVariablesAndParametersResponse(script.Variables, script.Parameters));
    }
    finally
    {
      runContext?.Dispose();
    }
  }

  /// <summary>
  /// Execute a cloud script that returns results of Get-Command
  /// </summary>
  /// <param name="runContextFactory"></param>
  /// <param name="ctx"></param>
  /// <param name="req"></param>
  /// <param name="cancellationToken"></param>
  /// <returns></returns>
  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.FindFunctions)]
  public async Task<ActionResult<List<GetCommandResult>>> FindFunctions(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IGetCommandListCmd cmd,
    CancellationToken cancellationToken)
  {
    var currentUser = _userService.GetCurrentUser();
    return Ok(await cmd.Run(currentUser, cancellationToken));
  }

  /// <summary>
  /// Execute a cloud script that returns the syntax for a specific command
  /// </summary>
  /// <param name="runContextFactory"></param>
  /// <param name="req"></param>
  /// <param name="cancellationToken"></param>
  /// <returns></returns>
  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpPost(ScriptApiRoutes.GetFunctionSyntax)]
  public async Task<ActionResult<string>> GetFunctionSyntax(
    [FromServices] ImmybotDbContext ctx,
   [FromServices] IGetCommandSyntaxCmd cmd,
   [FromBody] GetFunctionSyntaxRequest req,
   CancellationToken cancellationToken)
  {
    var currentUser = _userService.GetCurrentUser();
    return Ok(await cmd.Run(currentUser, req.FunctionName, cancellationToken));
  }


  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpPost(ScriptApiRoutes.ValidateParamBlockParameters)]
  public async Task<ActionResult<DynamicFormBindResult>> ValidateParamBlockParameters(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IDynamicFormService dynamicFormService,
    [FromServices] IRunContextFactory runContextFactory,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromBody] ValidateParamBlockParametersFromScriptRequest request,
    CancellationToken cancellationToken)
  {
    var tenantId = _userService.GetTenantId();
    var currentUser = _userService.GetCurrentUser();

    // TODO: Revisit this logic to handle custom roles where users can have permissions
    // to validate parameters across multiple tenants but aren't MSP users. Future custom roles may grant users
    // access to validate parameters across tenant boundaries. Consider using RBAC claims to determine
    // tenant access rather than simple MSP check.
    if (request.TenantId.HasValue && await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<IScriptsViewPermission>(User, strict: false))
    {
      tenantId = request.TenantId.Value;
    }

    IRunContext? runContext = null;
    try
    {
      if (request.MaintenanceSessionId is { } maintenanceSessionId)
      {
        var sessionRunContext = await runContextFactory.GenerateSessionRunContext(
          maintenanceSessionId,
          actionIdToRerun: null,
          cancellationToken);
        runContext = sessionRunContext;
      }
      else if (request.ComputerId is { } computerId)
      {
        // Use RBAC resource authorization for computer access
        await _resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
          new DefaultKeyParameters(computerId), strict: true);

        runContext = await runContextFactory.GenerateComputerOneOffRunContext(
          computerId,
          cancellationToken,
          manuallyTriggeredBy: currentUser);
      }
      else
      {
        runContext = await runContextFactory.GenerateTenantRunContext(
          currentUser,
          tenantId,
          cancellationToken);
      }

      if (request.TerminalId.HasValue)
      {
        runContext.Args.MetascriptInvoker.SetTerminalId(request.TerminalId.Value);
      }

      var isMsp = await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<IScriptsViewPermission>(User, strict: false);
      var canAccessParentTenant = runContext.CanAccessParentTenant();
      var ret = await dynamicFormService.BindParameters(
        canAccessMspResources: isMsp,
        canAccessParentTenant: canAccessParentTenant,
        runContext,
        request.Script,
        request.DatabaseType,
        cancellationToken,
        request.ParameterValues ?? [],
        ignoreCache: request.ForceRebind);

      return Ok(ret.GetBaseResult());
    }
    finally
    {
      runContext?.Dispose();
    }

  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpPost(ScriptApiRoutes.DoesScriptHaveParamBlock)]
  public ActionResult<bool> DoesScriptHaveParamBlock(
    [FromBody] DoesScriptHaveParamBlockRequest request)
  {
    var scriptBlockAst = ScriptBlock.Create(request.Script).Ast as ScriptBlockAst;
    var hasParamBlock = scriptBlockAst?.ParamBlock is not null || scriptBlockAst?.DynamicParamBlock is not null;
    return Ok(hasParamBlock);
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetDisabledPreflightScripts)]
  public ActionResult<List<DisabledPreflightScriptResponse>> GetDisabledPreflightScripts(
    [FromServices] ImmybotDbContext ctx)
  {
    var scripts = ctx.DisabledPreflightScripts
      .AsNoTracking()
      .Select(a => new DisabledPreflightScriptResponse
      {
        Id = a.Id, DatabaseType = a.DatabaseType, ScriptId = a.ScriptId,
      });

    return Ok(scripts);
  }

  [SubjectPermissionAuthorize(typeof(IScriptsManagePermission))]
  [HttpPost(ScriptApiRoutes.SetPreflightScriptEnablement)]
  public async Task<ActionResult<OpResult>> SetPreflightScriptEnablement(
    [FromBody] SetPreflightScriptEnablementRequest request,
    [FromServices] ISetPreflightScriptEnablementCmd cmd,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] SoftwareDbContext globalCtx)
  {
    var script = request.DatabaseType is DatabaseType.Global ?
      globalCtx.GetScriptById(request.ScriptId) :
      ctx.GetScriptById(request.ScriptId);

    if (script is null) return NotFound();

    if (script.ScriptType is DatabaseType.Global)
    {
      // Global scripts use subject permissions - authorization handled by method-level attribute
      // Note: This method requires IScriptsManagePermission which should cover global script updates
    }
    else
    {
      // Use RBAC resource authorization for local script updates
      await _resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Script, IScriptsManagePermission>(
        new DefaultKeyParameters(script.Id), strict: true);
    }

    var res = await cmd.Execute(_userService.GetCurrentUser(), script, request.IsEnabled);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetLocalScriptAudits)]
  public async Task<ActionResult<ScriptActionAuditResult>> GetLocalScriptAudits(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int scriptId,
    [FromQuery] int skip = 0)
  {
    // Use RBAC resource authorization - verify access without returning resource
    await _resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Script, IScriptsViewPermission>(
      new DefaultKeyParameters(scriptId), strict: true);

    var primaryKey = scriptId.ToString();

    // grab all audits for the script
    var audits = ctx.Audits
      .AsNoTracking()
      .Where(a => a.ObjectType == "Script" && a.PrimaryKey == primaryKey);

    // get the count of audits
    var count = await audits.CountAsync();

    // select the audit to return, skipping the number specified from the request
    var audit = await audits
      .OrderByDescending(a => a.DateTimeUtc)
      .Skip(skip)
      .FirstOrDefaultAsync();

    var ret = BuildScriptActionAuditResult(count, audit);
    return Ok(ret);
  }

  [SubjectPermissionAuthorize(typeof(IScriptsViewPermission))]
  [HttpGet(ScriptApiRoutes.GetGlobalScriptAudits)]
  public async Task<ActionResult<ScriptActionAuditResult>> GetGlobalScriptAudits(
    [FromServices] SoftwareDbContext globalCtx,
    [FromRoute] int scriptId,
    [FromQuery] int skip = 0)
  {
    var script = globalCtx.GetScriptById(scriptId);
    if (script is null) return NotFound();

    var primaryKey = scriptId.ToString();
    var audits = globalCtx.Audits
      .AsNoTracking()
      .Where(a => a.ObjectType == "Script" && a.PrimaryKey == primaryKey);

    var count = await audits.CountAsync();
    var audit = await audits
      .OrderByDescending(a => a.DateTimeUtc)
      .Skip(skip)
      .FirstOrDefaultAsync();

    var ret = BuildScriptActionAuditResult(count, audit);
    return Ok(ret);
  }

  private static ScriptActionAuditResult BuildScriptActionAuditResult(int count, Audit? audit)
  {
    if (audit is null) return new ScriptActionAuditResult(count,null);

    ScriptAction? newValues = null;
    ScriptAction? oldValues = null;
    if (!string.IsNullOrEmpty(audit.NewValues))
    {
      newValues = JsonSerializer.Deserialize<ScriptAction?>(audit.NewValues);
    }
    if (!string.IsNullOrEmpty(audit.OldValues))
    {
      oldValues = JsonSerializer.Deserialize<ScriptAction?>(audit.OldValues);
    }

    var scriptActionAudit = new ScriptActionAudit(newValues?.Action, oldValues?.Action, audit.DateTimeUtc, audit.UserDisplayName);

    return new ScriptActionAuditResult(count, scriptActionAudit);
  }

  private static void ScrubScript(IScriptDetailsBase script)
  {
    if (script.ScriptCategory == ScriptCategory.DynamicVersions)
    {
      script.ScriptExecutionContext = ScriptExecutionContext.CloudScript;
    }
  }
}
