using DevExtreme.AspNet.Data;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using LinqKit;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class NotificationsController(IUserService userService) : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(NotificationApiRoutes.GetDx)]
  public async Task<ActionResult<ICollection<Notification>>> GetDx(
    DataSourceLoadOptions loadOptions,
    [FromServices] INotificationsStore notificationsStore,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    CancellationToken token)
  {
    var currentUser = userService.GetCurrentUser();
    var userCanViewAdminNotifications = await subjectPermissionAuthorizationService
      .AuthorizeAsync<INotificationsViewAdminNotificationsPermission>(User, strict: false);

    var notifications = notificationsStore.GetNotifications(currentUser, userCanViewAdminNotifications);
    Response.RegisterForDispose(notifications);
    return Ok(await DataSourceLoader.LoadAsync(notifications.Value, loadOptions, token));
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(NotificationApiRoutes.Acknowledge)]
  public async Task<IActionResult> Acknowledge(
    [FromServices] INotificationsStore notificationsStore,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromBody] AcknowledgeNotificationsRequest request)
  {
    var userCanViewAdminNotifications = await subjectPermissionAuthorizationService
      .AuthorizeAsync<INotificationsViewAdminNotificationsPermission>(User, strict: false);
    var currentUser = userService.GetCurrentUser();

    await notificationsStore.AcknowledgeNotifications(request.NotificationIds, currentUser, userCanViewAdminNotifications);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(NotificationApiRoutes.GetUnacknowledgedNotifications)]
  public async Task<ActionResult<ICollection<Notification>>> GetUnacknowledgedNotifications(
    [FromServices] INotificationsStore notificationsStore,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromQuery] int? limit = null)
  {
    var currentUser = userService.GetCurrentUser();
    var userCanViewAdminNotifications = await subjectPermissionAuthorizationService
      .AuthorizeAsync<INotificationsViewAdminNotificationsPermission>(User, strict: false);

    using var q = notificationsStore
      .GetNotifications(currentUser, userCanViewAdminNotifications);

    var q2 =  q.Value
      .Where(a => a.Acknowledgement == NotificationAcknowledgement.Unacknowledged && !a.Resolved)
      .OrderByDescending(a => a.CreatedDate)
      .AsNoTracking();

    if (limit.HasValue)
    {
      q2 = q2.Take(limit.Value);
    }

    return Ok(await q2.ToListAsync());
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(NotificationApiRoutes.SilenceNotification)]
  public async Task<ActionResult<UserSilencedNotificationResponse?>> SilenceNotification(
    [FromServices] INotificationsStore notificationsStore,
    [FromRoute] NotificationType type,
    [FromQuery] string? objectId = null)
  {
    if (!ModelState.IsValid) return BadRequest(ModelState);

    var currentUser = userService.GetCurrentUser();

    var silenced = await notificationsStore.SilenceNotification(currentUser.Id, type, objectId);
    if (silenced is null) return NoContent();

    return Ok(UserSilencedNotificationResponse.Projection.Invoke(silenced));
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(NotificationApiRoutes.GetSilencedNotificationsForUser)]
  public ActionResult<IEnumerable<UserSilencedNotificationResponse>> GetSilencedNotificationsForUser(
    [FromServices] INotificationsStore notificationsStore)
  {
    var currentUser = userService.GetCurrentUser();

    var q = notificationsStore.GetSilencedNotificationsForUser(currentUser.Id);
    Response.RegisterForDispose(q);
    var notifications = q.Value.Select(UserSilencedNotificationResponse.Projection);
    return Ok(notifications);
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpDelete(NotificationApiRoutes.RemoveSilencedNotification)]
  public async Task<ActionResult> RemoveSilencedNotification(
    [FromServices] INotificationsStore notificationsStore,
    [FromRoute] int id)
  {
    var currentUser = userService.GetCurrentUser();

    await notificationsStore.RemoveSilencedNotification(id, currentUser.Id);

    return NoContent();
  }
}
