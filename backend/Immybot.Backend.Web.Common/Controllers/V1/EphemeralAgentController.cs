using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Immybot.Backend.Application.Lib;
using Microsoft.Extensions.Logging;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Microsoft.Extensions.Options;
using Microsoft.Net.Http.Headers;
using System.Diagnostics;
using Immybot.Backend.Application.Lib.Scripts.EphemeralAgent;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Web.Common.Contracts.V1;
using System.Security.Cryptography;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Microsoft.AspNetCore.Http.HttpResults;

namespace Immybot.Backend.Web.Common.Controllers.V1;

[ApiController]
public class EphemeralAgentController : ControllerBase
{
  private readonly IEphemeralAgentSessionStore _ephemeralAgentSessionStore;
  private readonly ILogger<EphemeralAgentController> _logger;
  private readonly IOptionsMonitor<AppSettingsOptions> _appSettings;

  public EphemeralAgentController(IEphemeralAgentSessionStore ephemeralAgentSessionStore,
    ILogger<EphemeralAgentController> logger,
    IOptionsMonitor<AppSettingsOptions> appSettings)
  {
    _logger = logger;
    _appSettings = appSettings;
    _ephemeralAgentSessionStore = ephemeralAgentSessionStore;
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(EphemeralAgentRoutes.GetDevelopmentEphemeralBinary)]
  public ActionResult GetDevelopmentEphemeralBinary()
  {
    if (_appSettings.CurrentValue is { UseLocalDevelopmentAgentInstaller: false, UseLocalEphemeralAgent: false })
    {
      return NotFound();
    }

    var agentPath = ApplicationPathHelpers.AgentBinaryDistPath;

    if (!Path.Exists(agentPath))
    {
      Debugger.Break();
      throw new FileNotFoundException(
        $"The binary '{agentPath}' does not exist! Ensure you have built an installer & agent locally, or, set 'UseLocalDevelopmentAgentInstaller' to false in the AppSettings.");
    }

    // Don't worry, FileStreamResult will dispose of the stream for us.
    var fileStream = new FileStream(agentPath, FileMode.Open, FileAccess.Read, FileShare.Read);

    return new FileStreamResult(fileStream, new MediaTypeHeaderValue("application/octet-stream"))
    {
      FileDownloadName = "Immybot.Agent.exe"
    };
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpHead(EphemeralAgentRoutes.GetDevelopmentEphemeralBinary)]
  public async Task<ActionResult> GetDevelopmentEphemeralBinaryHead()
  {
    if (_appSettings.CurrentValue is { UseLocalDevelopmentAgentInstaller: false, UseLocalEphemeralAgent: false })
    {
      return NotFound();
    }

    var agentPath = ApplicationPathHelpers.AgentBinaryDistPath;
    if (!Path.Exists(agentPath))
    {
      Debugger.Break();
      throw new FileNotFoundException(
        $"The binary '{agentPath}' does not exist! Ensure you have built an installer & agent locally, or, set 'UseLocalDevelopmentAgentInstaller' to false in the AppSettings.");
    }

    var fileInfo = new FileInfo(agentPath);
    Response.Headers.ContentLength = fileInfo.Length;
    Response.Headers.ContentType = "application/octet-stream";

    using var fs = new FileStream(agentPath, FileMode.Open, FileAccess.Read, FileShare.Read);
    var sha256 = await SHA256.HashDataAsync(fs);
    Response.Headers["x-ms-meta-sha256"] = Convert.ToHexString(sha256);
    return Ok();
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(EphemeralAgentRoutes.SessionEndpointBase)]
  public async Task<ActionResult> HandleConnectionAsync(
    [FromRoute] Guid ephemeralSessionId,
    [FromRoute] string agentInstanceId,
    [FromRoute] int providerAgentId,
    CancellationToken ct)
  {
    using var _ = _logger.BeginScope(
      "Ephemeral session {EphemeralSessionId} | agent instance {AgentInstanceId} | provider agent `{ProviderAgentId}`",
      ephemeralSessionId,
      agentInstanceId,
      providerAgentId);

    if (!HttpContext.WebSockets.IsWebSocketRequest)
    {
      _logger.LogWarning("Client tried to access Ephemeral Agent WS endpoint without upgrade request.");
      return BadRequest();
    }

    if (!_ephemeralAgentSessionStore.TryGetEphemeralAgentSession(ephemeralSessionId, out var it)
        || it is not IEphemeralAgentSessionHttpHandler agentSession)
    {
      _logger.LogWarning("Non-existent ephemeral session {ephemeralSessionId} requested.", ephemeralSessionId);
      return NotFound($"Ephemeral session {ephemeralSessionId} not found.");
    }

    var result = await agentSession.RunAgentConnectionAsync(HttpContext, agentInstanceId, providerAgentId, ct);
    if (result.IsSuccess)
      return Empty;

    switch (result.Exception)
    {
      case EphemeralAgentAlreadyConnectedException:
        // if we couldn't acquire, then we are already connected or in the process of connecting.
        // we don't need this web socket as it is likely from a second/third/etc shotgun ephemeral agent web socket attempt.
        // so we return a 423 to more accurately report why the agent is exiting instead of an ambiguous HTTP 400 error in the agent logs
        _logger.LogTrace("Agent connection already established. Responding with status code 423");
        return StatusCode(StatusCodes.Status423Locked);
      case EphemeralAgentProviderAgentNotFoundException:
        _logger.LogWarning("Non-existent provider #{providerAgentId} specified.", providerAgentId);
        return NotFound($"Provider agent #{providerAgentId} not found.");
      default:
        _logger.LogError(result.Exception, "Error establishing connection to ephemeral agent");
        return Problem(result.Exception?.Message);
    }
  }
}
