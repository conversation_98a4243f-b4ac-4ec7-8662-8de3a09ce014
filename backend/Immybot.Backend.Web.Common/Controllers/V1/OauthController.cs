using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Oauth;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.OAuth.Domain.Interfaces;
using Immybot.Backend.OAuth.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Shared.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class OauthController(IUserService userService, IResourceAuthorizerFlow resourceAuthorizerFlow) : Controller
{
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(OauthApiRoutes.BeginAuthCodeFlow)]
  public ActionResult<BeginAuthCodeFlowResponse> BeginAuthCodeFlow(
    [FromServices] IOauthHooksRepository oauthHookService,
    [FromBody] BeginAuthCodeFlowRequest body)
  {
    var id = Guid.NewGuid();
    oauthHookService.CreateHook(id, body.OauthConsentData, body.AllowSilentRefresh);
    return Ok(new BeginAuthCodeFlowResponse(id));
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(OauthApiRoutes.FailAuthCodeFlow)]
  public ActionResult FailAuthCodeFlow(
    [FromServices] IOauthHooksRepository oauthHookService,
    [FromServices] ILogger<WebHookController> logger,
    [FromBody] FailAuthCodeFlowRequest body)
  {
    var oauthHook = oauthHookService.GetHook(body.OauthHookId);
    if (oauthHook is null)
    {
      logger.LogWarning("Non-existent oauth hook {oauthId}.", body.OauthHookId);
      return NotFound("OAuth consent hook is missing - it may have expired");
    }
    if (oauthHook.OauthConsentData is null)
    {
      return BadRequest("OAuth consent data is missing from hook - you must begin the consent process with BeginAuthCodeFlow");
    }
    oauthHookService.SetHookFailed(oauthHook.Id, body.OauthErrorResponse);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(OauthApiRoutes.FinishAuthCodeFlow)]
  public async Task<ActionResult> ReceiveAuthCode(
    [FromServices] IOauthHooksRepository oauthHookService,
    [FromServices] IOauthConsentService oauthConsentService,
    [FromServices] IOauthAccessTokenStore oauthAccessTokenStore,
    [FromServices] ILogger<WebHookController> logger,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromServices] IOptions<AppSettingsOptions> appSettings,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] IAzureActions azureActions,
    [FromBody] FinishAuthCodeFlowRequest body)
  {
    var oauthHook = oauthHookService.GetHook(body.OauthHookId);
    if (oauthHook is null)
    {
      logger.LogWarning("Non-existent oauth hook {oauthHookId}.", body.OauthHookId);
      return NotFound("OAuth consent hook is missing - it may have expired");
    }
    if (oauthHook.OauthConsentData is not { } consentData)
    {
      return BadRequest("OAuth consent data is missing from hook - you must begin the consent process with BeginAuthCodeFlow");
    }

    try
    {
      // We got an auth code, so we can now complete the auth code flow
      var authResponse = await oauthConsentService.RedeemAuthCodeForAccessTokenAsync(
        new(consentData, body.AuthCode, body.RedirectUriUsedInAuthLeg),
        appLifetime.ApplicationStopping);

      if (authResponse.Value is Oauth2AccessTokenErrorResponse errorResponse)
      {
        oauthHookService.SetHookFailed(oauthHook.Id, errorResponse);
      }
      else if (authResponse.Value is Oauth2AccessTokenResponse accessTokenResponse)
      {
        _ = userService.TryGetCurrentUser(out var user);

        string tenantPrincipalId = "";
        if (!string.IsNullOrEmpty(accessTokenResponse.ClientInfo))
        {
          try
          {
            // Add padding characters to base64 string if needed
            var paddedClientInfo = accessTokenResponse.ClientInfo;
            switch (accessTokenResponse.ClientInfo.Length % 4)
            {
              case 2: paddedClientInfo += "=="; break;
              case 3: paddedClientInfo += "="; break;
            }
            var base64Bytes = Convert.FromBase64String(paddedClientInfo);
            var decodedClientInfo = System.Text.Encoding.UTF8.GetString(base64Bytes);
            var clientInfoJson = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(decodedClientInfo);
            if (clientInfoJson.TryGetProperty("utid", out var utidElement))
            {
              tenantPrincipalId = utidElement.GetString() ?? "";
            }
          }
          catch (Exception ex)
          {
            logger.LogWarning(ex, "Failed to decode client_info or extract utid");
          }
        }

        var oauthToken = await oauthAccessTokenStore.CreateOauthAccessToken(
          new CreateAccessTokenPayload(
            CreatedByUser: user,
            ConsentData: consentData,
            AccessToken: accessTokenResponse,
            TenantPrincipalId: tenantPrincipalId,
            AllowSilentRefresh: oauthHook.AllowSilentRefresh),
          appLifetime.ApplicationStopping);

        try
        {
          // TODO: Find a better place to put this
          // E.g. in an event handler defined in the azure area handling oauth token created events
          if ((oauthToken.ConsentData.Scopes?.Split(' ') ?? Array.Empty<string>()).Contains(appSettings.Value.PartnerCenterUserImpersonationScope))
          {
            await using var ctx = ctxFactory();
            var result = await azureActions
              .GetPartnerCenterOrganizationDetails(oauthToken, appLifetime.ApplicationStopping);

            if (result.Result.Value is PartnerOrganizationProfile profile)
            {
              // Successfully determined what partner tenant the provided oauth2accesstoken id is for
              oauthToken.TenantPrincipalId = profile.TenantId;
              await ctx.OauthAccessTokens.Where(t => t.Id == oauthToken.Id)
                .ExecuteUpdateAsync(a =>
                    a.SetProperty(b => b.TenantPrincipalId, profile.TenantId),
                  appLifetime.ApplicationStopping);
            }
          }
        }
        finally
        {
          oauthHookService.SetHookSucceeded(oauthHook.Id, oauthToken);
        }
      }
      else
      {
        logger.LogError("Unexpected response type from {service}: {responseType}",
          nameof(oauthConsentService),
          authResponse.ToString());
        throw new InvalidOperationException("Unexpected response type from OAuth consent service");
      }
    }
    catch (OauthException ex)
    {
      oauthHookService.SetHookFailed(oauthHook.Id, ex);
    }

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IOAuthViewPermission))]
  [HttpGet(OauthApiRoutes.ListOauthAccessTokens)]
  public async Task<ActionResult<ICollection<Oauth2AccessTokenWithTenantNameResponse>>> ListOauthAccessTokens(
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromServices] IOauthAccessTokenStore oauthAccessTokenStore,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromServices] ImmybotDbContext ctx)
  {
    var tokens = await oauthAccessTokenStore.ListOauthAccessTokens(appLifetime.ApplicationStopping);
    var tokensWithTenantName = new List<Oauth2AccessTokenWithTenantNameResponse>();
    var tenantPrincipleIds = tokens.Where(t => t.TenantPrincipalId != "").Select(t => t.TenantPrincipalId).ToList();

    var canOnlySeeOwnTokens = !await subjectPermissionAuthorizationService
      .AuthorizeGlobalAsync<IOAuthViewPermission>(User, strict: false);

    // Get names of immy tenants linked to the tokens' azure principal ids
    // Limit to only the primary links (i.e., not the ones that are split to other domains)
    var tenantDict = await ctx.Tenants
      .Where(t => t.AzureTenantLink != null && t.AzureTenantLink.ShouldLimitDomains == false &&
                  tenantPrincipleIds.Contains(t.AzureTenantLink.AzTenantId))
      .PipeIf(canOnlySeeOwnTokens, t => t.Where(t2 => t2.Id == userService.GetCurrentUser().TenantId))
      .Select(t => new { t.AzureTenantLink!.AzTenantId, t.Name, t.Id })
      .ToDictionaryAsync(t => t.AzTenantId, t => new { t.Id, t.Name });

    foreach (var token in tokens)
    {
      var t = token.TenantPrincipalId != "" ? tenantDict.GetValueOrDefault(token.TenantPrincipalId) : null;

      var newToken = new Oauth2AccessTokenWithTenantNameResponse(token, t?.Name)
      {
        ConsentData = token.ConsentData,
        AccessTokenId = token.AccessTokenId,
        TokenType = token.TokenType,
        TenantPrincipalId = token.TenantPrincipalId
      };

      tokensWithTenantName.Add(newToken);
    }
    return Ok(tokensWithTenantName);
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(OauthApiRoutes.GetOauthAccessToken)]
  public async Task<ActionResult<Oauth2AccessToken>> GetOauthAccessToken(
    [FromServices] IOauthAccessTokenStore oauthAccessTokenStore,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromRoute] int id,
    [FromRoute] string accessTokenId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Oauth2AccessToken, IOAuthViewPermission>(
      new DefaultKeyParameters(id),
      strict: true);
    try
    {
      var oauthAccessToken = await oauthAccessTokenStore.GetOauthAccessTokenById(id, appLifetime.ApplicationStopping);
      if (oauthAccessToken.AccessTokenId != accessTokenId)
        return NotFound("OAuth access token not found");
      return Ok(oauthAccessToken);
    }
    catch (EntityNotFoundException)
    {
      return NotFound("OAuth access token not found");
    }
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(OauthApiRoutes.RefreshOauthAccessToken)]
  public async Task<ActionResult<Oauth2AccessToken>> RefreshOauthAccessToken(
    [FromServices] IOauthAccessTokenStore oauthAccessTokenStore,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Oauth2AccessToken, IOAuthViewPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    oauthAccessTokenStore.SetUser(userService.GetCurrentUser());

    Oauth2AccessToken accessToken;
    try
    {
      accessToken = await oauthAccessTokenStore.RefreshTokensForOauthAccessTokenId(id, appLifetime.ApplicationStopping);
    }
    catch (EntityNotFoundException)
    {
      return NotFound("OAuth access token not found");
    }
    catch (ValidationException ex)
    {
      return BadRequest(ex.Message);
    }

    return Ok(accessToken);
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpDelete(OauthApiRoutes.DeleteOauthAccessToken)]
  public async Task<ActionResult> DeleteOauthAccessToken(
    [FromServices] IOauthAccessTokenStore oauthAccessTokenStore,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Oauth2AccessToken, IOAuthViewPermission>(
      new DefaultKeyParameters(id),
      strict: true);
    oauthAccessTokenStore.SetUser(userService.GetCurrentUser());

    try
    {
      await oauthAccessTokenStore.DeleteOauthAccessToken(id, appLifetime.ApplicationStopping);
    }
    catch (EntityNotFoundException)
    {
      return NotFound("OAuth access token not found");
    }

    return NoContent();
  }
}
