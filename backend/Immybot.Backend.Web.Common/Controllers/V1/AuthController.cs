using System.Security.Claims;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions.AccessRequestExtensions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.KeyVaultRepositories;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Manager.Domain;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Manager.Shared;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class AuthController(IUserService userService) : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(AuthApiRoutes.Get)]
  public async Task<ActionResult<GetAuthResponse>> Get(
    [FromServices] IOptions<DatabaseOptions> dbOptions,
    [FromServices] IOptions<HangfireSettings> hangfireOptions,
    [FromServices] IFeatureManager featureManager,
    [FromServices] IManagerProvidedSettings mgrSettings,
    [FromServices] IOptions<AppSettingsOptions> appSettings,
    [FromServices] IOptions<AzureActiveDirectoryAuthOptions> azAdOpts,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IHangfireWorkerCountCalculator hangfireCalculator,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService)
  {
    var hasBillingPermission = await subjectPermissionAuthorizationService
      .AuthorizeAsync<IBillingManagePermission>(User, strict: false);
    // ^ IBillingManagePermission permission because the user is going to be redirected to the customer portal

    // check if the instance is disabled in the auth request since it is earliest in the pipeline
    if (IsInstanceDisabled(mgrSettings))
    {
      if (hasBillingPermission)
      {
        // if this is a msp admin authenticated, then return 403 and make them redirect to the customer portal
        var url = await mgrSettings.GetCustomerPortalUrl();
        return StatusCode(StatusCodes.Status403Forbidden, new AuthDetail(AuthCode.RedirectCustomerPortal, url?.ToString() ?? string.Empty));
      }

      // if this is a non-msp or msp-non-admin, return a 403 instance disabled
      return StatusCode(StatusCodes.Status403Forbidden, new AuthDetail(AuthCode.InstanceDisabled, "Immybot is currently disabled."));
    }

    var currentUser = userService.GetCurrentUser();
    var dbOpts = dbOptions.Value;

    DevInstanceDetails? devInstanceDetails = null;
    var appOpts = appSettings.Value;
    if (appOpts.IsDevInstance)
    {
      devInstanceDetails = new DevInstanceDetails
      {
        BackendVersion = appOpts.BackendVersion,
        IsHangfireServerRunning = hangfireOptions.Value.RunHangfireServer,
      };
      devInstanceDetails.PostRoutes.Add("startHangfireServer", $"/{DevInstanceManagementApiRoutes.StartHangfireServer}");
      devInstanceDetails.PostRoutes.Add("stopHangfireServer", $"/{DevInstanceManagementApiRoutes.StopHangfireServer}");
    }

    var isMspAdmin = currentUser is { IsMsp: true, IsAdmin: true };

    var trackedDevicesFeature = featureManager.TrackedDevicesFeature;

    int? maximumTrackableComputers = trackedDevicesFeature != null ? trackedDevicesFeature.MaxCount ?? -1 : null;

    return base.Ok(new GetAuthResponse
    {
      Impersonating = currentUser.ImpersonatorUser is not null,
      UserId = currentUser.Id,
      TenantId = currentUser.TenantId,
      TenantName = currentUser.TenantName ?? string.Empty,
      IsAdmin = currentUser.IsAdmin,
      IsMSP = currentUser.IsMsp is true,
      HasManagementAccess = currentUser.HasManagementAccess,
      IsSupportTechnician = currentUser.IsSupportTechnician,
      IsImmySupportAccessEnabled = mgrSettings.IsImmySupportAccessEnabled,
      PersonId = currentUser.PersonId,
      DisplayName = currentUser.DisplayName ?? string.Empty,
      FirstName = currentUser.FirstName ?? string.Empty,
      LastName = currentUser.LastName ?? string.Empty,
      Email = currentUser.Email ?? string.Empty,
      CanManageCrossTenantDeployments = currentUser.CanManageCrossTenantDeployments,
      IsImmense =
        await subjectPermissionAuthorizationService.AuthorizeAsync<IGlobalManagePermission>(User, strict: false),
      DevInstanceDetails = devInstanceDetails,
      LocalSoftwareEndpoint = dbOpts.LocalBlobStorageEndpoint,
      GlobalSoftwareEndpoint = dbOpts.GlobalBlobStorageEndpoint,
      LocalPublicMediaContainerName = dbOpts.LocalPublicMediaContainerName,
      GlobalPublicMediaContainerName = dbOpts.GlobalPublicMediaContainerName,
      BackendRegAppId = isMspAdmin ? azAdOpts.Value.ClientId : null,
      UserLevelAuthSelected = mgrSettings.AzurePermissionLevel == AzurePermissionLevel.Me,
      Status = mgrSettings.SubscriptionStatus,
      PlanId = mgrSettings.SubscriptionPlanId,
      PlanPrice = mgrSettings.SubscriptionPlanPrice,
      PlanQuantity = mgrSettings.SubscriptionPlanQuantity,
      Addons = mgrSettings.SubscriptionAddons,
      TrialStartUtc = mgrSettings.SubscriptionTrialStartUtc,
      TrialEndUtc = mgrSettings.SubscriptionTrialEndUtc,
      UpdateAvailable = mgrSettings.UpdateAvailable,
      CurrentReleaseVersion = mgrSettings.CurrentRelease?.Tag,
      CurrentReleaseReleaseChannel = mgrSettings.CurrentRelease?.ReleaseChannel,
      IsInstanceUpdating = mgrSettings.IsInstanceUpdating,
      IsInstanceRestarting = mgrSettings.IsInstanceRestarting,
      InstanceUpdateSource = mgrSettings.InstanceUpdateSource,
      InstanceUpdateHasFailed = mgrSettings.InstanceUpdateHasFailed,
      Features = featureManager.GetFeatures(),
      MaxRunningSessionCount = hangfireCalculator.GetHangfireWorkerCounts().MaintenanceWorkers,
      OpenAccessRequestCount = isMspAdmin ? ctx.GetOpenAccessRequestCount() : 0,
      MaximumTrackableComputers = maximumTrackableComputers,
      InstanceReleaseChannel = mgrSettings.ReleaseChannel,
      Claims = GetClaims(),
    });
  }

  [SubjectPermissionAuthorize(typeof(IAzureOperationsUpdatePermissionLevelsPermission))]
  [HttpPost(AuthApiRoutes.UpdateAzureTenantAuthDetails)]
  public async Task<ActionResult<UpdateAzureTenantAuthDetailsCmdResult>> UpdateAzureTenantAuthDetails(
    [FromServices] IUpdateAzureTenantAuthDetailsCmd updateAuthDetailsCmd,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromBody] UpdateAzureTenantAuthDetailsCmdPayload body)
  {
    try
    {
      var cmdResult = await updateAuthDetailsCmd
        .UpdateAzureTenantAuthDetails(body, appLifetime.ApplicationStopping);
      return Ok(cmdResult);
    }
    catch (EntityNotFoundException ex)
    {
      return NotFound(ex.Message);
    }
    catch (ValidationException ex)
    {
      return BadRequest(ex.Message);
    }
  }

  [SubjectPermissionAuthorize(typeof(IAzureOperationsUpdatePermissionLevelsPermission))]
  [HttpPost(AuthApiRoutes.DeleteAzureTenantAuthDetails)]
  public async Task<ActionResult<DeleteAzureTenantAuthDetailsCmdResult>> DeleteAzureTenantAuthDetails(
  [FromServices] IUpdateAzureTenantAuthDetailsCmd updateAuthDetailsCmd,
  [FromServices] IHostApplicationLifetime appLifetime,
  [FromBody] DeleteAzureTenantAuthDetailsCmdPayload body)
  {
    try
    {
      var cmdResult = await updateAuthDetailsCmd
        .DeleteAzureTenantAuthDetails(body, appLifetime.ApplicationStopping);
      return Ok(cmdResult);
    }
    catch (EntityNotFoundException ex)
    {
      return NotFound(ex.Message);
    }
    catch (ValidationException ex)
    {
      return BadRequest(ex.Message);
    }
  }

  [SubjectPermissionAuthorize(typeof(IAzureOperationsUpdatePermissionLevelsPermission))]
  [HttpGet(AuthApiRoutes.GetAzureTenantAuthDetails)]
  public async Task<ActionResult<AzureTenantAuthDetails>> GetAzureTenantAuthDetails(
    [FromServices] IAzureTenantAuthDetailsRepository authDetailsRepository,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromRoute] string azureTenantPrincipalId)
  {
    try
    {
      var authDetails = await authDetailsRepository.GetAzureTenantAuthDetailsByAzurePrincipalId(
        azureTenantPrincipalId,
        appLifetime.ApplicationStopping);
      if (authDetails == null) return Ok(authDetails);
      return Ok(authDetails with { CustomAppRegSecret = null });
    }
    catch (EntityNotFoundException ex)
    {
      return NotFound(ex.Message);
    }
    catch (ValidationException ex)
    {
      return BadRequest(ex.Message);
    }
  }

  [SubjectPermissionAuthorize(typeof(ISystemOperationsFetchIpAddressesPermission))]
  [HttpGet(AuthApiRoutes.GetImmybotIPAddresses)]
  public async Task<ActionResult<ImmyIpAndHostnamesResponse>> GetImmybotIPAddresses(
    [FromServices] IManagerProvidedSettings manager,
    [FromServices] IOptionsMonitor<DatabaseOptions> dbOpts)
  {
    // fetch ip addresses from manager
    var res = await manager.FetchIPAddressesFromManager();
    var dbOptions = dbOpts.CurrentValue;
    var localBlobStorageEndpoint = dbOptions.LocalBlobStorageEndpoint;
    var globalBlobStorageEndpoint = dbOptions.GlobalBlobStorageEndpoint;
    return Ok(new ImmyIpAndHostnamesResponse(res, localBlobStorageEndpoint, globalBlobStorageEndpoint));
  }

  // todo: no authorization but should still have an attribute
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(AuthApiRoutes.RequestAccess)]
  public async Task<ActionResult<CommandResult>> RequestAccess(
    [FromServices] IRequestAccessCommand cmd,
    [FromServices] ICachedSingleton<ApplicationPreferences> cachedPrefs,
    [FromServices] IHostApplicationLifetime appLifetime)
  {
    if (!cachedPrefs.Value.EnableRequestAccess) return BadRequest("Access requests are not allowed.");

    _ = userService.TryGetCurrentUser(out var user);
    var principal = userService.GetCurrentPrincipal();
    return Ok(await cmd.RequestAccess(principal, user, appLifetime.ApplicationStopping));
  }

  private static bool IsInstanceDisabled(IManagerProvidedSettings mgrSettings)
  {
    // If no status, then this is an out-of-band instance, all good
    if (mgrSettings.SubscriptionStatus == null) return false;

    // If we have a status other than active or InTrial, then we need to redirect to the customer portal
    if (mgrSettings.SubscriptionStatus == SubscriptionStatus.Inactive) return true;

    // if we are in a trial but the trial is expired, then we need to redirect to the customer portal
    if (mgrSettings.SubscriptionStatus == SubscriptionStatus.InTrial && DateTime.UtcNow > mgrSettings.SubscriptionTrialEndUtc) return true;

    // all good
    return false;
  }

  private IEnumerable<ClaimResponse> GetClaims()
  {
    // Get the current user's ClaimsPrincipal
    ClaimsPrincipal? principal = userService.GetCurrentPrincipal();

    // Guard clause: Ensure principal exists
    if (principal == null) return [];

    // Extract all claims from the principal
    IEnumerable<ClaimResponse> allClaims = principal.Claims
      .Select(c => new ClaimResponse(c));

    // Return the response with all claims
    return allClaims;
  }
}
