using Immybot.Backend.Application.DbContextExtensions.Preferences;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Domain.Events;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Exceptions;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Serialization;

#pragma warning disable CS0618 // Type or member is obsolete

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class PreferencesController(
  ISubjectPermissionAuthorizationService authorizationService,
  IUserService userService)
  : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(PreferenceApiRoutes.Get)]
  public ActionResult<GetPreferencesResponse> GetPreferences(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ICachedSingleton<ApplicationPreferences> cachedAppPrefs,
    [FromServices] ICachedCollection<TenantPreferences> cachedTenantPrefs)
  {
    var currentUser = userService.GetCurrentUser();
    var userPreferences = ctx.GetUserPreferences(currentUser.Id) ?? new UserPreferences();
    var tenantPreferences = cachedTenantPrefs.Value.FirstOrDefault(a => a.TenantId == currentUser.TenantId);
    return Ok(new GetPreferencesResponse(cachedAppPrefs.Value, tenantPreferences, userPreferences));
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(PreferenceApiRoutes.GetTenantPreferences)]
  public async Task<ActionResult<TenantPreferences>> GetTenantPreferences(
    [FromRoute] int tenantId,
    [FromServices] ICachedCollection<TenantPreferences> cachedPrefs)
  {
    await authorizationService.AuthorizeTenantAsync<ITenantPreferencesViewPermission>(User, tenantId, strict: true);
    return Ok(cachedPrefs.Value.FirstOrDefault(a => a.TenantId == tenantId));
  }

  [SubjectPermissionAuthorize(typeof(IApplicationPreferencesManagePermission))]
  [HttpPatch(PreferenceApiRoutes.UpdateAppPreferences)]
  public ActionResult<ApplicationPreferences> UpdateAppPreferences(
    [FromBody] List<Operation<ApplicationPreferences>> preferencePatch,
    [FromServices] IDomainEventEmitter domainEventEmitter,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] ICachedSingleton<ApplicationPreferences> cachedPrefs)
  {
    try
    {
      using var ctx = dbFactory();
      var patch = new JsonPatchDocument<ApplicationPreferences>(preferencePatch, new DefaultContractResolver());
      var updated = ctx.PatchApplicationPreferences(patch);
      if (updated is null) return NotFound();
      cachedPrefs.Value = updated;

      // handle user preference updated event
      // todo: remove this when the new rbac ui is implemented
      if (preferencePatch.Any(a =>
            (a.path.Contains(nameof(ApplicationPreferences.AllowNonAdminsAndNonMspUsersToUseTerminalsAndEditScripts),
               StringComparison.CurrentCultureIgnoreCase) ||
             a.path.Contains(nameof(ApplicationPreferences.MspNonAdminsRequireChangeRequestsForCrossTenantDeployments),
               StringComparison.CurrentCultureIgnoreCase) ||
             a.path.Contains(nameof(ApplicationPreferences.AllowNonAdminsToManageAssignments),
               StringComparison.CurrentCultureIgnoreCase))))
      {
        domainEventEmitter.EmitEvent(new PermissionPreferenceUpdatedEvent());
      }

      return Ok(updated);
    }
    catch (JsonPatchException ex)
    {
      return BadRequest(ex);
    }
  }

  [SubjectPermissionAuthorize(typeof(ITenantPreferencesManagePermission))]
  [HttpPatch(PreferenceApiRoutes.UpdateTenantPreferences)]
  public async Task<ActionResult<TenantPreferences>> UpdateTenantPreferences(
    [FromRoute] int tenantId,
    [FromBody] List<Operation<TenantPreferences>> preferencePatch,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] ICachedCollection<TenantPreferences> cachedPrefs)
  {
    try
    {
      await authorizationService.AuthorizeTenantAsync<ITenantPreferencesManagePermission>(User, tenantId, strict: true);
      await using var ctx = dbFactory();
      var patch = new JsonPatchDocument<TenantPreferences>(preferencePatch, new DefaultContractResolver());
      var updated = ctx.PatchTenantPreferences(tenantId, patch);

      var existing = cachedPrefs.Value.FirstOrDefault(a => a.Id == updated.Id);
      if (existing != null) cachedPrefs.Value.Remove(existing);
      cachedPrefs.Value.Add(updated);

      return Ok(updated);
    }
    catch (JsonPatchException ex)
    {
      return BadRequest(ex);
    }
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPatch(PreferenceApiRoutes.UpdateUserPreferences)]
  public ActionResult<UserPreferences> UpdateUserPreferences(
    [FromBody] List<Operation<UserPreferences>> preferencePatch,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory)
  {
    try
    {
      using var ctx = dbFactory();
      var patch = new JsonPatchDocument<UserPreferences>(preferencePatch, new DefaultContractResolver());
      return Ok(ctx.PatchUserPreferences(userService.GetCurrentUser().Id, patch));
    }
    catch (JsonPatchException ex)
    {
      return BadRequest(ex);
    }
  }
}
