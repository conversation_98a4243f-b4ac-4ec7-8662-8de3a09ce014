using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using Immybot.Backend.Application.DbContextExtensions.MediaExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Application.SoftwareDbContextExtensions;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class MediaController(
  ISubjectPermissionAuthorizationService authorizationService,
  IUserService userService,
  IResourceAuthorizerFlow resourceAuthorizerFlow)
  : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(IMediaViewPermission))]
  [HttpPost(MediaApiRoutes.RequestFileDownloadUrl)]
  public ActionResult<string> GetDownloadUrl(
    [FromBody] GetFileDownloadUrlRequest request,
    [FromServices] IAzureBlobStorageSasService azureBlobStorageSasService)
  {
    // should this be authorized?
    var uri = request.DatabaseType == DatabaseType.Global
      ? azureBlobStorageSasService.GetGlobalSoftwareDownloadUrl(request.BlobName)
      : azureBlobStorageSasService.GetLocalSoftwareDownloadUrl(request.BlobName);
    return Ok(uri);
  }

  [SubjectPermissionAuthorize(typeof(IMediaViewPermission))]
  [HttpGet(MediaApiRoutes.Search)]
  public async Task<ActionResult<MediaSearchResponse>> Search(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromServices] ImmybotDbContext localCtx,
    [FromServices] SoftwareDbContext globalCtx,
    [FromQuery] AppSieveModel? sieveModel = null,
    [FromQuery] bool globalOnly = false
  )
  {
    IQueryable<Media>? localMedia = null;

    var currentTenantId = userService.GetTenantId();

    if (!globalOnly)
    {
      // Use permission filter builder for tenant-aware filtering
      var mediaFilter = permissionFilterBuilder.BuildFilterExpression<Media, IMediaViewPermission>();
      localMedia = localCtx.GetAllMedia().Where(mediaFilter);
      if (sieveModel != null) localMedia = sieveProcessor.Apply(sieveModel, localMedia);
    }

    var globalMedia = globalCtx.GetAllMedia();
    if (sieveModel != null) globalMedia = sieveProcessor.Apply(sieveModel, globalMedia);

    List<LocalMediaResponse>? localRes;
    // This condition is used so that we do not return tenant information to users that are not MSP users.
    // When converting to rbac, we'll need to consider how to handle this.
    if (await authorizationService.AuthorizeGlobalAsync<IMediaViewPermission>(User, strict: false))
      localRes = localMedia?.OrderByDescending(a => a.UpdatedDate).Select(a => new LocalMediaResponse(a)).ToList();
    else
      localRes = localMedia?.OrderByDescending(a => a.UpdatedDate)
        .Select(a => new LocalMediaResponse(a, currentTenantId)).ToList();

    var globalRes = globalMedia.OrderByDescending(a => a.UpdatedDate).Select(a => new GlobalMediaResponse(a)).ToList();

    var res = new MediaSearchResponse();
    res.Global.AddRange(globalRes);
    if (localRes is not null)
    {
      res.Local.AddRange(localRes);
    }

    return Ok(res);
  }

  #region local

  [SubjectPermissionAuthorize(typeof(IMediaViewPermission))]
  [HttpGet(MediaApiRoutes.GetLocal)]
  public ActionResult<LoadResult> GetLocal(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx,
    DataSourceLoadOptions loadOptions,
    [FromQuery] MediaCategory? category = null)
  {
    // Use permission filter builder for tenant-aware filtering
    var mediaFilter = permissionFilterBuilder.BuildFilterExpression<Media, IMediaViewPermission>();
    var q = ctx.GetAllMedia().Where(mediaFilter);

    if (category.HasValue)
      q = q.Where(a => a.Category == category);

    var source = q.Select(LocalMediaResponse.Projection).OrderByDescending(a => a.UpdatedDateUTC);

    return Ok(DataSourceLoader.Load(source, loadOptions));
  }

  [SubjectPermissionAuthorize(typeof(IMediaViewPermission))]
  [HttpGet(MediaApiRoutes.GetLocalById)]
  public async Task<ActionResult<LocalMediaResponse>> GetLocalById(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Media, IMediaViewPermission>(
      new DefaultKeyParameters(id),
      strict: true
    );
    var media = ctx.GetMediaById(id);
    if (media == null) return NotFound();

    bool isMspUser = await authorizationService.AuthorizeGlobalAsync<IMediaViewPermission>(User, strict: false);

    return Ok(isMspUser
      ? new LocalMediaResponse(media)
      : new LocalMediaResponse(media, userService.GetTenantId()));
  }

  [SubjectPermissionAuthorize(typeof(IMediaManagePermission))]
  [HttpPost(MediaApiRoutes.UpdateLocal)]
  public async Task<ActionResult<LocalMediaResponse>> UpdateLocal(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromRoute] int id,
    [FromBody] UpdateLocalMediaPayload payload
  )
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Media, IMediaManagePermission>(
      new DefaultKeyParameters(id),
      strict: true
    );
    await using var ctx = dbFactory();
    var existing = ctx.GetMediaById(id);
    if (existing == null) return NotFound();

    payload.Id = id;
    var currentUser = userService.GetCurrentUser();

    var tenantRelationshipAuthorizationResult =
      await authorizationService.AuthorizeTenantRelationshipsAsync<IMediaManagePermission>(
      User,
      relationships: payload.Tenants.OfType<ITenantRelationship>().ToList());

    if (tenantRelationshipAuthorizationResult.RequiresOwnedRelationship)
    {
      payload.Tenants.Add(new TenantMedia { TenantId = currentUser.TenantId, Relationship = Relationship.Owned });
    }

    var media = ctx.UpdateMedia(payload);
    if (media == null) return BadRequest("Failed to update local media");

    bool isMspUser = await authorizationService.AuthorizeGlobalAsync<IMediaViewPermission>(User, strict: false);

    return Ok(isMspUser
      ? new LocalMediaResponse(media)
      : new LocalMediaResponse(media, currentUser.TenantId));
  }

  [SubjectPermissionAuthorize(typeof(IMediaManagePermission))]
  [HttpDelete(MediaApiRoutes.DeleteLocal)]
  public async Task<ActionResult> DeleteLocal(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IAzureBlobStorageUploadService azureBlobStorageUploadService,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Media, IMediaManagePermission>(
      new DefaultKeyParameters(id),
      strict: true
    );
    await using var ctx = dbFactory();
    var media = ctx.GetMediaById(id);
    if (media == null) return NotFound();
    _ = await azureBlobStorageUploadService.DeleteMediaBlobIfExists(media);
    ctx.DeleteMedia(media);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IMediaManagePermission))]
  [HttpPost(MediaApiRoutes.UploadLocalMedia)]
  public async Task<ActionResult> UploadLocalMedia(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IMimeMapper mimeMapper,
    [FromServices] IAzureBlobStorageUploadService azureBlobStorageService,
    IFormFile? file,
    [FromForm] string chunkMetadata,
    [FromForm] MediaCategory mediaCategory = MediaCategory.None,
    [FromForm] int? mediaId = null)
  {
    await using var ctx = dbFactory();
    Media? existing = null;
    if (mediaId is { } id and > 0)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Media, IMediaManagePermission>(
        new DefaultKeyParameters(id),
        strict: true
      );
      existing = ctx.GetMediaById(id);
      if (existing is null) return NotFound();
    }

    if (file == null || string.IsNullOrEmpty(chunkMetadata)) return NoContent();

    var chunk = JsonConvert.DeserializeObject<ChunkMetadata>(chunkMetadata);
    if (chunk is null) return BadRequest("File chunk was could not be deserialized");

    var identifier = $"{chunk.FileGuid}-{chunk.FileName}";
    var mimeType = mimeMapper.Map(chunk.FileName);

    if (mediaCategory == MediaCategory.SoftwareIcon && !mimeType.StartsWith("image"))
    {
      return BadRequest("File must be of type image for software icons");
    }

    var azureUploadData = new AzureUploadData()
    {
      FileGuid = new Guid(chunk.FileGuid), FileName = chunk.FileName, MimeType = mimeType,
    };

    if (mediaCategory == MediaCategory.SoftwareIcon)
      await azureBlobStorageService.PrepareCloudBlockBlobForPublicMedia(DatabaseType.Local,
        identifier,
        azureUploadData);
    else
      await azureBlobStorageService.PrepareCloudBlockBlobForMedia(DatabaseType.Local, identifier, azureUploadData);

    await azureBlobStorageService.UploadChunkForBlob(identifier, file);

    var currentUser = userService.GetCurrentUser();

    // once all chunks are uploaded, commit chunks and create media object
    if (chunk.Index == chunk.TotalCount - 1)
    {
      var hash = azureBlobStorageService.GetMD5Hash(identifier);
      var blobName = await azureBlobStorageService.CommitBlocks(identifier);

      // if existing then update, otherwise create
      Media? media;
      if (existing != null)
      {
        var updatePayload = new UpdateLocalMediaPayload
        {
          Id = existing.Id,
          FileName = chunk.FileName,
          Name = existing.Name,
          BlobReference = blobName,
          RelativeCacheSourcePath = chunk.FileGuid,
          MimeType = mimeType,
          PackageHash = hash,
          Category = mediaCategory,
        };
        media = ctx.UpdateMedia(updatePayload);
      }
      else
      {
        var mediaPayload = new CreateLocalMediaPayload
        {
          FileName = chunk.FileName,
          Name = chunk.FileName,
          BlobReference = blobName,
          RelativeCacheSourcePath = chunk.FileGuid,
          MimeType = mimeType,
          PackageHash = hash,
          Category = mediaCategory,
        };

        mediaPayload.Tenants.Clear();

        // if not the msp tenant, set the media as owned by the uploaders tenant.
        if (!currentUser.IsMsp)
        {
          mediaPayload.Tenants.Add(new TenantMedia
          {
            TenantId = currentUser.TenantId, Relationship = Relationship.Owned
          });
        }

        media = ctx.CreateMedia(mediaPayload);
      }

      if (media == null) return BadRequest("Failed to upload media");
      if (await authorizationService.AuthorizeGlobalAsync<IMediaViewPermission>(User, strict: false))
        return Ok(new LocalMediaResponse(media));

      return Ok(new LocalMediaResponse(media, currentUser.TenantId));
    }

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(MediaApiRoutes.UploadSupportMedia)]
  public async Task<ActionResult> UploadSupportMedia(
    [FromServices] IMimeMapper mimeMapper,
    [FromServices] IAzureBlobStorageUploadService azureBlobStorageService,
    IFormFile? file,
    [FromForm] string chunkMetadata)
  {
    if (file == null || string.IsNullOrEmpty(chunkMetadata))
      return NoContent();

    var chunk = JsonConvert.DeserializeObject<ChunkMetadata>(chunkMetadata);
    if (chunk is null)
      return BadRequest("File chunk could not be deserialized");


    var identifier = $"{chunk.FileGuid}-{chunk.FileName}";
    var mimeType = mimeMapper.Map(chunk.FileName);

    await azureBlobStorageService.PrepareCloudBlockBlobForSupportMedia(
      identifier,
      new AzureUploadData { FileGuid = new Guid(chunk.FileGuid), FileName = chunk.FileName, MimeType = mimeType }
    );

    await azureBlobStorageService.UploadChunkForBlob(identifier, file);

    return Ok(new SupportBlobUploadResponse(
      chunk.FileName,
      chunk.Index == chunk.TotalCount - 1 ? await azureBlobStorageService.CommitBlocks(identifier) : null
    ));
}

[SubjectPermissionAuthorize(typeof(IMediaViewPermission))]
[HttpGet(MediaApiRoutes.GetLocalDownloadUrl)]
public async Task<ActionResult<string>> GetLocalDownloadUrl(
  [FromServices] ImmybotDbContext ctx,
  [FromServices] IAzureBlobStorageSasService azureBlobStorageSasService,
  [FromRoute] int id)
{
  await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Media, IMediaViewPermission>(
    new DefaultKeyParameters(id),
    strict: true
  );
  var media = ctx.GetMediaById(id);
  if (media == null) return NotFound();

  var url = azureBlobStorageSasService.GetLocalMediaDownloadUrl(media.BlobReference);
  return Ok(url);
}

#endregion local

#region global

[SubjectPermissionAuthorize(typeof(IMediaViewPermission))]
[HttpGet(MediaApiRoutes.GetGlobal)]
public ActionResult<LoadResult> GetGlobal(
  [FromServices] SoftwareDbContext ctx,
  DataSourceLoadOptions loadOptions,
  [FromQuery] MediaCategory? category = null)
{
  var q = ctx.GetAllMedia();

  if (category.HasValue)
    q = q.Where(a => a.Category == category);

  var source = q.Select(GlobalMediaResponse.Projection).OrderByDescending(a => a.UpdatedDateUTC);

  return Ok(DataSourceLoader.Load(source, loadOptions));
}

[SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
[HttpGet(MediaApiRoutes.GetGlobalById)]
public ActionResult<GlobalMediaResponse> GetGlobalById(
  [FromServices] SoftwareDbContext ctx,
  [FromRoute] int id)
{
  var media = ctx.GetMediaById(id);
  if (media == null) return NotFound();
  return Ok(new GlobalMediaResponse(media));
}

[SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
[HttpPost(MediaApiRoutes.UpdateGlobal)]
public ActionResult<GlobalMediaResponse> UpdateGlobal(
  [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory,
  [FromRoute] int id,
  [FromBody] UpdateGlobalMediaPayload payload)
{
  using var ctx = dbFactory();

  var existing = ctx.GetMediaById(id);
  if (existing == null) return NotFound();
  payload.Id = id;

  var media = ctx.UpdateMedia(payload);
  if (media == null) return BadRequest("Failed to update global media");
  return Ok(new GlobalMediaResponse(media));
}

[SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
[HttpDelete(MediaApiRoutes.DeleteGlobal)]
public async Task<ActionResult> DeleteGlobal(
  [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory,
  [FromServices] IAzureBlobStorageUploadService azureBlobStorageUploadService,
  [FromRoute] int id)
{
  await using var ctx = dbFactory();
  var media = ctx.GetMediaById(id);
  if (media == null) return NotFound();

  _ = await azureBlobStorageUploadService.DeleteMediaBlobIfExists(media);
  ctx.DeleteMedia(media);
  return NoContent();
}

[SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
[HttpPost(MediaApiRoutes.UploadGlobalMedia)]
public async Task<ActionResult> UploadGlobalMedia(
  [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory,
  [FromServices] IMimeMapper mimeMapper,
  [FromServices] IAzureBlobStorageUploadService azureBlobStorageService,
  IFormFile? file,
  [FromForm] string chunkMetadata,
  [FromForm] MediaCategory mediaCategory = MediaCategory.None,
  [FromForm] int? mediaId = null)
{
  await using var ctx = dbFactory();

  Media? existing = null;
  if (mediaId is { } id and > 0)
    existing = ctx.GetMediaById(id);

  if (file == null || string.IsNullOrEmpty(chunkMetadata)) return NoContent();

  var chunk = JsonConvert.DeserializeObject<ChunkMetadata>(chunkMetadata);
  if (chunk is null) return BadRequest("File failed to deserialize");
  var identifier = $"{chunk.FileGuid}-{chunk.FileName}";
  var mimeType = mimeMapper.Map(chunk.FileName);

  if (mediaCategory == MediaCategory.SoftwareIcon && !mimeType.StartsWith("image"))
  {
    return BadRequest("File must be of type image for software icons");
  }

  var azureUploadData = new AzureUploadData()
  {
    FileGuid = new Guid(chunk.FileGuid), FileName = chunk.FileName, MimeType = mimeType, TotalChunks = chunk.TotalCount,
  };

  if (mediaCategory == MediaCategory.SoftwareIcon)
    await azureBlobStorageService.PrepareCloudBlockBlobForPublicMedia(DatabaseType.Global,
      identifier,
      azureUploadData);
  else
    await azureBlobStorageService.PrepareCloudBlockBlobForMedia(DatabaseType.Global, identifier, azureUploadData);

  await azureBlobStorageService.UploadChunkForBlob(identifier, file);

  // once all chunks are uploaded, commit chunks and create media object
  if (chunk.Index != chunk.TotalCount - 1)
  {
    return NoContent();
  }

  var hash = azureBlobStorageService.GetMD5Hash(identifier);
  var blobName = await azureBlobStorageService.CommitBlocks(identifier);

  // if existing then update, otherwise create
  Media? media;
  if (existing != null)
  {
    var updatePayload = new UpdateGlobalMediaPayload
    {
      Id = existing.Id,
      FileName = chunk.FileName,
      Name = existing.Name,
      BlobReference = blobName,
      RelativeCacheSourcePath = chunk.FileGuid,
      MimeType = mimeType,
      PackageHash = hash,
      Category = mediaCategory,
    };
    media = ctx.UpdateMedia(updatePayload);
  }
  else
  {
    var mediaPayload = new CreateGlobalMediaPayload
    {
      FileName = chunk.FileName,
      Name = chunk.FileName,
      BlobReference = blobName,
      RelativeCacheSourcePath = chunk.FileGuid,
      MimeType = mimeType,
      PackageHash = hash,
      Category = mediaCategory,
    };

    media = ctx.CreateMedia(mediaPayload);
  }

  if (media != null)
  {
    return Ok(new GlobalMediaResponse(media));
  }

  return NoContent();
}

[SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
[HttpGet(MediaApiRoutes.GetGlobalDownloadUrl)]
public ActionResult<string> GetGlobalDownloadUrl(
  [FromServices] SoftwareDbContext ctx,
  [FromServices] IAzureBlobStorageSasService azureBlobStorageSasService,
  [FromRoute] int id)
{
  var media = ctx.GetMediaById(id);
  if (media == null) return NotFound();
  var url = azureBlobStorageSasService.GetGlobalMediaDownloadUrl(media.BlobReference);
  return Ok(url);
}

#endregion global

}
