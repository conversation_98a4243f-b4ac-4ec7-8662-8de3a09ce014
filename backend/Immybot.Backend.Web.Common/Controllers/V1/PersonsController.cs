using DevExtreme.AspNet.Data;
using Immybot.Backend.Application.DbContextExtensions.PersonExtensions;
using Immybot.Backend.Application.DbContextExtensions.TagExtensions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class PersonsController(
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IResourceAuthorizerFlow resourceAuthorizerFlow,
  IUserService httpUserService) : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(IPersonsViewPermission))]
  [HttpGet(PersonApiRoutes.Dx)]
  public ActionResult<GetSimplePersonResponse[]> DxGet(
    DataSourceLoadOptions loadOptions,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx,
    [FromQuery] int? tagId = null,
    [FromQuery] bool includeTenant = false,
    [FromQuery] bool includeUserData = false,
    [FromQuery] PersonType? personType = null)
  {
    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<Person, IPersonsViewPermission>();
    var q = ctx
      .GetAllPersons(includeTenant, includeUserData)
      .AsSplitQuery()
      .Where(permissionFilter)
      .TagForTelemetry();

    if (tagId != null)
    {
      q = q.Where(a => a.Tags.Any(b => b.Id == tagId));
    }

    q = personType switch
    {
      PersonType.Person => q.Where(a => a.User == null),
      PersonType.User => q.Where(a => a.User != null),
      _ => q
    };

    q = HandleRoleIdFilter(loadOptions, q);

    var source = q.Select(GetSimplePersonResponse.Projection);
    return Ok(DataSourceLoader.Load(source, loadOptions));
  }

  // extract out role ids from the filter since devextreme can't handle array contains
  private static IQueryable<Person> HandleRoleIdFilter(DataSourceLoadOptions loadOptions, IQueryable<Person> q)
  {
    const string roleIdsFilterName = "roleIds";

    // short-circuit if there is no filter
    if (loadOptions.Filter is null) return q;

    var roleIdsFilter = new List<int>();

    // edge case where the filter is just a single roleIds filter
    if (loadOptions.Filter.Count is 3 && loadOptions.Filter[0] is roleIdsFilterName)
    {
      var roleId = Convert.ToInt32(loadOptions.Filter[2]);
      loadOptions.Filter.Clear();
      q = ApplyRoleIdFilterToQuery(q, [roleId]);
      return q;
    }

    var filtersToRemove = new List<object>();
    var removeNextOrAndFilter = false;

    foreach (var filter in loadOptions.Filter)
    {
      // if the previous filter was removed and the current filter is "or" or "and", we need to remove it
      if (removeNextOrAndFilter && filter is "or" or "and")
      {
        filtersToRemove.Add(filter);
      }

      // reset the flag for the next iteration
      removeNextOrAndFilter = false;

      // if the filter is not a list, we can't process it
      if (filter is not List<object> listFilter) continue;

      // the roleIds filter could come after other column filters, in which case the filter might be nested one level deeper
      var removeParentFilter = false;
      foreach (var nestedFilter in listFilter)
      {
        if (nestedFilter is not List<object> nestedListFilter) continue;
        if (nestedListFilter.Count is not 3) continue;
        if (nestedListFilter[0].ToString() != roleIdsFilterName) continue;
        if (nestedListFilter[2] is not int roleId2) continue;
        roleIdsFilter.Add(roleId2);

        // mark that the parent filter should be removed
        removeParentFilter = true;

        // marks the next iteration to remove "or" or "and" if it exists
        removeNextOrAndFilter = true;
      }

      if (removeParentFilter) filtersToRemove.Add(filter);

      // if the filter is not a roleIds filter, we can skip it
      if (listFilter.Count != 3) continue;
      if (listFilter[0].ToString() != roleIdsFilterName) continue;
      if (listFilter[2] is not int roleId) continue;

      // if we reach here, we have a roleIds filter
      roleIdsFilter.Add(roleId);

      // if we didn't already remove the parent filter, then do it now
      if (!removeParentFilter)
      {
        filtersToRemove.Add(filter);
        removeNextOrAndFilter = true;
      }
    }

    // remove all roleIds filters from the load options
    foreach (var f in filtersToRemove)
    {
      loadOptions.Filter.Remove(f);
    }

    // apply the roleIds filter to the db query
    q = ApplyRoleIdFilterToQuery(q, roleIdsFilter);

    return q;
  }

  private static IQueryable<Person> ApplyRoleIdFilterToQuery(IQueryable<Person> q, List<int> roleIdsFilter)
  {
    if (roleIdsFilter.Count != 0)
    {
      q = q.Where(a => a.User != null && a.User.UserRoles.Any(b => roleIdsFilter.Contains(b.RoleId)));
    }

    return q;
  }


  [SubjectPermissionAuthorize(typeof(IPersonsViewPermission))]
  [HttpGet(PersonApiRoutes.GetAll)]
  public ActionResult<GetSimplePersonResponse[]> GetAllPersons(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromQuery] AppSieveModel? sieveModel = null)
  {
    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<Person, IPersonsViewPermission>();
    var q = ctx.GetAllPersons().Where(permissionFilter).TagForTelemetry();
    var persons = q.OrderByDescending(a => a.UpdatedDate).Select(GetSimplePersonResponse.Projection);
    if (sieveModel != null) persons = sieveProcessor.Apply(sieveModel, persons);

    // .net core 3: use "AsEnumerable to avoid returning as
    // IAsyncEnumerable which has a buffer limit and would throw an error
    return Ok(persons.ToList());
  }

  [SubjectPermissionAuthorize(typeof(IPersonsViewPermission))]
  [HttpGet(PersonApiRoutes.Get)]
  public async Task<ActionResult<GetSimplePersonResponse>> Get(
    [FromRoute] int id,
    [FromServices] ImmybotDbContext ctx)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Person, IPersonsViewPermission>(
      new DefaultKeyParameters(id),
      true);

    var person = ctx.GetPerson(id, includeTags: true, includeUserData: true);

    if (person == null) return NotFound();

    var personResponse = new GetSimplePersonResponse(person);

    return Ok(personResponse);
  }

  [SubjectPermissionAuthorize(typeof(IPersonsManagePermission))]
  [HttpDelete(PersonApiRoutes.Delete)]
  public async Task<ActionResult> Delete(
    [FromRoute] int id,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Person, IPersonsManagePermission>(
      new DefaultKeyParameters(id),
      true);

    await using var ctx = dbFactory();
    var person = ctx.GetPerson(id);
    if (person == null) return NotFound();
    if (httpUserService.GetCurrentUser().PersonId == person.Id) return BadRequest("Cannot delete your own person.");

    await ctx.DeleteComputerPersonsByPersonIds([id], HttpContext.RequestAborted);

    ctx.DeletePerson(person);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IPersonsManagePermission))]
  [HttpPut(PersonApiRoutes.Put)]
  public async Task<ActionResult<GetSimplePersonResponse>> Put(
    [FromRoute] int id,
    [FromBody] UpdatePersonPayload payload,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Person, IPersonsManagePermission>(
      new DefaultKeyParameters(id),
      true);

    await using var ctx = dbFactory();
    var existing = ctx.GetPerson(id);
    if (existing is null) return NotFound();

    if (existing.TenantId != payload.TenantId)
    {
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IPersonsManagePermission>(
        User,
        payload.TenantId,
        strict: true);
    }

    if (payload.Id == 0) payload = payload with { Id = id };
    var updatedPerson = ctx.UpdatePerson(payload);
    if (updatedPerson == null) return BadRequest("Person was not updated");
    return Ok(new GetSimplePersonResponse(updatedPerson));
  }

  [SubjectPermissionAuthorize(typeof(IPersonsManagePermission))]
  [HttpPost(PersonApiRoutes.Create)]
  public async Task<ActionResult<GetSimplePersonResponse>> Post(
    [FromBody] CreatePersonPayload payload,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory)
  {
    await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IPersonsManagePermission>(
      User,
      payload.TenantId,
      strict: true);
    await using var ctx = dbFactory();
    return Ok(new GetSimplePersonResponse(ctx.CreatePerson(payload)));
  }

  [SubjectPermissionAuthorize(typeof(IUsersManageAccessRequestsPermission))]
  [HttpGet(PersonApiRoutes.GetPersonsRequestingAccess)]
  public ActionResult<AccessRequestResponse[]> GetPersonsRequestingAccess(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx)
  {
    var permissionFilter =
      permissionFilterBuilder.BuildFilterExpression<Person, IUsersManageAccessRequestsPermission>();
    var q = ctx.Persons
      .AsNoTracking()
      .TagForTelemetry()
      .Where(permissionFilter)
      .SelectMany(a => a.AccessRequests)
      .OrderByDescending(a => a.Id)
      .Where(a => a.DateAcknowledgedUTC == null);
    return Ok(q.Select(AccessRequestResponse.Projection));
  }

  [SubjectPermissionAuthorize(typeof(IUsersManageAccessRequestsPermission))]
  [HttpPost(PersonApiRoutes.GrantAccess)]
  public async Task<ActionResult<CommandResult>> GrantAccess(
    [FromRoute] int personId,
    [FromBody] GrantAccessRequest req,
    [FromServices] IGrantAccessCommand cmd)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Person, IPersonsViewPermission>(
      new DefaultKeyParameters(personId),
      true);

    return Ok(cmd.Run(personId,
      req.IsAdmin,
      req.HasManagementAccess,
      req.ExpirationTime,
      httpUserService.GetCurrentUser().Id));
  }

  [SubjectPermissionAuthorize(typeof(IUsersManageAccessRequestsPermission))]
  [HttpPost(PersonApiRoutes.DenyAccess)]
  public async Task<ActionResult<CommandResult>> DenyAccess(
    [FromRoute] int personId,
    [FromServices] IDenyAccessCommand cmd)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Person, IPersonsViewPermission>(
      new DefaultKeyParameters(personId),
      true);
    return Ok(cmd.Run(personId, httpUserService.GetCurrentUser().Id));
  }

  [SubjectPermissionAuthorize(typeof(IPersonsManagePermission))]
  [HttpPost(PersonApiRoutes.AddTags)]
  public async Task<IActionResult> AddTags(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx,
    [FromBody] AddTagsRequest request)
  {
    var ids = request.EntityIds;

    if (ids.Count == 0) return BadRequest("No person ids were provided");
    if (request.TagIds.Count == 0) return BadRequest("No tag ids were provided");

    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<Person, IPersonsManagePermission>();
    var permissiblePersonIds = await ctx.Persons
      .AsNoTracking()
      .TagForTelemetry()
      .Where(permissionFilter)
      .Where(a => request.EntityIds.Contains(a.Id))
      .Select(a => a.Id)
      .ToListAsync();

    if (permissiblePersonIds.Count is 0)
      return BadRequest("No persons were found with the provided ids or you do not have permission to manage them.");

    var tags = await ctx.GetTagsByIds(request.TagIds).ToListAsync();
    if (tags.Count == 0) return NotFound();

    foreach (var tag in tags)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tag, ITagsViewPermission>(
        new DefaultKeyParameters(tag.Id),
        strict: true);
    }

    await ctx.AddEntityTag<PersonTag>(tags.Select(a => a.Id).ToList(), permissiblePersonIds);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IPersonsManagePermission))]
  [HttpPost(PersonApiRoutes.RemoveTags)]
  public async Task<IActionResult> RemoveTags(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx,
    [FromBody] RemoveTagsRequest request)
  {
    // needs authorization sql filtering

    var ids = request.EntityIds;

    if (ids.Count == 0) return BadRequest("No person ids were provided");
    if (request.TagIds.Count == 0) return BadRequest("No tag ids were provided");

    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<Person, IPersonsManagePermission>();
    var permissiblePersonIds = await ctx.Persons
      .AsNoTracking()
      .TagForTelemetry()
      .Where(permissionFilter)
      .Where(a => request.EntityIds.Contains(a.Id))
      .Select(a => a.Id)
      .ToListAsync();

    if (permissiblePersonIds.Count is 0)
      return BadRequest("No persons were found with the provided ids or you do not have permission to manage them.");

    var tags = await ctx.GetTagsByIds(request.TagIds).ToListAsync();
    if (tags.Count == 0) return NotFound();
    foreach (var tag in tags)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tag, ITagsViewPermission>(
        new DefaultKeyParameters(tag.Id),
        strict: true);
    }

    await ctx.RemoveEntityTags<PersonTag>(tags.Select(a => a.Id).ToList(), permissiblePersonIds);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(PersonApiRoutes.GetSelfServiceItems)]
  public async Task<ActionResult<List<TargetAssignmentResource>>> GetSelfServiceItems(
    [FromRoute] int id,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ITargetAssignmentResolver resolver,
    [FromServices] IRunContextFactory runContextFactory,
    [FromServices] IHostApplicationLifetime appLifetime,
    CancellationToken cancellationToken)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Person, IPersonsViewPermission>(
      new DefaultKeyParameters(id),
      true);

    var person = ctx.GetPerson(id, includeTags: true);
    if (person == null) return NotFound();
    var currentUser = httpUserService.GetCurrentUser();
    using var context = await runContextFactory.GenerateTenantRunContext(manuallyTriggeredBy: currentUser,
      tenantId: person.TenantId,
      cancellationToken: cancellationToken);
    var optionalDeployments = await resolver.GetOptionalTargetAssignments(context);
    var local = optionalDeployments.Where(a => a.DatabaseType == DatabaseType.Local)
      .Select(a => new LocalTargetAssignmentResource(a));
    var global = optionalDeployments.Where(a => a.DatabaseType == DatabaseType.Global)
      .Select(a => new GlobalTargetAssignmentResource(a));
    var resources = local.OfType<TargetAssignmentResource>().Concat(global).ToList();
    return Ok(resources);
  }
}
