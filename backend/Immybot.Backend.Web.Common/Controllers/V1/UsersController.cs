using System.Diagnostics;
using Immybot.Backend.Application.DbContextExtensions.PersonExtensions;
using Immybot.Backend.Application.DbContextExtensions.UserExtensions;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Persistence;
using Microsoft.AspNetCore.Mvc;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Shared.Primitives;
using Immybot.Backend.Persistence.Shared;
using System.Security.Claims;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Domain.Events;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.RoleClaimMetadataService.Interfaces;
using Immybot.Backend.RBAC.Domain.RoleManagement.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.UserCacheManagement.Events;
using Immybot.Backend.RBAC.ResourceAuthorization.Implementations.ExceptionFactories;
using LinqKit;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class UsersController(
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IResourceAuthorizerFlow resourceAuthorizerFlow,
  IUserService userService) : Controller
{
  [SubjectPermissionAuthorize(typeof(IUsersViewPermission))]
  [HttpGet(UserApiRoutes.GetAll)]
  public ActionResult<GetUserResponse[]> GetAll(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromQuery] AppSieveModel? appSieveModel = null)
  {
    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<User, IUsersViewPermission>();
    var users = ctx.GetAllUsers()
      .TagForTelemetry()
      // exclude support technician users since they are not regular users
      .Where(a => !a.IsSupportTechnician)
      // authorization
      .Where(permissionFilter);

    if (appSieveModel != null) users = sieveProcessor.Apply(appSieveModel, users);
    return Ok(users.Select(GetUserResponse.Projection));
  }

  [SubjectPermissionAuthorize(typeof(IUsersViewPermission))]
  [HttpGet(UserApiRoutes.Get)]
  public async Task<ActionResult<GetUserResponse>> Get(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int userId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<User, IUsersViewPermission>(
      new DefaultKeyParameters(userId),
      true);

    var user = ctx.GetUserById(userId, populatePerson: true, populateTenant: true);
    if (user == null) return NotFound();
    return Ok(new GetUserResponse(user));
  }

  [SubjectPermissionAuthorize(typeof(IUsersManagePermission))]
  [HttpPost(UserApiRoutes.Update)]
  public async Task<ActionResult<GetUserResponse>> Update(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] IDomainEventEmitter domainEventEmitter,
    [FromBody] UpdateUserPayload user,
    [FromRoute] int userId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<User, IUsersManagePermission>(
      new DefaultKeyParameters(userId),
      true);

    await using var ctx = ctxFactory();
    var existingUser = ctx.GetUserById(userId);
    if (existingUser is null) return NotFound();

    if (user.TenantId != existingUser.TenantId)
    {
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IUsersManagePermission>(
        User,
        user.TenantId,
        strict: true);
    }

    user = user with { Id = userId };
    ctx.UpdateUser(user);
    var updated = ctx.GetUserById(userId, populatePerson: true, populateTenant: true);
    if (updated == null) return NotFound();
    domainEventEmitter.EmitEvent(new UserUpdatedEvent(updated));

    // Update UserName based on Person's AzurePrincipalId or ServicePrincipalId
    if (updated.Person?.AzurePrincipalId is not null)
      updated.UserName = updated.Person.AzurePrincipalId;
    else if (updated.ServicePrincipalId is not null)
      updated.UserName = updated.ServicePrincipalId;

    await ctx.SaveChangesAsync();

    return Ok(new GetUserResponse(updated));
  }

  [SubjectPermissionAuthorize(typeof(IUsersManagePermission))]
  [HttpPost(UserApiRoutes.Create)]
  public async Task<ActionResult<GetUserResponse>> Create(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] IPendoEventManagementService pendoEventManagement,
    [FromServices] IDomainEventEmitter domainEventEmitter,
    [FromBody] User user)
  {
    await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IUsersManagePermission>(
      User,
      user.TenantId,
      strict: true);

    pendoEventManagement.Enqueue(
      PendoEventManagementService.PendoEvents.ImmyUserCreatedEvent()
    );

    await using var ctx = ctxFactory();
    var created = ctx.CreateUser(user);
    domainEventEmitter.EmitEvent(new UserCreatedEvent(created));
    var res = new GetUserResponse(created);
    return Ok(res);
  }

  // todo: remove this route when the feature flag for Rbac is removed
  [SubjectPermissionAuthorize(typeof(IUsersManagePermission))]
  [HttpPost(UserApiRoutes.CreateFromPerson)]
  public async Task<ActionResult<GetUserResponse>> CreateFromPerson(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] IPendoEventManagementService pendoEventManagement,
    [FromServices] IFeatureTracker featureTracker,
    [FromServices] IDomainEventEmitter domainEventEmitter,
    [FromBody] CreateUserFromPersonRequest request)
  {
    if (!featureTracker.IsEnabled(FeatureEnum.SelfServiceFeature) && !request.HasManagementAccess)
      return BadRequest("Self service users are not enabled.");

    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Person, IPersonsViewPermission>(
      new DefaultKeyParameters(request.PersonId),
      true);

    await using var ctx = ctxFactory();
    var person = ctx.GetPersonById(request.PersonId);
    if (person == null) return NotFound();
    if (ctx.IsPersonAUser(person.Id))
    {
      throw new ImmyWebException(
        new HttpProblem()
        {
          Detail = $"{person.FirstName} {person.LastName} is already a user.",
          Status = System.Net.HttpStatusCode.Conflict,
          Title = "Entity Already Exists"
        }
      );
    }

    var user = new User
    {
      PersonId = person.Id,
      TenantId = person.TenantId,
      // todo: remove when rbac feature flag is removed
      IsAdmin = false,
      HasManagementAccess = request.HasManagementAccess,
      UserName = person.AzurePrincipalId // Set UserName from Person's AzurePrincipalId
    };

    pendoEventManagement.Enqueue(
      PendoEventManagementService.PendoEvents.ImmyUserCreatedEvent()
    );

    var created = ctx.CreateUser(user);
    domainEventEmitter.EmitEvent(new UserCreatedEvent(created));
    return Ok(new GetUserResponse(created));
  }

  [SubjectPermissionAuthorize(typeof(IUsersManagePermission))]
  [HttpDelete(UserApiRoutes.Delete)]
  public async Task<IActionResult> Delete(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromRoute] int userId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<User, IUsersManagePermission>(
      new DefaultKeyParameters(userId),
      true);

    await using var ctx = ctxFactory();
    var user = ctx.GetUserById(userId);
    if (user == null) return NotFound();
    if (user.Id == userService.GetCurrentUser().Id)
      return BadRequest("You cannot remove your own user. Have an admin user remove it instead.");
    ctx.DeleteUser(user);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IUsersManagePermission))]
  [HttpDelete(UserApiRoutes.BulkDelete)]
  public async Task<ActionResult> BulkDelete(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromQuery] List<int> userIds)
  {
    if (userIds.Count < 1)
      return BadRequest("No user IDs provided for deletion.");

    var currentUserId = userService.GetCurrentUser().Id;
    if (userIds.Contains(currentUserId))
    {
      return BadRequest("You cannot remove your own user. Have an admin user remove it instead.");
    }

    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<User, IUsersManagePermission>();

    await using var ctx = ctxFactory();
    var permissibleUserIds = await ctx.Users
      .Where(a => userIds.Contains(a.Id))
      .Where(permissionFilter)
      .Select(a => a.Id)
      .ToListAsync();

    await ctx.BulkDeleteUsers(permissibleUserIds);
    return Ok($"{permissibleUserIds.Count} users deleted successfully.");
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(UserApiRoutes.SubmitFeedback)]
  public IActionResult SubmitFeedback(
    [FromBody] SubmitFeedbackRequestBody body,
    [FromServices] IServiceProvider serviceProvider)
  {
    // send details to app insights
    Activity.Current?.AddEvent(new(
      "feedback",
      tags: new()
      {
        { "details", body.Details },
        { "rating", body.Rating },
        { "user", userService.GetCurrentUser().Email },
        { "tenant", userService.GetCurrentUser().TenantName }
      }));

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IUsersImpersonatePermission))]
  [HttpPost(UserApiRoutes.ImpersonateUser)]
  public async Task<ActionResult<OpResult>> ImpersonateUser(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IUserImpersonationStore store,
    [FromServices] ICachedSingleton<ApplicationPreferences> appPreferences,
    [FromRoute] int userId,
    [FromBody] ImpersonationRequest request)
  {
    if (!appPreferences.Value.EnableUserImpersonation) return Ok(OpResult.Fail("Impersonation is disabled."));

    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<User, IUsersImpersonatePermission>(
      new DefaultKeyParameters(userId),
      true);

    var user = ctx.GetUserById(userId);
    if (user == null) return NotFound();
    var currentUser = userService.GetCurrentUser();
    if (currentUser.Id == user.Id) return Ok(OpResult.Fail("You cannot impersonate yourself."));

    var impersonator = currentUser.ImpersonatorUser ?? currentUser;
    var res = await store.ImpersonateUser(impersonator, user, request.ExpiresAtUtc);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(UserApiRoutes.StopImpersonatingUser)]
  public async Task<ActionResult<OpResult>> StopImpersonatingUser(
    [FromServices] ICachedSingleton<ApplicationPreferences> appPreferences,
    [FromServices] IUserImpersonationStore store)
  {
    if (!appPreferences.Value.EnableUserImpersonation) return Ok(OpResult.Fail("Impersonation is disabled."));

    var impersonator = userService.GetCurrentUser()?.ImpersonatorUser;
    if (impersonator is null) return Ok(OpResult.Fail("User is not impersonating anyone."));
    var res = await store.StopImpersonating(impersonator);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(IUsersManagePermission))]
  [HttpGet(UserApiRoutes.GetClaims)]
  public ActionResult<GetClaimsResponse> GetClaims()
  {
    // Get the current user's ClaimsPrincipal
    ClaimsPrincipal? principal = userService.GetCurrentPrincipal();

    // Guard clause: Ensure principal exists
    if (principal == null) return Unauthorized("No authenticated user found");

    // Extract all claims from the principal
    IEnumerable<ClaimResponse> allClaims = principal.Claims
      .Select(c => new ClaimResponse(c));

    // Return the response with all claims
    return Ok(new GetClaimsResponse
    {
      Claims = allClaims
    });
  }

  [SubjectPermissionAuthorize(typeof(IUsersManagePermission))]
  [HttpPost(UserApiRoutes.GrantAccess)]
  public async Task<ActionResult<CommandResult>> GrantAccessRbac(
    [FromServices] Func<ImmybotDbContext> dbFactory,
    [FromServices] IGetRoleOperation getRoleOperation,
    [FromServices] IRoleClaimMetadataService roleClaimMetadataService,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromBody] GrantAccessRequestRbac req,
    [FromServices] IGrantAccessCommand cmd)
  {
    await using var db = dbFactory();

    // if the user does not have permission to assign roles with cross-tenant permissions, then verify the user did
    // not specify any roles that contain cross-tenant permissions
    if (!await CanAssignCrossTenantRoles())
    {
      foreach (var roleId in req.RoleIds)
      {
        var role = await getRoleOperation.ExecuteAsync(roleId);

        // if role contains any tenant-based permission other than "tenant:my", then it is considered cross-tenant.
        if (role.RoleClaims.Any(a => roleClaimMetadataService.IsCrossTenantRoleClaim(a.ClaimValue)))
        {
          throw ResourceExceptionFactory.CreateResourceAuthorizationFailedForCrossTenantRole(role.Id);
        }
      }
    }

    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<Person, IPersonsViewPermission>();
    var permissiblePersonIds = await db.Persons
      .AsNoTracking()
      .TagForTelemetry()
      .Where(a => req.PersonIds.Contains(a.Id) && a.AzurePrincipalId != null)
      .Where(permissionFilter)
      .Select(a => a.Id)
      .ToListAsync();

    return Ok(await cmd.RunRbac(permissiblePersonIds, req.RoleIds, req.ExpiresIn));
  }

  [SubjectPermissionAuthorize(typeof(IUsersManagePermission))]
  [HttpPost(UserApiRoutes.InvalidateCache)]
  public async Task<IActionResult> InvalidateCache(
    [FromServices] IFeatureTracker featureTracker,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] IDomainEventEmitter domainEventEmitter,
    [FromRoute] int userId)
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);

    // Create context once and ensure proper disposal with using statement
    await using var context = ctxFactory();

    var user = await context.Users
      .AsNoTracking()
      .Where(a => a.Id == userId)
      .FirstOrDefaultAsync();

    if (user is null)
      return NotFound();

    domainEventEmitter.EmitEvent(new InvalidateSingleUserCacheRequestEvent(userId));

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IUsersManagePermission))]
  [HttpPost(UserApiRoutes.RemoveRoles)]
  public async Task<ActionResult> BulkRemoveRolesFromPeople(
    [FromServices] IFeatureTracker featureTracker,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] IDomainEventEmitter domainEventEmitter,
    [FromBody] BulkUpdateRoleRequest request)
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);

    if (request.RoleIds.Count == 0 || request.PersonIds.Count == 0)
      return BadRequest("RoleIds and PersonIds cannot be empty.");

    await using var ctx = ctxFactory();

    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<Person, IPersonsViewPermission>();
    var permissiblePersonIds = await ctx.Persons
      .AsNoTracking()
      .TagForTelemetry()
      .Where(a => request.PersonIds.Contains(a.Id))
      .Where(permissionFilter)
      .Select(a => a.Id)
      .ToListAsync();

    // Find all UserRoles that match the requested RoleIds for the found people and delete them
    var rolesToRemove = await ctx.UserRoles
      .Include(a => a.User)
      .ThenInclude(a => a!.Person)
      .Include(a => a.Role)
      .Where(a => a.User != null && request.RoleIds.Contains(a.RoleId) &&
                  permissiblePersonIds.Contains(a.User.PersonId!.Value))
      .ToListAsync();

    ctx.UserRoles.RemoveRange(rolesToRemove);

    rolesToRemove.GroupBy(a => a.User).ForEach(u =>
    {
      if (u.Key is null) return;
      var auditValue = string.Join(",\n", u.Select(a => a.Role?.Name ?? string.Empty));
      u.Key.UpdatedDate = DateTime.UtcNow;
      u.Key.CustomAuditProperties.Add(new AuditPropertyChange("Removed Roles", auditValue, null));
    });

    await ctx.SaveChangesAsync();

    // if we updated more than 5 users then invalidate the cache for all users
    if (rolesToRemove.Count > 5)
    {
      domainEventEmitter.EmitEvent(new InvalidateUserCacheRequestEvent());
    }
    else
    {
      rolesToRemove.ForEach(a =>
        domainEventEmitter.EmitEvent(new InvalidateSingleUserCacheRequestEvent(a.UserId)));
    }

    return Ok($"{rolesToRemove.Count} roles removed successfully from {request.PersonIds.Count} specified people.");
  }

  [SubjectPermissionAuthorize(typeof(IUsersManagePermission))]
  [HttpPost(UserApiRoutes.AddRoles)]
  public async Task<ActionResult<string>> BulkAssignRolesToPeople(
    [FromServices] IFeatureTracker featureTracker,
    [FromServices] IDomainEventEmitter domainEventEmitter,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] IRoleClaimMetadataService roleClaimMetadataService,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromBody] BulkUpdateRoleRequest request)
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);

    if (request.RoleIds.Count == 0 || request.PersonIds.Count == 0)
      return BadRequest("RoleIds and PersonIds cannot be empty.");

    await using var ctx = ctxFactory();

    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<Person, IPersonsViewPermission>();

    // Validate that all requested PersonIds and RoleIds exist
    var foundPeople = await ctx.Persons
      .AsSplitQuery()
      .Where(permissionFilter)
      .Where(x => request.PersonIds.Contains(x.Id) && x.AzurePrincipalId != null)
      .Include(person => person.User)
      .ThenInclude(u => u!.UserRoles)
      .ToListAsync();

    var foundRoles = await ctx.Roles
      .AsNoTracking()
      .AsSplitQuery()
      .Where(x => request.RoleIds.Contains(x.Id))
      .Select(x => new { x.Id, x.Name, x.RoleClaims })
      .ToListAsync();

    // Data to be used for creating users or updating roles

    // If no people or roles were found, return NotFound
    if (foundPeople.Count < 1 || foundRoles.Count < 1)
      return NotFound("Error assigning roles to selected people.");

    // create a list of users that we plan on creating or updating
    var usersToCreate = new List<User>();
    var userRolesToCreate = new List<UserRole>();

    if (!await CanAssignCrossTenantRoles())
    {
      foreach (var role in foundRoles)
      {
        // if role contains any tenant-based permission other than "tenant:my", then it is considered cross-tenant.
        if (role.RoleClaims.Any(a => roleClaimMetadataService.IsCrossTenantRoleClaim(a.ClaimValue)))
        {
          throw ResourceExceptionFactory.CreateResourceAuthorizationFailedForCrossTenantRole(role.Id);
        }
      }
    }

    foreach (var person in foundPeople)
    {
      // Create a new User if the person does not have one
      if (person.User is null)
      {
        var user = new User
        {
          PersonId = person.Id,
          TenantId = person.TenantId,
          CreatedDate = DateTime.UtcNow,
          UpdatedDate = DateTime.UtcNow,
          UserRoles = foundRoles.Select(a => new UserRole { RoleId = a.Id }).ToList()
        };
        var auditValue = string.Join(", ", foundRoles.Select(a => a.Name));
        user.CustomAuditProperties.Add(new AuditPropertyChange("Roles", null, auditValue));
        usersToCreate.Add(user);
        continue;
      }

      foreach (var role in foundRoles)
      {
        // check if the person already has the role
        var existingRole = person.User.UserRoles.FirstOrDefault(ur => ur.RoleId == role.Id);

        if (existingRole is null)
        {
          // Only add the new role if user doesn't already have it
          userRolesToCreate.Add(new UserRole { UserId = person.User.Id, RoleId = role.Id, });
        }
      }
    }

    if (usersToCreate.Count > 0)
      await ctx.Users.AddRangeAsync(usersToCreate);

    if (userRolesToCreate.Count > 0)
    {
      await ctx.UserRoles.AddRangeAsync(userRolesToCreate);
      var updatedUserIds = userRolesToCreate.Select(a => a.UserId).Distinct().ToList();
      var users = await ctx.Users
        .Include(a => a.Person)
        .Include(a => a.UserRoles)
        .ThenInclude(a => a.Role)
        .Where(a => updatedUserIds.Contains(a.Id)).ToListAsync();
      foreach (var user in users)
      {
        // Update the UpdatedDate for each user
        user.UpdatedDate = DateTime.UtcNow;
        var newAuditValue = string.Join(",\n",
          userRolesToCreate
            .Where(a => a.UserId == user.Id)
            .Select(a => foundRoles.FirstOrDefault(r => r.Id == a.RoleId)?.Name));
        user.CustomAuditProperties.Add(new AuditPropertyChange("Added Roles", null, newAuditValue));
      }
    }

    await ctx.SaveChangesAsync();

    // if we updated more than 5 users then invalidate the cache for all users
    if (userRolesToCreate.Count > 5)
    {
      domainEventEmitter.EmitEvent(new InvalidateUserCacheRequestEvent());
    }
    else
    {
      userRolesToCreate.ForEach(a =>
        domainEventEmitter.EmitEvent(new InvalidateSingleUserCacheRequestEvent(a.UserId)));
    }

    return Ok($"Roles assigned successfully to {request.PersonIds.Count} specified people.");
  }

  [SubjectPermissionAuthorize(typeof(IUsersManagePermission))]
  [HttpPost(UserApiRoutes.UpdateExpiration)]
  public async Task<ActionResult> UpdateExpiration(
    [FromServices] IFeatureTracker featureTracker,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromBody] UpdateExpirationRequest request)
  {
    featureTracker.IsEnabled(FeatureEnum.RBACFeature, strict: true);

    await using var ctx = ctxFactory();

    var filter = permissionFilterBuilder.BuildFilterExpression<User, IUsersViewPermission>();

    var users = await ctx.Users
      .AsSplitQuery()
      .Where(filter)
      .Where(a => a.PersonId != null && request.PersonIds.Contains(a.PersonId.Value))
      .ToListAsync();

    foreach (var user in users)
    {
      user.ExpirationDateUTC = GetExpirationDate(request.ExpiresIn);
    }

    await ctx.SaveChangesAsync();

    return NoContent();
  }

  private static DateTime? GetExpirationDate(ExpirationTime expiresIn) => expiresIn switch
  {
    ExpirationTime.OneHour => DateTime.UtcNow.AddHours(1),
    ExpirationTime.OneDay => DateTime.UtcNow.AddDays(1),
    ExpirationTime.ThreeDays => DateTime.UtcNow.AddDays(3),
    ExpirationTime.Indefinite => null,
    _ => throw new NotImplementedException()
  };

  private async Task<bool> CanAssignCrossTenantRoles() => await subjectPermissionAuthorizationService
    .AuthorizeAsync<IUsersAssignRolesWithCrossTenantPermissions>(User, strict: false);
}
