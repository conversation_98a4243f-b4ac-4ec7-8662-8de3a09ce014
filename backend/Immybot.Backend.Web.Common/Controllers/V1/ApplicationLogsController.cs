using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib.SignalRLogger;
using Microsoft.AspNetCore.Mvc;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class ApplicationLogsController : Controller
{
  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(ApplicationLogsApiRoutes.UpdateSourceContext)]
  public ActionResult UpdateSourceContext(
    [FromBody] UpdateApplicationLogSourceContextRequest request,
    [FromServices] ISignalRLoggerInMemoryOptionsStore store)
  {
    store.AddOrUpdateSourceContextLogLevel(request.SourceContext, request.MinimumLevel);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(ApplicationLogsApiRoutes.ClearSourceContext)]
  public ActionResult ClearSourceContext(
    [FromBody] ClearApplicationLogSourceContextRequest request,
    [FromServices] ISignalRLoggerInMemoryOptionsStore store)
  {
    store.ClearSourceContextLogLevel(request.SourceContext);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(ApplicationLogsApiRoutes.ClearAllSourceContexts)]
  public ActionResult ClearAllSourceContexts(
    [FromServices] ISignalRLoggerInMemoryOptionsStore store)
  {
    store.ResetAllLogLevels();
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(ApplicationLogsApiRoutes.ToggleStreaming)]
  public ActionResult ToggleStreaming(
    [FromBody] ToggleApplicationLogStreamingRequest request,
    [FromServices] ISignalRLoggerInMemoryOptionsStore store)
  {
    store.ToggleStreaming(request.Enabled);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpGet(ApplicationLogsApiRoutes.GetSourceContexts)]
  public ActionResult<GetSourceContextsResponse> GetSourceContexts(
    [FromServices] ISignalRLoggerInMemoryOptionsStore store)
  {
    var sourceContexts = store.GetLogLevels();
    var enabled = store.StreamingEnabled;
    return Ok(new GetSourceContextsResponse(enabled, sourceContexts));
  }
}
