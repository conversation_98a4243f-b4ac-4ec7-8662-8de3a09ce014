using Immybot.Backend.Application.Lib;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Shared.DataContracts.Agent;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
using Microsoft.Extensions.Hosting;

namespace Immybot.Backend.Web.Common.Controllers.V1;

/// <summary>
/// This is intentionally created outside of <see cref="ImmyAgentProvider.ImmyAgentProvider"/>'s
/// <see cref="ISupportsHttpRequest"/> implementation to allow use of output caching.
/// </summary>

[ApiController]
public class ImmyAgentMetadataController : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(ImmyAgentMetadataRoutes.AgentHash)]
  [OutputCache(Duration = 60)]
  public async Task<ActionResult<GetAgentSha256HashResponse>> GetAgentHash(
    [FromServices] IImmyAgentMetadataProvider metadataProvider,
    [FromServices] IHostApplicationLifetime appLifetime)
  {
    var downloadUrl = await metadataProvider.GetAgentBinaryDownloadUrl();
    var agentHash = await metadataProvider.GetAgentBinarySha256Hash(appLifetime.ApplicationStopping);
    return new GetAgentSha256HashResponse(downloadUrl, agentHash);
  }
}
