<?xml version="1.0" encoding="utf-8"?>
<Project
    ToolsVersion="4.0"
    DefaultTargets="Build"
    xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--
		Hi! This is settings file for Reinforced.Typings.
		Since Reinforced.Typings is not a framework itself but build process extension,
		therefore settings file is basically piece of MSBuild script.
		This file is being imported to your .csproj during each build the same way
		as Reinforced.Typings.targets that is located in its package /build directory.

		Reinforced.Typings has completely few parameters. So, here we go.
	-->

  <PropertyGroup>

    <!--
			This property points target file where to put generated sources.
			It is not used if RtDivideTypesAmongFiles='true' specified.
			Important! Do not forget to include generated file to solution.
		-->
    <RtTargetFile>$(ProjectDir)..\..\frontend\src\api\backend\v1\contracts.ts</RtTargetFile>

    <!--
			This property sets RT configuration method for fluent configuration.
			If you don't want to use attributes for some reason then here you can
			specify full-qualified configuration method name (including namespace,
			type and method name, e.g. My.Assembly.Configuration.ConfigureTypings)
			of method that consumes Reinforce.Typings.Fluent.ConfigurationBuilder type
			and is void (nothing returns). This method will be executed to build
			types exporting configuration in fluent manner.
			Surely you can continue using attributes. Also fluent configuration methods
			could not provide a way to configure some specific things (generics parameters
			override and classes' TsBaseParam). However in this case fluent configuration
			would be preferred. It means that if member configuration is supplied both
			in attributes and in fluent methods then configuration from fluent methods will
			be used.
		-->
    <RtConfigurationMethod>Immybot.Backend.Web.Common.ReinforcedTypings.Configuration.Configure</RtConfigurationMethod>

    <!--
			By default all of your typings will be located in single file specified by RtTargetFile.
			It may be heavy for large projects because you will get single large file containing significant
			part of TS sources. It could lead to various problems (e.g. monstrous SCM merges).
			So here we have RtDivideTypesAmongFiles parameter that will make Reinforced.Typings
			generate TS sources in C#/Java/somewhat-OO-language-manner (class per file) when set to true.

			Important! In case of using this setting, do not forget to add generated files to solution
    manually.
		-->
    <RtDivideTypesAmongFiles>true</RtDivideTypesAmongFiles>

    <!--
			... and if you use RtDivideTypesAmongFiles then please specify target directory
			where to put all generated stuff. Reinforced.Typings will automatically create
			directories structure according to used namespaces.
			Note that in case of using RtDivideTypesAmongFiles, RtTargetFile parameter will
			NOT be used anymore.
		-->
    <RtTargetDirectory>$(ProjectDir)..\..\frontend\src\api\backend\generated</RtTargetDirectory>


    <!--
			Disables typescript compilation in solution.
			Use it when your TypeScripts are broken and you need to rebuild project and then regenerate typings
			to fix it, but TypeScript compilation fails and failing project build. This option will temporary
			disable typescripts build.
		-->
    <RtBypassTypeScriptCompilation>false</RtBypassTypeScriptCompilation>

    <!--
			Disables Reinforced.Typings generation on build. Use it when it is necessary to temporary disable
			typings generation.
		-->
    <RtDisable>false</RtDisable>

    <!--
			Forces RT to use specified target framework.
			Use this parameter if your RT is experiencing some problems with identifying framework version to
    use.
			Available values:
				net45, net461, netcoreapp1.0, netcoreapp1.1, netcoreapp2.0, netcoreapp2.1, netcoreapp2.2
		-->
    <!--<RtForceTargetFramework>netcoreapp3.1</RtForceTargetFramework>-->

  </PropertyGroup>

  <!--
		If you want Reinforced.Typings to lookup for attributed classes not only current
		project's assembly but also another assembly then you can specify it in RtAdditionalAssembly
		item group.

		Reinforced.Typings receives reference assemblies list from CoreCompile task so you
		can specify here assemblies from your project's references (with or without .dll extension).
		If desired assembly is not in references list of current project then you will have
		to specify full path to it.
	-->

  <ItemGroup Condition="$(PSESDebug) != 'true'">
    <!--This
    is necessary to prevent Reinforced.Typings from not being able to find the types in the Web
    project-->
    <RtAdditionalAssembly Include="$(MSBuildProjectDirectory)\..\Immybot.Backend.Domain\$(OutputPath)Microsoft.PowerShell.Commands.Utility.dll" />
    <RtAdditionalAssembly Include="$(MSBuildProjectDirectory)\..\Immybot.Backend.Domain\$(OutputPath)System.Management.Automation.dll" />
  </ItemGroup>
</Project>
