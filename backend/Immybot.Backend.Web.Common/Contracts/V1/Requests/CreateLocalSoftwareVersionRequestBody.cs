using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class CreateLocalSoftwareVersionRequestBody : SoftwareVersionRequestBodyBase, ICreateLocalSoftwareVersionPayload
{
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public int SoftwareId { get; set; }

  public override SoftwareType SoftwareType => SoftwareType.LocalSoftware;
  public override string SoftwareIdentifier => SoftwareId.ToString();

  public DatabaseType? InstallScriptType { get; set; }
  public DatabaseType? TestScriptType { get; set; }
  public DatabaseType? UpgradeScriptType { get; set; }
  public DatabaseType? UninstallScriptType { get; set; }
  public DatabaseType? PostUninstallScriptType { get; set; }
  public DatabaseType? PostInstallScriptType { get; set; }
}
