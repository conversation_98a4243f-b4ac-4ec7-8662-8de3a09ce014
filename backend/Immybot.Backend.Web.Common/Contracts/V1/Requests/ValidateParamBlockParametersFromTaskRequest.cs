using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public record ValidateParamBlockParametersFromTaskRequest(
  int MaintenanceTaskId,
  DatabaseType DatabaseType,
  Dictionary<string, ParameterValue>? ParameterValues,
  int? TenantId = null,
  int? ComputerId = null,
  // todo: wire this up
  int? PersonId = null,
  bool ForceRebind = false,
  Guid? TerminalId = null,
  int? DeploymentId = null,
  DatabaseType? DeploymentDatabaseType = null,
  int? MaintenanceSessionId = null) : IValidateParamBlockParametersRequest;
