using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class CreateGlobalSoftwareVersionRequestBody : SoftwareVersionRequestBodyBase, ICreateGlobalSoftwareVersionPayload
{
  public int SoftwareId { get; set; }

  public override SoftwareType SoftwareType => SoftwareType.LocalSoftware;
  public override string SoftwareIdentifier => SoftwareId.ToString();
}
