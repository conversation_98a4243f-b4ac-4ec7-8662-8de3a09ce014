using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public record GetScriptVariablesAndParametersRequest(
  ScriptExecutionContext ScriptExecutionContext,
  ScriptCategory ScriptCategory,
  int? ComputerId = null,
  int? TenantId = null,
  int? SoftwareId = null,
  SoftwareType? SoftwareType = null,
  int? TaskId = null,
  DatabaseType? TaskType = null);
