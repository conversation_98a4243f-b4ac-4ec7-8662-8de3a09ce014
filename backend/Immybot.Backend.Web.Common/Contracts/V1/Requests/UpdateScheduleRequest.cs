using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class UpdateScheduleRequest : IUpdateSchedulePayload
{
  public int Id { get; set; }

  public string? Target { get; set; }

  public TargetType TargetType { get; set; }

  public TargetGroupFilter TargetGroupFilter { get; set; }
  public TargetCategory TargetCategory { get; set; }
  public Guid? ProviderDeviceGroupType { get; set; }

  public Guid? ProviderClientGroupType { get; set; }

  public int? ProviderLinkId { get; set; }

  public int? TenantId { get; set; }
  public bool PropagateToChildTenants { get; set; }

  public string? CustomCronExpression { get; set; }

  public string? Time { get; set; }

  public int? Day { get; set; }

  public string? MaintenanceTime { get; set; }

  public bool Disabled { get; set; }

  public bool SendDetectionEmail { get; set; }
  public bool SendDetectionEmailWhenAllActionsAreCompliant { get; set; }

  public bool SendFollowUpEmail { get; set; }

  public bool SendFollowUpOnlyIfActionNeeded { get; set; }

  public bool ShowRunNowButton { get; set; }

  public bool ShowPostponeButton { get; set; }

  public MaintenanceType? MaintenanceType { get; set; }

  public string? MaintenanceIdentifier { get; set; }

  public string? TimeZoneInfoId { get; set; }

  public RebootPreference RebootPreference { get; set; }

  public PromptTimeoutAction PromptTimeoutAction { get; set; }

  public bool AutoConsentToReboots { get; set; }

  public int PromptTimeoutMinutes { get; set; }

  public bool ApplyWindowsUpdates { get; set; }

  public bool ShowMaintenanceActions { get; set; }

  public bool AllowAccessToMSPResources { get; set; }

  public bool AllowAccessToParentTenant { get; set; }

  public ComputerOfflineMaintenanceSessionBehavior OfflineBehavior { get; set; }

  public bool SuppressRebootsDuringBusinessHours { get; set; }
  public bool UseComputersTimezoneForExecution { get; set; }
  public bool ScheduleExecutionAfterActiveHours { get; set; }
}
