using System.Text.Json;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class CreateProviderLinkWithExternalProviderReferenceRequestBody
{
  public required ProviderLink ProviderLink { get; set; }
  public ProviderLinkExternalReferenceDataBody? ProviderLinkExternalReferenceData { get; set; }
}

public class ProviderLinkExternalReferenceDataBody
{
  public int ProviderLinkId { get; set; }
  public JsonElement ProviderTypeFormData { get; set; }
  public bool EnableClientExternalLinking { get; set; }
}
