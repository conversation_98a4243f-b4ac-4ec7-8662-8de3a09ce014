using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class CalculateTargetsRequest : ICalculatableTargetAssignmentProps
{
  public TargetType TargetType { get; set; }
  public string? Target { get; set; }
  public int? ProviderLinkId { get; set; }
  public bool OnboardingOnly { get; set; }

  // device group
  public TargetGroupFilter TargetGroupFilter { get; set; }
  public Guid? ProviderDeviceGroupType { get; set; }
  public Guid? ProviderClientGroupType { get; set; }

  public int? TenantId { get; set; }
  public bool PropagateToChildTenants { get; set; }
  public bool AllowAccessToParentTenant { get; set; }
}
