using System.Text.Json;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;
public class UpdateComputerRequestBody
{
  public int TenantId { get; set; }
  public int? PrimaryPersonId { get; set; }
  public HashSet<int> AdditionalPersonIds { get; set; } = [];
  public ComputerOnboardingStatus OnboardingStatus { get; set; }
  public List<ProviderLinkUpdate> ProviderLinkUpdates { get; set; } = [];
  public string? Notes { get; set; }
}

public class ProviderLinkUpdate
{
  public int ProviderLinkId { get; set; }
  public string? ClientId { get; set; }
  public JsonElement? DeviceUpdateFormData { get; set; }
}
