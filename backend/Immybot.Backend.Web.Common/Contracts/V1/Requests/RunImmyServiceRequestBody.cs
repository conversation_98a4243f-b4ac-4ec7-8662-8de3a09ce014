using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using NuGet.Versioning;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class RunImmyServiceRequestBody : IMaintenanceEmailConfiguration
{
  public RunImmyServiceRequestBody()
  {
    Computers = [];
    Tenants = [];
    Persons = [];
  }

  public List<RunImmybotComputerRequestBody> Computers { get; set; }
  public List<RunImmybotTenantRequestBody> Tenants { get; set; }
  public List<RunImmybotPersonRequestBody> Persons { get; set; }


  public RunImmybotMaintenanceRequestBody? MaintenanceParams { get; set; }

  /// <summary>
  /// If true then hangfire is not used
  /// </summary>
  public bool SkipBackgroundJob { get; set; }

  public bool CacheOnly { get; set; }
  public RebootPreference RebootPreference { get; set; } = RebootPreference.Suppress;
  public PromptTimeoutAction PromptTimeoutAction { get; set; } = PromptTimeoutAction.Suppress;
  public bool AutoConsentToReboots { get; set; }
  public int PromptTimeoutMinutes { get; set; }
  public int? LicenseId { get; set; }
  public string? UpdateTime { get; set; }
  public string? TimeZoneInfoId { get; set; }
  public bool UseComputersTimezoneForExecution { get; set; }
  public bool ScheduleExecutionAfterActiveHours { get; set; }
  public bool FullMaintenance { get; set; }
  public bool DetectionOnly { get; set; }
  public bool ResolutionOnly { get; set; }
  public bool InventoryOnly { get; set; }
  public bool RunInventoryInDetection { get; set; }
  public int? DeploymentId { get; set; }
  public DatabaseType? DeploymentType { get; set; }
  public ComputerOfflineMaintenanceSessionBehavior OfflineBehavior { get; set; } = ComputerOfflineMaintenanceSessionBehavior.Skip;
  public bool SuppressRebootsDuringBusinessHours { get; set; }
  // email configuration
  public bool SendDetectionEmail { get; set; }
  public bool SendDetectionEmailWhenAllActionsAreCompliant { get; set; }
  public bool SendFollowUpEmail { get; set; }
  public bool SendFollowUpOnlyIfActionNeeded { get; set; }
  public bool ShowRunNowButton { get; set; }
  public bool ShowPostponeButton { get; set; }
  public bool ShowMaintenanceActions { get; set; }

  public bool UseWinningDeployment { get; set; }

  public Guid? SessionGroupId { get; set; }
  public int? ProviderLinkIdForAgentUpdates { get; set; }

  public bool PropagateToChildTenants { get; set; }
  public bool AllowAccessToParentTenant { get; set; }
}

public class RunImmybotComputerRequestBody
{
  public int ComputerId { get; set; }

  public Dictionary<string, Dictionary<string, DeploymentParameterValue>?>? MaintenanceTaskParameterValueOverrides
  {
    get;
    set;
  }
}
public class RunImmybotTenantRequestBody
{
  public int TenantId { get; set; }

  public Dictionary<string, Dictionary<string, DeploymentParameterValue>?>? MaintenanceTaskParameterValueOverrides
  {
    get;
    set;
  }
}
public class RunImmybotPersonRequestBody
{
  public int PersonId { get; set; }

  public Dictionary<string, Dictionary<string, DeploymentParameterValue>?>? MaintenanceTaskParameterValueOverrides
  {
    get;
    set;
  }
}
public class RunImmybotMaintenanceRequestBody
{
  public string? MaintenanceIdentifier { get; set; }
  public MaintenanceType? MaintenanceType { get; set; }
  public int? ProviderLinkIdForMaintenanceItem { get; set; }
  public bool Repair { get; set; }
  public DesiredSoftwareState? DesiredSoftwareState { get; set; }
  public SoftwareProviderType? SoftwareProviderType { get; set; }
  public SemanticVersion? SemanticVersion { get; set; }
  public string? SemanticVersionNormalized => SemanticVersion?.ToNormalizedString();
  public MaintenanceTaskMode? MaintenanceTaskMode { get; set; }
  public Dictionary<string, DeploymentParameterValue>? TaskParameterValues { get; set; }
  public SoftwareType? SoftwareType
  {
    get
    {
      switch (MaintenanceType)
      {
        case Domain.Models.MaintenanceType.GlobalSoftware:
          return Domain.Models.SoftwareType.GlobalSoftware;
        case Domain.Models.MaintenanceType.LocalSoftware:
          return Domain.Models.SoftwareType.LocalSoftware;
        case Domain.Models.MaintenanceType.NiniteSoftware:
          return Domain.Models.SoftwareType.Ninite;
        case Domain.Models.MaintenanceType.ChocolateySoftware:
          return Domain.Models.SoftwareType.Chocolatey;
        default:
          return null;
      }
    }
  }
}
