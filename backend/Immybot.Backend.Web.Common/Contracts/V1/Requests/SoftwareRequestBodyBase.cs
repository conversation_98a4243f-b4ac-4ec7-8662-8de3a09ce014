using Immybot.Backend.Application.Interface;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public abstract class SoftwareRequestBodyBase : ISoftwareDetailsBase
{
  public string Name { get; set; } = null!;
  public SoftwareLicenseRequirement LicenseRequirement { get; set; }
  public string? LicenseDescription { get; set; }
  public int InstallOrder { get; set; }
  public bool Hidden { get; set; }
  public int? DetectionScriptId { get; set; }
  public int? RepairScriptId { get; set; }
  public int? RepairType { get; set; }
  public string? SoftwareTableName { get; set; }
  public int? MaintenanceTaskId { get; set; }
  public string? Notes { get; set; }
  public bool RebootNeeded { get; set; }
  public string? UpgradeCode { get; set; }
  public DetectionMethod DetectionMethod { get; set; }
  public SoftwareTableNameSearchMode? SoftwareTableNameSearchMode { get; set; }
  public int? SoftwareIconMediaId { get; set; }
  public bool Recommended { get; set; }
  public string? ChocoProviderSoftwareId { get; set; }
  public string? NiniteProviderSoftwareId { get; set; }
  public int? InstallScriptId { get; set; }
  public int? TestScriptId { get; set; }
  public bool TestRequired { get; set; }
  public string? TestFailedError { get; set; }
  public int? UpgradeScriptId { get; set; }
  public UpdateActionType UpgradeStrategy { get; set; }
  public int? UninstallScriptId { get; set; }
  public int? PostInstallScriptId { get; set; }
  public int? PostUninstallScriptId { get; set; }
  public LicenseType LicenseType { get; set; } = LicenseType.None;
  public bool UseDynamicVersions { get; set; }
  public int? DynamicVersionsScriptId { get; set; }
  public int? DownloadInstallerScriptId { get; set; }
  public Guid? AgentIntegrationTypeId { get; set; }
}
