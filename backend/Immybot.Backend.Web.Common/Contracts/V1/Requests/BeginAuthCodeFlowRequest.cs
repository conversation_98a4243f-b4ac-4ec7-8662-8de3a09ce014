using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Oauth;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public record BeginAuthCodeFlowRequest(
  OauthConsentData OauthConsentData,
  bool AllowSilentRefresh);

public record BeginAuthCodeFlowResponse(
  Guid OauthHookId);

public record FailAuthCodeFlowRequest(
  Guid OauthHookId,
  Oauth2AuthCodeErrorResponse OauthErrorResponse);
