using Immybot.Backend.Application.Interface.Commands.Payloads;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class UpdateBrandingRequestBody : IUpdateBrandingPayload
{
  public int Id { get; set; }

  public int? TenantId { get; set; }
  public DateTime? StartDate { get; set; }
  public DateTime? EndDate { get; set; }
  public bool? IgnoreYear { get; set; }
  public string? TimeFormat { get; set; }
  public string? FromAddress { get; set; }
  public string? MascotImgUri { get; set; }
  public string? MascotName { get; set; }
  public string? LogoUri { get; set; }
  public string? LogoAltText { get; set; }
  public string? BackgroundColor { get; set; }
  public string? ForegroundColor { get; set; }
  public string? TableHeaderColor { get; set; }
  public required string Description { get; set; }

  public string? TableHeaderTextColor { get; set; }

  public string? TextColor { get; set; }
}
