using System.Text.Json;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public record SetupTestIntegrationRequest(string Script);

public record TestIntegrationBindConfigurationFormRequest(Dictionary<string, ParameterValue> ParameterValues);

/// <param name="ParameterValues">The converted parameter values</param>
public record TestIntegrationMethodRequest(JsonElement ProviderTypeFormData);
