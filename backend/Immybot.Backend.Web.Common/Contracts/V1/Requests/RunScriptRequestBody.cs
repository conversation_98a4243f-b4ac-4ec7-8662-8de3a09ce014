using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class RunScriptRequestBody
{
  public required Script Script { get; set; }
  public Guid CancellationId { get; set; }
  public Guid? TerminalId { get; set; }

  public int? MaintenanceSessionId { get; set; }
  public int? MaintenanceActionId { get; set; }
  public int? SessionLogId { get; set; }
  public int? ComputerId { get; set; }
  public int? TenantId { get; set; }

  public int? MaintenanceTaskId { get; set; }
  public DatabaseType? MaintenanceTaskType { get; set; }
}
