using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;
using NuGet.Versioning;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class UpdateGlobalSoftwareVersionRequestBody : SoftwareVersionRequestBodyBase, IUpdateGlobalSoftwareVersionPayload
{
  public int SoftwareId { get; set; }
  public SemanticVersion? CurrentSemanticVersion { get; set; }

  public override SoftwareType SoftwareType => SoftwareType.LocalSoftware;
  public override string SoftwareIdentifier => SoftwareId.ToString();
}
