using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;
public record ValidateParamBlockParametersFromScriptRequest(
  string Script,
  DatabaseType DatabaseType,
  Dictionary<string, ParameterValue>? ParameterValues,
  int? TenantId = null,
  int? ComputerId = null,
  bool ForceRebind = false,
  bool ValidateRequiresOverrideForOnboarding = false,
  Guid? TerminalId = null,
  int? MaintenanceSessionId = null) : IValidateParamBlockParametersRequest;
