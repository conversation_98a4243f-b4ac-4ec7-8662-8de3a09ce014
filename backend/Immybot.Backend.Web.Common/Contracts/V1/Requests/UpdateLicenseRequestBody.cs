using Immybot.Backend.Application.Interface.Commands.Payloads.Licenses;
using Immybot.Backend.Domain.Models;
using NuGet.Versioning;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class UpdateLicenseRequestBody : IUpdateLicensePayload
{
  public int Id { get; set; }

  public required string Name { get; set; }

  public required string LicenseValue { get; set; }

  public SoftwareType SoftwareType { get; set; }

  public required string SoftwareIdentifier { get; set; }

  /// <summary>
  /// Set in controller, not by the frontend
  /// </summary>
  public string SoftwareName { get; set; } = "";

  public SemanticVersion? SemanticVersion { get; set; }

  public int? TenantId { get; set; }

  public bool RestrictToMajorVersion { get; set; }
}
