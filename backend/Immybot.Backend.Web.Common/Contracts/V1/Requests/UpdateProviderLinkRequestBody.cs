using System.Text.Json;
using Immybot.Backend.Application.Interface.Commands.Payloads;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class UpdateProviderLinkRequestBody : IUpdateProviderLinkPayload
{
  public int Id { get; set; }
  public required string Name { get; set; }
  public bool Disabled { get; set; }
  public JsonElement ProviderTypeFormData { get; set; }
  public List<string> ExcludedCapabilities { get; set; } = [];
  public int? UpdatedBy { get; set; }
}
