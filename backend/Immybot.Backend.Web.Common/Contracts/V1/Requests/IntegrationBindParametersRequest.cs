using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

/// <param name="ProviderLinkId">Optionally provide a provider link id that will be used to supply password values stored in the provider link if the parameter values in this request do not contain values for the password fields.</param>
public record IntegrationBindParametersRequest(
  Dictionary<string, ParameterValue> ParameterValues,
  int? ProviderLinkId = null);
