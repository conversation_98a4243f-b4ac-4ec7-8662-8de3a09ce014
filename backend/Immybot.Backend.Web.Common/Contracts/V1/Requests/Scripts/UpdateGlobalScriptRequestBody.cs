using Immybot.Backend.Application.Interface.Commands.Payloads.Scripts;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Scripts;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests.Scripts;

public class UpdateGlobalScriptRequestBody : IUpdateGlobalScriptPayload
{
  public required string Action { get; set; }

  public required string Name { get; set; }

  public ScriptLanguage ScriptLanguage { get; set; }

  public ScriptExecutionContext ScriptExecutionContext { get; set; }


  public DatabaseType ScriptType => DatabaseType.Global;

  public ScriptCategory ScriptCategory { get; set; }
  public ScriptOutputType OutputType { get; set; }

  public int? Timeout { get; set; }
}
