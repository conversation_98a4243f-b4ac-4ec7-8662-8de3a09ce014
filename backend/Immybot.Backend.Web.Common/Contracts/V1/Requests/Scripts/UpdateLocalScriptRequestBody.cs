using Immybot.Backend.Application.Interface.Commands.Payloads.Scripts;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Scripts;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests.Scripts;

public class UpdateLocalScriptRequestBody : IUpdateLocalScriptPayload
{
  public required string Action { get; set; }

  public required string Name { get; set; }

  public ScriptLanguage ScriptLanguage { get; set; }

  public ScriptExecutionContext ScriptExecutionContext { get; set; }

  public DatabaseType ScriptType => DatabaseType.Local;

  public ScriptCategory ScriptCategory { get; set; }

  public ScriptOutputType OutputType { get; set; }

  public int? Timeout { get; set; }

  public ICollection<TenantScript> Tenants { get; } = new HashSet<TenantScript>();
  public string? ScriptCacheName { get; set; }
  internal string? PublicStorageDownloadUrl { get; set; }
  internal string? ScriptHash { get; set; }
}
