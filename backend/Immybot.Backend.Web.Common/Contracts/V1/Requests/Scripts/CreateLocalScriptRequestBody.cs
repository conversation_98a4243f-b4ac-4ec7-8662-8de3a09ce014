using Immybot.Backend.Application.Interface.Commands.Payloads.Scripts;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Scripts;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests.Scripts;

public class CreateLocalScriptRequestBody : ICreateLocalScriptPayload
{
  public required string Action { get; set; }
  public required string Name { get; set; }
  public ScriptLanguage ScriptLanguage { get; set; }
  public ScriptExecutionContext ScriptExecutionContext { get; set; }
  public DatabaseType ScriptType => DatabaseType.Local;
  public int? Timeout { get; set; }
  public ScriptOutputType OutputType { get; set; }

  public ScriptCategory ScriptCategory { get; set; }
  public ICollection<TenantScript> Tenants { get; set; } = new List<TenantScript>();


  public static CreateLocalScriptRequestBody FromScript(Script script)
  {
    return new CreateLocalScriptRequestBody
    {
      Action = script.Action,
      Name = script.Name,
      ScriptLanguage = script.ScriptLanguage,
      ScriptExecutionContext = script.ScriptExecutionContext,
      Timeout = script.Timeout,
      ScriptCategory = script.ScriptCategory,
      Tenants = script.TenantRelationships,
    };
  }
}
