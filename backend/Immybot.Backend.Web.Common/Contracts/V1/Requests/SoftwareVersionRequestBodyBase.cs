using Immybot.Backend.Application.Interface;
using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;
using NuGet.Versioning;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public abstract class SoftwareVersionRequestBodyBase : ISoftwareVersionDetailsBase
{
  public string? DisplayName { get; set; }
  public string DisplayVersion => SemanticVersion.ToNormalizedString();
  public required SemanticVersion SemanticVersion { get; set; }

  public bool TestRequired { get; set; }

  public string? URL { get; set; }
  public string? RelativeCacheSourcePath { get; set; }
  public string? InstallerFile { get; set; }
  public string? TestFailedError { get; set; }
  public string? PackageHash { get; set; }

  public int? TestScriptId { get; set; }
  public int? InstallScriptId { get; set; }
  public int? UninstallScriptId { get; set; }
  public int? UpgradeScriptId { get; set; }
  public int? PostUninstallScriptId { get; set; }
  public int? PostInstallScriptId { get; set; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public LicenseType LicenseType { get; set; }
  public UpdateActionType UpgradeStrategy { get; set; }
  public PackageType PackageType { get; set; }
  public SoftwareVersionInstallerType InstallerType { get; set; }

  public abstract SoftwareType SoftwareType { get; }
  public abstract string SoftwareIdentifier { get; }

  public string? BlobName { get; set; }

  public string? Notes { get; set; }
  public string? ProductCode { get; set; }

  public SemanticVersion? DependsOnSemanticVersion { get; set; }
}
