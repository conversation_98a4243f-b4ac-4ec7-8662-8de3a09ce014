using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class CreateLocalSoftwareRequestBody : SoftwareRequestBodyBase, ICreateLocalSoftwarePayload
{
  public int? OwnerTenantId { get; set; }
  public ICollection<int> TenantSoftware { get; set; } = [];
  public ICollection<SoftwarePrerequisite> SoftwarePrerequisites { get; set; } = [];
  public DatabaseType? DetectionScriptType { get; set; }
  public DatabaseType? MaintenanceTaskType { get; set; }
  public DatabaseType? RepairScriptType { get; set; }
  public DatabaseType? InstallScriptType { get; set; }
  public DatabaseType? TestScriptType { get; set; }
  public DatabaseType? UpgradeScriptType { get; set; }
  public DatabaseType? UninstallScriptType { get; set; }
  public DatabaseType? PostInstallScriptType { get; set; }
  public DatabaseType? PostUninstallScriptType { get; set; }
  public DatabaseType? DynamicVersionsScriptType { get; set; }
  public DatabaseType? DownloadInstallerScriptType { get; set; }
}
