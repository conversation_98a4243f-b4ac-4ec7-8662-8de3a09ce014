using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;
using NuGet.Versioning;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public class UpdateLocalSoftwareVersionRequestBody : SoftwareVersionRequestBodyBase, IUpdateLocalSoftwareVersionPayload
{
  public int SoftwareId { get; set; }
  public SemanticVersion? CurrentSemanticVersion { get; set; }

  public override SoftwareType SoftwareType => SoftwareType.LocalSoftware;
  public override string SoftwareIdentifier => SoftwareId.ToString();

  public DatabaseType? InstallScriptType { get; set; }

  public DatabaseType? TestScriptType { get; set; }

  public DatabaseType? UpgradeScriptType { get; set; }

  public DatabaseType? UninstallScriptType { get; set; }

  public DatabaseType? PostUninstallScriptType { get; set; }

  public DatabaseType? PostInstallScriptType { get; set; }
}
