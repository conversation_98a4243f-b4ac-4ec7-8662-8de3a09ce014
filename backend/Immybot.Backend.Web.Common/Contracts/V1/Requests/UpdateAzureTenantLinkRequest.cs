using Immybot.Backend.Application.Commands;

namespace Immybot.Backend.Web.Common.Contracts.V1.Requests;

public record UpdateAzureTenantLinkRequest(
  int TenantId,
  string? PrincipalId,
  string? PartnerPrincipalId,
  List<string>? LimitToDomains,
  bool RemoveSyncedUsers,
  bool UnlinkCustomers,
  bool RemoveCustomersSyncedUsers) : UpdateAzureTenantLinkPayload(
  TenantId,
  PrincipalId,
  PartnerPrincipalId,
  LimitToDomains,
  RemoveSyncedUsers,
  UnlinkCustomers,
  RemoveCustomersSyncedUsers);
