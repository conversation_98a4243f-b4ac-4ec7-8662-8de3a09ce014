using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetSmtpConfigResponse
{
  public int? TenantId { get; set; }
  public string? TenantName { get; set; }
  public int Port { get; set; }
  public string Host { get; set; } = string.Empty;
  public bool EnableSSL { get; set; }
  public int Timeout { get; set; }
  public string? Username { get; set; }
  public bool Enabled { get; set; }
  public bool UseAuthentication { get; set; }
  public GetSmtpConfigResponse() { }
  public GetSmtpConfigResponse(SmtpConfig smtp)
  {
    TenantId = smtp.TenantId;
    TenantName = smtp.OwnerTenant?.Name;
    Port = smtp.Port;
    Host = smtp.Host;
    EnableSSL = smtp.EnableSSL;
    Timeout = smtp.Timeout;
    Username = smtp.Username;
    Enabled = smtp.Enabled;
    UseAuthentication = smtp.UseAuthentication;
  }

  internal static Expression<Func<SmtpConfig, GetSmtpConfigResponse>> Projection =>
    x => new GetSmtpConfigResponse()
    {
      TenantId = x.TenantId,
      TenantName = x.OwnerTenant != null ? x.OwnerTenant.Name : null,
      Port = x.Port,
      Host = x.Host,
      EnableSSL = x.EnableSSL,
      Timeout = x.Timeout,
      Username = x.Username,
      UseAuthentication = x.UseAuthentication
    };

}
