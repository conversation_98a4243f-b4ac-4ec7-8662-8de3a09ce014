using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetSoftwarePrerequisiteResponse
{
  public GetSoftwarePrerequisiteResponse(SoftwarePrerequisite p)
  {
    SoftwaresForCondition = p.SoftwaresForCondition.Select(s => new GetSpecifiedSoftwareResponse(s)).ToList();
    SoftwaresToPerformActionOn = p.SoftwaresToPerformActionOn.Select(s => new GetSpecifiedSoftwareResponse(s)).ToList();
    SubjectQualifier = p.SubjectQualifier;
    Condition = p.Condition;
    ActionToPerform = p.ActionToPerform;
  }

  public ICollection<GetSpecifiedSoftwareResponse> SoftwaresForCondition { get; }
  public ICollection<GetSpecifiedSoftwareResponse> SoftwaresToPerformActionOn { get; }
  public SubjectQualifier SubjectQualifier { get; }
  public Condition Condition { get; }
  public ActionToPerform ActionToPerform { get; }
}
