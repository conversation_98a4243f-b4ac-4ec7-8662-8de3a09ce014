using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class DynamicIntegrationTypeResponse
{
  public int Id { get; set; }
  public DatabaseType DatabaseType { get; set; }
  public Guid IntegrationTypeId { get; set; }
  public required string Name { get; set; }
  public bool Enabled { get; set; }
  public int ScriptId { get; set; }
  public DateTime CreatedDateUtc { get; set; }
  public DateTime UpdatedDateUtc { get; set; }
  public string? CreationErrorMessage { get; set; }
  public string? DocsUrl { get; set; }
  public int LogoId { get; set; }
  public GlobalMediaResponse? Logo { get; set; }

  public IntegrationTag Tag { get; set; }

  public DynamicIntegrationTypeResponse() { }

  [SetsRequiredMembers]
  public DynamicIntegrationTypeResponse(
    DynamicIntegrationType dynamicIntegrationType
  )
  {
    Id = dynamicIntegrationType.Id;
    DatabaseType = dynamicIntegrationType.DatabaseType;
    IntegrationTypeId = dynamicIntegrationType.IntegrationTypeId;
    Name = dynamicIntegrationType.Name;
    Enabled = dynamicIntegrationType.Enabled;
    ScriptId = dynamicIntegrationType.ScriptId;
    CreatedDateUtc = dynamicIntegrationType.CreatedDate;
    UpdatedDateUtc = dynamicIntegrationType.UpdatedDate;
    CreationErrorMessage = dynamicIntegrationType.CreationErrorMessage;
    LogoId = dynamicIntegrationType.LogoId;
    Logo = dynamicIntegrationType.Logo != null ? new GlobalMediaResponse(dynamicIntegrationType.Logo) : null;
    DocsUrl = dynamicIntegrationType.DocsUrl;
    Tag = dynamicIntegrationType.Tag;
  }

  internal static Expression<Func<DynamicIntegrationType, DynamicIntegrationTypeResponse>> Projection =>
    x => new DynamicIntegrationTypeResponse
    {
      Id = x.Id,
      DatabaseType = x.DatabaseType,
      IntegrationTypeId = x.IntegrationTypeId,
      Name = x.Name,
      Enabled = x.Enabled,
      ScriptId = x.ScriptId,
      CreatedDateUtc = x.CreatedDate,
      UpdatedDateUtc = x.UpdatedDate,
      CreationErrorMessage = x.CreationErrorMessage,
      LogoId = x.LogoId,
      Logo = x.Logo != null ? new GlobalMediaResponse(x.Logo) : null,
      DocsUrl = x.DocsUrl,
      Tag = x.Tag
    };
}
