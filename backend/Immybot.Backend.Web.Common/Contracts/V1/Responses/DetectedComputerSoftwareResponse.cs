using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class DetectedComputerSoftwareResponse
{
  public int Id { get; set; }
  public string? ComputerName { get; set; }
  public int ComputerId { get; set; }
  public string? SoftwareName { get; set; }
  public int? SoftwareId { get; set; }
  public string? TenantName { get; set; }
  public int TenantId { get; set; }
  public string? PersonName { get; set; }
  public string? PersonEmail { get; set; }
  public int? PersonId { get; set; }
  public string? Version { get; set; }
  public DateTimeOffset DetectedAt { get; set; }
  public DateTime? InstallDate { get; set; }
  public string? Platform { get; set; }
  public Guid? ProductCode { get; set; }
  public Guid? UpgradeCode { get; set; }
  public string? QuietUninstallString { get; set; }
  public string? UninstallString { get; set; }
  public int? SystemComponent { get; set; }
  public string? RegistryPath { get; set; }
  public string? InstallLocation { get; set; }

  internal static readonly Expression<Func<DetectedComputerSoftware, DetectedComputerSoftwareResponse>> Projection = c =>
    new DetectedComputerSoftwareResponse()
    {
      Id = c.Id,
      ComputerName = c.Computer != null && c.Computer.ComputerName != null ? c.Computer.ComputerName : null,
      PersonName = c.PrimaryPerson != null ? c.PrimaryPerson.FirstName + " " + c.PrimaryPerson.LastName : null,
      PersonEmail = c.PrimaryPerson != null ? c.PrimaryPerson.EmailAddress : null,
      SoftwareId = c.GlobalSoftwareId,
      TenantName = c.Tenant != null ? c.Tenant.Name : null,
      TenantId = c.TenantId,
      ComputerId = c.ComputerId,
      PersonId = c.PrimaryPersonId,
      Version = c.DisplayVersion ?? null,
      DetectedAt = c.DetectedAt,
      InstallDate = c.InstallDate,
      Platform = c.Platform,
      ProductCode = c.ProductCode,
      UpgradeCode = c.UpgradeCode,
      RegistryPath = c.RegistryPath,
      InstallLocation = c.InstallLocation,
      QuietUninstallString = c.QuietUninstallString,
      UninstallString = c.UninstallString,
      SystemComponent = c.SystemComponent,
      SoftwareName = c.GlobalSoftwareName ?? c.DisplayName,
    };
}
