using System.Linq.Expressions;
using System.Text.Json;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class ChangeRequestResponse : IChangeRequest
{
  public int Id { get; set; }
  public ChangeRequestObjectType ObjectType { get; set; }
  public int? TargetAssignmentId { get; set; }
  public int? ScriptId { get; set; }
  public JsonElement NewValuesJson { get; set; }
  public ChangeRequestState State { get; set; }
  public int? AcknowledgedByUserId { get; set; }
  public string? AcknowledgedByUserName { get; set; }
  public DateTime CreatedDateUtc { get; set; }
  public string? CreatedByUserName { get; set; }
  public DateTime UpdatedDateUtc { get; set; }
  public List<ChangeRequestCommentResponse> Comments { get; set; } = [];

  internal static Expression<Func<ChangeRequest, ChangeRequestResponse>> Projection => x => new ChangeRequestResponse()
  {
    Id = x.Id,
    ObjectType = x.ObjectType,
    TargetAssignmentId = x.TargetAssignmentId,
    ScriptId = x.ScriptId,
    NewValuesJson = x.NewValuesJson,
    State = x.State,
    AcknowledgedByUserId = x.AcknowledgedByUserId,
    AcknowledgedByUserName = x.AcknowledgedByUserName,
    CreatedDateUtc = x.CreatedDate,
    UpdatedDateUtc = x.UpdatedDate,
    Comments = x.Comments.AsQueryable().Select(ChangeRequestCommentResponse.Projection).ToList(),
    CreatedByUserName = x.CreatedByUser != null && x.CreatedByUser.Person != null ? x.CreatedByUser.Person.FirstName + " " + x.CreatedByUser.Person.LastName : null,
  };

  internal static ChangeRequestResponse CreateResponse(ChangeRequest req) => (new[] { req }).AsQueryable().Select(Projection).First();
}
