using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;
using Sieve.Attributes;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetSimplePersonResponse
{
  [Sieve(CanFilter = true)]
  public int Id { get; set; }
  public string? FirstName { get; set; }
  public string? LastName { get; set; }
  [Sieve(CanFilter = true)]
  public required string EmailAddress { get; set; }
  public string? AzurePrincipalId { get; set; }
  public string? TenantName { get; set; }
  [Sieve(CanFilter = true)]
  public int TenantId { get; set; }
  [Sieve(CanFilter = true)]
  public string? FullName { get; set; }
  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public string? UpdatedBy { get; set; }
  public List<int> PersonTagIds { get; set; } = [];
  public string? OnPremisesSecurityIdentifier { get; set; }
  public List<int> RoleIds { get; set; } = [];
  public int? UserId { get; set; }

  public DateTime? ExpirationDateUTC { get; set; }

  public GetSimplePersonResponse() { }

  [SetsRequiredMembers]
  public GetSimplePersonResponse(Person person)
  {
    Id = person.Id;
    FirstName = person.FirstName;
    LastName = person.LastName;
    EmailAddress = person.EmailAddress;
    AzurePrincipalId = person.AzurePrincipalId;
    TenantId = person.TenantId;
    TenantName = person.OwnerTenant?.Name;
    FullName = person.FirstName + " " + person.LastName;
    UpdatedDateUTC = person.UpdatedDate;
    CreatedDateUTC = person.CreatedDate;
    UpdatedBy = person.UpdatedByUser?.DisplayName;
    PersonTagIds = person.Tags.Select(a => a.Id).ToList();
    OnPremisesSecurityIdentifier = person.OnPremisesSecurityIdentifier;
    RoleIds = person.User?.UserRoles.Select(a => a.RoleId).ToList() ?? [];
    UserId = person.User?.Id;
    ExpirationDateUTC = person.User?.ExpirationDateUTC;
  }

  internal static Expression<Func<Person, GetSimplePersonResponse>> Projection =>
    x => new GetSimplePersonResponse()
    {
      Id = x.Id,
      AzurePrincipalId = x.AzurePrincipalId,
      EmailAddress = x.EmailAddress,
      FirstName = x.FirstName,
      LastName = x.LastName,
      TenantId = x.TenantId,
      TenantName = x.OwnerTenant != null ? x.OwnerTenant.Name : null,
      FullName = x.FirstName + " " + x.LastName,
      UpdatedDateUTC = x.UpdatedDate,
      CreatedDateUTC = x.CreatedDate,
      UpdatedBy = x.UpdatedByUser != null && x.UpdatedByUser.Person != null ? x.UpdatedByUser.Person.DisplayName : null,
      PersonTagIds = x.Tags.Select(a => a.Id).ToList(),
      RoleIds = x.User!.UserRoles.Select(a => a.RoleId).ToList(),
      UserId = x.User != null ? x.User.Id : null,
      ExpirationDateUTC = x.User != null ? x.User.ExpirationDateUTC : null,
    };
}
