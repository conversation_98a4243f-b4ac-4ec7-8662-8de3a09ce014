using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GlobalTargetAssignmentResource : TargetAssignmentResource
{
  public override DatabaseType DatabaseType => DatabaseType.Global;

  private GlobalTargetAssignmentResource() { }

  [SetsRequiredMembers]
  public GlobalTargetAssignmentResource(TargetAssignment a) : base(a) { }

  internal static Expression<Func<TargetAssignment, GlobalTargetAssignmentResource>> Projection =>
    x => new GlobalTargetAssignmentResource()
    {
      Id = x.Id,
      TargetType = x.TargetType,
      Target = x.Target,
      TargetGroupFilter = x.TargetGroupFilter,
      TargetEnforcement = x.TargetEnforcement,
      MaintenanceType = x.MaintenanceType,
      MaintenanceIdentifier = x.MaintenanceIdentifier,
      MaintenanceTaskMode = x.MaintenanceTaskMode,
      SoftwareSemanticVersion = x.SoftwareSemanticVersion,
      SoftwareSemanticVersionString = x.SoftwareSemanticVersionString,
      TargetName = x.TargetName,
      DesiredSoftwareState = x.DesiredSoftwareState,
      SoftwareProviderType = x.SoftwareProviderType,
      UpdatedDateUTC = x.UpdatedDate,
      CreatedDateUTC = x.CreatedDate,
      UpdatedBy = x.UpdatedByUser != null && x.UpdatedByUser.Person != null ? x.UpdatedByUser.Person.DisplayName : null,
      SortOrder = x.SortOrder,
      Excluded = x.Excluded,
      IntegrationTypeId = x.IntegrationTypeId,
      IntegrationPrompt = x.IntegrationPrompt,
      IsCore = x.IsCore,
      ProviderLinkIdForMaintenanceItem = x.ProviderLinkIdForMaintenanceItem,
      Notes = x.Notes != null ? x.Notes.Notes : null,
      NotesUpdatedByUserName = x.Notes != null ? x.Notes.UpdatedByName : null,
      NotesUpdatedUtc = x.Notes != null ? x.Notes.UpdatedDate : null
    };
}
