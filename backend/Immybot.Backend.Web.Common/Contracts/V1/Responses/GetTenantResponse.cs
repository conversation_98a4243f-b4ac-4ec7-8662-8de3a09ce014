using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;
using Sieve.Attributes;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public record AzureTenantResponse(
  string PrincipalId,
  AzureTenantConsentDetails ConsentDetails,
  AzTenantType AzureTenantType,
  string? PartnerPrincipalId,
  AzureTenantInfo? InfoSyncedFromAzure,
  AzureSyncResult? LastGetUsersSyncResult,
  AzureSyncResult? LastGetTenantInfoSyncResult);
public record AzureTenantLinkResponse(
  int ImmyTenantId,
  string AzTenantId,
  AzureTenantResponse AzureTenant,
  bool ShouldLimitDomains,
  List<AzureTenantLinkDomainFilter> LimitToDomains);
public class GetTenantResponse
{
  public GetTenantResponse() { }

  [SetsRequiredMembers]
  public GetTenantResponse(
    Tenant tenant,
    bool includeTags = false)
  {
    Id = tenant.Id;
    Name = tenant.Name;
    Slug = tenant.Slug;
    OwnerTenantId = tenant.TenantId;
    ParentTenantId = tenant.ParentTenantId;
    Active = tenant.Active;
    IsMsp = tenant.IsMsp;
    UpdatedBy = tenant.UpdatedBy;
    UpdatedDateUTC = tenant.UpdatedDate;
    CreatedBy = tenant.CreatedBy;
    CreatedDateUTC = tenant.CreatedDate;
    MarkedForDeletionAtUtc = tenant.MarkedForDeletionAtUtc;

    var link = tenant.AzureTenantLink;
    AzureTenantLink = link?.AzureTenant == null ? null : new AzureTenantLinkResponse(
      link.ImmyTenantId,
      link.AzTenantId,
      new AzureTenantResponse(
        link.AzureTenant.PrincipalId,
        link.AzureTenant.ConsentDetails,
        link.AzureTenant.AzureTenantType,
        link.AzureTenant.PartnerPrincipalId,
        link.AzureTenant.InfoSyncedFromAzure,
        link.AzureTenant.LastGetUsersSyncResult,
        link.AzureTenant.LastGetTenantInfoSyncResult),
      link.ShouldLimitDomains,
      link.LimitToDomains);
    if (includeTags) TenantTagIds = tenant.Tags.Select(a => a.Id).ToList();
  }

  public int Id { get; set; }

  [Sieve(CanFilter = true)]
  public required string Name { get; set; }
  public string? Slug { get; set; }
  public int? ParentTenantId { get; set; }
  public int? OwnerTenantId { get; set; }
  public bool Active { get; set; }
  public AzureTenantLinkResponse? AzureTenantLink { get; set; }
  public bool IsMsp { get; set; }
  public int? UpdatedBy { get; set; }
  public DateTime UpdatedDateUTC { get; set; }
  public int? CreatedBy { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public List<int>? TenantTagIds { get; private set; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public List<string>? TenantTagNames { get; private set; }

  public DateTime? MarkedForDeletionAtUtc { get; set; }

  internal static Expression<Func<Tenant, GetTenantResponse>> GetProjection()
  {
    return x => new GetTenantResponse()
    {
      Id = x.Id,
      Name = x.Name,
      Slug = x.Slug,
      Active = x.Active,
      AzureTenantLink = x.AzureTenantLink != null && x.AzureTenantLink.AzureTenant != null ? new AzureTenantLinkResponse(
        x.AzureTenantLink.ImmyTenantId,
        x.AzureTenantLink.AzTenantId,
        new AzureTenantResponse(
          x.AzureTenantLink.AzureTenant.PrincipalId,
          x.AzureTenantLink.AzureTenant.ConsentDetails,
          x.AzureTenantLink.AzureTenant.AzureTenantType,
          x.AzureTenantLink.AzureTenant.PartnerPrincipalId,
          x.AzureTenantLink.AzureTenant.InfoSyncedFromAzure,
          x.AzureTenantLink.AzureTenant.LastGetUsersSyncResult,
          x.AzureTenantLink.AzureTenant.LastGetTenantInfoSyncResult),
        x.AzureTenantLink.ShouldLimitDomains,
        x.AzureTenantLink.LimitToDomains) : null,
      IsMsp = x.IsMsp,
      UpdatedBy = x.UpdatedBy,
      UpdatedDateUTC = x.UpdatedDate,
      CreatedBy = x.CreatedBy,
      CreatedDateUTC = x.CreatedDate,
      TenantTagIds = x.Tags.Select(a => a.Id).ToList(),
      ParentTenantId = x.ParentTenantId,
      OwnerTenantId = x.TenantId,
      MarkedForDeletionAtUtc = x.MarkedForDeletionAtUtc
    };
  }
}
