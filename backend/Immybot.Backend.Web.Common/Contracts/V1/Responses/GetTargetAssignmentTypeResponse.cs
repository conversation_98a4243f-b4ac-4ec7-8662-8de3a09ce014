using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetTargetAssignmentTypeResponse
{
  public GetTargetAssignmentTypeResponse(TargetAssignment assignment)
  {
    TargetType = assignment.TargetType;
    TenantId = assignment.TenantId;
    PropagateToChildTenants = assignment.PropagateToChildTenants;
    AllowAccessToParentTenant = assignment.AllowAccessToParentTenant;
    Target = assignment.Target;
    DesiredSoftwareState = assignment.DesiredSoftwareState;
    MaintenanceTaskMode = assignment.MaintenanceTaskMode;
    TargetEnforcement = assignment.TargetEnforcement;
  }

  public TargetType TargetType { get; }
  public int? TenantId { get; }
  public bool PropagateToChildTenants { get; }
  public bool AllowAccessToParentTenant { get; }
  public string? Target { get; }
  public DesiredSoftwareState? DesiredSoftwareState { get; }
  public MaintenanceTaskMode? MaintenanceTaskMode { get; }
  public TargetEnforcement TargetEnforcement { get; }
}
