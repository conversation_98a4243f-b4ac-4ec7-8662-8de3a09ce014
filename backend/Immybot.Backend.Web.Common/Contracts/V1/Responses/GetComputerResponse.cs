using System.Text.Json;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Extensions;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Primitives;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetComputerResponse
{
  public GetComputerResponse(
    ExpandedComputer computer,
    bool isLicensed = false,
    bool isEphemeralAgentConnected = false,
    bool includeSessions = false,
    bool includeAdditionalPersons = false,
    bool includePrimaryPerson = false,
    bool includeTenant = false,
    bool includeAgents = false,
    bool includeInventory = false,
    bool includeTags = false)
  {
    // expanded properties
    IsDomainController = computer.IsDomainController;
    IsServer = computer.IsServer;
    IsDesktop = computer.IsDesktop;
    IsPortable = computer.IsPortable;

    Init(computer.Computer, isLicensed, isEphemeralAgentConnected, includeSessions, includeAdditionalPersons, includePrimaryPerson, includeTenant, includeAgents, includeInventory, includeTags: includeTags);
  }

  public GetComputerResponse(
    Computer computer,
    bool isLicensed = false,
    bool isEphemeralAgentConnected = false,
    bool includeSessions = false,
    bool includeAdditionalPersons = false,
    bool includePrimaryPerson = false,
    bool includeTenant = false,
    bool includeAgents = false,
    bool includeInventory = false,
    bool includeTags = false)
  {
    IsDomainController = computer.GetIsDomainController();
    IsServer = computer.GetIsServer();
    IsPortable = computer.ChassisTypes is { } c
      && c.Intersect(ComputerInventoryConstants.PortableChassisTypes).Any();
    IsDesktop = computer.ChassisTypes is not null && IsServer is not true && IsPortable is not true;
    Init(computer, isLicensed, isEphemeralAgentConnected, includeSessions, includeAdditionalPersons, includePrimaryPerson, includeTenant, includeAgents, includeInventory, includeTags: includeTags);
  }

  private void Init(
    Computer computer,
    bool isLicensed,
    bool isEphemeralAgentConnected,
    bool includeSessions,
    bool includeAdditionalPersons,
    bool includePrimaryPerson,
    bool includeTenant,
    bool includeAgents,
    bool includeInventory,
    bool includeTags)
  {
    ArgumentNullException.ThrowIfNull(computer);

    Id = computer.Id;
    TenantId = computer.TenantId;
    PrimaryPersonId = computer.PrimaryPersonId;
    OnboardingStatus = computer.OnboardingStatus;
    ComputerName = computer.GetName();
    EphemeralAgentConnected = isEphemeralAgentConnected;
    IsOnline = EphemeralAgentConnected || computer.GetRunScriptAgents().Any(r => r is { IsOnline: true, ProviderLink.DisabledOrUnhealthy: false });
    DetectionOutdated = computer.DetectionOutdated;
    IsSandbox = computer.IsSandbox;
    ExcludeFromMaintenance = computer.ExcludeFromMaintenance;
    IsDevLab = !string.IsNullOrEmpty(computer.DevLabVmName);
    DevLabVmClaimExpirationDateUtc = computer.DevLabVmClaimExpirationDateUtc;
    DevLabVmUnclaimed = computer.DevLabVmUnclaimed;
    DeviceId = computer.DeviceId;
    Licensed = isLicensed;
    Notes = computer.ComputerNote?.Content;
    ExcludedFromUserAffinity = computer.ExcludedFromUserAffinity;
    if (includeTags)
    {
      ComputerTagIds = computer.Tags.Select(t => t.Id).ToList();
    }

    if (includeSessions)
    {
      Sessions = computer.Sessions.Select(s => new GetMaintenanceSessionResponse(s, includeComputer: false)).ToList();
    }
    if (includeAdditionalPersons)
    {
      AdditionalPersons = computer.AdditionalPersons.Where(a => a.Person != null).Select(a =>
        new GetPersonResponse(a.Person!, includePrimaryComputers: false, includeAdditionalComputers: false)).ToList();
    }
    if (includePrimaryPerson && computer.PrimaryPerson != null)
    {
      PrimaryPerson = new GetPersonResponse(computer.PrimaryPerson, includePrimaryComputers: false, includeAdditionalComputers: false);
    }

    if (includeTenant && computer.OwnerTenant != null)
    {
      Tenant = new GetTenantResponse(computer.OwnerTenant);
      TenantName = Tenant.Name;
    }

    if (includeAgents)
    {
      Agents = computer.Agents.Select(a => new GetProviderAgentResponse(a)).ToList();
    }
    if (includeInventory)
    {
      IsMissingSomeRequiredInventoryResults = !InventoryKeys.SystemRequiredInventoryKeys
        .All(k => computer.LatestInventoryScriptResults.Select(r => r.InventoryKey).Contains(k));
      using var stream = new MemoryStream();
      using (var writer = new Utf8JsonWriter(stream))
      {
        writer.WriteStartObject();
        writer.WriteStartObject(InventoryKeys.InventoryTaskMetaKey);
        foreach (var result in computer.LatestInventoryScriptResults)
        {
          writer.WriteString(result.InventoryKey, result.Timestamp);
        }
        writer.WriteEndObject();
        foreach (var result in computer.LatestInventoryScriptResults)
        {
          WriteInventoryScriptResult(writer, result);
        }
        writer.WriteEndObject();
        writer.Flush();
      }
      Inventory = JsonDocument.Parse(stream.ToArray());
    }
  }

  public static void WriteInventoryScriptResult(
    Utf8JsonWriter writer,
    ComputerInventoryTaskScriptResult result,
    bool includeInventoryTaskMeta = false)
  {
    if (includeInventoryTaskMeta)
    {
      writer.WriteStartObject(InventoryKeys.InventoryTaskMetaKey);
      writer.WriteString(result.InventoryKey, result.Timestamp);
      writer.WriteEndObject();
    }
    if (result.LatestSuccessResult != null)
    {
      writer.WritePropertyName(result.InventoryKey);
      result.LatestSuccessResult.WriteTo(writer);
    }
    if (result.LatestErrorResult != null)
    {
      writer.WritePropertyName($"{result.InventoryKey}_ErrorResult");
      result.LatestErrorResult.WriteTo(writer);
      writer.WriteBoolean($"{result.InventoryKey}_LatestResultIsError", result.LatestResultIsError);
    }
  }

  public int Id { get; private set; }
  public Guid DeviceId { get; private set; }
  public int TenantId { get; private set; }
  public string? ComputerName { get; private set; }
  public int? PrimaryPersonId { get; private set; }
  public ComputerOnboardingStatus OnboardingStatus { get; private set; } = ComputerOnboardingStatus.Onboarded;
  public bool IsOnline { get; private set; }
  public bool? IsDomainController { get; private set; }
  public bool? IsPortable { get; private set; }
  public bool? IsServer { get; private set; }
  public bool? IsDesktop { get; private set; }
  public string? TenantName { get; private set; }
  public JsonDocument? Inventory { get; private set; }
  public bool? IsMissingSomeRequiredInventoryResults { get; set; }
  public bool DetectionOutdated { get; set; }
  public bool IsSandbox { get; set; }
  public bool ExcludeFromMaintenance { get; set; }
  public DateTime? DevLabVmClaimExpirationDateUtc { get; set; }
  public bool IsDevLab { get; set; }
  public bool DevLabVmUnclaimed { get; set; }
  public bool EphemeralAgentConnected { get; set; }
  public bool Licensed { get; set; }
  public string? Notes { get; set; }

  public bool ExcludedFromUserAffinity { get; set; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetMaintenanceSessionResponse> Sessions { get; private set; } = [];

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetPersonResponse> AdditionalPersons { get; private set; } = [];

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetPersonResponse? PrimaryPerson { get; private set; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetTenantResponse? Tenant { get; private set; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetProviderAgentResponse> Agents { get; private set; } = [];

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<int> ComputerTagIds { get; private set; } = [];
}
