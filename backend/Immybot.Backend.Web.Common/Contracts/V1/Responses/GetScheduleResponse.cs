using System.Linq.Expressions;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetScheduleResponse : PopulatedTargetGroup
{
  public GetScheduleResponse() { }
  public GetScheduleResponse(Schedule schedule,
    bool includeProviderLink = false)
  {
    Id = schedule.Id;
    Target = schedule.Target;
    TargetText = schedule.TargetName;
    TargetType = schedule.TargetType;
    TargetGroupFilter = schedule.TargetGroupFilter;
    TargetCategory = schedule.TargetCategory;
    ProviderDeviceGroupType = schedule.ProviderDeviceGroupType;
    ProviderClientGroupType = schedule.ProviderClientGroupType;
    ProviderLinkId = schedule.ProviderLinkId;
    TenantId = schedule.TenantId;
    PropagateToChildTenants = schedule.PropagateToChildTenants;
    Disabled = schedule.Disabled;
    CustomCronExpression = schedule.CustomCronExpression;
    Time = schedule.Time;
    Day = schedule.Day;
    MaintenanceTime = schedule.MaintenanceTime;
    SendDetectionEmail = schedule.SendDetectionEmail;
    SendDetectionEmailWhenAllActionsAreCompliant = schedule.SendDetectionEmailWhenAllActionsAreCompliant;
    SendFollowUpEmail = schedule.SendFollowUpEmail;
    SendFollowUpOnlyIfActionNeeded = schedule.SendFollowUpOnlyIfActionNeeded;
    ShowMaintenanceActions = schedule.ShowMaintenanceActions;
    ShowRunNowButton = schedule.ShowRunNowButton;
    ShowPostponeButton = schedule.ShowPostponeButton;
    UpdatedDateUTC = schedule.UpdatedDate;
    UpdatedBy = schedule.UpdatedBy;
    CreatedDateUTC = schedule.CreatedDate;
    CreatedBy = schedule.CreatedBy;
    UpdatedByName = schedule.UpdatedByUser?.DisplayName;
    MaintenanceType = schedule.MaintenanceType;
    MaintenanceIdentifier = schedule.MaintenanceIdentifier;
    TimeZoneInfoId = schedule.TimeZoneInfoId;
    RebootPreference = schedule.RebootPreference;
    PromptTimeoutAction = schedule.PromptTimeoutAction;
    AutoConsentToReboots = schedule.AutoConsentToReboots;
    PromptTimeoutMinutes = schedule.PromptTimeoutMinutes;
    ApplyWindowsUpdates = schedule.ApplyWindowsUpdates;
    AllowAccessToMSPResources = schedule.AllowAccessToMSPResources;
    AllowAccessToParentTenant = schedule.AllowAccessToParentTenant;
    SuppressRebootsDuringBusinessHours = schedule.SuppressRebootsDuringBusinessHours;
    OfflineBehavior = schedule.OfflineBehavior;
    UseComputersTimezoneForExecution = schedule.UseComputersTimezoneForExecution;
    ScheduleExecutionAfterActiveHours = schedule.ScheduleExecutionAfterActiveHours;

    if (includeProviderLink && schedule.ProviderLink != null)
      ProviderLink = new GetProviderLinkResponse(schedule.ProviderLink, includeSchedules: false);
  }

  public int Id { get; set; }
  public bool Disabled { get; set; }
  public string? CustomCronExpression { get; set; }
  public string? Time { get; set; }
  public int? Day { get; set; }
  public string? MaintenanceTime { get; set; }
  public bool SendDetectionEmail { get; set; }
  public bool SendDetectionEmailWhenAllActionsAreCompliant { get; set; }
  public bool SendFollowUpEmail { get; set; }
  public bool SendFollowUpOnlyIfActionNeeded { get; set; }
  public bool ShowRunNowButton { get; set; }
  public bool ShowPostponeButton { get; set; }
  public bool ShowMaintenanceActions { get; set; }
  public MaintenanceType? MaintenanceType { get; set; }
  public string? MaintenanceIdentifier { get; set; }
  public RebootPreference RebootPreference { get; set; }
  public PromptTimeoutAction PromptTimeoutAction { get; set; }
  public bool AutoConsentToReboots { get; set; }
  public int PromptTimeoutMinutes { get; set; }
  public bool ApplyWindowsUpdates { get; set; }
  public bool AllowAccessToMSPResources { get; set; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)] public GetProviderLinkResponse? ProviderLink { get; set; }

  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public int? UpdatedBy { get; set; }
  public int? CreatedBy { get; set; }
  public string? UpdatedByName { get; set; }

  public string? TimeZoneInfoId { get; set; }
  public ComputerOfflineMaintenanceSessionBehavior OfflineBehavior { get; set; }
  public bool SuppressRebootsDuringBusinessHours { get; set; }

  public DateTime? NextOccurenceDate { get; set; }

  public bool UseComputersTimezoneForExecution { get; set; }
  public bool ScheduleExecutionAfterActiveHours { get; set; }

  internal static Expression<Func<Schedule, GetScheduleResponse>> Projection =>
   x => new GetScheduleResponse()
   {
     Id = x.Id,
     Target = x.Target,
     TargetType = x.TargetType,
     TargetGroupFilter = x.TargetGroupFilter,
     TargetCategory = x.TargetCategory,
     ProviderLinkId = x.ProviderLinkId,
     ProviderDeviceGroupType = x.ProviderDeviceGroupType,
     ProviderClientGroupType = x.ProviderClientGroupType,
     TenantId = x.TenantId,
     PropagateToChildTenants = x.PropagateToChildTenants,
     Disabled = x.Disabled,
     CustomCronExpression = x.CustomCronExpression,
     Time = x.Time,
     Day = x.Day,
     MaintenanceTime = x.MaintenanceTime,
     SendDetectionEmail = x.SendDetectionEmail,
     SendDetectionEmailWhenAllActionsAreCompliant = x.SendDetectionEmailWhenAllActionsAreCompliant,
     SendFollowUpEmail = x.SendFollowUpEmail,
     SendFollowUpOnlyIfActionNeeded = x.SendFollowUpOnlyIfActionNeeded,
     ShowMaintenanceActions = x.ShowMaintenanceActions,
     ShowRunNowButton = x.ShowRunNowButton,
     ShowPostponeButton = x.ShowPostponeButton,
     UpdatedDateUTC = x.UpdatedDate,
     CreatedDateUTC = x.CreatedDate,
     UpdatedBy = x.UpdatedBy,
     CreatedBy = x.CreatedBy,
     UpdatedByName = x.UpdatedBy != null && x.UpdatedByUser != null && x.UpdatedByUser.Person != null ? x.UpdatedByUser.Person.DisplayName : null,
     TimeZoneInfoId = x.TimeZoneInfoId,
     RebootPreference = x.RebootPreference,
     PromptTimeoutAction = x.PromptTimeoutAction,
     AutoConsentToReboots = x.AutoConsentToReboots,
     PromptTimeoutMinutes = x.PromptTimeoutMinutes,
     ApplyWindowsUpdates = x.ApplyWindowsUpdates,
     AllowAccessToMSPResources = x.AllowAccessToMSPResources,
     AllowAccessToParentTenant = x.AllowAccessToParentTenant,
     MaintenanceIdentifier = x.MaintenanceIdentifier,
     MaintenanceType = x.MaintenanceType,
     OfflineBehavior = x.OfflineBehavior,
     SuppressRebootsDuringBusinessHours = x.SuppressRebootsDuringBusinessHours,
     UseComputersTimezoneForExecution = x.UseComputersTimezoneForExecution,
     ScheduleExecutionAfterActiveHours = x.ScheduleExecutionAfterActiveHours,
   };
}
