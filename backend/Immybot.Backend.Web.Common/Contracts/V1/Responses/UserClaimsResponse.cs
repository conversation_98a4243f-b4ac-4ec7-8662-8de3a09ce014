using System.Security.Claims;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

/// <summary>
/// Response model for user claims information
/// </summary>
public class GetClaimsResponse
{
    /// <summary>
    /// Collection of all claims for the user
    /// </summary>
    public IEnumerable<ClaimResponse> Claims { get; set; } = Array.Empty<ClaimResponse>();
}

/// <summary>
/// Represents a single claim with type and value
/// </summary>
public class ClaimResponse
{
    /// <summary>
    /// The type of the claim
    /// </summary>
    public string Type { get; set; } = string.Empty;
    
    /// <summary>
    /// The value of the claim
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Creates a new ClaimResponse
    /// </summary>
    public ClaimResponse() { }

    /// <summary>
    /// Creates a new ClaimResponse from a Claim
    /// </summary>
    /// <param name="claim">The claim to convert</param>
    public ClaimResponse(Claim claim)
    {
        // Guard clause: Ensure claim is not null
        if (claim == null) return;
        
        Type = claim.Type;
        Value = claim.Value;
    }
}
