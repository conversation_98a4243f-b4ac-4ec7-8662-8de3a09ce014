using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetProviderAgentIdentificationLogResponse : IAgentIdentificationLogProperties
{
  public int Id { get; set; }

  public int ProviderAgentId { get; set; }

  public string Message { get; set; } = string.Empty;

  public AgentIdentificationLogType LogType { get; set; }

  public DateTime TimeUtc { get; set; }

  internal static Expression<Func<AgentIdentificationLog, GetProviderAgentIdentificationLogResponse>> Projection =>
    x => new GetProviderAgentIdentificationLogResponse()
    {
      Id = x.Id,
      ProviderAgentId = x.ProviderAgentId,
      Message = x.Message,
      LogType = x.LogType,
      TimeUtc = x.TimeUtc
    };
}
