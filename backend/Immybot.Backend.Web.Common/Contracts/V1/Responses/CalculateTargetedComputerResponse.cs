using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class CalculateTargetedComputerResponse
{
  public int Id { get; set; }
  public string Cn { get; set; } = string.Empty;
  public bool On { get; set; }
  public string Tn { get; set; } = string.Empty;
  public int? Ppi { get; set; }
  public string Ppn { get; set; } = string.Empty;
  public int Ti { get; set; }
  public string Os { get; set; } = string.Empty;
  public ComputerOnboardingStatus Obs { get; set; }
  public string Sn { get; set; } = string.Empty;
  public int? Dr { get; set; }
  public bool Sb { get; set; }
  public List<int> Ct { get; set; } = [];
}
