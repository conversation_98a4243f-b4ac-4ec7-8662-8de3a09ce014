using Immybot.Backend.Application.Interface;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetBrandingResponse : IBrandingDetailsBase
{
  public GetBrandingResponse() { }
  public GetBrandingResponse(Branding branding)
  {
    Id = branding.Id;
    TenantId = branding.TenantId;
    TenantName = branding.OwnerTenant?.Name;
    StartDate = branding.StartDate;
    EndDate = branding.EndDate;
    IgnoreYear = branding.IgnoreYear;
    TimeFormat = branding.TimeFormat;
    FromAddress = branding.FromAddress;
    MascotImgUri = branding.MascotImgUri;
    MascotName = branding.MascotName;
    LogoUri = branding.LogoUri;
    LogoAltText = branding.LogoAltText;
    BackgroundColor = branding.BackgroundColor;
    ForegroundColor = branding.ForegroundColor;
    TableHeaderColor = branding.TableHeaderColor;
    Description = branding.Description;
    UpdatedDateUTC = branding.UpdatedDate;
    CreatedDateUTC = branding.CreatedDate;
    UpdatedBy = branding.UpdatedByUser?.DisplayName;
    TextColor = branding.TextColor;
    TableHeaderTextColor = branding.TableHeaderTextColor;
  }
  public int Id { get; set; }
  public int? TenantId { get; set; }
  public string? TenantName { get; set; }
  public DateTime? StartDate { get; set; }
  public DateTime? EndDate { get; set; }
  public bool? IgnoreYear { get; set; }
  public string? TimeFormat { get; set; }
  public string FromAddress { get; set; } = string.Empty;
  public string? MascotImgUri { get; set; }
  public string? MascotName { get; set; }
  public string? LogoUri { get; set; }
  public string? LogoAltText { get; set; }
  public string? BackgroundColor { get; set; }
  public string? ForegroundColor { get; set; }
  public string? TableHeaderColor { get; set; }
  public string? TableHeaderTextColor { get; set; }
  public string? TextColor { get; set; }
  public string Description { get; set; } = string.Empty;
  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public string? UpdatedBy { get; set; }

  internal static Expression<Func<Branding, GetBrandingResponse>> Projection =>
    x => new GetBrandingResponse()
    {
      Id = x.Id,
      TenantId = x.TenantId,
      StartDate = x.StartDate,
      EndDate = x.EndDate,
      IgnoreYear = x.IgnoreYear,
      TimeFormat = x.TimeFormat,
      FromAddress = x.FromAddress,
      MascotImgUri = x.MascotImgUri,
      MascotName = x.MascotName,
      LogoUri = x.LogoUri,
      LogoAltText = x.LogoAltText,
      BackgroundColor = x.BackgroundColor,
      ForegroundColor = x.ForegroundColor,
      TableHeaderColor = x.TableHeaderColor,
      Description = x.Description,
      TenantName = x.OwnerTenant != null ? x.OwnerTenant.Name : null,
      UpdatedDateUTC = x.UpdatedDate,
      CreatedDateUTC = x.CreatedDate,
      UpdatedBy = x.UpdatedBy != null &&
        x.UpdatedByUser != null &&
        x.UpdatedByUser.Person != null
          ? x.UpdatedByUser.Person.DisplayName
          : null,
      TextColor = x.TextColor,
      TableHeaderTextColor = x.TableHeaderTextColor,
    };
}
