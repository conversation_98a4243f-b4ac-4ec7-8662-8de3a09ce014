using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class ComputerUserAffinityResponse
{
  public int Id { get; set; }
  public int PersonId { get; set; }
  public DateTime Date { get; set; }
  public string PersonName { get; set; } = "";
  public int ComputerId { get; set; }
  public string ComputerName { get; set; } = "";
  public int TenantId { get; set; }
  public string TenantName { get; set; } = "";

  public static readonly Expression<Func<UserAffinity, ComputerUserAffinityResponse>> Project =
    source => new ComputerUserAffinityResponse
    {
      Id = source.Id,
      PersonId = source.PersonId,
      Date = source.Date,
      PersonName = source.Person != null ? source.Person.FirstName + " " + source.Person.LastName : String.Empty,
      ComputerId = source.ComputerId,
      ComputerName = source.Computer != null && source.Computer.ComputerName != null ? source.Computer.ComputerName : String.Empty,
      TenantId = source.Computer!.TenantId,
      TenantName = source.Computer != null && source.Computer.OwnerTenant != null
        ? source.Computer.OwnerTenant.Name
        : String.Empty
    };
}
