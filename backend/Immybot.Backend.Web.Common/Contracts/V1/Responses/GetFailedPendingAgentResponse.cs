using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;
using Sieve.Attributes;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

// TODO: Remove this and just use GetPendingAgentResponse
public class GetFailedPendingAgentResponse
{
  public int Id { get; set; }
  public string? ComputerName { get; set; }
  [Sieve(CanFilter = true)]
  public string? OperatingSystemName { get; set; }
  public string? Serial { get; set; }
  [Sieve(CanFilter = true)]
  public required string ExternalAgentId { get; set; }
  public required string DeviceId { get; set; }
  [Sieve(CanFilter = true)]
  public DateTime DateAdded { get; set; }
  [Sieve(CanFilter = true)]
  public bool IsOnline { get; set; }
  public required string ProviderLinkName { get; set; }
  public required string ExternalClientName { get; set; }
  public DateTime? OSInstallDate { get; set; }
  public DateTime? OsInstallDateUtc { get; set; }

  public List<GetAgentIdentificationFailureResponse> IdentificationFailures { get; } = [];
  internal static Expression<Func<ProviderAgent, GetFailedPendingAgentResponse>> Projection =>
   x => new GetFailedPendingAgentResponse()
   {
     Id = x.Id,
     ExternalAgentId = x.ExternalAgentId,
     DeviceId = x.ExternalAgentId,
     DateAdded = x.DateAddedUTC,
     IsOnline = x.IsOnline,
     ProviderLinkName = x.ProviderLink != null ? x.ProviderLink.Name : String.Empty,
     ExternalClientName = x.ProviderClient != null ? x.ProviderClient.ExternalClientName : String.Empty,
     ComputerName = x.DeviceDetails.DeviceName,
     OperatingSystemName = x.DeviceDetails.OperatingSystemName,
     Serial = x.DeviceDetails.SerialNumber,
     OSInstallDate = x.DeviceDetails.OSInstallDateUTC,
     OsInstallDateUtc = x.DeviceDetails.OSInstallDateUTC,
   };
}
