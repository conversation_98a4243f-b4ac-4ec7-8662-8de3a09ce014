using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetOptionalTargetAssignmentApprovalResponse
{
  public int Id { get; set; }
  public int TargetAssignmentId { get; set; }
  public TargetAssignmentApprovalStatus Approved { get; set; }
  public TargetType TargetType { get; set; }
  public string? Target { get; set; }
  public int? UpdatedBy { get; set; }
  public int? CreatedBy { get; set; }
  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public MaintenanceType MaintenanceType { get; set; }

  public GetOptionalTargetAssignmentApprovalResponse() { }

  public GetOptionalTargetAssignmentApprovalResponse(OptionalTargetAssignmentApproval approval)
  {
    Id = approval.Id;
    TargetAssignmentId = approval.TargetAssignmentId;
    Approved = approval.Approved;
    TargetType = approval.TargetType;
    Target = approval.Target;
    UpdatedBy = approval.UpdatedBy;
    CreatedBy = approval.CreatedBy;
    UpdatedDateUTC = approval.UpdatedDate;
    CreatedDateUTC = approval.CreatedDate;
    MaintenanceType = approval.MaintenanceType;
  }

  internal static Expression<Func<OptionalTargetAssignmentApproval, GetOptionalTargetAssignmentApprovalResponse>> Projection =>
  x => new GetOptionalTargetAssignmentApprovalResponse()
  {
    Id = x.Id,
    TargetAssignmentId = x.TargetAssignmentId,
    Approved = x.Approved,
    TargetType = x.TargetType,
    Target = x.Target,
    UpdatedBy = x.UpdatedBy,
    CreatedBy = x.CreatedBy,
    UpdatedDateUTC = x.UpdatedDate,
    CreatedDateUTC = x.CreatedDate,
    MaintenanceType = x.MaintenanceType
  };
}
