using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Scripts;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetMaintenanceSessionLogResponse
{
  public int Id { get; }
  public Guid? ProgressCorrelationId { get; set; }

  public int? SessionPhaseId { get; }
  public string? Message { get; }
  public int MaintenanceSessionId { get; }
  public int? MaintenanceSessionStageId { get; }
  public int? MaintenanceActionId { get; }
  public MaintenanceActionStatus? MaintenanceActionStatus { get; set; }
  public int? ScriptId { get; }
  public string? Script { get; }
  public string? ScriptOutput { get; }
  public DatabaseType? ScriptType { get; }
  public ScriptLanguage? ScriptLanguage { get; }
  public Dictionary<string, object?> ScriptParameters { get; }
  public Dictionary<string, object?> ParamBlockParameters { get; }
  public DateTime Time { get; }
  public DateTime? UpdatedTime { get; }
  public SessionLogType SessionLogType { get; }
  public int? ScriptTimeout { get; }
  public ScriptExecutionContext? ScriptExecutionContext { get; }
  public bool IsPrimary { get; }
  public decimal? ProgressPercentComplete { get; set; }
  public string? ProgressActivity { get; set; }
  public string? ProgressStatus { get; set; }
  public double? ProgressSecondsRemaining { get; set; }
  public string? ProgressCurrentOperation { get; set; }
  public bool ProgressCompleted { get; set; }

  public GetMaintenanceSessionLogResponse(SessionLog sessionLog)
  {
    Id = sessionLog.Id;
    SessionPhaseId = sessionLog.SessionPhaseId;
    ProgressCorrelationId = sessionLog.ProgressCorrelationId;
    Message = sessionLog.Message;
    MaintenanceActionId = sessionLog.MaintenanceActionId;
    MaintenanceSessionId = sessionLog.MaintenanceSessionId;
    MaintenanceSessionStageId = sessionLog.MaintenanceSessionStageId;
    Time = sessionLog.Time;
    UpdatedTime = sessionLog.UpdatedTime;
    ScriptId = sessionLog.ScriptId;
    Script = sessionLog.Script;
    ScriptOutput = sessionLog.ScriptOutput;
    ScriptType = sessionLog.ScriptType;
    ScriptLanguage = sessionLog.ScriptLanguage;
    ScriptParameters = sessionLog.ScriptParameters;
    ParamBlockParameters = sessionLog.ParamBlockParameters;
    MaintenanceActionStatus = sessionLog.MaintenanceActionStatus;
    SessionLogType = sessionLog.SessionLogType;
    ScriptTimeout = sessionLog.ScriptTimeout;
    ScriptExecutionContext = sessionLog.ScriptExecutionContext;
    IsPrimary = sessionLog.IsPrimary;
    ProgressPercentComplete = sessionLog.ProgressPercentComplete;
    ProgressActivity = sessionLog.ProgressActivity;
    ProgressSecondsRemaining = sessionLog.ProgressSecondsRemaining;
    ProgressCurrentOperation = sessionLog.ProgressCurrentOperation;
    ProgressCompleted = sessionLog.ProgressCompleted;
    ProgressStatus = sessionLog.ProgressStatus;
  }

  internal static Expression<Func<SessionLog, GetMaintenanceSessionLogResponse>> Projection =>
    x => new GetMaintenanceSessionLogResponse(x);
}
