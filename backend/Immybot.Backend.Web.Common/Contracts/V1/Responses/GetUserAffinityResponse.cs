using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetUserAffinityResponse
{
  public GetUserAffinityResponse(
    UserAffinity userAffinity,
    bool includePerson = false)
  {
    Id = userAffinity.Id;
    ComputerId = userAffinity.ComputerId;
    PersonId = userAffinity.PersonId;
    Date = userAffinity.Date;
    if (includePerson && userAffinity.Person != null)
      Person = new GetPersonResponse(userAffinity.Person, includeUserAffinities: false);
  }
  public int Id { get; }
  public int ComputerId { get; }
  public int PersonId { get; }
  public DateTime Date { get; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetPersonResponse? Person { get; }
}
