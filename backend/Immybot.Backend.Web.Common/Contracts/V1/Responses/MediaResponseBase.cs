using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class MediaResponseBase
{
  public int Id { get; private protected set; }
  public string Name { get; private protected set; } = string.Empty;
  public string FileName { get; private protected set; } = string.Empty;
  public string? MimeType { get; private protected set; }
  public string? PackageHash { get; private protected set; }
  public string BlobReference { get; private protected set; } = string.Empty;
  public string? RelativeCacheSourcePath { get; private protected set; }
  public DateTime UpdatedDateUTC { get; private protected set; }
  public DateTime CreatedDateUTC { get; private protected set; }
  public DatabaseType DatabaseType { get; private protected set; }
  public MediaCategory Category { get; private protected set; }

  protected MediaResponseBase() { }

  internal MediaResponseBase(Media media)
  {
    if (media == null) return;
    Id = media.Id;
    Name = media.Name;
    FileName = media.FileName;
    MimeType = media.MimeType;
    PackageHash = media.PackageHash;
    BlobReference = media.BlobReference;
    RelativeCacheSourcePath = media.RelativeCacheSourcePath;
    UpdatedDateUTC = media.UpdatedDate;
    CreatedDateUTC = media.CreatedDate;
    DatabaseType = media.DatabaseType;
    Category = media.Category;
  }
}
