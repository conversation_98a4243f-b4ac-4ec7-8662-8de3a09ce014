using Immybot.Backend.Application.Interface.Commands.Payloads.Scripts;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Scripts;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class PackageAnalyzerScriptResponse : IScriptDetailsBase
{
  public PackageAnalyzerScriptResponse(Script script)
  {
    Id = script.Id;
    Action = script.Action;
    Name = script.Name;
    ScriptLanguage = script.ScriptLanguage;
    ScriptExecutionContext = script.ScriptExecutionContext;
    ScriptType = script.ScriptType;
    ScriptCategory = script.ScriptCategory;
    Timeout = script.Timeout;
  }
  public int Id { get; set; }
  public string Action { get; set; }
  public string Name { get; set; }
  public ScriptLanguage ScriptLanguage { get; set; }
  public ScriptExecutionContext ScriptExecutionContext { get; set; }
  public DatabaseType ScriptType { get; set; }
  public ScriptCategory ScriptCategory { get; set; }
  public ScriptOutputType OutputType { get; set; }
  public int? Timeout { get; set; }
}
