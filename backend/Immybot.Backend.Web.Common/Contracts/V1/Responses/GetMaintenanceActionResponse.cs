using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Application.Interface.Extensions;
using Immybot.Backend.Domain.Models;
using Nito.Disposables.Internals;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetMaintenanceActionResponse
{
  public GetMaintenanceActionResponse() { }

  [SetsRequiredMembers]
  public GetMaintenanceActionResponse(MaintenanceAction maintenanceAction)
  {
    ArgumentNullException.ThrowIfNull(maintenanceAction);

    Id = maintenanceAction.Id;
    ComputerId = maintenanceAction.ComputerId;
    MaintenanceSessionId = maintenanceAction.MaintenanceSessionId;
    MaintenanceDisplayName = maintenanceAction.MaintenanceDisplayName;
    AssignmentId = maintenanceAction.AssignmentId;
    AssignmentType = maintenanceAction.AssignmentType;
    MaintenanceIdentifier = maintenanceAction.MaintenanceIdentifier;
    MaintenanceType = maintenanceAction.MaintenanceType;
    DetectedVersionString = maintenanceAction.DetectedVersionString;
    DesiredVersionString = maintenanceAction.DesiredVersionString;
    ActionType = maintenanceAction.ActionType;
    StartTime = maintenanceAction.StartTime;
    EndTime = maintenanceAction.EndTime;
    Reason = maintenanceAction.ActionReason;
    Result = maintenanceAction.ActionResult;
    Status = maintenanceAction.ActionStatus;
    CreatedDateUTC = maintenanceAction.CreatedDate;
    UpdatedDateUTC = maintenanceAction.UpdatedDate;
    CreatedBy = maintenanceAction.CreatedBy;
    UpdatedBy = maintenanceAction.UpdatedBy;
    TenantName = maintenanceAction.MaintenanceSession?.OwnerTenant?.Name ?? String.Empty;
    TenantId = maintenanceAction.MaintenanceSession?.TenantId;
    MaintenanceTaskMode = maintenanceAction.MaintenanceTaskMode;
    SoftwareType = maintenanceAction.SoftwareType;
    MaintenanceTaskType = maintenanceAction.MaintenanceTaskType;
    ResultReason = maintenanceAction.ActionResultReason;
    ResultReasonMessage = maintenanceAction.ActionResultReasonMessage ?? String.Empty;
    MaintenanceTaskGetResult = maintenanceAction.MaintenanceTaskGetResult ?? String.Empty;
    DependentsNames = maintenanceAction.Dependents.Select(a => a.Dependent?.MaintenanceDisplayName).WhereNotNull().ToList();
    DependsOnNames = maintenanceAction.DependsOn.Select(a => a.DependsOn?.MaintenanceDisplayName).WhereNotNull().ToList();
    DesiredSoftwareState = maintenanceAction.DesiredSoftwareState;
    Description = maintenanceAction.Description;
    PostMaintenanceTest = maintenanceAction.PostMaintenanceTest ?? String.Empty;
    PostMaintenanceTestType = maintenanceAction.PostMaintenanceTestType;
    PostMaintenanceTestResult = maintenanceAction.PostMaintenanceTestResult;
    PostMaintenanceTestResultMessage = maintenanceAction.PostMaintenanceTestResultMessage ?? String.Empty;
    SoftwareActionIdForConfigurationTask = maintenanceAction.SoftwareActionIdForConfigurationTask;
    PolicyDescription = maintenanceAction.PolicyDescription;
    ParentId = maintenanceAction.ParentId;
  }
  public int Id { get; set; }
  public int? ParentId { get; set; }
  public int MaintenanceSessionId { get; set; }
  public int? TenantId { get; set; }
  public int? ComputerId { get; set; }
  public string? ComputerName { get; set; }
  public bool IsComputerOnline { get; set; }

  public DateTime StartTime { get; set; }
  public DateTime EndTime { get; set; }

  public MaintenanceActionType ActionType { get; set; }
  public string ActionTypeName => ActionType.ToString();
  public MaintenanceActionStatus Status { get; set; }
  public string StatusName => Status.ToString();

  public int? AssignmentId { get; set; }
  public DatabaseType? AssignmentType { get; set; }
  public string? TenantName { get; set; }
  public MaintenanceType MaintenanceType { get; set; }
  public required string MaintenanceIdentifier { get; set; }

  public DesiredSoftwareState? DesiredSoftwareState { get; set; }
  public string Description { get; set; } = string.Empty;
  public string? PostMaintenanceTest { get; }
  public int? PostMaintenanceTestType { get; }
  public bool? PostMaintenanceTestResult { get; }
  public string? PostMaintenanceTestResultMessage { get; }

  public string MaintenanceTypeName => MaintenanceType.ToString();
  public string? MaintenanceDisplayName { get; set; }
  public string? DetectedVersionString { get; set; }
  public string? DesiredVersionString { get; set; }
  public MaintenanceActionReason Reason { get; set; }
  public MaintenanceActionResult Result { get; set; }
  public string ResultName => Result.ToString();
  public MaintenanceTaskMode? MaintenanceTaskMode { get; set; }
  public string? MaintenanceTaskGetResult { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public DateTime UpdatedDateUTC { get; set; }

  public int? UpdatedBy { get; set; }
  public int? CreatedBy { get; set; }

  public SoftwareType? SoftwareType { get; set; }
  public DatabaseType? MaintenanceTaskType { get; set; }

  public MaintenanceActionResultReason? ResultReason { get; set; }
  public string? ResultReasonMessage { get; set; }

  public ICollection<string> DependsOnNames { get; set; } = [];
  public ICollection<string> DependentsNames { get; set; } = [];

  public int? SoftwareActionIdForConfigurationTask { get; set; }
  public SoftwareProviderType SoftwareProviderType { get; set; } = SoftwareProviderType.Inherent;
  public string? PolicyDescription { get; set; }

  internal static Expression<Func<MaintenanceAction, GetMaintenanceActionResponse>> Projection =>
    x => new GetMaintenanceActionResponse()
    {
      Id = x.Id,
      ParentId = x.ParentId,
      ComputerId = x.ComputerId,
      ComputerName = x.MaintenanceSession != null && x.MaintenanceSession.Computer != null && x.MaintenanceSession.Computer.ComputerName != null ? x.MaintenanceSession.Computer.ComputerName : null,
      MaintenanceSessionId = x.MaintenanceSessionId,
      MaintenanceDisplayName = x.MaintenanceDisplayName ?? null,
      AssignmentId = x.AssignmentId,
      AssignmentType = x.AssignmentType,
      MaintenanceIdentifier = x.MaintenanceIdentifier,
      MaintenanceType = x.MaintenanceType,
      DetectedVersionString = x.DetectedVersionString ?? null,
      DesiredVersionString = x.DesiredVersionString ?? null,
      ActionType = x.ActionType,
      StartTime = x.StartTime,
      EndTime = x.EndTime,
      Reason = x.ActionReason,
      Result = x.ActionResult,
      Status = x.ActionStatus,
      CreatedDateUTC = x.CreatedDate,
      UpdatedDateUTC = x.UpdatedDate,
      CreatedBy = x.CreatedBy,
      UpdatedBy = x.UpdatedBy,
      TenantId = x.TenantId,
      TenantName = x.MaintenanceSession != null && x.MaintenanceSession.OwnerTenant != null
        ? x.MaintenanceSession.OwnerTenant.Name
        : null,
      MaintenanceTaskMode = x.MaintenanceTaskMode,
      DesiredSoftwareState = x.DesiredSoftwareState,
      SoftwareType = x.SoftwareType,
      MaintenanceTaskType = x.MaintenanceTaskType,
      ResultReason = x.ActionResultReason,
      ResultReasonMessage = x.ActionResultReasonMessage ?? null,
      MaintenanceTaskGetResult = x.MaintenanceTaskGetResult ?? null,
      DependsOnNames = x.DependsOn.Select(a => a.DependsOn != null ? a.DependsOn.MaintenanceDisplayName : null).WhereNotNull().ToList(),
      DependentsNames = x.Dependents.Select(a => a.Dependent != null ? a.Dependent.MaintenanceDisplayName : null).WhereNotNull().ToList(),
      Description = x.Description,
      SoftwareActionIdForConfigurationTask = x.SoftwareActionIdForConfigurationTask,
      PolicyDescription = x.PolicyDescription
    };
}
