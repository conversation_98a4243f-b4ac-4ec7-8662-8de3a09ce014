using Immybot.Backend.Application.Interface.Commands.Payloads.Scripts;
using Immybot.Backend.Application.PackageAnalyzer;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public abstract class BaseAnalyzePackageResponse
{
  protected BaseAnalyzePackageResponse(PackageAnalyzerResponse analyzerResponse)
  {
    PackageHash = analyzerResponse.MD5;
    PackageType = analyzerResponse.PackageType;
    DisplayVersion = analyzerResponse.DisplayVersion;
    Extension = analyzerResponse.Extension;
    Description = analyzerResponse.Description;
    SoftwareTableName = analyzerResponse.SoftwareTableName;
    FileName = analyzerResponse.FileName;

    if (Guid.TryParse(analyzerResponse.ProductCode, out var p) &&
      p != Guid.Empty)
    {
      ProductCode = p;
    }

    if (Guid.TryParse(analyzerResponse.UpgradeCode, out var u) &&
      u != Guid.Empty)
    {
      UpgradeCode = u;
    }

    if (analyzerResponse.SuggestedPowerShellInstallScript != null)
      PowerShellInstallScript = new PackageAnalyzerScriptResponse(analyzerResponse.SuggestedPowerShellInstallScript);
    if (analyzerResponse.SuggestedBatchInstallScript != null)
      BatchInstallScript = new PackageAnalyzerScriptResponse(analyzerResponse.SuggestedBatchInstallScript);
    if (analyzerResponse.SuggestedDetectionScript != null)
      DetectionScript = new PackageAnalyzerScriptResponse(analyzerResponse.SuggestedDetectionScript);
    if (analyzerResponse.SuggestedPowerShellUninstallScript != null)
      PowerShellUninstallScript = new PackageAnalyzerScriptResponse(analyzerResponse.SuggestedPowerShellUninstallScript);
    if (analyzerResponse.SuggestedPowershellSoftwareUninstallScript != null)
      DefaultPowershellUninstallScript = new PackageAnalyzerScriptResponse(analyzerResponse.SuggestedPowershellSoftwareUninstallScript);
  }
  public string? FileName { get; set; }
  public string? PackageHash { get; }
  public PackageType? PackageType { get; set; }
  public string? DisplayVersion { get; set; }
  public string? Extension { get; set; }
  public string? Description { get; set; }
  public string? SoftwareTableName { get; set; }
  public Guid? UpgradeCode { get; set; }
  public Guid? ProductCode { get; set; }

  public bool IsExe => Extension == ".exe";
  public bool IsMsi => Extension == ".msi";

  public IScriptDetailsBase? PowerShellInstallScript { get; set; }
  public IScriptDetailsBase? BatchInstallScript { get; set; }
  public IScriptDetailsBase? PowerShellUninstallScript { get; set; }
  public IScriptDetailsBase? DetectionScript { get; set; }
  public IScriptDetailsBase? DefaultPowershellUninstallScript { get; set; }
}
