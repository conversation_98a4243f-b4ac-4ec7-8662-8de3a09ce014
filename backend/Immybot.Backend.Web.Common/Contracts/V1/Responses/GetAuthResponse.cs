using Immybot.Backend.Application.Lib;
using Immybot.Manager.Shared;
using NuGet.Versioning;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetAuthResponse
{
  public bool Impersonating { get; set; }
  public int UserId { get; set; }
  public int TenantId { get; set; }
  public int? PersonId { get; set; }
  public string DisplayName { get; set; } = string.Empty;
  public string FirstName { get; set; } = string.Empty;
  public string LastName { get; set; } = string.Empty;
  public string Email { get; set; } = string.Empty;
  public string TenantName { get; set; } = string.Empty;
  public bool IsAdmin { get; set; }
  public bool IsMSP { get; set; }
  public bool IsImmense { get; set; }
  public bool HasManagementAccess { get; set; }
  public bool IsSupportTechnician { get; set; }
  public bool IsImmySupportAccessEnabled { get; set; }
  public DevInstanceDetails? DevInstanceDetails { get; set; }
  public string LocalSoftwareEndpoint { get; set; } = string.Empty;
  public string GlobalSoftwareEndpoint { get; set; } = string.Empty;
  public string LocalPublicMediaContainerName { get; set; } = string.Empty;
  public string GlobalPublicMediaContainerName { get; set; } = string.Empty;
  public string? BackendRegAppId { get; internal set; }
  public bool UserLevelAuthSelected { get; set; }
  public SubscriptionStatus? Status { get; set; }
  public string? PlanId { get; set; }
  public int? PlanPrice { get; set; }
  public int? PlanQuantity { get; set; }
  public ICollection<SubscriptionAddonDto> Addons { get; set; }
  public DateTime? TrialStartUtc { get; set; }
  public DateTime? TrialEndUtc { get; set; }
  public bool UpdateAvailable { get; set; }
  public SemanticVersion? CurrentReleaseVersion { get; set; }
  public ReleaseChannel? CurrentReleaseReleaseChannel { get; set; }
  public bool IsInstanceUpdating { get; set; }
  public bool IsInstanceRestarting { get; set; }
  public bool InstanceUpdateHasFailed { get; set; }
  public string? InstanceUpdateSource { get; set; }
  public int MaxRunningSessionCount { get; set; }
  public int OpenAccessRequestCount { get; set; }
  public int? MaximumTrackableComputers { get; set; }
  public bool CanManageCrossTenantDeployments { get; set; }

  // FEATURES
  public string? ImmyProduct { get; set; }
  public ICollection<Feature> Features { get; set; }

  public ReleaseChannel InstanceReleaseChannel { get; set; }

  public int? DaysLeftInTrial
  {
    get
    {
      // if no trial end date then return null
      if (!TrialEndUtc.HasValue) return null;

      // if we are no longer in a trial then return null
      if (Status != SubscriptionStatus.InTrial || DateTime.UtcNow > TrialEndUtc) return null;

      // add a day to the trial end since the 'Days' property will always return 1 less than what is actually expected.
      // e.g. 23 hours and 59 minutes left in a trial will return 0 days in timespan.
      return (TrialEndUtc.Value.AddDays(1) - DateTime.UtcNow).Days;
    }
  }

  public IEnumerable<ClaimResponse>? Claims { get; set; }

  public GetAuthResponse()
  {
    Addons = [];
    Features = [];
  }
}

public class DevInstanceDetails
{
  public required string BackendVersion { get; set; }
  public bool IsHangfireServerRunning { get; set; }
  public Dictionary<string, string> PostRoutes { get; } = [];
}

