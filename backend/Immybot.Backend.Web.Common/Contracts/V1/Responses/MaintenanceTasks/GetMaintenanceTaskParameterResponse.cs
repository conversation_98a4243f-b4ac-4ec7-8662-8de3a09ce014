using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses.MaintenanceTasks;

public class GetMaintenanceTaskParameterResponse
{
  public GetMaintenanceTaskParameterResponse(MaintenanceTaskParameter p)
  {
    Id = p.Id;
    MaintenanceTaskId = p.MaintenanceTaskId;
    Name = p.Name;
    DataType = p.DataType;
    Required = p.Required;
    SelectableValues = p.SelectableValues ?? [];
    Notes = p.Notes;
    DefaultValue = p.DefaultValue;
    Hidden = p.Hidden;
    Order = p.Order;
    DefaultMediaId = p.DefaultMediaId;
    DefaultMediaDatabaseType = p.DefaultMediaDatabaseType;
    if (p.DefaultMedia != null) DefaultMedia = new(p.DefaultMedia);
  }

  public int Id { get; set; }
  public int MaintenanceTaskId { get; set; }
  public string Name { get; set; }
  public MaintenanceTaskParameterType DataType { get; set; }
  public bool Required { get; set; }
  public ICollection<string> SelectableValues { get; set; }
  public string? Notes { get; set; }
  public string? DefaultValue { get; set; }
  public bool Hidden { get; set; }
  public int Order { get; set; }
  public int? DefaultMediaId { get; set; }
  public DatabaseType? DefaultMediaDatabaseType { get; set; }
  public MediaResponseBase? DefaultMedia { get; set; }
}
