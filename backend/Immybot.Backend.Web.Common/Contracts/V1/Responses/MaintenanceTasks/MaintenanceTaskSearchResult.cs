using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses.MaintenanceTasks;

public class MaintenanceTaskSearchResult
{
  public int Id { get; }
  public DatabaseType DatabaseType { get; }
  public string Name { get; }
  public bool Owned { get; }
  public bool UseScriptParamBlock { get; }
  public MaintenanceTaskSearchResult(MaintenanceTask maintenanceTask)
  {
    Name = maintenanceTask.Name;
    Id = maintenanceTask.Id;
    DatabaseType = maintenanceTask.DatabaseType;
    UseScriptParamBlock = maintenanceTask.UseScriptParamBlock;
    Owned = maintenanceTask.TenantRelationships.Any(a => a.Relationship == Relationship.Owned);
  }
}
