using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses.MaintenanceTasks;

public class GetLocalMaintenanceTaskResponse : IMaintenanceTaskBase
{
  public GetLocalMaintenanceTaskResponse() { }

  [SetsRequiredMembers]
  public GetLocalMaintenanceTaskResponse(MaintenanceTask task)
  {
    Id = task.Id;
    Name = task.Name;
    OnboardingOnly = task.OnboardingOnly;
    IgnoreDuringAutomaticOnboarding = task.IgnoreDuringAutomaticOnboarding;
    MaintenanceTaskCategory = task.MaintenanceTaskCategory;
    TestEnabled = task.TestEnabled;
    TestScriptId = task.TestScriptId;
    TestScriptType = task.TestScriptType;
    SetEnabled = task.SetEnabled;
    SetScriptId = task.SetScriptId;
    SetScriptType = task.SetScriptType;
    GetEnabled = task.GetEnabled;
    GetScriptId = task.GetScriptId;
    GetScriptType = task.GetScriptType;
    Parameters = task.Parameters
      .Select(p => new GetMaintenanceTaskParameterResponse(p))
      .ToList();
    Tenants = task.TenantRelationships;
    UpdatedDateUTC = task.UpdatedDate;
    CreatedDateUTC = task.CreatedDate;
    UpdatedBy = task.UpdatedByUser?.DisplayName;
    Recommended = task.Recommended;
    IsConfigurationTask = task.IsConfigurationTask;
    IconMediaId = task.IconMediaId;
    ExecuteSerially = task.ExecuteSerially;
    Notes = task.Notes;
    if (task.Icon != null)
    {
      Icon = new MediaResponseBase(task.Icon);
    }
    UseScriptParamBlock = task.UseScriptParamBlock;
    SupersededByTaskMigrationScriptId = task.SupersededByTaskMigrationScriptId;
    SupersededByTaskMigrationScriptType = task.SupersededByTaskMigrationScriptType;
    SupersededByTaskId = task.SupersededByTaskId;
    SupersededByTaskType = task.SupersededByTaskType;
    IntegrationTypeId = task.IntegrationTypeId;
  }

  public DateTime CreatedDateUTC { get; set; }
  public DatabaseType DatabaseType => DatabaseType.Local;
  public bool ExecuteSerially { get; set; }
  public bool GetEnabled { get; set; }
  public int? GetScriptId { get; set; }
  public DatabaseType? GetScriptType { get; set; }
  public MediaResponseBase? Icon { get; set; }
  public int? IconMediaId { get; set; }
  public int Id { get; set; }
  public bool IgnoreDuringAutomaticOnboarding { get; set; }
  public bool IsConfigurationTask { get; set; }
  public MaintenanceTaskCategory MaintenanceTaskCategory { get; set; }
  public required string Name { get; set; }
  public string? Notes { get; set; }
  public bool OnboardingOnly { get; set; }
  public bool Owned => Tenants?.Any(a => a.Relationship == Relationship.Owned) == true;
  public ICollection<GetMaintenanceTaskParameterResponse> Parameters { get; set; } = [];
  public bool Recommended { get; set; }
  public bool SetEnabled { get; set; }
  public int? SetScriptId { get; set; }
  public DatabaseType? SetScriptType { get; set; }
  public int? SupersededByTaskId { get; set; }
  public int? SupersededByTaskMigrationScriptId { get; set; }
  public DatabaseType? SupersededByTaskMigrationScriptType { get; set; }
  public Guid? IntegrationTypeId { get; set; }
  public DatabaseType? SupersededByTaskType { get; set; }
  public ICollection<TenantMaintenanceTask> Tenants { get; set; } = [];
  public bool TestEnabled { get; set; }
  public int? TestScriptId { get; set; }
  public DatabaseType? TestScriptType { get; set; }
  public string? UpdatedBy { get; set; }
  public DateTime UpdatedDateUTC { get; set; }
  public bool UseScriptParamBlock { get; set; }
  internal static Expression<Func<MaintenanceTask, GetLocalMaintenanceTaskResponse>> Projection =>
   x => new GetLocalMaintenanceTaskResponse()
   {
     Id = x.Id,
     Name = x.Name,
     OnboardingOnly = x.OnboardingOnly,
     IgnoreDuringAutomaticOnboarding = x.IgnoreDuringAutomaticOnboarding,
     MaintenanceTaskCategory = x.MaintenanceTaskCategory,
     TestEnabled = x.TestEnabled,
     TestScriptId = x.TestScriptId,
     TestScriptType = x.TestScriptType,
     GetEnabled = x.GetEnabled,
     GetScriptId = x.GetScriptId,
     GetScriptType = x.GetScriptType,
     SetEnabled = x.SetEnabled,
     SetScriptId = x.SetScriptId,
     SetScriptType = x.SetScriptType,
     Parameters = x.Parameters
       .Select(p => new GetMaintenanceTaskParameterResponse(p))
       .ToList(),
     Tenants = x.TenantRelationships,
     UpdatedDateUTC = x.UpdatedDate,
     CreatedDateUTC = x.CreatedDate,
     UpdatedBy = x.UpdatedByUser != null && x.UpdatedByUser.Person != null
       ? x.UpdatedByUser.Person.DisplayName
       : null,
     Recommended = x.Recommended,
     IsConfigurationTask = x.IsConfigurationTask,
     IconMediaId = x.IconMediaId,
     Icon = x.Icon != null ? new MediaResponseBase(x.Icon) : null,
     ExecuteSerially = x.ExecuteSerially,
     Notes = x.Notes,
     UseScriptParamBlock = x.UseScriptParamBlock,
     SupersededByTaskId = x.SupersededByTaskId,
     SupersededByTaskType = x.SupersededByTaskType,
     SupersededByTaskMigrationScriptId = x.SupersededByTaskMigrationScriptId,
     SupersededByTaskMigrationScriptType = x.SupersededByTaskMigrationScriptType,
     IntegrationTypeId = x.IntegrationTypeId
   };
}
