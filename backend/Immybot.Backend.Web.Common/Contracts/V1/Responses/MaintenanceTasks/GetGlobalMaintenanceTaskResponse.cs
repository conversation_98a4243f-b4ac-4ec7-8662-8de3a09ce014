using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses.MaintenanceTasks;
public class GetGlobalMaintenanceTaskResponse : IMaintenanceTaskBase
{
  public GetGlobalMaintenanceTaskResponse() { }

  [SetsRequiredMembers]
  public GetGlobalMaintenanceTaskResponse(MaintenanceTask task)
  {
    Id = task.Id;
    Name = task.Name;
    OnboardingOnly = task.OnboardingOnly;
    IgnoreDuringAutomaticOnboarding = task.IgnoreDuringAutomaticOnboarding;
    MaintenanceTaskCategory = task.MaintenanceTaskCategory;
    TestEnabled = task.TestEnabled;
    TestScriptId = task.TestScriptId;
    SetEnabled = task.SetEnabled;
    SetScriptId = task.SetScriptId;
    GetEnabled = task.GetEnabled;
    GetScriptId = task.GetScriptId;
    Parameters = task.Parameters
      .Select(p => new GetMaintenanceTaskParameterResponse(p))
      .ToList();
    UpdatedDateUTC = task.UpdatedDate;
    CreatedDateUTC = task.CreatedDate;
    Recommended = task.Recommended;
    IsConfigurationTask = task.IsConfigurationTask;
    IconMediaId = task.IconMediaId;
    ExecuteSerially = task.ExecuteSerially;
    Notes = task.Notes;
    UseScriptParamBlock = task.UseScriptParamBlock;
    if (task.Icon != null)
    {
      Icon = new MediaResponseBase(task.Icon);
    }
    SupersededByTaskId = task.SupersededByTaskId;
    SupersededByTaskMigrationScriptId = task.SupersededByTaskMigrationScriptId;
    IntegrationTypeId = task.IntegrationTypeId;
  }

  public DateTime CreatedDateUTC { get; set; }
  public DatabaseType DatabaseType => DatabaseType.Global;
  public bool ExecuteSerially { get; set; }
  public bool GetEnabled { get; set; }
  public int? GetScriptId { get; set; }
  public DatabaseType? GetScriptType => DatabaseType.Global;
  public MediaResponseBase? Icon { get; set; }
  public int? IconMediaId { get; set; }
  public int Id { get; set; }
  public bool IgnoreDuringAutomaticOnboarding { get; set; }
  public bool IsConfigurationTask { get; set; }
  public MaintenanceTaskCategory MaintenanceTaskCategory { get; set; }
  public required string Name { get; set; }
  public string? Notes { get; set; }
  public bool OnboardingOnly { get; set; }
  public ICollection<GetMaintenanceTaskParameterResponse> Parameters { get; set; } = [];
  public bool Recommended { get; set; }
  public bool SetEnabled { get; set; }
  public int? SetScriptId { get; set; }
  public DatabaseType? SetScriptType => DatabaseType.Global;
  public int? SupersededByTaskId { get; set; }
  public int? SupersededByTaskMigrationScriptId { get; set; }
  public DatabaseType? SupersededByTaskMigrationScriptType => DatabaseType.Global;
  public DatabaseType? SupersededByTaskType => DatabaseType.Global;
  public bool TestEnabled { get; set; }
  public int? TestScriptId { get; set; }
  public DatabaseType? TestScriptType => DatabaseType.Global;
  public DateTime UpdatedDateUTC { get; set; }
  public bool UseScriptParamBlock { get; set; }
  public Guid? IntegrationTypeId { get; set; }
  internal static Expression<Func<MaintenanceTask, GetGlobalMaintenanceTaskResponse>> Projection =>
   x => new GetGlobalMaintenanceTaskResponse()
   {
     Id = x.Id,
     Name = x.Name,
     OnboardingOnly = x.OnboardingOnly,
     IgnoreDuringAutomaticOnboarding = x.IgnoreDuringAutomaticOnboarding,
     MaintenanceTaskCategory = x.MaintenanceTaskCategory,
     TestEnabled = x.TestEnabled,
     TestScriptId = x.TestScriptId,
     GetEnabled = x.GetEnabled,
     GetScriptId = x.GetScriptId,
     SetEnabled = x.SetEnabled,
     SetScriptId = x.SetScriptId,
     Parameters = x.Parameters
       .Select(p => new GetMaintenanceTaskParameterResponse(p))
       .ToList(),
     UpdatedDateUTC = x.UpdatedDate,
     CreatedDateUTC = x.CreatedDate,
     Recommended = x.Recommended,
     IsConfigurationTask = x.IsConfigurationTask,
     IconMediaId = x.IconMediaId,
     Icon = x.Icon != null ? new MediaResponseBase(x.Icon) : null,
     ExecuteSerially = x.ExecuteSerially,
     Notes = x.Notes,
     UseScriptParamBlock = x.UseScriptParamBlock,
     SupersededByTaskId = x.SupersededByTaskId,
     SupersededByTaskMigrationScriptId = x.SupersededByTaskMigrationScriptId,
     IntegrationTypeId = x.IntegrationTypeId
   };
}
