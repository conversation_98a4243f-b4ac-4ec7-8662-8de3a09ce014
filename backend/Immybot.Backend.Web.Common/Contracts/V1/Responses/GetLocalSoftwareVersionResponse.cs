using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetLocalSoftwareVersionResponse : GetSoftwareVersionResponseBase
{
  public GetLocalSoftwareVersionResponse(LocalSoftwareVersion softwareVersion,
    bool includeSoftware = false)
    : base(softwareVersion)
  {
    ArgumentNullException.ThrowIfNull(softwareVersion);
#pragma warning disable CS0618 // Type or member is obsolete
    DeprecatedIdField = softwareVersion.DeprecatedIdField;
#pragma warning restore CS0618 // Type or member is obsolete
    SoftwareId = softwareVersion.SoftwareId;

    InstallScriptType = softwareVersion.InstallScriptType;
    UninstallScriptType = softwareVersion.UninstallScriptType;
    TestScriptType = softwareVersion.TestScriptType;
    UpgradeScriptType = softwareVersion.UpgradeScriptType;
    PostInstallScriptType = softwareVersion.PostInstallScriptType;
    PostUninstallScriptType = softwareVersion.PostUninstallScriptType;
    UpdatedDateUTC = softwareVersion.UpdatedDate;
    CreatedDateUTC = softwareVersion.CreatedDate;
    UpdatedBy = softwareVersion.UpdatedByUser?.DisplayName;

    if (includeSoftware && softwareVersion.Software != null)
      Software = new GetLocalSoftwareResponse(softwareVersion.Software, includeSoftwareVersions: false);
  }

  public int? DeprecatedIdField { get; }
  public int SoftwareId { get; }
  public DatabaseType? InstallScriptType { get; }
  public DatabaseType? TestScriptType { get; }
  public DatabaseType? UpgradeScriptType { get; }
  public DatabaseType? UninstallScriptType { get; }
  public DatabaseType? PostUninstallScriptType { get; }
  public DatabaseType? PostInstallScriptType { get; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetLocalSoftwareResponse? Software { get; }

  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public string? UpdatedBy { get; set; }
}
