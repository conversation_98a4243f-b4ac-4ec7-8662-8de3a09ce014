using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetMaintenanceSessionPhaseResponse
{
  public int Id { get; set; }
  public string PhaseName { get; set; }
  public ActionProgressPhaseName? ActionProgressPhaseName { get; set; }
  public int MaintenanceSessionId { get; set; }
  public int MaintenanceSessionStageId { get; set; }
  public int? MaintenanceActionId { get; set; }

  public SessionPhaseStatus Status { get; set; }

  // for progress logs: indicates the progress of this log as an integer in % complete
  public decimal? ProgressPercentComplete { get; set; }

  // for progress logs: indicates the current status of this progress log
  public string? ProgressStatus { get; set; }

  // for progress logs: indicates whether progress has been completed
  public bool ProgressCompleted { get; set; }
  public DateTime? DateStartedUtc { get; set; }
  public DateTime? DateCompletedUtc { get; set; }
  public GetMaintenanceSessionPhaseResponse(SessionPhase sessionPhase)
  {
    Id = sessionPhase.Id;
    PhaseName = sessionPhase.PhaseName;
    ActionProgressPhaseName = sessionPhase.ActionProgressPhaseName;
    MaintenanceSessionId = sessionPhase.MaintenanceSessionId;
    MaintenanceSessionStageId = sessionPhase.MaintenanceSessionStageId;
    MaintenanceActionId = sessionPhase.MaintenanceActionId;
    Status = sessionPhase.Status;
    ProgressPercentComplete = sessionPhase.ProgressPercentComplete;
    ProgressStatus = sessionPhase.ProgressStatus;
    ProgressCompleted = sessionPhase.ProgressCompleted;
    DateStartedUtc = sessionPhase.DateStartedUtc;
    DateCompletedUtc = sessionPhase.DateCompletedUtc;
  }
}
