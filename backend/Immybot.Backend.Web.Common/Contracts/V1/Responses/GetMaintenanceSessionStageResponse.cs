using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetMaintenanceSessionStageResponse
{
  public GetMaintenanceSessionStageResponse(
    MaintenanceSessionStage maintenanceSessionStage,
    bool includeSession = false)
  {
    ArgumentNullException.ThrowIfNull(maintenanceSessionStage);
    Id = maintenanceSessionStage.Id;
    MaintenanceSessionId = maintenanceSessionStage.MaintenanceSessionId;
    StageStatus = maintenanceSessionStage.StageStatus;
    Type = maintenanceSessionStage.Type;
    JobId = maintenanceSessionStage.JobId;
    UpdatedBy = maintenanceSessionStage.UpdatedBy;
    UpdatedDateUTC = maintenanceSessionStage.UpdatedDate;
    CreatedBy = maintenanceSessionStage.CreatedBy;
    CreatedDateUTC = maintenanceSessionStage.CreatedDate;
    if (includeSession && maintenanceSessionStage.MaintenanceSession != null)
    {
      MaintenanceSession = new GetMaintenanceSessionResponse(maintenanceSessionStage.MaintenanceSession, includeStages: false);
    }
  }
  public int Id { get; }
  public int MaintenanceSessionId { get; }
  public SessionStatus StageStatus { get; }
  public SessionStageType Type { get; }
  public string? JobId { get; }
  public int? UpdatedBy { get; set; }
  public int? CreatedBy { get; set; }
  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetMaintenanceSessionResponse? MaintenanceSession { get; }
}
