using System.Linq.Expressions;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Primitives;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetTagResponse : ITag
{
  public int Id { get; set; }
  public string Name { get; set; } = null!;
  public string? Description { get; set; }
  public string? Color { get; set; }
  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public string? UpdatedBy { get; set; }
  public bool Owned { get; set; }

  public ICollection<TenantTagAuthorization> TenantTagAuthorizations { get; set; } =
    new HashSet<TenantTagAuthorization>();

  // ef complaining about potential mem leaks when initializing a new collection in an expression
  private static HashSet<TenantTagAuthorization> EmptyAuthorizations = [];

  internal static Expression<Func<Tag, GetTagResponse>> Projection(bool includeAuthorizations = false) =>
    x => new GetTagResponse()
    {
      Id = x.Id,
      Name = x.Name,
      Description = x.Description,
      Color = x.Color,
      UpdatedDateUTC = x.UpdatedDate,
      CreatedDateUTC = x.CreatedDate,
      UpdatedBy = x.UpdatedByUser != null && x.UpdatedByUser.Person != null ? x.UpdatedByUser.Person.DisplayName : null,
      TenantTagAuthorizations = includeAuthorizations ? x.TenantRelationships : EmptyAuthorizations,
      Owned = x.TenantRelationships.Any(a => a.Relationship == Relationship.Owned),
    };

  internal static GetTagResponse CreateFromEntity(Tag tag, bool includeAuthorizations = false)
  {
    return new GetTagResponse()
    {
      Id = tag.Id,
      Name = tag.Name,
      Description = tag.Description,
      Color = tag.Color,
      UpdatedDateUTC = tag.UpdatedDate,
      CreatedDateUTC = tag.CreatedDate,
      UpdatedBy =
        tag.UpdatedByUser != null && tag.UpdatedByUser.Person != null ? tag.UpdatedByUser.Person.DisplayName : null,
      TenantTagAuthorizations = includeAuthorizations ? tag.TenantRelationships : EmptyAuthorizations,
      Owned = tag.TenantRelationships.Any(a => a.Relationship == Relationship.Owned),
    };
  }
}
