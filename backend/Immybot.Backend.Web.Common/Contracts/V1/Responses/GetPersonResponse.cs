using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;
using System.Linq.Expressions;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetPersonResponse
{
  public GetPersonResponse(
    Person person,
    bool includePrimaryComputers = false,
    bool includeAdditionalComputers = false,
    bool includeUser = false,
    bool includeUserAffinities = false)
  {
    ArgumentNullException.ThrowIfNull(person);
    Id = person.Id;
    TenantId = person.TenantId;
    AzurePrincipalId = person.AzurePrincipalId;
    FirstName = person.FirstName;
    LastName = person.LastName;
    EmailAddress = person.EmailAddress;
    DisplayName = person.DisplayName;
    UpdatedBy = person.UpdatedBy;
    UpdatedDateUTC = person.UpdatedDate;
    CreatedBy = person.CreatedBy;
    CreatedDateUTC = person.CreatedDate;

    if (includePrimaryComputers)
      PrimaryComputers = person.PrimaryComputers
        .Select(
          c => new GetComputerResponse(
            c,
            false,
            includeAdditionalPersons: false,
            includePrimaryPerson: false))
        .ToList();

    if (includeAdditionalComputers)
      AdditionalComputers = person.AdditionalComputers
        .Where(c => c.Computer != null)
        .Select(c => new GetComputerResponse(
          c.Computer!,
          false,
          includeAdditionalPersons: false,
          includePrimaryPerson: false))
        .ToList();

    if (includeUser && person.User != null)
      User = new GetUserResponse(person.User);

    if (includeUserAffinities)
      UserAffinities = person.UserAffinities
        .Select(u => new GetUserAffinityResponse(u, includePerson: false))
        .ToList();
  }

  public int Id { get; }
  public int TenantId { get; }
  public string? AzurePrincipalId { get; }
  public string? FirstName { get; }
  public string? LastName { get; }
  public string EmailAddress { get; }
  public string DisplayName { get; }
  public int? UpdatedBy { get; }
  public int? CreatedBy { get; }
  public DateTime UpdatedDateUTC { get; }
  public DateTime CreatedDateUTC { get; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetComputerResponse>? PrimaryComputers { get; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetComputerResponse>? AdditionalComputers { get; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetUserResponse? User { get; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetUserAffinityResponse>? UserAffinities { get; }
}
