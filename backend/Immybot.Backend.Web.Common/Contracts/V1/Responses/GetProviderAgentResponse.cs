using System.Text.Json;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using NuGet.Versioning;
using JsonConverterStj = System.Text.Json.Serialization.JsonConverterAttribute;
using JsonConverterNewtonsoft = Newtonsoft.Json.JsonConverterAttribute;
using Immybot.Shared.DataContracts.Converters.SemanticVersioning;
using Immybot.Shared.JsonConverters;
using SemanticVersionConverter = Immybot.Shared.JsonConverters.SemanticVersionConverter;


namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetProviderAgentResponse : IProviderAgentDetails
{
  [JsonConverterStj(typeof(SemanticVersionConverterStj))]
  [JsonConverterNewtonsoft(typeof(SemanticVersionConverter))]
  public SemanticVersion? AgentVersion { get; init; }
  public int Id { get; }
  public int? ComputerId { get; }
  public int ProviderLinkId { get; }
  public string ExternalClientName { get; }
  public string ExternalClientId { get; set; }
  public string ExternalAgentId { get; set; }
  public int RunScriptPriority { get; }
  public Guid ProviderTypeId { get; }
  public JsonElement? InternalData { get; }
  public bool IsOnline { get; set; }
  public bool SupportsRunningScripts { get; }
  public JsonElement? DeviceUpdateFormData { get; set; }
  public DateTime LastUpdatedUTC { get; }
  public DeviceDetails DeviceDetails { get; }
  public AgentOnboardingOptions OnboardingOptions { get; }
  public bool RequireManualIdentification { get; }


  public GetProviderAgentResponse(ProviderAgent agent)
  {
    Id = agent.Id;
    ComputerId = agent.ComputerId;
    ProviderLinkId = agent.ProviderLinkId;
    ExternalClientId = agent.ExternalClientId;
    ExternalAgentId = agent.ExternalAgentId;
    RunScriptPriority = agent.ProviderLink?.RunScriptPriority ?? 0;
    ProviderTypeId = agent.ProviderLink?.ProviderTypeId ?? Guid.Empty;
    InternalData = agent.InternalData;
    ExternalClientName = agent.ProviderClient?.ExternalClientName ?? String.Empty;
    IsOnline = agent.IsOnline;
    SupportsRunningScripts = agent.SupportsRunningScripts;
    LastUpdatedUTC = agent.LastUpdatedUTC;
    DeviceDetails = agent.DeviceDetails;
    OnboardingOptions = agent.OnboardingOptions;
    AgentVersion = agent.AgentVersion;
    RequireManualIdentification = agent.RequireManualIdentification;
  }
}
