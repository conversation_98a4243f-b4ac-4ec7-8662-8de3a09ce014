using Immybot.Backend.Domain.Models.Preferences;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetTenantPreferencesResponse(TenantPreferences tenantPreferences)
{
  public int Id { get; } = tenantPreferences.Id;
  public int TenantId { get; } = tenantPreferences.TenantId;
  public bool EnableOnboarding { get; } = tenantPreferences.EnableOnboarding;
  public bool EnableSessionEmails { get; } = tenantPreferences.EnableSessionEmails;
}
