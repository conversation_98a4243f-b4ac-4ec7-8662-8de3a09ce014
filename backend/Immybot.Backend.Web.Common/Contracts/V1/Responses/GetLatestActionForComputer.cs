using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public record GetLatestActionForComputer(
  int Id,
  int MaintenanceSessionId,
  MaintenanceActionType ActionType,
  MaintenanceActionResult Result,
  MaintenanceActionReason Reason,
  MaintenanceActionStatus Status,
  MaintenanceActionResultReason? ResultReason,
  string? DetectedVersion,
  string? DesiredVersion,
  string MaintenanceDisplayName,
  DesiredSoftwareState? DesiredSoftwareState,
  string MaintenanceIdentifier,
  MaintenanceType MaintenanceType,
  MaintenanceTaskMode? MaintenanceTaskMode,
  int? AssignmentId,
  DatabaseType? AssignmentType,
  DateTime? StartTime,
  DateTime? EndTime,
  DateTime? CreatedDate,
  int? SoftwareActionIdForConfigurationTask,
  string? PolicyDescription);
