using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetAgentIdentificationFailureResponse
{
  public int Id { get; set; }
  public int? ComputerId { get; set; }
  public int? ExistingAgentId { get; set; }
  public int? PendingAgentId { get; set; }
  public string? Message { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public bool Resolved { get; set; }
  public bool RequiresManualResolution { get; set; }
  public bool FeatureUsageExceeded { get; set; }
  public AgentIdentificationManualResolutionDecision? ManualResolutionDecision { get; set; }
  public string? ExistingComputerName { get; set; }
  public string? ExistingPrimaryUserFirstName { get; set; }
  public string? ExistingPrimaryUserLastName { get; set; }
  public string? ExistingTenantName { get; set; }
  public string? ExistingOperatingSystem { get; set; }
  public string? ExistingSerialNumber { get; set; }
  public string? ExistingManufacturer { get; set; }
  public DateTime? ExistingOSInstallDate { get; set; }
  public IProviderAgentDetails? ExistingAgent { get; set; }
  public GetAgentIdentificationFailureResponse() { }
  public GetAgentIdentificationFailureResponse(AgentIdentificationFailure failure)
  {
    Id = failure.Id;
    ComputerId = failure.ComputerId;
    ExistingAgentId = failure.ExistingAgentId;
    PendingAgentId = failure.PendingAgentId;
    Message = failure.Message;
    CreatedDateUTC = failure.CreatedDateUTC;
    Resolved = failure.Resolved;
    RequiresManualResolution = failure.RequiresManualResolution;
    ManualResolutionDecision = failure.ManualResolutionDecision;
    ExistingComputerName = failure.Computer?.ComputerName;
    ExistingPrimaryUserFirstName = failure.Computer?.PrimaryPerson?.FirstName;
    ExistingPrimaryUserLastName = failure.Computer?.PrimaryPerson?.LastName;
    ExistingTenantName = failure.Computer?.OwnerTenant?.Name;
    ExistingOperatingSystem = failure.Computer?.OperatingSystem;
    ExistingSerialNumber = failure.Computer?.SerialNumber;
    ExistingOSInstallDate = failure.Computer?.OSInstallDate;
    ExistingManufacturer = failure.Computer?.Manufacturer;
    ExistingAgent = failure.Computer?.Agents.FirstOrDefault(a => a.Id == failure.ExistingAgentId);
    FeatureUsageExceeded = failure.FeatureUsageExceeded;
  }

  internal static Expression<Func<AgentIdentificationFailure, GetAgentIdentificationFailureResponse>> Projection =>
   x => new GetAgentIdentificationFailureResponse()
   {
     Id = x.Id,
     ComputerId = x.ComputerId,
     ExistingAgentId = x.ExistingAgentId,
     PendingAgentId = x.PendingAgentId,
     Message = x.Message,
     CreatedDateUTC = x.CreatedDateUTC,
     Resolved = x.Resolved,
     RequiresManualResolution = x.RequiresManualResolution,
     ManualResolutionDecision = x.ManualResolutionDecision,
     ExistingComputerName = x.Computer != null ? x.Computer.ComputerName : null,
     ExistingPrimaryUserFirstName = x.Computer != null && x.Computer.PrimaryPerson != null ? x.Computer.PrimaryPerson.FirstName : null,
     ExistingPrimaryUserLastName = x.Computer != null && x.Computer.PrimaryPerson != null ? x.Computer.PrimaryPerson.LastName : null,
     ExistingTenantName = x.Computer != null && x.Computer.OwnerTenant != null ? x.Computer.OwnerTenant.Name : null,
     ExistingOperatingSystem = x.Computer != null ? x.Computer.OperatingSystem : null,
     ExistingSerialNumber = x.Computer != null ? x.Computer.SerialNumber : null,
     ExistingManufacturer = x.Computer != null ? x.Computer.Manufacturer : null,
     ExistingOSInstallDate = x.Computer != null ? x.Computer.OSInstallDate : null,
     ExistingAgent = x.Computer != null ? x.Computer.Agents.FirstOrDefault(a => a.Id == x.ExistingAgentId) : null,
     FeatureUsageExceeded = x.FeatureUsageExceeded,
   };

}
