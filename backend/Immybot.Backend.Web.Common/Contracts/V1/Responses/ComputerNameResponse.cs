using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class ComputerNameResponse
{
  public int Id { get; set; }
  public string? Name { get; set; }
  internal static Expression<Func<Computer, ComputerNameResponse>> Projection =>
   x => new ComputerNameResponse()
   {
     Id = x.Id,
     Name = x.ComputerName,
   };
}
