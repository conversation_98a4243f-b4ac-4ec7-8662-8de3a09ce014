using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetGlobalSoftwareVersionResponse : GetSoftwareVersionResponseBase
{
  public GetGlobalSoftwareVersionResponse(GlobalSoftwareVersion softwareVersion,
    bool includeSoftware = false)
    : base(softwareVersion)
  {
    ArgumentNullException.ThrowIfNull(softwareVersion);
    DeprecatedIdField = softwareVersion.DeprecatedIdField;
    SoftwareId = softwareVersion.SoftwareId;
    if (includeSoftware && softwareVersion.Software != null)
      Software = new GetGlobalSoftwareResponse(softwareVersion.Software, includeSoftwareVersions: false);
    UpdatedDateUTC = softwareVersion.UpdatedDate;
    CreatedDateUTC = softwareVersion.CreatedDate;

    InstallScriptType = DatabaseType.Global;
    TestScriptType = DatabaseType.Global;
    UpgradeScriptType = DatabaseType.Global;
    UninstallScriptType = DatabaseType.Global;
    PostUninstallScriptType = DatabaseType.Global;
    PostInstallScriptType = DatabaseType.Global;
  }
  public int? DeprecatedIdField { get; }
  public int SoftwareId { get; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetGlobalSoftwareResponse? Software { get; }

  public DatabaseType InstallScriptType { get; }
  public DatabaseType TestScriptType { get; }
  public DatabaseType UpgradeScriptType { get; }
  public DatabaseType UninstallScriptType { get; }
  public DatabaseType PostUninstallScriptType { get; }
  public DatabaseType PostInstallScriptType { get; }

  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }
}
