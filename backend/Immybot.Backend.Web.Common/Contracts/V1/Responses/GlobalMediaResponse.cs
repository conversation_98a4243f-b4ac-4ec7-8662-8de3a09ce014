using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GlobalMediaResponse : MediaResponseBase
{
  internal GlobalMediaResponse() { }

  internal GlobalMediaResponse(Media media) : base(media)
  {
    Id = media.Id;
  }

  internal static Expression<Func<Media, GlobalMediaResponse>> Projection =>
    x => new GlobalMediaResponse()
    {
      Id = x.Id,
      Name = x.Name,
      FileName = x.FileName,
      MimeType = x.MimeType,
      PackageHash = x.PackageHash,
      BlobReference = x.BlobReference,
      RelativeCacheSourcePath = x.RelativeCacheSourcePath,
      UpdatedDateUTC = x.UpdatedDate,
      CreatedDateUTC = x.CreatedDate,
      DatabaseType = DatabaseType.Global,
      Category = x.Category,
    };
}
