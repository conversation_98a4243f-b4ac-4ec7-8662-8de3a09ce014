using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetDxMaintenanceSessionResponse
{
  public int Id { get; set; }
  public string StatusName { get; set; } = string.Empty;
  public SessionStatus SessionStatus { get; set; }
  public string ComputerName { get; set; } = string.Empty;
  public string OperatingSystem { get; set; } = string.Empty;
  public string Manufacturer { get; set; } = string.Empty;
  public string Model { get; set; } = string.Empty;
  public string SerialNumber { get; set; } = string.Empty;
  public string Domain { get; set; } = string.Empty;
  public int? ComputerId { get; set; }
  public int? TenantId { get; set; }
  public int? PersonId { get; set; }
  public string? CreatedBy { get; set; }
  public DateTime CreatedDate { get; set; }
  public DateTime UpdatedDate { get; set; }
  public SessionStatus? OnboardingStageStatus { get; set; }
  public SessionStatus? DetectionStageStatus { get; set; }
  public SessionStatus? ExecutionStageStatus { get; set; }
  public SessionStatus? AgentUpdatesStageStatus { get; set; }
  public SessionStatus? ResolutionStageStatus { get; set; }
  public SessionStatus? InventoryStageStatus { get; set; }

  public string? TenantName { get; set; }
  public string? PrimaryPersonName { get; set; }
  public string? PrimaryPersonEmail { get; set; }
  public int? ScheduleId { get; set; }
  public bool FullMaintenance { get; set; }
  public DateTime? ScheduledExecutionDate { get; set; }
  public string? PersonName { get; set; }
  public string? PersonEmail { get; set; }
  public TimeSpan? Duration { get; set; }


  internal static Expression<Func<MaintenanceSession, GetDxMaintenanceSessionResponse>> Projection =>
    x => new GetDxMaintenanceSessionResponse()
    {
      Id = x.Id,
      StatusName = x.SessionStatus.ToString(),
      ComputerName = x.Computer != null ? x.Computer.ComputerName ?? x.Computer.DeviceId.ToString() : String.Empty,
      PersonName = x.Person != null ? x.Person.FirstName + " " + x.Person.LastName : null,
      PersonEmail = x.Person != null ? x.Person.EmailAddress : null,
      OperatingSystem = x.Computer != null && x.Computer.OperatingSystem != null ? x.Computer.OperatingSystem : String.Empty,
      Manufacturer = x.Computer != null && x.Computer.Manufacturer != null ? x.Computer.Manufacturer : String.Empty,
      Model = x.Computer != null && x.Computer.Model != null ? x.Computer.Model : String.Empty,
      SerialNumber = x.Computer != null && x.Computer.SerialNumber != null ? x.Computer.SerialNumber : String.Empty,
      Domain = x.Computer != null && x.Computer.Domain != null ? x.Computer.Domain : String.Empty,
      ComputerId = x.ComputerId,
      TenantId = x.TenantId,
      PersonId = x.PersonId,
      CreatedDate = x.CreatedDate,
      UpdatedDate = x.UpdatedDate,
      OnboardingStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.Onboarding)!.StageStatus,
      DetectionStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.Detection)!.StageStatus,
      ExecutionStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.Execution)!.StageStatus,
      AgentUpdatesStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.AgentUpdates)!.StageStatus,
      ResolutionStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.Resolution)!.StageStatus,
      InventoryStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.Inventory)!.StageStatus,
      TenantName = (x.Computer != null && x.Computer.OwnerTenant != null ? x.Computer.OwnerTenant.Name :
        x.OwnerTenant != null ? x.OwnerTenant.Name : null),
      SessionStatus = x.SessionStatus,
      PrimaryPersonName = x.Computer!.PrimaryPerson!.FirstName + " " + x.Computer.PrimaryPerson.LastName,
      PrimaryPersonEmail = x.Computer.PrimaryPerson.EmailAddress,
      ScheduleId = x.ScheduledId,
      FullMaintenance = x.FullMaintenance,
      ScheduledExecutionDate = x.ScheduledExecutionDate,
      CreatedBy = x.CreatedByUser!.Person!.FirstName + " " + x.CreatedByUser.Person.LastName,
      Duration = x.Duration
    };
}
