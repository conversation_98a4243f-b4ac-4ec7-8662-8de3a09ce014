using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetGlobalSoftwareResponse : ISharedSoftwareProperties
{
  public GetGlobalSoftwareResponse(
    GlobalSoftware software,
    bool includeSoftwareVersions = false,
    bool includeSoftwarePrerequisites = false,
    bool includeSoftwareIcon = false)
  {
    ArgumentNullException.ThrowIfNull(software);
    Name = software.Name;
    LicenseRequirement = software.LicenseRequirement;
    LicenseType = software.LicenseType;
    LicenseDescription = software.LicenseDescription;
    InstallOrder = software.InstallOrder;
    Hidden = software.Hidden;
    SoftwareType = software.SoftwareType;
    Identifier = software.Identifier;
    Id = software.Id;
    DetectionMethod = software.DetectionMethod;
    SoftwareTableName = software.SoftwareTableName;

    DetectionScriptId = software.DetectionScriptId;
    UninstallScriptId = software.UninstallScriptId;
    PostUninstallScriptId = software.PostUninstallScriptId;
    InstallScriptId = software.InstallScriptId;
    PostInstallScriptId = software.PostInstallScriptId;
    UpgradeScriptId = software.UpgradeScriptId;
    TestScriptId = software.TestScriptId;
    TestFailedError = software.TestFailedError;
    TestRequired = software.TestRequired;
    UpgradeStrategy = software.UpgradeStrategy;

    if (includeSoftwareVersions)
      SoftwareVersions = software.SoftwareVersions.Select(v => new GetGlobalSoftwareVersionResponse(v, includeSoftware: false)).ToList();
    if (includeSoftwarePrerequisites)
      SoftwarePrerequisites = software.SoftwarePrerequisites.Select(p => new GetSoftwarePrerequisiteResponse(p)).ToList();
    if (includeSoftwareIcon && software.SoftwareIcon != null)
      SoftwareIcon = new GlobalMediaResponse(software.SoftwareIcon);

    MaintenanceTaskId = software.MaintenanceTaskId;
    Notes = software.Notes;
    RebootNeeded = software.RebootNeeded;
    UpdatedDateUTC = software.UpdatedDate;
    CreatedDateUTC = software.CreatedDate;
    UpgradeCode = software.UpgradeCode;
    RepairType = software.RepairType;
    RepairScriptId = software.RepairScriptId;
    SoftwareTableNameSearchMode = software.SoftwareTableNameSearchMode;
    SoftwareIconMediaId = software.SoftwareIconMediaId;
    Recommended = software.Recommended;
    ChocoProviderSoftwareId = software.ChocoProviderSoftwareId;
    NiniteProviderSoftwareId = software.NiniteProviderSoftwareId;
    UseDynamicVersions = software.UseDynamicVersions || software.AgentIntegrationTypeId is not null;
    DynamicVersionsScriptId = software.DynamicVersionsScriptId;
    DownloadInstallerScriptId = software.DownloadInstallerScriptId;
    AgentIntegrationTypeId = software.AgentIntegrationTypeId;

    // global script types
    DetectionScriptType = DatabaseType.Global;
    MaintenanceTaskType = DatabaseType.Global;
    RepairScriptType = DatabaseType.Global;
    InstallScriptType = DatabaseType.Global;
    TestScriptType = DatabaseType.Global;
    UpgradeScriptType = DatabaseType.Global;
    UninstallScriptType = DatabaseType.Global;
    PostInstallScriptType = DatabaseType.Global;
    PostUninstallScriptType = DatabaseType.Global;
    DynamicVersionsScriptType = DatabaseType.Global;
    DownloadInstallerScriptType = DatabaseType.Global;
  }
  public string Name { get; }
  public SoftwareLicenseRequirement LicenseRequirement { get; }
  public int InstallOrder { get; }
  public bool Hidden { get; }
  public SoftwareType SoftwareType { get; }
  public string Identifier { get; }
  public int? Id { get; }
  public int? OwnerTenantId { get; }
  public DetectionMethod DetectionMethod { get; }
  public string? SoftwareTableName { get; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetGlobalSoftwareVersionResponse>? SoftwareVersions { get; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetSoftwarePrerequisiteResponse>? SoftwarePrerequisites { get; }
  public int? DetectionScriptId { get; }
  public int? RepairScriptId { get; }
  public string? Notes { get; }
  public DatabaseType DetectionScriptType { get; }
  public DatabaseType MaintenanceTaskType { get; }
  public DatabaseType RepairScriptType { get; }
  public int? MaintenanceTaskId { get; }
  public bool RebootNeeded { get; }
  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public string? UpgradeCode { get; set; }
  public RepairActionType RepairType { get; set; }
  public SoftwareTableNameSearchMode? SoftwareTableNameSearchMode { get; set; }
  public int? SoftwareIconMediaId { get; set; }
  public bool Recommended { get; set; }

  public string? ChocoProviderSoftwareId { get; set; }
  public string? NiniteProviderSoftwareId { get; set; }
  public GlobalMediaResponse? SoftwareIcon { get; set; }

  public int? InstallScriptId { get; set; }
  public DatabaseType InstallScriptType { get; }
  public int? TestScriptId { get; set; }
  public DatabaseType TestScriptType { get; }
  public bool TestRequired { get; set; }
  public string? TestFailedError { get; set; }
  public int? UpgradeScriptId { get; set; }
  public DatabaseType UpgradeScriptType { get; }
  public UpdateActionType UpgradeStrategy { get; set; }

  public Guid? AgentIntegrationTypeId { get; set; }
  public int? UninstallScriptId { get; set; }
  public DatabaseType UninstallScriptType { get; }
  public int? PostInstallScriptId { get; set; }
  public DatabaseType PostInstallScriptType { get; }
  public int? PostUninstallScriptId { get; set; }
  public DatabaseType PostUninstallScriptType { get; }
  public LicenseType LicenseType { get; set; }
  public string? LicenseDescription { get; set; }
  public bool UseDynamicVersions { get; set; }
  public int? DynamicVersionsScriptId { get; set; }
  public DatabaseType DynamicVersionsScriptType { get; }
  public int? DownloadInstallerScriptId { get; set; }
  public DatabaseType DownloadInstallerScriptType { get; }
}
