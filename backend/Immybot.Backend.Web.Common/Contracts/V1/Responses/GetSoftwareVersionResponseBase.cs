using Immybot.Backend.Domain.Models;
using NuGet.Versioning;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public abstract class GetSoftwareVersionResponseBase
{
  protected GetSoftwareVersionResponseBase(SoftwareVersion softwareVersion)
  {
    ArgumentNullException.ThrowIfNull(softwareVersion);

    DisplayName = softwareVersion.DisplayName;
    DisplayVersion = softwareVersion.DisplayVersion;
    SemanticVersion = softwareVersion.SemanticVersion;
    SemanticVersionString = softwareVersion.SemanticVersionString;
    TestRequired = softwareVersion.TestRequired;
    URL = softwareVersion.URL;
    RelativeCacheSourcePath = softwareVersion.RelativeCacheSourcePath;
    InstallerFile = softwareVersion.InstallerFile;
    TestFailedError = softwareVersion.TestFailedError;
    PackageHash = softwareVersion.PackageHash;
    UpgradeStrategy = softwareVersion.UpgradeStrategy;
    PackageType = softwareVersion.PackageType;
    InstallerType = softwareVersion.InstallerType;
    SoftwareType = softwareVersion.SoftwareType;
    SoftwareIdentifier = softwareVersion.SoftwareIdentifier;
    NumActionSuccesses = softwareVersion.NumActionSuccesses;
    NumActionFailures = softwareVersion.NumActionFailures;
    LastResult = softwareVersion.LastResult;
    BlobName = softwareVersion.BlobName;

    InstallScriptId = softwareVersion.InstallScriptId;
    UninstallScriptId = softwareVersion.UninstallScriptId;
    TestScriptId = softwareVersion.TestScriptId;
    UpgradeScriptId = softwareVersion.UpgradeScriptId;
    PostInstallScriptId = softwareVersion.PostInstallScriptId;
    PostUninstallScriptId = softwareVersion.PostUninstallScriptId;
    Notes = softwareVersion.Notes;
    ProductCode = softwareVersion.ProductCode;
    DependsOnSemanticVersion = softwareVersion.DependsOnSemanticVersion;
  }
  public string? DisplayName { get; }
  public string? DisplayVersion { get; }
  public SemanticVersion SemanticVersion { get; }
  public string SemanticVersionString { get; }
  public bool TestRequired { get; }
  public bool UninstallIfNotInTarget { get; }
  public string? URL { get; }
  public string? RelativeCacheSourcePath { get; }
  public string? InstallerFile { get; }
  public string? TestFailedError { get; }
  public string? PackageHash { get; }
  public int? InstallScriptId { get; }
  public int? TestScriptId { get; }
  public int? UpgradeScriptId { get; }
  public int? UninstallScriptId { get; }
  public int? PostUninstallScriptId { get; }
  public int? PostInstallScriptId { get; }
  public string SoftwareIdentifier { get; }
  public int NumActionSuccesses { get; }
  public int NumActionFailures { get; }
  public string? BlobName { get; }
  public LicenseType LicenseType { get; }
  public UpdateActionType UpgradeStrategy { get; }
  public PackageType PackageType { get; }
  public SoftwareVersionInstallerType InstallerType { get; }
  public MaintenanceActionResult? LastResult { get; }
  public SoftwareType SoftwareType { get; }
  public string? Notes { get; }
  public string? ProductCode { get; }
  public SemanticVersion? DependsOnSemanticVersion { get; set; }

  public bool HasOverrides =>
    InstallScriptId.HasValue
    || UninstallScriptId.HasValue
    || UpgradeStrategy is not UpdateActionType.None
    || UpgradeScriptId.HasValue && UpgradeStrategy is UpdateActionType.UpgradeScript
    || PostInstallScriptId.HasValue
    || PostUninstallScriptId.HasValue
    || TestRequired && TestScriptId.HasValue;
}
