using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses.Scripts;

public class GetLocalScriptResponse : ScriptResource
{
  public bool Owned { get; set; }

  public string UpdatedBy { get; set; } = string.Empty;

  internal GetLocalScriptResponse() { }

  [SetsRequiredMembers]
  internal GetLocalScriptResponse(Script script, int tenantId) : base(script)
  {
    Owned = script.TenantRelationships.Any(a => a.Relationship == Relationship.Owned && a.TenantId == tenantId);
    UpdatedBy = script.UpdatedByUser?.DisplayName ?? string.Empty;
  }

  [SetsRequiredMembers]
  internal GetLocalScriptResponse(Script script) : base(script)
  {
    UpdatedBy = script.UpdatedByUser?.DisplayName ?? string.Empty;
  }

  internal static Expression<Func<Script, GetLocalScriptResponse>> Projection =>
   x => new GetLocalScriptResponse()
   {
     ScriptType = DatabaseType.Local,
     Name = x.Name,
     Id = x.Id,
     Action = x.Action,
     ScriptLanguage = x.ScriptLanguage,
     Timeout = x.Timeout,
     ScriptExecutionContext = x.ScriptExecutionContext,
     ScriptCategory = x.ScriptCategory,
     Owned = x.TenantRelationships.Any(a => a.Relationship == Relationship.Owned),
     UpdatedDateUTC = x.UpdatedDate,
     CreatedDateUTC = x.CreatedDate,
     UpdatedBy = x.UpdatedByUser == null || x.UpdatedByUser.Person == null
       ? string.Empty
       : x.UpdatedByUser.Person.DisplayName,
   };
}
