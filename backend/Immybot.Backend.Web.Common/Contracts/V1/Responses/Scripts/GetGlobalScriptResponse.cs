using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses.Scripts;

public class GetGlobalScriptResponse : ScriptResource
{
  internal GetGlobalScriptResponse() : base() { }

  [SetsRequiredMembers]
  internal GetGlobalScriptResponse(Script script) : base(script) { }

  internal static Expression<Func<Script, GetGlobalScriptResponse>> Projection =>
   x => new GetGlobalScriptResponse()
   {
     ScriptType = DatabaseType.Global,
     Name = x.Name,
     Id = x.Id,
     Action = x.Action,
     ScriptLanguage = x.ScriptLanguage,
     Timeout = x.Timeout,
     ScriptExecutionContext = x.ScriptExecutionContext,
     ScriptCategory = x.ScriptCategory,
     UpdatedDateUTC = x.UpdatedDate,
     CreatedDateUTC = x.CreatedDate,
   };
}
