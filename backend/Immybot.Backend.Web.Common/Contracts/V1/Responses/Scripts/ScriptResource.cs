using System.Diagnostics.CodeAnalysis;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Scripts;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses.Scripts;

public class ScriptResource
{
  protected ScriptResource() { }

  [SetsRequiredMembers]
  internal ScriptResource(Script script)
  {
    ScriptType = script.ScriptType;
    Name = script.Name;
    Id = script.Id;
    Action = script.Action;
    ScriptLanguage = script.ScriptLanguage;
    Timeout = script.Timeout;
    ScriptExecutionContext = script.ScriptExecutionContext;
    ScriptCategory = script.ScriptCategory;
    UpdatedDateUTC = script.UpdatedDate;
    CreatedDateUTC = script.CreatedDate;
    OutputType = script.OutputType;
  }
  public DatabaseType ScriptType { get; private protected set; }
  public required string Name { get; set; }
  public int Id { get; private protected set; }
  public string Action { get; private protected set; } = string.Empty;
  public ScriptLanguage ScriptLanguage { get; private protected set; }
  public int? Timeout { get; private protected set; }
  public ScriptExecutionContext ScriptExecutionContext { get; private protected set; }
  public ScriptCategory ScriptCategory { get; private protected set; }
  public ScriptOutputType OutputType { get; private protected set; }
  public DateTime UpdatedDateUTC { get; private protected set; }
  public DateTime CreatedDateUTC { get; private protected set; }
}
