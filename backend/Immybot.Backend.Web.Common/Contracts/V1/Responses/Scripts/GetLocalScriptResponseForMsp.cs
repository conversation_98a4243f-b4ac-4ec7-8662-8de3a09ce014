using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses.Scripts;

public class GetLocalScriptResponseForMsp : GetLocalScriptResponse
{
  /// <summary>
  /// msp users can see which tenants are assigned to this script through the Tenants property.
  /// non-msp users should only see the Owned property.
  /// if owned is set to true, then they own the script, otherwise they do not.
  /// </summary>
  public IEnumerable<GetTenantScriptResponse> Tenants { get; private set; } = [];

  internal GetLocalScriptResponseForMsp() { }

  [SetsRequiredMembers]
  internal GetLocalScriptResponseForMsp(Script script) : base(script)
  {
    Tenants = script.TenantRelationships.Select(a =>
     new GetTenantScriptResponse
     {
       ScriptId = a.ScriptId,
       TenantId = a.TenantId,
       Relationship = a.Relationship
     }).ToList();
  }

  internal static new Expression<Func<Script, GetLocalScriptResponseForMsp>> Projection =>
   x => new GetLocalScriptResponseForMsp()
   {
     ScriptType = DatabaseType.Local,
     Name = x.Name,
     Id = x.Id,
     Action = x.Action,
     ScriptLanguage = x.ScriptLanguage,
     Timeout = x.Timeout,
     ScriptExecutionContext = x.ScriptExecutionContext,
     ScriptCategory = x.ScriptCategory,
     Tenants = x.TenantRelationships.Select(a =>
      new GetTenantScriptResponse
      {
        ScriptId = a.ScriptId,
        TenantId = a.TenantId,
        Relationship = a.Relationship
      }).ToList(),
     UpdatedDateUTC = x.UpdatedDate,
     CreatedDateUTC = x.CreatedDate,
     UpdatedBy = x.UpdatedByUser == null || x.UpdatedByUser.Person == null
       ? string.Empty
       : x.UpdatedByUser.Person.DisplayName,
   };
}
