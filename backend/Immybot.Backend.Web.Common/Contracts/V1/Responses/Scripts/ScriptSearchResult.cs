using Immybot.Backend.Domain.Models;
using Immybot.Shared.Scripts;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses.Scripts;

public class ScriptSearchResult
{
  public int Id { get; }
  public DatabaseType ScriptType { get; }
  public ScriptCategory ScriptCategory { get; }
  public string Name { get; }
  public bool Owned { get; }
  public string Action { get; }
  public ScriptLanguage ScriptLanguage { get; }
  public ScriptExecutionContext ScriptExecutionContext { get; }
  public ScriptSearchResult(Script script)
  {
    Id = script.Id;
    ScriptType = script.ScriptType;
    ScriptCategory = script.ScriptCategory;
    Name = script.Name;
    Owned = script.TenantRelationships.Any(a => a.Relationship == Relationship.Owned);
    Action = script.Action;
    ScriptLanguage = script.ScriptLanguage;
    ScriptExecutionContext = script.ScriptExecutionContext;
  }
}
