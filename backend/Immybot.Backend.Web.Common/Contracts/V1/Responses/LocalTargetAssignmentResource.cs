using System.Collections.Immutable;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class LocalTargetAssignmentResource : TargetAssignmentResource
{
  public override DatabaseType DatabaseType => DatabaseType.Local;
  public TargetAssignmentVisibilityResource? Visibility { get; set; }
  private LocalTargetAssignmentResource() { }

  [SetsRequiredMembers]
  public LocalTargetAssignmentResource(TargetAssignment a) : base(a)
  {
    Visibility = new TargetAssignmentVisibilityResource()
    {
      SelfService = a.Visibility?.SelfService ?? false, TechnicianPod = a.Visibility?.TechnicianPod ?? false
    };
  }

  internal static Expression<Func<TargetAssignment, LocalTargetAssignmentResource>> Projection =>
    x => new LocalTargetAssignmentResource()
    {
      Id = x.Id,
      TargetType = x.TargetType,
      Target = x.Target,
      TargetCategory = x.TargetCategory,
      TargetGroupFilter = x.TargetGroupFilter,
      TargetEnforcement = x.TargetEnforcement,
      MaintenanceType = x.MaintenanceType,
      MaintenanceIdentifier = x.MaintenanceIdentifier,
      MaintenanceTaskMode = x.MaintenanceTaskMode,
      SoftwareSemanticVersion = x.SoftwareSemanticVersion,
      SoftwareSemanticVersionString = x.SoftwareSemanticVersionString,
      TargetName = x.TargetName,
      DesiredSoftwareState = x.DesiredSoftwareState,
      SoftwareProviderType = x.SoftwareProviderType,
      LicenseId = x.LicenseId,
      TenantId = x.TenantId,
      PropagateToChildTenants = x.PropagateToChildTenants,
      AllowAccessToParentTenant = x.AllowAccessToParentTenant,
      ProviderLinkId = x.ProviderLinkId,
      ProviderDeviceGroupType = x.ProviderDeviceGroupType,
      ProviderClientGroupType = x.ProviderClientGroupType,
      UpdatedDateUTC = x.UpdatedDate,
      CreatedDateUTC = x.CreatedDate,
      UpdatedBy = x.UpdatedByUser != null && x.UpdatedByUser.Person != null ? x.UpdatedByUser.Person.DisplayName : null,
      Excluded = x.Excluded,
      TaskParameterValues = x.TaskParameterValues!,
      ProviderLinkIdForMaintenanceItem = x.ProviderLinkIdForMaintenanceItem,
      Notes = x.Notes != null ? x.Notes.Notes : null,
      NotesUpdatedByUserName = x.Notes != null ? x.Notes.UpdatedByName : null,
      NotesUpdatedUtc = x.Notes != null ? x.Notes.UpdatedDate : null,
      Visibility = x.Visibility != null
        ? new TargetAssignmentVisibilityResource()
        {
          SelfService = x.Visibility.SelfService, TechnicianPod = x.Visibility.TechnicianPod
        }
        : null
    };
}
