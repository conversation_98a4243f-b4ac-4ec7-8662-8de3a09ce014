using System.Text.Json;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetExternalLinkInitializationInfoResponse
{
  public GetExternalLinkInitializationInfoResponse(
    ProviderLink link,
    JsonElement? externalLinkFormData,
    ICollection<string> inputsWithStoredPasswords,
    ProviderTypeDto providerType,
    bool supportsCrossProviderClientExternalLinking,
    string crossProviderClientExternalLinkingDescription)
  {
    ProviderLinkName = link.Name;
    ProviderTypeFormData = externalLinkFormData ?? JsonSerializer.Deserialize<JsonElement>("{}");
    InputsWithStoredPasswords = inputsWithStoredPasswords;
    ProviderType = providerType;
    SupportsCrossProviderClientExternalLinking = supportsCrossProviderClientExternalLinking;
    CrossProviderClientExternalLinkingDescription = crossProviderClientExternalLinkingDescription;
  }

  public string ProviderLinkName { get; }
  public ProviderTypeDto ProviderType { get; }
  public JsonElement ProviderTypeFormData { get; }
  public ICollection<string> InputsWithStoredPasswords { get; }
  public bool SupportsCrossProviderClientExternalLinking { get; }
  public string CrossProviderClientExternalLinkingDescription { get; }
}
