using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetRecommendedApprovalResponse
{
  public int GlobalTargetAssignmentId { get; set; }
  public bool Approved { get; set; }
  public string? UpdatedBy { get; set; }
  public string? CreatedBy { get; set; }
  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }

  public GetRecommendedApprovalResponse() { }

  public GetRecommendedApprovalResponse(RecommendedTargetAssignmentApproval approval)
  {
    GlobalTargetAssignmentId = approval.GlobalTargetAssignmentId;
    Approved = approval.Approved;
    UpdatedBy = approval.UpdatedByUser?.Person?.DisplayName;
    CreatedBy = approval.CreatedByUser?.Person?.DisplayName;
    UpdatedDateUTC = approval.UpdatedDate;
    CreatedDateUTC = approval.CreatedDate;
  }

  internal static Expression<Func<RecommendedTargetAssignmentApproval, GetRecommendedApprovalResponse>> Projection =>
  x => new GetRecommendedApprovalResponse()
  {
    GlobalTargetAssignmentId = x.GlobalTargetAssignmentId,
    Approved = x.Approved,
    UpdatedBy = x.UpdatedBy != null && x.UpdatedByUser != null && x.UpdatedByUser.Person != null ? x.UpdatedByUser.Person.DisplayName : null,
    CreatedBy = x.CreatedBy == null && x.CreatedByUser != null && x.CreatedByUser.Person != null ? x.CreatedByUser.Person.DisplayName : null,
    UpdatedDateUTC = x.UpdatedDate,
    CreatedDateUTC = x.CreatedDate,
  };
}
