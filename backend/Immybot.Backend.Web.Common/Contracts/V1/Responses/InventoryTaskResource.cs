using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class InventoryTaskResource
{
  public InventoryTaskResource(InventoryTask t)
  {
    Id = t.Id;
    InventoryTaskType = t.InventoryTaskType;
    Name = t.Name;
    Frequency = t.Frequency;
    SpecifiedNumMinutes = t.SpecifiedNumMinutes;
    Scripts = t.Scripts
      .Select(s => new InventoryTaskScriptResource(s))
      .ToList();
    UpdatedDate = t.UpdatedDate;
    CreatedDate = t.CreatedDate;
    FromProvider = t.FromProvider;
  }

  public int Id { get; private set; }
  public DatabaseType InventoryTaskType { get; private set; }
  public string Name { get; private set; }
  public InventoryTaskFrequency Frequency { get; private set; }
  public int? SpecifiedNumMinutes { get; private set; }
  public ICollection<InventoryTaskScriptResource> Scripts { get; private set; }
  public DateTime UpdatedDate { get; private set; }
  public DateTime CreatedDate { get; private set; }
  public bool FromProvider { get; private set; }
}
public class InventoryTaskScriptResource
{
  public InventoryTaskScriptResource(InventoryTaskScript s)
  {
    InventoryKey = s.InventoryKey;
    ScriptId = s.ScriptId;
    FromProvider = s.FromProvider;
  }

  public string InventoryKey { get; }
  public int ScriptId { get; }
  public bool FromProvider { get; }
}
