
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;
public class Oauth2AccessTokenWithTenantNameResponse : Oauth2AccessToken
{
  public string? TenantName { get; set; }

  public Oauth2AccessTokenWithTenantNameResponse(Oauth2AccessToken baseToken, string? tenantName = null)
  {
    // Copy all properties from the base token
    foreach (var prop in typeof(Oauth2AccessToken).GetProperties())
    {
      prop.SetValue(this, prop.GetValue(baseToken));
    }

    TenantName = tenantName;
  }
}
