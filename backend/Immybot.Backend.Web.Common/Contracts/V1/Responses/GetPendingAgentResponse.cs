using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetPendingAgentResponse
{
  public int Id { get; set; }
  public int? ComputerId { get; set; }
  public int ProviderLinkId { get; set; }
  public string? ComputerName { get; set; }
  public bool IsComputerDeleted { get; set; }
  public string? Manufacturer { get; set; }
  public string? OperatingSystemName { get; set; }
  public required string ExternalAgentId { get; set; }
  public DateTime DateAdded { get; set; }
  public bool IsOnline { get; set; }
  public string? Serial { get; set; }
  public required string ProviderLinkName { get; set; }
  public string? ExternalClientName { get; set; }
  public DateTime? OsInstallDateUtc { get; set; }

  public bool RequiresManualResolution => IdentificationFailures.Exists(a => !a.Resolved && a.RequiresManualResolution);
  public bool Failed => !RequiresManualResolution && IdentificationFailures.Count(a => !a.Resolved) >= 5;

  public List<GetAgentIdentificationFailureResponse> IdentificationFailures { get; set; }

  public bool RequireManualIdentification { get; set; }

  public GetPendingAgentResponse()
  {
    IdentificationFailures = [];
  }

  [SetsRequiredMembers]
  public GetPendingAgentResponse(ProviderAgent agent, string providerLinkName, string? externalClientName) : this()
  {
    Id = agent.Id;
    ComputerId = agent.ComputerId;
    ExternalAgentId = agent.ExternalAgentId;
    ProviderLinkId = agent.ProviderLinkId;
    DateAdded = agent.DateAddedUTC;
    IsOnline = agent.IsOnline;
    ProviderLinkName = providerLinkName;
    ExternalClientName = externalClientName;
    ComputerName = agent.DeviceDetails.DeviceName;
    OperatingSystemName = agent.DeviceDetails.OperatingSystemName;
    Serial = agent.DeviceDetails.SerialNumber;
    OsInstallDateUtc = agent.DeviceDetails.OSInstallDateUTC;
    Manufacturer = agent.DeviceDetails.Manufacturer;
    RequireManualIdentification = agent.RequireManualIdentification;
  }

  internal static Expression<Func<ProviderAgent, GetPendingAgentResponse>> Projection =>
   x => new GetPendingAgentResponse()
   {
     Id = x.Id,
     ComputerId = x.ComputerId,
     ExternalAgentId = x.ExternalAgentId,
     ProviderLinkId = x.ProviderLinkId,
     DateAdded = x.DateAddedUTC,
     IsOnline = x.IsOnline,
     ProviderLinkName = x.ProviderLink != null ? x.ProviderLink.Name : String.Empty,
     ExternalClientName = x.ProviderClient != null ? x.ProviderClient.ExternalClientName : null,
     ComputerName = x.DeviceDetails.DeviceName,
     OperatingSystemName = x.DeviceDetails.OperatingSystemName,
     Serial = x.DeviceDetails.SerialNumber,
     OsInstallDateUtc = x.DeviceDetails.OSInstallDateUTC,
     Manufacturer = x.DeviceDetails.Manufacturer,
     RequireManualIdentification = x.RequireManualIdentification,
   };
}
