using Reinforced.Typings.Attributes;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

// RT chokes if we don't specify a different name for one of these
[TsInterface(Name = "CircuitBreakerState2")]
public record CircuitBreakerState<T>(string PolicyName, string CircuitState, Exception? LastException = null, T? LastResult = null) where T : class;
public record CircuitBreakerState(string PolicyName, string CircuitState, Exception? LastException = null);
