using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetLocalSoftwareResponse : ISharedSoftwareProperties
{
  public GetLocalSoftwareResponse(LocalSoftware software,
    bool includeSoftwareVersions = false,
    bool includeSoftwarePrerequisites = false,
    bool includeTenantSoftware = false,
    bool includeSoftwareIcon = false)
  {
    if (software == null) throw new ArgumentNullException(nameof(software));
    Name = software.Name;
    LicenseRequirement = software.LicenseRequirement;
    InstallOrder = software.InstallOrder;
    Hidden = software.Hidden;
    SoftwareType = software.SoftwareType;
    Identifier = software.Identifier;
    Id = software.Id;
    OwnerTenantId = software.OwnerTenantId;
    DetectionMethod = software.DetectionMethod;
    SoftwareTableName = software.SoftwareTableName;
    if (includeSoftwareVersions)
      SoftwareVersions = software.SoftwareVersions.Select(v => new GetLocalSoftwareVersionResponse(v, includeSoftware: false)).ToList();
    if (includeSoftwarePrerequisites)
      SoftwarePrerequisites = software.SoftwarePrerequisites.Select(p => new GetSoftwarePrerequisiteResponse(p)).ToList();
    if (includeTenantSoftware)
      TenantSoftware = software.TenantSoftware.Select(t => t.TenantId).ToList();

    if (includeSoftwareIcon && software.SoftwareIcon != null)
      SoftwareIcon = new LocalMediaResponse(software.SoftwareIcon);

    UninstallScriptId = software.UninstallScriptId;
    PostUninstallScriptId = software.PostUninstallScriptId;
    InstallScriptId = software.InstallScriptId;
    PostInstallScriptId = software.PostInstallScriptId;
    UpgradeScriptId = software.UpgradeScriptId;
    TestScriptId = software.TestScriptId;
    TestFailedError = software.TestFailedError;
    TestRequired = software.TestRequired;
    UpgradeStrategy = software.UpgradeStrategy;
    UninstallScriptType = software.UninstallScriptType;
    PostUninstallScriptType = software.PostUninstallScriptType;
    InstallScriptType = software.InstallScriptType;
    PostInstallScriptType = software.PostInstallScriptType;
    UpgradeScriptType = software.UpgradeScriptType;
    TestScriptType = software.TestScriptType;
    DetectionScriptId = software.DetectionScriptId;
    DetectionScriptType = software.DetectionScriptType;
    MaintenanceTaskType = software.MaintenanceTaskType;
    MaintenanceTaskId = software.MaintenanceTaskId;
    Notes = software.Notes;
    RebootNeeded = software.RebootNeeded;
    UpdatedDate = software.UpdatedDate;
    UpdatedDateUTC = software.UpdatedDate;
    CreatedDateUTC = software.CreatedDate;
    UpdatedBy = software.UpdatedByUser?.DisplayName;
    UpgradeCode = software.UpgradeCode;
    RepairType = software.RepairType;
    RepairScriptId = software.RepairScriptId;
    RepairScriptType = software.RepairScriptType;
    SoftwareTableNameSearchMode = software.SoftwareTableNameSearchMode;
    SoftwareIconMediaId = software.SoftwareIconMediaId;
    Recommended = software.Recommended;
    ChocoProviderSoftwareId = software.ChocoProviderSoftwareId;
    NiniteProviderSoftwareId = software.NiniteProviderSoftwareId;
    LicenseType = software.LicenseType;
    LicenseDescription = software.LicenseDescription;
    UseDynamicVersions = software.UseDynamicVersions || software.AgentIntegrationTypeId is not null;
    DynamicVersionsScriptId = software.DynamicVersionsScriptId;
    DynamicVersionsScriptType = software.DynamicVersionsScriptType;
    DownloadInstallerScriptId = software.DownloadInstallerScriptId;
    DownloadInstallerScriptType = software.DownloadInstallerScriptType;
    AgentIntegrationTypeId = software.AgentIntegrationTypeId;
  }

  public string Name { get; }
  public SoftwareLicenseRequirement LicenseRequirement { get; }
  public int InstallOrder { get; }
  public bool Hidden { get; }
  public SoftwareType SoftwareType { get; }

  public string Identifier { get; }
  public int? Id { get; }
  public int? OwnerTenantId { get; }

  public int? DetectionScriptId { get; set; }
  public int? RepairScriptId { get; set; }

  public DetectionMethod DetectionMethod { get; set; }
  public string? SoftwareTableName { get; set; }
  public string? Notes { get; }

  public DatabaseType? DetectionScriptType { get; set; }
  public DatabaseType? RepairScriptType { get; set; }

  public int? MaintenanceTaskId { get; set; }

  public DatabaseType? MaintenanceTaskType { get; set; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<int>? TenantSoftware { get; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetLocalSoftwareVersionResponse>? SoftwareVersions { get; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetSoftwarePrerequisiteResponse>? SoftwarePrerequisites { get; }

  public DateTime UpdatedDate { get; }
  public bool RebootNeeded { get; }
  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public string? UpdatedBy { get; set; }
  public string? UpgradeCode { get; set; }
  public RepairActionType RepairType { get; set; }
  public SoftwareTableNameSearchMode? SoftwareTableNameSearchMode { get; set; }
  public int? SoftwareIconMediaId { get; set; }
  public bool Recommended { get; set; }

  public string? ChocoProviderSoftwareId { get; set; }
  public string? NiniteProviderSoftwareId { get; set; }

  public LocalMediaResponse? SoftwareIcon { get; set; }

  public int? InstallScriptId { get; set; }

  public DatabaseType? InstallScriptType { get; set; }

  public int? TestScriptId { get; set; }

  public DatabaseType? TestScriptType { get; set; }

  public bool TestRequired { get; set; }

  public string? TestFailedError { get; set; }

  public int? UpgradeScriptId { get; set; }

  public DatabaseType? UpgradeScriptType { get; set; }

  public UpdateActionType UpgradeStrategy { get; set; }

  public Guid? AgentIntegrationTypeId { get; set; }

  public int? UninstallScriptId { get; set; }

  public DatabaseType? UninstallScriptType { get; set; }

  public int? PostInstallScriptId { get; set; }

  public DatabaseType? PostInstallScriptType { get; set; }

  public int? PostUninstallScriptId { get; set; }

  public DatabaseType? PostUninstallScriptType { get; set; }

  public LicenseType LicenseType { get; set; }
  public string? LicenseDescription { get; set; }

  public bool UseDynamicVersions { get; set; }
  public int? DynamicVersionsScriptId { get; set; }
  public DatabaseType? DynamicVersionsScriptType { get; set; }

  public int? DownloadInstallerScriptId { get; set; }
  public DatabaseType? DownloadInstallerScriptType { get; set; }
}
