using System.Linq.Expressions;
using Immybot.Backend.Application.Interface.Extensions;
using Immybot.Backend.Domain.Models;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetMaintenanceSessionResponse
{
  private GetMaintenanceSessionResponse()
  {
  }

  public GetMaintenanceSessionResponse(
    MaintenanceSession session,
    bool includeActions = false,
    bool includeStages = false,
    bool includeLogs = false,
    bool includeComputer = false,
    bool includeTenant = false)
  {
    ArgumentNullException.ThrowIfNull(session);
    Id = session.Id;
    JobId = session.JobId;
    ComputerId = session.ComputerId;
    TenantId = session.TenantId;
    PersonId = session.PersonId;
    ScheduledId = session.ScheduledId;
    SessionStatus = session.SessionStatus;
    Onboarding = session.Onboarding;
    CreatedDateUTC = session.CreatedDate;
    UpdatedDateUTC = session.UpdatedDate;
    UpdatedById = session.UpdatedBy;
    CreatedById = session.CreatedBy;
    FullMaintenance = session.FullMaintenance;
    Duration = session.Duration;

    if (includeActions)
    {
      MaintenanceActions = session.MaintenanceActions
        .Select(a => new GetMaintenanceActionResponse(a))
        .ToList();
    }

    if (includeStages)
    {
      Stages = session.Stages
        .Select(a => new GetMaintenanceSessionStageResponse(a, includeSession: false))
        .ToList();
    }
    if (includeLogs)
    {
      Logs = session.Logs
        .Select(a => new GetSessionLogResponse(a, includeSession: false))
        .ToList();
    }
    if (includeComputer && session.Computer != null)
    {
      Computer = new GetComputerResponse(session.Computer, false, includeSessions: false, includeTenant: true);
      Tenant = Computer.Tenant;
    }

    if (includeTenant && !includeComputer && session.OwnerTenant != null)
    {
      Tenant = new GetTenantResponse(session.OwnerTenant);
    }
  }

  public int Id { get; set; }
  public string? JobId { get; set; }
  public int? ComputerId { get; set; }
  public int? TenantId { get; set; }
  public int? PersonId { get; set; }
  public int? ScheduledId { get; set; }
  public SessionStatus SessionStatus { get; set; }
  public bool Onboarding { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  public DateTime UpdatedDateUTC { get; set; }
  public int? UpdatedById { get; set; }
  public int? CreatedById { get; set; }
  public string? CreatedBy { get; set; }
  public bool FullMaintenance { get; set; }
  public string ComputerName { get; set; } = string.Empty;
  public string TenantName { get; set; } = string.Empty;
  public TimeSpan? Duration { get; set; }
  public SessionStatus? OnboardingStageStatus { get; set; }
  public SessionStatus? DetectionStageStatus { get; set; }
  public SessionStatus? ExecutionStageStatus { get; set; }
  public SessionStatus? AgentUpdatesStageStatus { get; set; }
  public SessionStatus? ResolutionStageStatus { get; set; }
  public SessionStatus? InventoryStageStatus { get; set; }
  public SessionJobArgs? SessionJobArgs { get; set; }
  public DateTime? ScheduledExecutionDate { get; set; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetMaintenanceActionResponse>? MaintenanceActions { get; set; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetMaintenanceSessionStageResponse>? Stages { get; set; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<MaintenanceActionActivityResponse>? Activities { get; set; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetSessionLogResponse>? Logs { get; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetComputerResponse? Computer { get; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetTenantResponse? Tenant { get; }

  internal static Expression<Func<MaintenanceSession, GetMaintenanceSessionResponse>> Projection(
    bool includeArgs = false,
    bool includeActions = false,
    bool includeStages = false) =>
    x => new GetMaintenanceSessionResponse()
    {
      Id = x.Id,
      JobId = x.JobId ?? string.Empty,
      ComputerId = x.ComputerId,
      TenantId = x.TenantId,
      PersonId = x.PersonId,
      ScheduledId = x.ScheduledId,
      SessionStatus = x.SessionStatus,
      Onboarding = x.Onboarding,
      CreatedDateUTC = x.CreatedDate,
      CreatedById = x.CreatedBy,
      UpdatedById = x.UpdatedBy,
#pragma warning disable S3220 // Method calls should not resolve ambiguously to overloads with "params"
      CreatedBy = (x.CreatedByUser != null && x.CreatedByUser.Person != null)
        ? string.Concat(
          x.CreatedByUser.Person.FirstName ?? string.Empty,
          " ",
          x.CreatedByUser.Person.LastName ?? string.Empty
        ).Trim()
        : null,
#pragma warning restore S3220 // Method calls should not resolve ambiguously to overloads with "params"
      UpdatedDateUTC = x.UpdatedDate,
      FullMaintenance = x.FullMaintenance,
      ComputerName = x.Computer != null ? x.Computer.GetName() ?? String.Empty : String.Empty,
      TenantName = x.OwnerTenant != null ? x.OwnerTenant.Name : String.Empty,
      OnboardingStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.Onboarding)!.StageStatus,
      DetectionStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.Detection)!.StageStatus,
      ExecutionStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.Execution)!.StageStatus,
      AgentUpdatesStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.AgentUpdates)!.StageStatus,
      ResolutionStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.Resolution)!.StageStatus,
      InventoryStageStatus = x.Stages.FirstOrDefault(b => b.Type == SessionStageType.Inventory)!.StageStatus,
      ScheduledExecutionDate = x.ScheduledExecutionDate,
      Duration = x.Duration,
      SessionJobArgs = includeArgs ? x.JobArgs : null,
      MaintenanceActions = includeActions ? x.MaintenanceActions.OrderBy(a => a.Id).Select(a => new GetMaintenanceActionResponse(a)).ToList() : null,
      Stages = includeStages ? x.Stages.Select(a => new GetMaintenanceSessionStageResponse(a, false)).ToList() : null,
    };
}
