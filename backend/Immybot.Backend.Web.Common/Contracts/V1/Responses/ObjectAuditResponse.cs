namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

/// <summary>
/// Response indicating the total number of audits for a given script object and the audit details requested.
/// </summary>
/// <param name="Total"></param>
/// <param name="Audit"></param>
public record ScriptActionAuditResult(int Total, ScriptActionAudit? Audit);

/// <summary>
/// Audit details for a script object.
/// </summary>
/// <param name="NewValue"></param>
/// <param name="OldValue"></param>
/// <param name="DateTimeUtc"></param>
/// <param name="UserDisplayName"></param>
public record ScriptActionAudit(string? NewValue, string? OldValue, DateTime DateTimeUtc, string? UserDisplayName);

/// <summary>
/// Object representing a script action from an audit's new_values or old_values jsonelement.
/// </summary>
/// <param name="Action"></param>
public record ScriptAction(string Action);
