using System.Text.Json;
using Immybot.Backend.Web.Common.Lib;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class DxComputerInventoryScriptResult
{
  public required string? ComputerName { get; set; }
  public required int ComputerId { get; set; }
  public required string TenantName { get; set; }
  public required int TenantId { get; set; }
  public required DateTime TimestampUtc { get; set; }
  public required bool Success { get; set; }
  public required string InventoryKey { get; set; }
  [ExcelIgnoreProperty] public JsonDocument? LatestSuccessResult { get; set; }

  public string? LatestSuccessResultJson =>
    LatestSuccessResult?.RootElement.GetProperty("Output").GetRawText();
}
