using Immybot.Backend.Application.Interface.Actions.Responses;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public record GetTenantSoftwareFromInventoryResponse(
  string? AzureUserObjectId,
  int ComputerId,
  string? ComputerName,
  string DisplayName,
  int GlobalSoftwareId,
  string GlobalSoftwareName,
  string? GlobalSoftwareVersion,
  int? PersonId,
  string? PersonName,
  int TenantId,
  DateTimeOffset DateDetectedUtc)
{

  public GetTenantSoftwareFromInventoryResponse(SoftwareFromInventoryResultsModel model)
    : this(
        model.AzureUserObjectId,
        model.ComputerId,
        model.ComputerName,
        model.DisplayName,
        model.GlobalSoftwareId,
        model.GlobalSoftwareName,
        model.GlobalSoftwareVersion,
        model.PersonId,
        model.PersonName,
        model.TenantId,
        model.DateDetectedUtc)
  { }
}
