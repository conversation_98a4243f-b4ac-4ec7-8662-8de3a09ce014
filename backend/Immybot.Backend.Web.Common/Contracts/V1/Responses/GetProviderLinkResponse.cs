using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetProviderLinkResponse : IProviderLinkDetails, IProviderTypeCapabilities
{
  public readonly struct LinkedExternalLink
  {
    public LinkedExternalLink(
      int providerLinkId,
      string providerLinkName,
      Guid providerTypeId,
      bool isExternalClientLinkingEnabled,
      bool isExternalProviderInitializedFromThisProvider)
    {
      ProviderLinkId = providerLinkId;
      ProviderLinkName = providerLinkName;
      ProviderTypeId = providerTypeId;
      IsExternalClientLinkingEnabled = isExternalClientLinkingEnabled;
      IsExternalProviderInitializedFromThisProvider = isExternalProviderInitializedFromThisProvider;
    }

    public int ProviderLinkId { get; }
    public string ProviderLinkName { get; }
    public Guid ProviderTypeId { get; }
    public bool IsExternalClientLinkingEnabled { get; }
    public bool IsExternalProviderInitializedFromThisProvider { get; }
  }
  public GetProviderLinkResponse() { }

  [SetsRequiredMembers]
  public GetProviderLinkResponse(
    ProviderLink link,
    bool includeClients = false,
    bool includeComputers = false,
    bool includeSchedules = false,
    bool includeProvidersLinkedFromThisProvider = false,
    bool includeUnlinkedClients = false,
    string? errorMessage = null)
  {
    Id = link.Id;
    Disabled = link.Disabled;
    ErrorMessage = errorMessage;
    HealthStatus = link.HealthStatus;
    HealthStatusMessage = link.HealthStatusMessage;
    ProviderTypeFormData = link.ProviderTypeFormData;
    OwnerTenantId = link.TenantId;
    ProviderTypeId = link.ProviderTypeId;
    Name = link.Name;
    RunScriptPriority = link.RunScriptPriority;
    UpdatedByName = link.UpdatedByUser?.DisplayName;
    UpdatedBy = link.UpdatedBy;
    CreatedBy = link.CreatedBy;
    UpdatedDateUTC = link.UpdatedDate;
    CreatedDateUTC = link.CreatedDate;
    ExcludedCapabilities = link.ExcludedCapabilities;
    if (includeClients)
    {
      IEnumerable<ProviderClient> clientQuery = link.ProviderClients;
      if (!includeUnlinkedClients)
      {
        clientQuery = link.ProviderClients.Where(a => a.TenantId != null);
      }
      ProviderClients = clientQuery.Select(c => new GetProviderClientResponse(c)).ToList();

    }
    if (includeComputers)
      Computers = link.Agents
        .Where(a => a.Computer != null)
        .Select(a => new GetComputerResponse(a.Computer!))
        .ToList();
    if (includeSchedules)
      Schedules = link.Schedules.Select(s => new GetScheduleResponse(s)).ToList();
    if (includeProvidersLinkedFromThisProvider)
    {
      ProvidersLinkedFromThisProvider = link
        .ProvidersLinkedFromThisProvider
        .Where(s => s.ProviderLink2 != null)
        .Select(s => new LinkedExternalLink(s.ProviderLink2Id, s.ProviderLink2!.Name, s.ProviderLink2.ProviderTypeId, s.IsExternalClientLinkingEnabled, s.IsProviderLink2InitializedFromProviderLink1))
        .ToList();
      LinkedFromProviders = link
        .LinkedFromProviders
        .Where(s => s.ProviderLink1 != null)
        .Select(s => new LinkedExternalLink(s.ProviderLink1Id, s.ProviderLink1!.Name, s.ProviderLink1.ProviderTypeId, s.IsExternalClientLinkingEnabled, s.IsProviderLink2InitializedFromProviderLink1))
        .ToList();
    }
  }
  public int Id { get; set; }
  public bool Disabled { get; set; }

  public string? ErrorMessage { get; set; }
  public HealthStatus HealthStatus { get; set; }
  public string? HealthStatusMessage { get; set; }
  public required string Name { get; set; }
  public int RunScriptPriority { get; set; }
  public JsonElement ProviderTypeFormData { get; set; }
  public ICollection<string>? InputsWithStoredPasswords { get; set; }
  public int OwnerTenantId { get; set; }
  public Guid ProviderTypeId { get; set; }
  public string? ProviderPluginBaseUrl { get; set; }
  public int? UpdatedBy { get; set; }
  public int? CreatedBy { get; set; }
  public DateTime UpdatedDateUTC { get; set; }
  public DateTime CreatedDateUTC { get; set; }

  public string? IncludeClientsFailedMessage { get; set; }
  public string? GetFormSchemaFailedMessage { get; set; }
  public string? GetProviderFailedMessage { get; set; }
  public string? UpdatedByName { get; set; }

  public string? LatestAgentVersion { get; set; }
  public string? GetLatestAgentVersionFailedMessage { get; set; }
  public bool SupportsDeviceUpdating { get; set; }
  public List<string> ProviderCapabilities { get; set; } = [];
  public List<string>? ExcludedCapabilities { get; set; }
  public ICollection<SupportedCrossProviderLinkage> SupportedCrossProviderClientLinkages { get; set; } = [];
  public ICollection<SupportedCrossProviderLinkage> SupportedCrossProviderInitializationLinkages { get; set; } = [];

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public IEnumerable<GetProviderClientResponse>? ProviderClients { get; set; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetComputerResponse>? Computers { get; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<GetScheduleResponse>? Schedules { get; }

  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<LinkedExternalLink>? ProvidersLinkedFromThisProvider { get; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<LinkedExternalLink>? LinkedFromProviders { get; }
}
