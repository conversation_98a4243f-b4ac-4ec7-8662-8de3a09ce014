using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class LocalMediaResponse : MediaResponseBase
{
  public bool Owned { get; set; }
  public string? UpdatedBy { get; set; }
  public ICollection<TenantMedia> Tenants { get; } = new HashSet<TenantMedia>();

  internal LocalMediaResponse() { }

  internal LocalMediaResponse(Media media, int tenantId) : base(media)
  {
    Id = media.Id;
    Owned = media.TenantRelationships.Any(a => a.Relationship == Relationship.Owned && a.TenantId == tenantId);
    UpdatedBy = media.UpdatedByUser?.DisplayName;
  }

  internal LocalMediaResponse(Media media) : base(media)
  {
    Id = media.Id;
    Tenants = media.TenantRelationships;
    UpdatedBy = media.UpdatedByUser?.DisplayName;
  }

  internal static Expression<Func<Media, LocalMediaResponse>> Projection =>
    x => new LocalMediaResponse()
    {
      Id = x.Id,
      Name = x.Name,
      FileName = x.FileName,
      MimeType = x.MimeType,
      PackageHash = x.PackageHash,
      BlobReference = x.BlobReference,
      RelativeCacheSourcePath = x.RelativeCacheSourcePath,
      UpdatedDateUTC = x.UpdatedDate,
      CreatedDateUTC = x.CreatedDate,
      UpdatedBy = x.UpdatedByUser != null && x.UpdatedByUser.Person != null ? x.UpdatedByUser.Person.DisplayName : null,
      Owned = x.TenantRelationships.Any(a => a.Relationship == Relationship.Owned),
      DatabaseType = DatabaseType.Local,
      Category = x.Category,
    };
}
