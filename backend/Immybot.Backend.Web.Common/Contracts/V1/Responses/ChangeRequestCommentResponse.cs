using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class ChangeRequestCommentResponse : IChangeRequestComment
{
  public int Id { get; set; }
  public string Comment { get; set; } = string.Empty;
  public string CommentedByUsername { get; set; } = string.Empty;
  public int ChangeRequestId { get; set; }
  public DateTime CreatedDateUtc { get; set; }

  internal static Expression<Func<ChangeRequestComment, ChangeRequestCommentResponse>> Projection =>
    x => new ChangeRequestCommentResponse()
    {
      Id = x.Id,
      Comment = x.Comment,
      CommentedByUsername = x.CommentedByUsername,
      ChangeRequestId = x.ChangeRequestId,
      CreatedDateUtc = x.CreatedDate,
    };
}
