namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class RunImmyServiceResponseSession
{
  public int? PersonId { get; internal set; }
  public int? TenantId { get; internal set; }
  public int? ComputerId { get; internal set; }
  public int SessionId { get; internal set; }
}
public class RunImmyServiceResponseBody
{
  public List<RunImmyServiceResponseSession> SessionsStarted { get; }
  public List<RunImmyServiceResponseSession> SessionsAlreadyInProgress { get; }

  internal RunImmyServiceResponseBody(
    List<RunImmyServiceResponseSession> sessionsStarted,
    List<RunImmyServiceResponseSession> sessionsAlreadyInProgress)
  {
    SessionsStarted = sessionsStarted;
    SessionsAlreadyInProgress = sessionsAlreadyInProgress;
  }
}
