using System.Collections.Immutable;
using System.Diagnostics;
using Immybot.Backend.Application.Lib;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public record ApplicationLocksResponse(string Key, ApplicationLockCallerInfo LockHolder, IEnumerable<ApplicationLockCallerInfo> LockWaiters)
{
  public static ApplicationLocksResponse From(string key, LockHolder lockHolder, IImmutableList<LockWaiter> lockWaiters)
  {
    return new ApplicationLocksResponse(key, ApplicationLockCallerInfo.From(lockHolder), lockWaiters.Select(x => ApplicationLockCallerInfo.From(x)));
  }
}

public record ApplicationLockCallerInfo(string Name, Guid Id, bool IsLockHolder, DateTime Since)
{
  public static ApplicationLockCallerInfo From(ILockCallerInfo callerInfo)
  {
    return new ApplicationLockCallerInfo(callerInfo.Name, callerInfo.Id, callerInfo is LockHolder, callerInfo.CalculateDatetimeStamp());
  }
}

public record ApplicationLockEvent(string Key, ApplicationLockEventTypes EventType, string callerInfo, DateTime EventTimestamp, string? Reason)
{
  public static ApplicationLockEvent From(KeyedLockEvent evt)
  {
    var eventType = evt switch
    {
      CurrentlyHoldsEvent => ApplicationLockEventTypes.CurrentlyHoldsEvent,
      WaitingEvent => ApplicationLockEventTypes.WaitingEvent,
      HoldingEvent => ApplicationLockEventTypes.HoldingEvent,
      ReleasedEvent => ApplicationLockEventTypes.ReleasedEvent,
      TokenAbortedWaitingEvent => ApplicationLockEventTypes.TokenAbortedWaitingEvent,
      TimeoutAbortedWaitingEvent => ApplicationLockEventTypes.TimeoutAbortedWaitingEvent,
      WatchdogAbortedHoldingEvent => ApplicationLockEventTypes.WatchdogAbortedHoldingEvent,
      AbortedLockHolderDisposedHandleEvent => ApplicationLockEventTypes.AbortedLockHolderDisposedHandleEvent,
      InputTokenAbortedHoldingEvent => ApplicationLockEventTypes.InputTokenAbortedHoldingEvent,
      TokenCancellationRequestedEvent => ApplicationLockEventTypes.TokenCancellationRequestedEvent,
      WatchdogTimeoutCancellationRequestedEvent => ApplicationLockEventTypes.WatchdogTimeoutCancellationRequestedEvent,
      ExternalTokenCancellationRequestedEvent => ApplicationLockEventTypes.ExternalTokenCancellationRequestedEvent,
      ExternalTokenAbortedHoldingEvent => ApplicationLockEventTypes.ExternalTokenAbortedHoldingEvent,
      _ => throw new NotImplementedException()
    };

    string? reasonString = evt switch
    {
      ExternalTokenCancellationRequestedEvent e => e.ExternalCancellationReasonMessage,
      ExternalTokenAbortedHoldingEvent e => e.ExternalCancellationReasonMessage,
      _ => null
    };

    return new ApplicationLockEvent(evt.Key, eventType, evt.CallerString, DateTime.UtcNow - Stopwatch.GetElapsedTime(evt.EventTimestampInTicks), reasonString);
  }
}

public enum ApplicationLockEventTypes
{
  CurrentlyHoldsEvent,
  WaitingEvent,
  HoldingEvent,
  ReleasedEvent,
  TokenAbortedWaitingEvent,
  TimeoutAbortedWaitingEvent,
  WatchdogAbortedHoldingEvent,
  AbortedLockHolderDisposedHandleEvent,
  InputTokenAbortedHoldingEvent,
  TokenCancellationRequestedEvent,
  WatchdogTimeoutCancellationRequestedEvent,
  ExternalTokenCancellationRequestedEvent,
  ExternalTokenAbortedHoldingEvent,
}
