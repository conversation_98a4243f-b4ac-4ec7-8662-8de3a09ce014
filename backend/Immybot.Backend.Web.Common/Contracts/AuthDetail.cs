namespace Immybot.Backend.Web.Common.Contracts;

public class AuthDetail
{
  public AuthCode AuthCode { get; }
  public string AuthCodeMessage => AuthCode.ToString();
  public string Message { get; }
  public bool AccessRequested { get; }
  public bool EnableRequestAccess { get; }

  public AuthDetail(AuthCode code, string message, bool accessRequested = false, bool enableRequestAccess = false)
  {
    AuthCode = code;
    Message = message;
    AccessRequested = accessRequested;
    EnableRequestAccess = enableRequestAccess;
  }
}
