using System.Reflection;
using System.Text.RegularExpressions;
using DevExtreme.AspNet.Data.ResponseModel;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Web.Common.Contracts.V1.Responses.ResponseCollectionTypes;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Routing;
using Reinforced.Typings;
using Reinforced.Typings.Ast;
using Reinforced.Typings.Ast.TypeNames;
using Reinforced.Typings.Generators;

namespace Immybot.Backend.Web.Common.ReinforcedTypings;

public record QueryParam(string Name, bool HasDefaultValue, object? DefaultValue);

public partial class ImmyBackendApiMethodGenerator : MethodCodeGenerator
{
  internal static readonly Type[] NonGenericActionResultTypes = [typeof(ActionResult), typeof(IActionResult), typeof(Task<ActionResult>), typeof(Task<IActionResult>)];
  public override RtFunction? GenerateNode(MethodInfo element, RtFunction result, TypeResolver resolver)
  {
    // Use reflection to modify the type resolver's private _resolveCache dictionary to
    // include the [FromService] parameters so that reinforced types doesn't complain
    // about not being able to find a suitable type.
    var fromServicesParams = element.GetParameters()
      .Where(p => p.GetCustomAttribute(typeof(FromServicesAttribute)) != null)
      .ToList();
    var resolveCache = typeof(TypeResolver).GetField("_resolveCache", BindingFlags.NonPublic | BindingFlags.Instance);
    var dict = (Dictionary<Type, RtTypeName>?)resolveCache?.GetValue(resolver);
    if (dict != null)
    {
      foreach (var p in fromServicesParams)
      {
        dict[p.ParameterType] = new RtSimpleTypeName("any");
      }
    }

    result = base.GenerateNode(element, result, resolver);
    if (result == null) return null;

    var httpAttrs = element.GetCustomAttributes(typeof(HttpMethodAttribute))
      .OfType<HttpMethodAttribute>()
      .FirstOrDefault();

    // Only generate code for methods with an HTTP attribute
    if (httpAttrs == null) return null;

    // Get list of all parameters that can be supplied to the c# method
    // We'll use this to determine what query params can be supplied and whether
    // an http body can be supplied
    var methodParams = element.GetParameters();

    if (Array.Exists(methodParams, p => p.ParameterType == typeof(DataSourceLoadOptions)))
    {
      // query params will be combined into a single query object
      var queryParams2 = methodParams
        .Where(
          p =>
            p.GetCustomAttribute(typeof(FromQueryAttribute)) != null
            && p.ParameterType != typeof(AppSieveModel)
            && p.Name != null
        )
        .Select(p => new QueryParam(p.Name!, p.HasDefaultValue, p.DefaultValue))
        .ToList();
      var queryParamNames2 = queryParams2.Select(p => p.Name).ToList();
      var queryArgs2 = result.Arguments.Where(a => queryParamNames2.Contains(a.Identifier.IdentifierName)).ToArray();

      // Determine what params are used for route construction
      var ss = RouteParamMatcher().Matches(httpAttrs.Template ?? "").Select(p => p.Groups[1].Value);

      // Remove args from the method that aren't used for route construction
      // This removes, e.g., injected services, model-bound objects, body, and query params
      // (we'll add back the body arg, and a single query param arg as the last argument instead)
      result.Arguments.RemoveAll(a => !ss.Contains(a.Identifier.IdentifierName));

      // Add back a single query arg to handle all the query params
      RtArgument? queryArg2 = null;
      if (queryParams2.Count != 0)
      {
        var queryTypeTs = $"{{{string.Join(", ", queryArgs2.Select(a => $"{a.Identifier.IdentifierName}{(a.Identifier.IsNullable || a.DefaultValue != null ? "?" : "")}: {a.Type}"))}}}";
        var areAllQueryParamsOptional = queryArgs2.All(q => q.Identifier.IsNullable || q.DefaultValue != null);
        queryArg2 = new RtArgument
        {
          Identifier = new RtIdentifier("queries"),
          Type = new RtSimpleTypeName(queryTypeTs),
          DefaultValue = areAllQueryParamsOptional ? "{}" : null,
        };
        result.Arguments.Add(queryArg2);
      }

      // just return a call to createStore
      var routeLine = $"const route = `{GetFullRoute(element, httpAttrs.Template)}`";
      var callLine = $"return createStore" + queryArg2 switch
      {
        not null => $"(route, {{ queries }});",
        _ => $"(route);"
      };
      result.Body = new RtRaw(string.Join("\n", routeLine, callLine));
      var t = result.ReturnType;
      result.ReturnType = resolver.ResolveTypeName(typeof(LoadResult));
      if (t is RtArrayType { ElementType: { } e })
      {
        result.ReturnType = new RtSimpleTypeName(result.ReturnType.ToString(), e);
      }
      return result;
    }
    if (element.ReturnType.IsGenericType
        && element.ReturnType.GetGenericTypeDefinition() == typeof(Task<>)
        && element.ReturnType.GenericTypeArguments[0] is { } ax
        && ax.IsGenericType
        && ax.GetGenericTypeDefinition() == typeof(ActionResult<>)
        && ax.GenericTypeArguments[0] is { } bx
        && bx.IsGenericType
        && bx.GetGenericTypeDefinition() == typeof(EventStream<>))
    {
      // generate an EventSource class that the frontend can use to subscribe to events
      var routeLine = $"const route = `{GetFullRoute(element, httpAttrs.Template)}`";
      var callLine = """
        return new EventSource(route, {
          withCredentials: true,
        });
        """;
      result.Body = new RtRaw(string.Join("\n", routeLine, callLine));
      result.ReturnType = new RtSimpleTypeName("EventSource");
      var ss = RouteParamMatcher().Matches(httpAttrs.Template ?? "").Select(p => p.Groups[1].Value);
      result.Arguments.RemoveAll(a => !ss.Contains(a.Identifier.IdentifierName));
      return result;
    }

    result.IsAsync = true;

    if (NonGenericActionResultTypes.Contains(element.ReturnType))
    {
      // If it's a non-generic ActionResult, use Promise<void> as the return type
      result.ReturnType = new RtAsyncType();
    }
    else
    {
      // If it's a generic ActionResult, use the generic type parameter as the return type
      result.ReturnType = new RtAsyncType(result.ReturnType);
    }

    // only one body param can be supplied - take the first one
    var bodyParams = Array.Find(methodParams, p => p.GetCustomAttribute(typeof(FromBodyAttribute)) != null) is { Name: { } bodyParamName }
      ? new[] { bodyParamName }
      : [];
    var bodyArgs = result.Arguments.Where(a => bodyParams.Contains(a.Identifier.IdentifierName)).ToArray();

    // query params will be combined into a single query object
    var queryParams = methodParams
      .Where(
        p =>
          p.GetCustomAttribute(typeof(FromQueryAttribute)) != null
          && p.ParameterType != typeof(AppSieveModel)
          && p.Name != null
      )
      .Select(p => new QueryParam(p.Name!, p.HasDefaultValue, p.DefaultValue))
      .ToList();
    var queryParamNames = queryParams.Select(p => p.Name).ToList();
    var queryArgs = result.Arguments.Where(a => queryParamNames.Contains(a.Identifier.IdentifierName)).ToArray();

    if (Array.Exists(methodParams, p => p.ParameterType == typeof(AppSieveModel)))
    {
      // add filters, sorts, page and pageSize to queryArgs
      var sieveQueries = new[]
      {
        new RtArgument
        {
          Identifier = new RtIdentifier("filters"),
          Type = resolver.ResolveTypeName(typeof(string)),
          DefaultValue = "null",
        },
        new RtArgument
        {
          Identifier = new RtIdentifier("sorts"),
          Type = resolver.ResolveTypeName(typeof(string)),
          DefaultValue = "null",
        },
        new RtArgument
        {
          Identifier = new RtIdentifier("page"),
          Type = resolver.ResolveTypeName(typeof(int?)),
          DefaultValue = "null",
        },
        new RtArgument
        {
          Identifier = new RtIdentifier("pageSize"),
          Type = resolver.ResolveTypeName(typeof(int?)),
          DefaultValue = "null",
        }
      };
      queryArgs = queryArgs.Concat(sieveQueries).ToArray();
      queryParams.AddRange(sieveQueries.Select(q => new QueryParam(q.Identifier.IdentifierName, true, null)));
    }

    // Determine what params are used for route construction
    var routeParamNames = RouteParamMatcher().Matches(httpAttrs.Template ?? "").Select(p => p.Groups[1].Value);

    // Remove args from the method that aren't used for route construction
    // This removes, e.g., injected services, model-bound objects, body, and query params
    // (we'll add back the body arg, and a single query param arg as the last argument instead)
    result.Arguments.RemoveAll(a => !routeParamNames.Contains(a.Identifier.IdentifierName));

    // Add back the body arg if it exists
    result.Arguments.AddRange(bodyArgs);

    // Add back a single query arg to handle all the query params
    RtArgument? queryArg = null;
    if (queryParams.Count != 0)
    {
      var queryTypeTs = $"{{{string.Join(", ", queryArgs.Select(a => $"{a.Identifier.IdentifierName}{(a.Identifier.IsNullable || a.DefaultValue != null ? "?" : "")}: {a.Type}"))}}}";
      var areAllQueryParamsOptional = queryArgs.All(q => q.Identifier.IsNullable || q.DefaultValue != null);
      queryArg = new RtArgument
      {
        Identifier = new RtIdentifier("queries"),
        Type = new RtSimpleTypeName(queryTypeTs),
        DefaultValue = areAllQueryParamsOptional ? "{}" : null,
      };
      result.Arguments.Add(queryArg);
    }

    // Add a cancel token arg to allow the js client caller to cancel the request
    result.Arguments.Add(new RtArgument
    {
      Identifier = new RtIdentifier("cancelToken") { IsNullable = true },
      Type = new RtSimpleTypeName("CancelToken"),
    });

    var httpFunctionName = $"${httpAttrs.HttpMethods.First().ToLowerInvariant()}";
    var retType = result.ReturnType switch
    {
      RtAsyncType { TypeNameOfAsync: RtSimpleTypeName s } => s.ToString(),
      RtAsyncType { TypeNameOfAsync: RtArrayType a } => a.ToString(),
      _ => "void"
    };
    var retTypeGeneric = retType == "void" ? "" : $"<{retType}>";
    var acceptsBody = httpFunctionName is not ("$get" or "$delete");

    // Determine how to call the http function based on whether the method supports an
    // http body, whether there is an http body to pass, and whether there are query params
    string bodyFragment;
    if (!acceptsBody)
    {
      bodyFragment = $"(route, {{cancelToken}});";
    }
    else if (bodyParams is [{ } p, ..])
    {
      bodyFragment = $"(route, {p}, {{cancelToken}});";
    }
    else
    {
      bodyFragment = $"(route, undefined, {{cancelToken}});";
    }

    var callLineTs = $"await {httpFunctionName}{retTypeGeneric}" + queryArg switch
    {
      not null when !acceptsBody => $"(route, {{params: queries, cancelToken}});",
      not null when bodyParams is [{ } p, ..] => $"(route, {p}, {{params: queries, cancelToken}});",
      not null => $"(route, undefined, {{params: queries, cancelToken}});",
      _ => bodyFragment
    };

    result.Body = new RtRaw(string.Join("\n", new[] {

      // Convert route template to js interpolated string - assume all the route params
      // specified in the template are arguments specified in result.Arguments
      // TODO: This might not always be a valid assumption - we should probably check
      $"const route = `{GetFullRoute(element, httpAttrs.Template)}`",

      // Call the http function and (if applicable) store the result in a variable
      retType == "void" ? callLineTs : $"const res = {callLineTs}",

      // If the http function should return a result, grab it from the response
      retType == "void" ? null : "return res.data;",

    }.Where(l => l != null)));

    if (queryArgs.Length != 0)
    {
      // Build up some documentation for the query params, to indicate what the
      // defaults are that are applied on the server

      var queryParamsHavingDefaultValues = queryParams.Where(p => p.HasDefaultValue).ToArray();
      result.Documentation = new RtJsdocNode() { };
      result.Documentation.TagToDescription.Add(new Tuple<DocTag, string>(DocTag.Param, "{Object} queries - Query params passed to the controller"));
      foreach (var arg in queryArgs)
      {
        var queryParamHavingDefault = Array.Find(queryParamsHavingDefaultValues, p => p.Name == arg.Identifier.IdentifierName);
        result.Documentation.TagToDescription.Add(
          new Tuple<DocTag, string>(
            DocTag.Param,
            $"{{{arg.Type}}} queries.{arg.Identifier.IdentifierName}{(queryParamHavingDefault != null ? $" - Defaults to {(queryParamHavingDefault.DefaultValue ?? "null")} in the controller" : "")}"
        )
    );
      }
    }

    return result;
  }

  [GeneratedRegex("{([^}]+)}")]
  private static partial Regex RouteParamMatcher();

  private static string GetFullRoute(MethodInfo method, string? methodTemplate)
  {
    // Extract the controller route from the declaring type's RouteAttribute
    var controllerType = method.DeclaringType;
    var routeAttribute = controllerType?.GetCustomAttribute<RouteAttribute>();
    var controllerRoute = routeAttribute?.Template ?? "";

    var methodRoute = (methodTemplate ?? "").TrimStart('/');

    // Combine controller and method routes
    var fullRoute = string.IsNullOrEmpty(controllerRoute)
      ? methodRoute
      : string.IsNullOrEmpty(methodRoute)
        ? controllerRoute
        : $"{controllerRoute}/{methodRoute}";

    // Ensure the route starts with / and replace {param} with ${param} for JavaScript template literals
    return "/" + fullRoute.TrimStart('/').Replace("{", "${");
  }
}
