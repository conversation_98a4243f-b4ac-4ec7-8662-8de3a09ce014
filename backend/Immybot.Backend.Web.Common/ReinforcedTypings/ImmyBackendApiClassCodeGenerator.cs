using Reinforced.Typings;
using Reinforced.Typings.Ast;
using Reinforced.Typings.Ast.TypeNames;
using Reinforced.Typings.Generators;

namespace Immybot.Backend.Web.Common.ReinforcedTypings;

public class RequestClassCodeGenerator : InterfaceCodeGenerator
{
  public override RtInterface? GenerateNode(Type element, RtInterface result, TypeResolver resolver)
  {
    result = base.GenerateNode(element, result, resolver);
    if (result == null) return null;
    result.Members.Sort((n1, n2) =>
    {
      return (n1, n2) switch
      {
        (RtField f1, RtField f2) when f1.Identifier.IsNullable != f2.Identifier.IsNullable => f1.Identifier.IsNullable ? 1 : -1,
        (RtField f1, RtField f2) => f1.Identifier.IdentifierName.CompareTo(f2.Identifier.IdentifierName),
        (RtField, _) => 1,
        (_, RtField) => -1,
        _ => 0,
      };
    });

    // Build implementation classes that the frontend can use to construct request bodies
    var @class = new RtClass()
    {
      Name = new RtSimpleTypeName(element.Name),
      Export = true,
    };
    var constructor = new RtConstructor();
    var fields = result.Members.OfType<RtField>().ToArray();
    var areAllFieldOptional = fields.All(f => f.Identifier.IsNullable);
    constructor.Arguments.Add(new RtArgument()
    {
      Identifier = new RtIdentifier("obj"),
      Type = result.Name,
      DefaultValue = areAllFieldOptional ? "{}" : ""
    });
    constructor.Body = new RtRaw($"{string.Join("\n", fields.Select(a => $"this.{a.Identifier.IdentifierName} = obj.{a.Identifier.IdentifierName};"))}");

    @class.Implementees.Add(result.Name);
    @class.Members.AddRange(fields);
    @class.Members.Add(constructor);

    Context.Location.CurrentNamespace.CompilationUnits.Add(@class);
    return result;
  }
}

public class ImmyBackendApiClassCodeGenerator : ClassCodeGenerator
{
  public override RtClass? GenerateNode(Type element, RtClass result, TypeResolver resolver)
  {
    result = base.GenerateNode(element, result, resolver);
    if (result == null) return null;

    // Add doc line to indicate where this class was generated from
    result.Documentation = new RtJsdocNode()
    {
      Description = $"Generated from: {element.FullName}",
    };

    return result;
  }
}
