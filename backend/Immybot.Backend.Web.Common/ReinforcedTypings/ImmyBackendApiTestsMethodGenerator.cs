using System.Reflection;
using Reinforced.Typings;
using Reinforced.Typings.Ast;
using Reinforced.Typings.Generators;

namespace Immybot.Backend.Web.Common.ReinforcedTypings;

public class ImmyBackendApiTestsMethodGenerator : MethodCodeGenerator
{
  public override RtFunction? GenerateNode(MethodInfo element, RtFunction result, TypeResolver resolver)
  {
    result = base.GenerateNode(element, result, resolver);

    return result;
  }
}
