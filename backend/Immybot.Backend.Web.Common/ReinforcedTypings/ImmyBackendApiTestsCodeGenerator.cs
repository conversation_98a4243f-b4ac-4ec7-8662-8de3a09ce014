using Microsoft.AspNetCore.Mvc;
using Reinforced.Typings;
using Reinforced.Typings.Ast;
using Reinforced.Typings.Ast.TypeNames;
using Reinforced.Typings.Generators;

namespace Immybot.Backend.Web.Common.ReinforcedTypings;

public class ImmyBackendApiTestsCodeGenerator : ClassCodeGenerator
{
  public override RtClass? GenerateNode(Type element, RtClass result, TypeResolver resolver)
  {
    var name = Configuration.ControllerNameReplacement().Replace(element.Name, "Api");
    result.Name = new RtSimpleTypeName($"{name}Tests");

    result.Extendee = new RtSimpleTypeName($"BaseApiTestSuite<{name}>");
    result.Implementees.Add(
      new RtSimpleTypeName(
        $"RequireTestCoverage<Pick<{name}, ApiMethods<{name}>>>"));

    // Add doc line to indicate where this class was generated from
    result.Documentation = new RtJsdocNode() { Description = $"Generated from: {element.FullName}", };

    var methods = element.GetMethods()
      .Where(m => ImmyBackendApiMethodGenerator.NonGenericActionResultTypes.Contains(m.ReturnType) ||
                  (m.ReturnType.IsGenericType && m.ReturnType.GetGenericTypeDefinition() == typeof(ActionResult<>)) ||
                  (m.ReturnType.IsGenericType
                   && m.ReturnType.GetGenericTypeDefinition() == typeof(Task<>)
                   && m.ReturnType.GenericTypeArguments[0] is { IsGenericType: true } a
                   && a.GetGenericTypeDefinition() == typeof(ActionResult<>)));

    foreach (var method in methods)
    {
      if (method.Name is "ValidationProblem") continue;
      var methodNameLower = $"{char.ToLower(method.Name[0])}{method.Name[1..]}";
      result.Members.Add(new RtRaw($"{methodNameLower} = [];"));
    }

    return result;
  }
}
