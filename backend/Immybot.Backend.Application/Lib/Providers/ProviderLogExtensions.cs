using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Management.Automation;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;
using Castle.DynamicProxy;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Shared.PowerShell.Attributes;
using Immybot.Shared.Primitives.Attributes;
using Immybot.Shared.Telemetry;
using JetBrains.Annotations;

namespace Immybot.Backend.Application.Lib.Providers;

public static class ProviderLogExtensions
{
  private static void IgnoreMaskedValues(JsonTypeInfo typeInfo)
  {
    if (typeInfo.Kind == JsonTypeInfoKind.Object)
      foreach (JsonPropertyInfo propertyInfo in typeInfo.Properties)
        if (propertyInfo.AttributeProvider is { } provider && provider.GetCustomAttributes(inherit: true)
              .Any(a => a is MaskAuditLogAttribute or PasswordAttribute))
          propertyInfo.Get = _ => "[REDACTED]";
  }

  private static readonly JsonSerializerOptions _jsonSerializerOptions = new()
  {
    ReferenceHandler = ReferenceHandler.IgnoreCycles,
    TypeInfoResolver = new DefaultJsonTypeInfoResolver { Modifiers = { IgnoreMaskedValues } }
  };

  private static Dictionary<string, object?> GetRedactedParameters(Dictionary<string, object?> parameters)
  {
    var redactedParameters = new Dictionary<string, object?>();
    foreach (var parameter in parameters)
    {
      try
      {
        redactedParameters[parameter.Key] = JsonSerializer.SerializeToElement(parameter.Value, _jsonSerializerOptions);
      }
      catch (Exception ex)
      {
        Activity.Current?.AddException(ex, [new("integration.method.parameter", parameter.Key)]);
        redactedParameters[parameter.Key] = "<Error serializing parameter>";
      }
    }

    return redactedParameters;
  }

  [MustDisposeResource]
  [SuppressMessage("ReSharper", "ExplicitCallerInfoArgument")]
  public static ProviderInvocationLogContext StartProviderInvocation(
    IDomainEventEmitter domainEventEmitter,
    int providerLinkId,
    string providerLinkName,
    IInvocation invocation,
    Dictionary<string, object?> parameters,
    ApplicationPreferences appPrefs,
    [CallerFilePath] string filePath = "",
    [CallerLineNumber] int lineNumber = 0)
  {
    var activity = Telemetry.StartActivity(ActivityType.Integration,
      $"{invocation.TargetType?.Name ?? providerLinkName}.{invocation.Method.Name}",
      new()
      {
        { "integration.type", invocation.TargetType?.Name },
        { "integration.link.id", providerLinkId },
        { "integration.link.name", providerLinkName },
        { "integration.method.name", invocation.Method.Name },
      },
      filePath: filePath,
      lineNumber: lineNumber);

    var redactedParameters = GetRedactedParameters(parameters);
    foreach (var parameter in redactedParameters)
      activity?.AddTag("integration.method.arg." + parameter.Key, parameter.Value);

    var shouldEmitToProviderAuditLog =
      appPrefs.EnableProviderAuditLogging
      && invocation.Method.Name != nameof(ISupportsHttpRequest.HandleHttpRequest);

    Guid? correlationId = null;
    if (shouldEmitToProviderAuditLog)
    {
      var log = new ProviderAuditLog(
        providerLinkId,
        providerLinkName,
        invocation.Method.Name,
        null,
        redactedParameters);
      domainEventEmitter.EmitEvent(new ProviderAuditLogAddedEvent(log));
      correlationId = log.Id;
    }

    return new ProviderInvocationLogContext(
      domainEventEmitter,
      providerLinkId,
      providerLinkName,
      invocation,
      redactedParameters,
      shouldEmitToProviderAuditLog,
      correlationId,
      activity
    );
  }

  public static void EndProviderInvocation(
    ProviderInvocationLogContext context,
    string? errorMessage = null,
    object? result = null)
  {
    var output = result is ScriptBlock scriptBlock
      ? scriptBlock.ToString()
      : result;

    if (errorMessage != null)
    {
      context.Activity?.SetStatus(ActivityStatusCode.Error);
      context.Activity?.AddEvent(new ActivityEvent("exception",
        tags: [new("exception.message", errorMessage)]));
    }
    else
    {
      context.Activity?.SetStatus(ActivityStatusCode.Ok);
      context.Activity?.AddTag("integration.method.output", output);
    }

    if (context.ShouldEmitToProviderAuditLog)
    {
      var redactedParameters = GetRedactedParameters(context.Parameters);
      var log = new ProviderAuditLog(
        context.ProviderLinkId,
        context.ProviderLinkName,
        context.Invocation.Method.Name,
        errorMessage,
        redactedParameters,
        output,
        context.CorrelationId);

      context.DomainEventEmitter.EmitEvent(new ProviderAuditLogAddedEvent(log));
    }
  }
}

public readonly record struct ProviderInvocationLogContext(
  IDomainEventEmitter DomainEventEmitter,
  int ProviderLinkId,
  string ProviderLinkName,
  IInvocation Invocation,
  Dictionary<string, object?> Parameters,
  bool ShouldEmitToProviderAuditLog,
  Guid? CorrelationId,
  Activity? Activity
) : IDisposable
{
  public void Dispose() =>
    Activity?.Dispose();
}
