using System;
using System.Collections.Generic;
using System.Linq;
using System.Management.Automation;
using System.Management.Automation.Runspaces;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.Helpers.Extensions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nito.AsyncEx;
using Nito.Disposables.Internals;

namespace Immybot.Backend.Application.Lib.MetaScripts;

public interface IFunctionScriptManager
{
  void InvalidateCache();
  Task<long> WaitForCacheChangeAsync(long? cacheVersion, CancellationToken cancellationToken);

  Task<SessionStateFunctionEntry[]> GetAddableScriptsAsync(IEnumerable<SessionStateCommandEntry> existingCommandEntries,
    bool includeGlobalScripts,
    bool includeTenantlessLocalScript,
    int? tenantIdForIncludingTenentedLocalScripts,
    CancellationToken cancellationToken);
}

internal class FunctionScriptManager(
  ILogger<FunctionScriptManager> logger,
  IOptionsMonitor<AppSettingsOptions> appSettings,
  Func<ImmybotDbContext> immybotDbContextFactory,
  Func<SoftwareDbContext> softwareDbContextFactory) : IFunctionScriptManager
{
  readonly VersionedCache _versionedCache = new();

  public void InvalidateCache() => _versionedCache.Invalidate();

  public Task<long> WaitForCacheChangeAsync(long? cacheVersion, CancellationToken cancellationToken) =>
    _versionedCache.WaitForChangeAsync(cacheVersion, cancellationToken);

  class VersionedCache
  {
    readonly AsyncMonitor _asyncMonitor = new();
    readonly Dictionary<string, object?> _map = new();
    long _cacheVersion;

    public async Task<T> GetAsync<T>(string key,
      Func<CancellationToken, Task<T>> getter,
      CancellationToken cancellationToken)
    {
      using (await _asyncMonitor.EnterAsync(cancellationToken))
      {
        if (_map.TryGetValue(key, out var value) && value is T typedValue)
          return typedValue;

        var result = await getter(cancellationToken);
        _map[key] = result;
        return result;
      }
    }

    public void Invalidate()
    {
      using (_asyncMonitor.Enter())
      {
        _cacheVersion++;
        _asyncMonitor.PulseAll();
        _map.Clear();
      }
    }

    public async Task<long> WaitForChangeAsync(long? cacheVersion, CancellationToken cancellationToken)
    {
      using (await _asyncMonitor.EnterAsync())
      {
        while (true)
        {
          if (_cacheVersion != cacheVersion)
            return _cacheVersion;

          await _asyncMonitor.WaitAsync(cancellationToken);
        }
      }
    }
  }

  public async Task<SessionStateFunctionEntry[]> GetAddableScriptsAsync(
    IEnumerable<SessionStateCommandEntry> existingCommandEntries,
    bool includeGlobalScripts,
    bool includeTenantlessLocalScript,
    int? tenantIdForIncludingTenentedLocalScripts,
    CancellationToken cancellationToken)
  {
    var (localData, globalData) = await _versionedCache.GetAsync(string.Empty,
      async token =>
      {
        await using var localCtx = immybotDbContextFactory();
        await using var globalCtx = softwareDbContextFactory();

        token.ThrowIfCancellationRequested();

        var localData = localCtx.Scripts
          .AsNoTracking()
          .Where(a => a.ScriptCategory == ScriptCategory.Function)
          .Select(a => new { a.Name, a.Action, Tenants = a.TenantRelationships })
          .AsEnumerable()
          .Select(a =>
          {
            try
            {
              return (
                TenantIds: a.Tenants.Select(b => b.TenantId).ToArray(),
                FunctionEntry: new SessionStateFunctionEntry(a.Name, a.Action)
                {
                  ScriptBlock =
                  {
                    LanguageMode = appSettings.CurrentValue.UseFullLanguagePowerShellModeForLocalScripts
                      ? PSLanguageMode.FullLanguage
                      : PSLanguageMode.ConstrainedLanguage,
                  },
                }
              );
            }
            catch (Exception ex)
            {
              logger.LogError(ex,
                "Error while generating SessionStateFunctionEntry for local function script {name}.",
                a.Name);
              return default;
            }
          })
          .Where(x => x != default)
          .ToArray();

        var globalData = globalCtx.Scripts
          .AsNoTracking()
          .Where(a => a.ScriptCategory == ScriptCategory.Function)
          .Select(a => new { a.Name, a.Action })
          .AsEnumerable()
          .Select(a =>
          {
            try
            {
              return new SessionStateFunctionEntry(a.Name, a.Action)
              {
                ScriptBlock = { LanguageMode = PSLanguageMode.FullLanguage },
              };
            }
            catch (Exception ex)
            {
              logger.LogError(ex,
                "Error while generating SessionStateFunctionEntry for global function script {name}.",
                a.Name);
              return null;
            }
          })
          .WhereNotNull()
          .ToArray();

        return (localData, globalData);
      },
      cancellationToken);

    var existingFuncDictionary = existingCommandEntries
      .GroupBy(a => a.Name, StringComparer.OrdinalIgnoreCase)
      .ToDictionary(a => a.Key, a => a.First(), StringComparer.OrdinalIgnoreCase);

    var functionScriptDictionary = new Dictionary<string, SessionStateFunctionEntry>(StringComparer.OrdinalIgnoreCase);

    if (includeGlobalScripts)
    {
      foreach (var fn in globalData)
      {
        if (existingFuncDictionary.TryGetValue(fn.Name, out var existingEntry) &&
            existingEntry is SessionStateCmdletEntry cmdletEntry && cmdletEntry.ProhibitsGlobalFunctionOverrides())
        {
          logger.LogTrace(
            "Not adding global function script {FunctionName} because it would override a built-in function which explicitly prohibits global function overrides.",
            fn.Name);
          continue;
        }

        functionScriptDictionary[fn.Name] = fn;
      }
    }

    var localScripts = localData
      .Where(script =>
        (includeTenantlessLocalScript && !script.TenantIds.Any()) ||
        (tenantIdForIncludingTenentedLocalScripts.HasValue &&
         script.TenantIds.Contains(tenantIdForIncludingTenentedLocalScripts.Value)))
      .Select(fn => fn.FunctionEntry);

    foreach (var fn in localScripts)
    {
      if (existingFuncDictionary.TryGetValue(fn.Name, out var existingEntry) &&
          existingEntry is SessionStateCmdletEntry cmdletEntry && cmdletEntry.ProhibitsLocalFunctionOverrides())
      {
        logger.LogWarning(
          "Not adding local function script {FunctionName} because it would override a built-in function which explicitly prohibits local function overrides.",
          fn.Name);
        continue;
      }

      functionScriptDictionary[fn.Name] = fn;
    }

    return functionScriptDictionary.Values.ToArray();
  }
}
