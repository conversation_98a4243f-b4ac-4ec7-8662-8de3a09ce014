using System;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Jobs;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Jobs;

/// <summary>
/// Background job that creates provider audit log partitions in advance to ensure 
/// seamless data insertion across month boundaries
/// </summary>
internal class ProviderAuditLogPartitionCreatorJob : IImmyJob
{
  private readonly Func<ImmybotDbContext> _dbContextFactory;
  private readonly ILogger<ProviderAuditLogPartitionCreatorJob> _logger;

  public ProviderAuditLogPartitionCreatorJob(
    Func<ImmybotDbContext> dbContextFactory,
    ILogger<ProviderAuditLogPartitionCreatorJob> logger)
  {
    _dbContextFactory = dbContextFactory;
    _logger = logger;
  }

  public string JobQueue => JobQueues.Job;
  public string JobName => "Provider Audit Log Partition Creator";
  public static string JobId => "provider-audit-log-partition-creator";

  public async Task Run(CancellationToken token)
  {
    _logger.LogInformation("Starting provider audit log partition creator job");

    try
    {
      await using var ctx = _dbContextFactory();
      
      // Create partitions for the next 2 months to ensure we're always ahead
      var currentDate = DateTime.UtcNow;
      
      for (int monthOffset = 1; monthOffset <= 2; monthOffset++)
      {
        var targetDate = currentDate.AddMonths(monthOffset);
        
        _logger.LogDebug("Creating partition for {Year}-{Month:D2}", targetDate.Year, targetDate.Month);
        
        await ctx.Database.ExecuteSqlRawAsync(
          "SELECT create_provider_audit_log_partition({0})",
          new object[] { new DateTime(targetDate.Year, targetDate.Month, 1, 0, 0, 0, DateTimeKind.Utc) });
          
        token.ThrowIfCancellationRequested();
      }

      _logger.LogInformation("Provider audit log partition creator job completed successfully");
    }
    catch (OperationCanceledException)
    {
      _logger.LogInformation("Provider audit log partition creator job was cancelled");
      throw;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error occurred while creating provider audit log partitions");
      throw;
    }
  }
}