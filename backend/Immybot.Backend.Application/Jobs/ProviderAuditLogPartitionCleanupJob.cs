using Immybot.Backend.Application.Interface.Jobs;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Jobs;

/// <summary>
/// Background job that cleans up old provider audit log partitions based on
/// the configured retention period in ApplicationPreferences
/// </summary>
internal class ProviderAuditLogPartitionCleanupJob : IImmyJob
{
  private readonly Func<ImmybotDbContext> _dbContextFactory;
  private readonly ICachedSingleton<ApplicationPreferences> _appPreferences;
  private readonly ILogger<ProviderAuditLogPartitionCleanupJob> _logger;

  public ProviderAuditLogPartitionCleanupJob(
    Func<ImmybotDbContext> dbContextFactory,
    ICachedSingleton<ApplicationPreferences> appPreferences,
    ILogger<ProviderAuditLogPartitionCleanupJob> logger)
  {
    _dbContextFactory = dbContextFactory;
    _appPreferences = appPreferences;
    _logger = logger;
  }

  public string JobQueue => JobQueues.Job;
  public string JobName => "Provider Audit Log Partition Cleanup";
  public static string JobId => "provider-audit-log-partition-cleanup";

  public async Task Run(CancellationToken token)
  {
    var retentionMonths = _appPreferences.Value.ProviderAuditLogRetentionMonths;

    _logger.LogInformation(
      "Starting provider audit log partition cleanup job with retention period of {RetentionMonths} months",
      retentionMonths);

    // Skip cleanup if retention is set to 0 or negative (indicating unlimited retention)
    if (retentionMonths <= 0)
    {
      _logger.LogInformation(
        "Provider audit log retention is disabled (retention months: {RetentionMonths}), skipping cleanup",
        retentionMonths);
      return;
    }

    try
    {
      await using var ctx = _dbContextFactory();

      _logger.LogDebug("Executing partition cleanup for retention period of {RetentionMonths} months", retentionMonths);

      // Convert months to cutoff date for partition cleanup
      var cutoffDate = DateTime.UtcNow.AddMonths(-retentionMonths);

      await ctx.Database.ExecuteSqlRawAsync(
        "SELECT cleanup_provider_audit_log_partitions({0})",
        cutoffDate);

      _logger.LogInformation("Provider audit log partition cleanup job completed successfully");
    }
    catch (OperationCanceledException)
    {
      _logger.LogInformation("Provider audit log partition cleanup job was cancelled");
      throw;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error occurred while cleaning up provider audit log partitions");
      throw;
    }
  }
}
