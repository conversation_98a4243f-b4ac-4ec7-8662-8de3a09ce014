using System.Diagnostics;
using System.IO.Abstractions;
using System.Runtime.CompilerServices;
using Azure.Security.KeyVault.Secrets;
using Hangfire;
using Hangfire.MemoryStorage;
using Hangfire.Pro.Redis;
using Hangfire.Throttling;
using Immense.RemoteControl.Server.Extensions;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.DynamicProviders;
using Immybot.Backend.Application.HangfireJobChecker.Extensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.Interface.Jobs;
using Immybot.Backend.Application.Interface.Language;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Jobs;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.AgentIdentification;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Application.Lib.ChocolateyApi;
using Immybot.Backend.Application.Lib.Cryptography;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Lib.InventorySinks;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Application.Lib.Ninite;
using Immybot.Backend.Application.Lib.Policies;
using Immybot.Backend.Application.Lib.Providers;
using Immybot.Backend.Application.Lib.RemoteControl;
using Immybot.Backend.Application.Lib.Scripts;
using Immybot.Backend.Application.Lib.Scripts.EphemeralAgent;
using Immybot.Backend.Application.Lib.SessionLogs;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Application.Maintenance.AssignmentResolution;
using Immybot.Backend.Application.Notifications;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.Services.TenantDeletionService.Infratructure;
using Immybot.Backend.Application.SoftwareManagement.CustomAudits.Implementations;
using Immybot.Backend.Application.SoftwareManagement.CustomAudits.Interfaces;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Azure.Domain.Credentials.Interfaces;
using Immybot.Backend.Azure.Infrastructure.Extensions;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Manager.ManagerProvider.Extensions;
using Immybot.Backend.Notifications.Mailer.Infrastructure;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.ImmyAgentProvider.Infrastructure;
using Immybot.Backend.Providers.ImmyAgentProvider.Metrics;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Backend.Providers.Shared;
using Immybot.Shared.Abstractions.Device;
using Immybot.Shared.Abstractions.Device.Timers;
using Immybot.Shared.JsonConverters;
using Immybot.Shared.Primitives.Buffers;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Constants = Immybot.Backend.Providers.Shared.Constants;

[assembly: InternalsVisibleTo("Immybot.Backend.UnitTests")]
[assembly: InternalsVisibleTo("Immybot.Backend.UnitTests.Shared.Sqlite")]
[assembly: InternalsVisibleTo("Immybot.Backend.UnitTests.Shared")]
[assembly: InternalsVisibleTo("Immybot.Backend.Web.Common.UnitTests")]
[assembly: InternalsVisibleTo("DynamicProxyGenAssembly2")]
[assembly: InternalsVisibleTo("Immybot.Backend.MetascriptInvoker.Benchmark")]
namespace Immybot.Backend.Application.Infrastructure;

public static class ServicesConfiguration
{
  public static void AddEditorServicesRequiredServices(this IServiceCollection services)
  {
    services.AddSingleton<MetascriptInvokerDefaults>();
    services.AddSingleton<LanguageServiceConnectionHandler>();

    services.AddSingleton<IFunctionScriptManager, FunctionScriptManager>();
    services.AddSingleton<IImmyCancellationManager, ImmyCancellationManager>();
    services.AddSingleton<IInitialSessionStateFactory, EditorServiceInitialSessionStateFactory>();
    services.AddSingleton<ILanguageService, LanguageService>();

    if (OperatingSystem.IsLinux())
    {
      // This makes it such that Get-Module -ListAvailable shows the modules included in PowerShell/Modules
      Environment.SetEnvironmentVariable("XDG_DATA_HOME", MetascriptInvokerDefaults.AssemblyFolder);
    }
  }

  public static void AddApplicationServices(
    this IHostApplicationBuilder builder)
  {
    var services = builder.Services;
    var configuration = builder.Configuration;

    services.AddSingleton(TimeProvider.System);
    // todo: move this into the azure projects configuration
    services.AddAzureServices();
    AzureServiceConfiguration.AddAzureServices(builder);

    // allows for easy encryption / decryption
    services.AddTransient<IEncryptionService, EncryptionService>();

    services.AddSingleton<IInitializationStatus, InitializationStatus>();

    services.AddSingleton<INotificationsCache, NotificationsCache>();

    services.AddHangfireJobChecker();
    services.AddTransient<IDynamicIntegrationsGlobalStore, DynamicIntegrationsGlobalStore>();
    services.AddTransient<IDynamicIntegrationsLocalStore, DynamicIntegrationsLocalStore>();
    services.AddTransient<IProviderStore, ProviderStore>();
    services.AddTransient<IInventoryStore, InventoryStore>();
    services.AddTransient<IAuditStore, AuditStore>();
    services.AddTransient<IAzureErrorStore, AzureErrorStore>();
    services.AddSingleton<IComputerNameRetrievalStore, ComputerNameRetrievalStore>();
    services.AddTransient<INotificationsStore, NotificationsStore>();
    services.AddTransient<IMaintenanceSessionsStore, MaintenanceSessionsStore>();
    services.AddTransient<IChangeRequestStore, ChangeRequestStore>();
    services.AddTransient<ITargetAssignmentStore, TargetAssignmentStore>();
    services.AddTransient<ILocalDbRepository, LocalDbRepository>();
    services.AddTransient<IGlobalDbRepository, GlobalDbRepository>();
    services.AddTransient<IComputerRegistryStore, ComputerRegistryStore>();
    services.AddSingleton<IUserImpersonationStore, UserImpersonationStore>();
    services.AddSingleton<ITenantStore, TenantStore>();
    services.AddTransient<IInventoryIdentificationCmd, InventoryIdentificationCmd>();
    services.AddSingleton<IPowershellLoader, PowerShellLoader>();
    services.AddManagerProviderServices();
    services.AddSingleton<ApplicationSieveProcessor>();
    services.AddSingleton<ICachedSingleton<ApplicationPreferences>>(new CachedSingleton<ApplicationPreferences>());
    services.AddSingleton<ICachedCollection<TenantPreferences>>(new CachedCollection<TenantPreferences>());
    services.AddSingleton<ICachedSingleton<ProviderLinkNames>>(
      new CachedSingleton<ProviderLinkNames> { Value = new ProviderLinkNames() });
    services.AddSingleton<IComputerInventoryUpdateHandler, ComputerInventoryUpdateHandler>();
    services.AddSingleton<IInventorySinkRouter, InventorySinkRouter>();
    services.AddSingleton<ISessionObjectsUpdateHandler, SessionObjectsUpdateHandler>();
    services.AddSingleton<ISessionLogUpdateQueue, SessionLogUpdateQueue>();
    services.AddSingleton<IAgentIdentificationLogHandler, AgentIdentificationLogHandler>();
    services.AddSingleton<IPublicScriptsContainerService, PublicScriptsBlobStorageContainerService>();
    services.AddSingleton<IScriptSerializer, ScriptSerializer>();
    services.AddTransient<IEphemeralAgentAcquisition, EphemeralAgentAcquisition>();
    services.AddSingleton<IEphemeralSessionCreator, EphemeralSessionCreator>();
    services.AddTransient<IProviderScriptInvoker, ProviderScriptInvoker>();
    services.AddTransient<IScriptInvoker, ScriptInvoker>();
    services.AddTransient<IPreflightScriptInvoker, PreflightScriptInvoker>();
    services.AddSingleton<IScriptSigner, ScriptSigner>();
    services.AddSingleton<IImmyProviderLinkSchedulerFactory, ImmyProviderLinkSchedulerFactory>();
    services.AddSingleton<ImmyScheduledJobRunner>();
    services.AddSingleton<IImmyScheduledJobRunner>(serviceProvider
      => serviceProvider.GetRequiredService<ImmyScheduledJobRunner>());
    services.AddSingleton<ImmySchedulerRepository>();
    services.AddSingleton(MemoryProvider.Default);


    // Inventory results sinks.
    services.AddSingleton<IInventorySink>(s =>
    {
      var dbFactory = s.GetRequiredService<Func<ImmybotDbContext>>();
      var logger = s.GetRequiredService<ILogger<SoftwareInventorySink>>();
      var systemTime = s.GetRequiredService<ISystemTime>();
      return new SoftwareInventorySink(dbFactory, systemTime, logger);
    });

    services.AddHttpClient();
    services.AddHostedService<IProviderRegistrationService, ProviderRegistrationService>();
    services.AddHostedService<IMaintenanceActionDiagnosticEventHubService, MaintenanceActionDiagnosticEventHubService>();
    services.AddHostedService<IPowerShellErrorEventHubService, PowerShellErrorEventHubService>();

    // providers
    services.AddImmyAgentProviderServices();
    services.AddRemoteControlServer(config =>
    {
      config.AddRemoteControlEventHandler<RemoteControlEventHandler>();
      config.AddViewerAuthorizer<ViewerAuthorizer>();
      config.AddViewerPageDataProvider<ViewerPageDataProvider>();
      config.AddViewerOptionsProvider<ViewerOptionsProvider>();
      config.AddSessionRecordingSink<RemoteControlRecordingSink>();
    });
    services.AddSingleton<IDynamicProviderStore, DynamicProviderStore>();
    services.AddSingleton<IRemoteControlRecordingsAccessor, RemoteControlRecordingsAccessor>();
    services.AddSingleton<IProviderLinkDataConsistencyLocker, ProviderLinkDataConsistencyLocker>();
    services.AddSingleton<IProviderAgentEventHandler, ProviderEventHandler>();
    services.AddSingleton<ProviderQueryHandlerFactory>();
    services.AddSingleton<IProviderQueryHandlerFactory<IProviderQueryHandler>>(s => s.GetRequiredService<ProviderQueryHandlerFactory>());
    services.AddSingleton<IProviderFactory, ProviderFactory>();
    services.AddSingleton<IProviderMetrics, ProviderMetrics>();
    services
      .AddProvider<ImmyAgentProvider.ImmyAgentProvider>(Guid.Parse(Constants.ImmyProviderTypeId))
      .AddProvider<CwAutomateProvider.CwAutomateProvider>(Guid.Parse(Constants.CwAutomateProviderId))
      .AddProvider<CwControlProvider.CwControlProvider>(Guid.Parse(Constants.CwControlProviderTypeId))
      .AddProvider<CwManageProvider.CwManageProvider>(Guid.Parse(Constants.CwManageProviderTypeId))
      .AddProvider<NCentralRmmProvider.NCentralProvider>(Guid.Parse(Constants.NCentralProviderTypeId))
      .AddProvider<HaloPsaProvider.HaloPsaProvider>(Guid.Parse(Constants.HaloPsaProviderTypeId));

    // Local system.
    services.AddSingleton<ISystemTime, SystemTime>();
    services.AddSingleton<IFileSystem, FileSystem>();
    services.AddSingleton<ISystemEnvironment, SystemEnvironment>();
    services.AddSingleton<ITimerFactory, TimerFactory>();
    services.AddSingleton<IDelayer, Delayer>();

    // the construction of the azure secret client is crazy expensive, and these are thread safe
    var azKvOpts = configuration.GetSection(AzureKeyVaultOptions.SectionKey).Get<AzureKeyVaultOptions>();
    if (azKvOpts?.KeyVaultUri is { } keyVaultUri)
      services.AddSingleton<SecretClient>(sp => new SecretClient(keyVaultUri, sp.GetRequiredService<IBackendIdentityAzureTokenCredential>().Credential));
    else
      services.AddSingleton<SecretClient, InMemorySecretClient>();

    services.AddAzureServices();
    services.AddOauthServices();
    services.AddSingleton<IEphemeralAgentSessionStore, EphemeralAgentSessionStore>();
    services.AddSingleton<EphemeralAgentSessionFactory>();
    services.AddMessagePipe();

    // service used to update system metrics pertaining to ephemeral agents
    services.AddHostedService<EphemeralAgentMetricsService>();

    var azFnOpts = configuration.GetSection(AzureFunctionOptions.SectionKey).Get<AzureFunctionOptions>();
    if (azFnOpts is { UseMockPackageAnalyzer: true })
    {
      services.AddTransient<IPackageAnalyzer, PackageAnalyzer.DevPackageAnalyzer>();
    }
    else
    {
      services.AddTransient<IPackageAnalyzer, PackageAnalyzer.PackageAnalyzer>();
    }
    services.AddSingleton<DomainEventBroker>();
    services.AddSingleton<IDomainEventBroker>(s => s.GetRequiredService<DomainEventBroker>());
    services.AddSingleton<IDomainEventEmitter>(s => s.GetRequiredService<DomainEventBroker>());
    services.AddSingleton<IDomainEventReceiver>(s => s.GetRequiredService<DomainEventBroker>());
    services.AddSingleton<IFeatureManager, FeatureManager>();
    services.AddSingleton<IFeatureTracker>(s => s.GetRequiredService<IFeatureManager>());
    services.AddSingleton<IAzureBlobStorageUploadService, AzureBlobStorageUploadService>();
    services.AddTransient<IAzureBlobStorageSasService, AzureBlobStorageSasService>();
    services.AddSingleton<IImmyCacheRepository, ImmyCacheRepository>();
    services.AddSingleton<KeyedLocker>();
    services.AddTransient<ILocalToGlobalMigratorActions, LocalToGlobalMigratorActions>();
    services.AddTransient<ILocalToGlobalMigrator, LocalToGlobalMigrator>();
    var provider = new FileExtensionContentTypeProvider();
    services.AddSingleton<IMimeMapper>(new MimeMapper(provider));
    services.AddTransient<IDynamicFormService, DynamicFormService>();
    services.AddTransient<IDynamicProviderInterceptorFactory, DynamicProviderInterceptorFactory>();
    services.AddTransient<IProviderAuditLogInterceptorFactory, ProviderAuditLogInterceptorFactory>();
    services.AddSingleton<IProviderMetadataFactory, ProviderMetadataFactory>();
    services.AddTransient<IDynamicProviderRegistrationFactory, DynamicProviderRegistrationFactory>();
    services.AddSingleton<MetascriptInvokerDefaults>();
    services.AddHostedService<PowerShellHookService>();
    services.AddSingleton<IImmyAgentMetadataProvider, ImmyAgentMetadataProvider>();
    services.AddHostedService<GarbageCollectionSignalHandlerService>();

    services.AddTransient<LanguageServiceConnectionHandler>();
    services.AddSingleton<InteractiveScriptExecutionService>();
    var psesOptions = configuration.GetSection(PowershellEditorServicesOptions.SectionKey).Get<PowershellEditorServicesOptions>();

    if (psesOptions is { } opt && opt.RunEditorServicesOutOfProcess)
    {
      services.AddSingleton<ILanguageService, OutOfProcessLanguageService>();
      services.AddHostedService((s) => (OutOfProcessLanguageService)s.GetRequiredService<ILanguageService>());
    }
    else
    {
      services.AddSingleton<ILanguageService, LanguageService>();
    }

    services.AddHostedService<IMetascriptRunspaceServer, MetascriptRunspaceServer>();
    services.AddTransient<IMetascriptInvoker, MetascriptInvoker>();
    services.AddTransient<IApplicationMetricsCollector, ApplicationMetricsCollector>();
    services.AddTransient<ICommandInfoConverter, CommandInfoConverter>();
    services.AddSingleton<IWebHookService, WebHookService>();
    services.AddSingleton<IFunctionScriptManager, FunctionScriptManager>();
    services.AddSingleton<IInitialSessionStateFactory, InitialSessionStateFactory>();

    // Actions
    services.AddTransient<IScriptActions, ScriptActions>();
    services.AddTransient<ISmtpConfigActions, SmtpConfigActions>();
    services.AddTransient<IProviderActions, ProviderActions>();
    services.AddTransient<IScheduleJobActions, ScheduleJobActions>();
    services.AddTransient<IComputerActions, ComputerActions>();
    services.AddTransient<IRunContextActions, RunContextActions>();
    services.AddTransient<ILocalSoftwareActions, LocalSoftwareActions>();
    services.AddTransient<IGlobalSoftwareActions, GlobalSoftwareActions>();
    services.AddTransient<ISoftwarePrerequisiteCustomAudit, SoftwarePrerequisiteCustomAudit>();
    services.AddTransient<ChocolateySoftwareActions>();
    services.AddSingleton<NiniteSoftwareCache>();
    services.AddTransient<IScheduledEmailActions, ScheduledEmailActions>();
    services.AddTransient<IMaintenanceSessionActions, MaintenanceSessionActions>();
    services.AddTransient<IMaintenanceSessionInitializationActions, MaintenanceSessionInitializationActions>();
    services.AddTransient<IDashboardActions, DashboardActions>();
    services.AddTransient<IRunContextActionsList, RunContextActionsList>();
    services.AddTransient<IChocolateyApi, ChocolateyApi>();
    services.AddTransient<PackageHealthActions>();
    services.AddTransient<IUserAffinityActions, UserAffinityActions>();
    services.AddTransient<IReentryActions, ReentryActions>();
    services.AddTransient<IDependencyResolver, DependencyResolver>();
    services.AddTransient<ITargetAssignmentResolver, TargetAssignmentResolver>();
    services.AddTransient<IMaintenanceActionInitializer, MaintenanceActionInitializer>();
    services.AddTransient<IWindowsPatchResolver, WindowsPatchResolver>();
    services.AddTransient<ISoftwareActions, SoftwareActions>();
    services.AddTransient<IComputerAssignmentActions, ComputerAssignmentActions>();
    services.AddTransient<ITenantAssignmentActions, TenantAssignmentActions>();
    services.AddTransient<IPersonAssignmentActions, PersonAssignmentActions>();
    services.AddTransient<IMaintenanceTaskActions, MaintenanceTaskActions>();
    services.AddTransient<IInventoryTaskActions, InventoryTaskActions>();
    services.AddTransient<ITargetAssignmentActions, TargetAssignmentActions>();
    services.AddTransient<IMediaActions, MediaActions>();
    services.AddTransient<ITargetPopulator, TargetPopulator>();
    services.AddTransient<IEntityValidator, EntityValidator>();
    services.AddTransient<IDevLabActions, DevLabActions>();
    services.AddTransient<ISupportRequestGenerator, SupportRequestGenerator>();
    services.AddTransient<IComputerMaintenanceDateOperations, ComputerMaintenanceDateOperations>();
    services.AddSingleton<IGettingStartedService, GettingStartedService>();

    // Caches
    services.AddFusionCache();
    services.AddPolicyRegistry(((serviceProvider, registry) =>
    {
      var appSettings = serviceProvider.GetRequiredService<IOptions<AppSettingsOptions>>().Value;
      registry.AddScheduleCachePolicy();
      registry.AddDeploymentGroupCachePolicy();
      registry.AddInventoryTaskCachePolicy();
      registry.Add5MinCachePolicy();
      registry.AddProviderInventoryTaskCachePolicy();
      registry.AddChocoCachePolicy();
      registry.AddAgentIdentificationProviderLinkCachePolicy();
      registry.AddImmenseChocoApiPolicy(appSettings.ChocolateyUrl);
      registry.AddChocoOrgApiPolicy(appSettings.ChocolateyOrgUrl);
      registry.AddProviderNamePolicy();
      registry.AddShowCommandInfoCachePolicy();
      registry.AddDisabledPreflightScriptsCachePolicy();
      registry.AddFindGlobalSoftwareCachePolicy();
      registry.AddAuthenticatedUserCachePolicy();
      registry.AddRolePolicyCachePolicy(); // RBAC
      registry.AddTenantAzureGroupNamesCachePolicy();
    }));
    services.AddSingleton<IPolicyCacheStore>(_ => PolicyCacheStore.Instance);

    // Immy Logic
    services.AddTransient<IRunContextFactory, RunContextFactory>();
    services.AddTransient<IImmyService, ImmyService>();
    services.AddTransient<IImmyEmail, ImmyEmail>();
    services.AddTransient<IMachineOperations, MachineOperations>();
    services.AddTransient<IMachineSoftwareOperations, MachineSoftwareOperations>();
    services.AddSingleton<IImmyCancellationManager, ImmyCancellationManager>();
    services.AddTransient<IImmyOnboardingStageRunner, ImmyOnboardingStageRunner>();
    services.AddTransient<IImmyDetectionRunner, ImmyDetectionRunner>();
    services.AddTransient<IImmyExecutionRunner, ImmyExecutionRunner>();
    services.AddTransient<IImmyDetectionStageRunner, ImmyDetectionStageRunner>();
    services.AddTransient<IImmyExecutionStageRunner, ImmyExecutionStageRunner>();
    services.AddTransient<IImmyAgentUpdatesStageRunner, ImmyAgentUpdatesStageRunner>();
    services.AddTransient<IImmyResolutionStageRunner, ImmyResolutionStageRunner>();
    services.AddTransient<IImmyDetectionResolver, ImmyDetectionResolver>();
    services.AddTransient<IImmyExecutionResolver, ImmyExecutionResolver>();
    services.AddTransient<IImmyInventoryStageRunner, ImmyInventoryStageRunner>();
    services.AddTransient<IMachineMaintenanceTaskOperations, MachineMaintenanceTaskOperations>();
    services.AddTransient<IRunContextComputerActions, RunContextComputerActions>();
    services.AddTransient<IRunContextArgs, RunContextArgs>();
    services.AddSingleton<IMaintenanceSessionPendingConnectivityHandler, MaintenanceSessionPendingConnectivityHandler>();
    // pending ephemeral agent connection services
    services.AddTransient<IPendingEphemeralAgentConnectionActions, PendingEphemeralAgentConnectionActions>();
    services.AddSingleton<IRetryPendingEphemeralAgentConnectionHandler, RetryPendingEphemeralAgentConnectionHandler>();
    services.AddHostedService<RetryPendingEphemeralAgentConnectionBackgroundService>();
    services.AddHostedService<PendingEphemeralAgentConnectionListenerBackgroundService>();
    // agent identification services
    services.AddSingleton<IAgentIdentificationManagerActions, AgentIdentificationManagerActions>();
    services.AddSingleton<IAgentIdentificationHandler, AgentIdentificationHandler>();
    services.AddSingleton<IAgentResolutionHandler, AgentResolutionHandler>();
    services.AddSingleton<IAgentIdentificationManager, AgentIdentificationManager>();

    // provider services
    services.AddSingleton<IProviderInventoryTaskCache, ProviderInventoryTaskCache>();
    services.AddHostedService<ProviderPostInitializationService>();
    services.AddHostedService<InventoryIdentificationService>();

    // preflight
    services.AddTransient<IMaintenanceSessionPreflightHandlerActions, MaintenanceSessionPreflightHandlerActions>();
    services.AddSingleton<IMaintenanceSessionPreflightHandler, MaintenanceSessionPreflightHandler>();
    services.AddHostedService<MaintenanceSessionPreflightBackgroundService>();

    services.AddTenantDeletionServices();

    // Hangfire
    services.AddTransient<IHangfireWorkerCountCalculator, HangfireWorkerCountCalculator>();
    services.AddHangfire((sp, hangfireConfig) =>
    {
      var hangfireOptions = sp.GetRequiredService<IOptions<HangfireSettings>>().Value;

      // use redis if connection string and db are present
      if (!string.IsNullOrEmpty(hangfireOptions.RedisConnectionString))
        hangfireConfig.UseRedisStorage(
          hangfireOptions.RedisConnectionString,
          new RedisStorageOptions { Database = hangfireOptions.RedisStorageDb, MaxStateHistoryLength = 5 });
      else
        hangfireConfig.UseMemoryStorage();

      hangfireConfig.UseThrottling(ThrottlingAction.RetryJob, TimeSpan.FromSeconds(20));
      var settings = new JsonSerializerSettings
      {
        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
        TypeNameHandling = TypeNameHandling.Auto,
      };
      settings.Converters.Add(new SemanticVersionConverter());
      hangfireConfig.UseSerializerSettings(settings);
      hangfireConfig.UseBatches();
    });

    // Jobs
    services.AddTransient<IImmyServiceJob, ImmyServiceJob>();
    services.AddTransient<IImmyScheduleJob, ImmyScheduleJob>();
    services.AddTransient<IInventoryTaskJob, InventoryTaskJob>();
    services.AddTransient<InventoryTaskSchedulerJob>();
    services.AddTransient<SyncAzureTenantDataJob>();
    services.AddTransient<UserAffinityJob>();
    services.AddTransient<ProviderHealthCheckJob>();
    services.AddTransient<ProviderClientSyncSchedulerJob>();
    services.AddTransient<IProviderClientSyncJob, ProviderClientSyncJob>();
    services.AddTransient<IAzureCustomerPreconsentJob, AzureCustomerPreconsentJob>();
    services.AddTransient<IntegrationTypeRefreshJob>();
    services.AddTransient<ProviderAuditLogPartitionCreatorJob>();
    services.AddTransient<ProviderAuditLogPartitionCleanupJob>();

    // services
    services.AddSingleton<IApplicationPreferenceWatcherService, ApplicationPreferenceWatcherService>();
    services.AddSingleton<TimelineEventBuilder>();
    services.AddSingleton<IPendoEventManagementService, PendoEventManagementService>();
    services.AddHostedService(s => s.GetRequiredService<IPendoEventManagementService>());
    services.AddHostedService<TimelineEventSaverService>();
    services.AddHostedService<ProviderAuditLogSaverService>();
    services.AddHostedService<ApplicationMetricsCollectorBackgroundService>();
    services.AddHostedService<FeatureUsageResetService>();
    services.AddHostedService<AgentHubMetricsCollector>();
    services.AddHostedService<RemoteControlRecordingCleanupService>();
    services.AddHostedService<MaintenanceSessionPendingConnectivityCheckerBackgroundService>();
    services.AddHostedService(s => s.GetRequiredService<ISessionObjectsUpdateHandler>());
    services.AddHostedService<SessionLogUpdateProcessor>();
    services.AddHostedService<AutomaticEphemeralConnectionService>();
    services.AddHostedService<FeatureUsageExceededEventNotificationService>();

    // add all notification services
    services.AddNotificationServices();
    // Monitors and logs events from KeyedSemaphoreLocker
    services.AddHostedService<KeyedLockerMonitoringService>();
    // Runs hangfire server if configured to do so
    // Restarts when hangfire worker count features are changed
    services.AddHostedService<HangfireService>();
    services.AddHostedService<HangfireWatchdogService>();

    // Runs redis connectivity watcher if configured to do so
    services.AddHostedService<IRedisConnectivityWatcher, RedisConnectivityWatcher>();
    services.AddHostedService<DevLabClaimService>();
    services.AddHostedService<DevLabClaimExpirationService>();

    // deployment acknowledgement
    services.AddTransient<IDeploymentAcknowledgementMonitorActions, DeploymentAcknowledgementMonitorActions>();
    services.AddHostedService<DeploymentAcknowledgementMonitor>();

    services.AddHostedService<ProviderAgentNeedsManualDecisionWatcherService>();

    services.AddSingleton<IImmyBotUpgradeActions, ImmyBotUpgradeActions>();
    services.AddHostedService<AutomaticImmyBotUpgradeService>();

    // Migrates available target assignments to provider links. Runs once at startup.

    services.AddTransient<IMigrateDeploymentsToProviderLinksService, MigrateDeploymentsToProviderLinksService>();
    services.AddTransient<IRecommendedProviderLinksGetter, RecommendedProviderLinksGetter>();
    services.AddHostedService<MigrateDeploymentsToProviderLinksBackgroundService>();

    // commands
    services.RegisterCommands();

    if (OperatingSystem.IsLinux())
    {
      // This makes it such that Get-Module -ListAvailable shows the modules included in PowerShell/Modules
      Environment.SetEnvironmentVariable("XDG_DATA_HOME", MetascriptInvokerDefaults.AssemblyFolder);
    }

    services.AddMaintenanceSessionWorkerServices();

    services.AddNotificationsMailer();
  }

  /// <summary>
  /// Services required for maintenance sessions.
  /// All services registered here will eventually become scoped services.
  /// The implementation of these services will also eventually use an http client
  /// instead of a db context to fetch data.
  /// </summary>
  /// <param name="services"></param>
  public static void AddMaintenanceSessionWorkerServices(this IServiceCollection services)
  {
    services.AddTransient<ISessionStageActions, SessionStageActions>();
    services.AddTransient<ILicenseActions, LicenseActions>();
    services.AddTransient<ITenantActions, TenantActions>();
    services.AddTransient<IPersonActions, PersonActions>();
    services.AddSingleton<IProviderLinkActions, ProviderLinkActions>();
    services.AddTransient<IMaintenanceItemOrderActions, MaintenanceItemOrderActions>();
    services.AddTransient<IScheduleActions, ScheduleActions>();
    services.AddTransient<IMaintenanceActionActions, MaintenanceActionActions>();
    services.AddTransient<IComputerInventoryActions, ComputerInventoryActions>();
  }

  public static async Task InitializeApplicationServices(
    this IApplicationBuilder app)
  {
    // Kick us to a new thread in case our caller wants to continue execution
    // while we do our work
    await Task.Yield();

    /*
      SERVER HANGFIRE JOB / SESSION STARTUP BEHAVIOR
      ----------------------------------------------
      Whenever a new version of immybot is deployed,
      it is possible that the method signatures stored within hangfire
      will be outdated and the hangfire jobs will fail to run.

      We can get ahead of this issue by re-enqueueing all of our jobs based on
      data stored within the immy database.

      Run this behavior on its own thread so we do not increase
      initial load time for the server process.
    */
    var sw = new Stopwatch();
    sw.Start();
    var serviceProvider = app.ApplicationServices;
    using var scope = serviceProvider.CreateScope();
    var scopedProvider = scope.ServiceProvider;
    var logger = scopedProvider.GetRequiredService<ILogger<ReentryActions>>();
    var state = new Dictionary<string, object?>()
    {
      ["Duration"] = 0,
    };
    using var _ = logger.BeginScope(state);
    try
    {
      // initialize
      var agentIdentificationManager = scopedProvider.GetRequiredService<IAgentIdentificationManager>();
      agentIdentificationManager.Init();

      var pendingConnectivityHandler = scopedProvider.GetRequiredService<IMaintenanceSessionPendingConnectivityHandler>();
      pendingConnectivityHandler.Init();

      var timelineEventBuilder = scopedProvider.GetRequiredService<TimelineEventBuilder>();
      timelineEventBuilder.Init();

      // new up the service
      scopedProvider.GetRequiredService<IApplicationPreferenceWatcherService>();
    }
    // intentionally catching exceptions and exiting the app process since we're likely
    // in a background thread and won't propagate to the app domain naturally
    catch (Exception ex)
    {
      Debugger.Break();
      // don't allow server to start if hangfire doesn't start
      const string msg = "Application stopping: Exception thrown when trying to initialize jobs and hangfire";
      logger.LogError(ex, msg);
      Environment.FailFast(msg, ex);
    }
    finally
    {
      sw.Stop();
      state["Duration"] = sw.ElapsedMilliseconds;
      logger.LogInformation($"Finished job initialization on application startup.");
    }
  }

  internal static IServiceCollection AddProvider<TProviderImpl>(
    this IServiceCollection collection,
    Guid providerTypeId)
    where TProviderImpl : class, IProvider
  {
    collection.AddTransient<TProviderImpl>();
    collection.AddSingleton<IProviderRegistration>(provider => new ProviderRegistration<TProviderImpl>(providerTypeId, provider));
    return collection;
  }
}
