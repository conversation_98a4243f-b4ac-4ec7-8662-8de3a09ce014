using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.Stores;

public interface ITenantStore
{
  public Task<List<TenantDeletion>> GetIncompleteTenantDeletions(CancellationToken token);
  public Task SetTenantDeletionStatus(int tenantId, TenantDeletion.DeletionStatus status, CancellationToken token);
  public Task SetTenantDeletionFailed(int tenantId, string message, CancellationToken token);
  public Task<string?> GetTenantName(int tenantId, CancellationToken token);
  public Task DeleteTenantData(TenantDeletion deletion, CancellationToken token);
  public Task<List<DeleteTenantProviderClientsDto>> GetDeleteTenantProviderClientsDtos(TenantDeletion deletion, CancellationToken token);
  public Task<string?> GetTenantPrincipalId(int tenantId, CancellationToken token);
}

public class TenantStore : ITenantStore
{
  private readonly Func<ImmybotDbContext> _ctxFactory;

  public TenantStore(Func<ImmybotDbContext> ctxFactory)
  {
    _ctxFactory = ctxFactory;
  }

  public async Task<List<TenantDeletion>> GetIncompleteTenantDeletions(CancellationToken token)
  {
    await using var ctx = _ctxFactory();
    return await ctx.GetIncompleteTenantDeletions(token);
  }

  public async Task SetTenantDeletionStatus(int tenantId, TenantDeletion.DeletionStatus status, CancellationToken token)
  {
    await using var ctx = _ctxFactory();
    await ctx.SetTenantDeletionStatus(tenantId, status, token);
  }

  public async Task<string?> GetTenantName(int tenantId, CancellationToken token)
  {
    await using var ctx = _ctxFactory();
    return await ctx.Tenants.Where(a => a.Id == tenantId).Select(a => a.Name).FirstOrDefaultAsync(token);
  }

  public async Task DeleteTenantData(TenantDeletion deletion, CancellationToken token)
  {
    await using var ctx = _ctxFactory();

    // set the command timeout to 1 hour to allow for large deletions
    ctx.Database.SetCommandTimeout(TimeSpan.FromHours(1));

    if (deletion.CreatedBy is not null)
    {
      var triggeredBy = await ctx.Users
        .AsNoTracking()
        .Include(a => a.Person)
        .Where(a => a.Id == deletion.CreatedBy)
        .Select(AuthUserDto.Projection)
        .FirstOrDefaultAsync(token);

      if (triggeredBy is not null)
        ctx.SetUser(triggeredBy);
    }

    // begin a transaction with a 24h statement timeout to prevent leaked settings on pooled connections
    await using var tx = await ctx.Database.BeginTransactionAsync(token);

    // SET LOCAL only works on Postgres; skip for in-memory Sqlite in unit tests
    if (ctx.Database.IsNpgsql())
      await ctx.Database.ExecuteSqlRawAsync("SET LOCAL statement_timeout = 86400000", token);

    // delete target assignments
    await ctx.TargetAssignments
      .TagForTelemetry()
      .Where(ta => ta.TenantId == deletion.TenantId)
      .ExecuteDeleteAsync(token);

    // delete user preferences
    await ctx.UserPreferences
      .TagForTelemetry()
      .Join(ctx.Users.Where(u => u.TenantId == deletion.TenantId),
        up => up.UserId,
        u => u.Id,
        (up, u) => up)
      .ExecuteDeleteAsync(token);

    // delete licenses
    await ctx.Licenses
      .TagForTelemetry()
      .Where(a => a.TenantId == deletion.TenantId)
      .ExecuteDeleteAsync(token);

    // delete azure errors
    var tenantPrincipalId = await GetTenantPrincipalId(deletion.TenantId, token);
    if (tenantPrincipalId is not null)
    {
      await ctx.AzureErrors
        .TagForTelemetry()
        .Where(a => a.TenantPrincipalId == tenantPrincipalId)
        .ExecuteDeleteAsync(token);
    }

    // delete session phases
    await ctx.SessionPhases
      .TagForTelemetry()
      .Where(sp => sp.MaintenanceSession != null
                   && sp.MaintenanceSession.TenantId == deletion.TenantId)
      .ExecuteDeleteAsync(token);

    // delete maintenance action dependencies
    await ctx.MaintenanceActionDependencies
      .TagForTelemetry()
      .Where(mad => mad.Dependent != null
                    && mad.Dependent.TenantId == deletion.TenantId)
      .ExecuteDeleteAsync(token);

    await ctx.MaintenanceActionDependencies
      .TagForTelemetry()
      .Where(mad => mad.DependsOn != null
                    && mad.DependsOn.TenantId == deletion.TenantId)
      .ExecuteDeleteAsync(token);

    // delete maintenance action activities
    await ctx.MaintenanceActionActivities
      .TagForTelemetry()
      .Where(maa => maa.MaintenanceAction.TenantId == deletion.TenantId)
      .ExecuteDeleteAsync(token);

    // delete maintenance actions
    await ctx.MaintenanceActions
      .TagForTelemetry()
      .Where(ma => ma.MaintenanceSession != null
                   && ma.MaintenanceSession.TenantId == deletion.TenantId)
      .ExecuteDeleteAsync(token);

    // delete maintenance session stages
    await ctx.MaintenanceSessionStages
      .TagForTelemetry()
      .Where(mss => mss.MaintenanceSession != null
                    && mss.MaintenanceSession.TenantId == deletion.TenantId)
      .ExecuteDeleteAsync(token);

    // delete active sessions
    await ctx.ActiveSessions
      .TagForTelemetry()
      .Where(asn => asn.MaintenanceSession != null
                    && asn.MaintenanceSession.TenantId == deletion.TenantId)
      .ExecuteDeleteAsync(token);

    // delete maintenance sessions
    await ctx.MaintenanceSessions
      .TagForTelemetry()
      .Where(ms => ms.TenantId == deletion.TenantId)
      .ExecuteDeleteAsync(token);

    // delete tenant
    var tenant = await ctx.Tenants
      .TagForTelemetry()
      .FirstOrDefaultAsync(t => t.Id == deletion.TenantId, token);

    if (tenant is not null)
    {
      ctx.Remove(tenant);
      await ctx.SaveChangesAsync(token);
    }

    await tx.CommitAsync(token);
  }

  public async Task<List<DeleteTenantProviderClientsDto>> GetDeleteTenantProviderClientsDtos(
    TenantDeletion deletion,
    CancellationToken token)
  {
    await using var ctx = _ctxFactory();
    var clients = await ctx.Tenants
      .AsNoTracking()
      .Where(a => a.Id == deletion.TenantId)
      .SelectMany(a => a.ProviderClients
        .Select(b => new DeleteTenantProviderClientsDto
        {
          ExternalClientId = b.ExternalClientId, ProviderLinkId = b.ProviderLinkId
        }))
      .ToListAsync(token);

    return clients;
  }

  public async Task<string?> GetTenantPrincipalId(int tenantId, CancellationToken token)
  {
    await using var ctx = _ctxFactory();
    var tenantPrincipalId = await ctx.Tenants
      .Where(a => a.Id == tenantId)
      .Select(a => a.AzureTenantLink!.AzureTenant!.PrincipalId)
      .FirstOrDefaultAsync(token);

    return tenantPrincipalId;
  }

  public async Task SetTenantDeletionFailed(int tenantId, string message, CancellationToken token)
  {
    await using var ctx = _ctxFactory();
    await ctx.SetTenantDeletionError(
      tenantId,
      message,
      token);
  }
}

public record DeleteTenantProviderClientsDto()
{
  public required int ProviderLinkId { get; init; }
  public required string ExternalClientId { get; init; }
}
