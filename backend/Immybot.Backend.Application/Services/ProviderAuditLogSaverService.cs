using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Services;

/// <summary>
/// Background service that persists provider audit logs to the partitioned PostgreSQL table
/// </summary>
internal class ProviderAuditLogSaverService : BackgroundService
{
  private readonly ILogger<ProviderAuditLogSaverService> _logger;
  private readonly Func<ImmybotDbContext> _contextFactory;
  private readonly IDomainEventReceiver _eventReceiver;

  private readonly ConcurrentQueue<ProviderAuditLog> _queue;
  private const int _processDelayMs = 2000; // Process every 2 seconds for faster response

  public ProviderAuditLogSaverService(
    ILogger<ProviderAuditLogSaverService> logger,
    Func<ImmybotDbContext> contextFactory,
    IDomainEventReceiver eventReceiver)
  {
    _logger = logger;
    _contextFactory = contextFactory;
    _eventReceiver = eventReceiver;
    _queue = new();
  }

  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    // Listen for provider audit log events
    _eventReceiver.AddEventHandler<ProviderAuditLogAddedEvent>(ev => _queue.Enqueue(ev.Log));

    // Process the queue periodically
    while (!stoppingToken.IsCancellationRequested)
    {
      try
      {
        await ProcessQueue();
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error occurred while saving provider audit logs");
      }
      finally
      {
        await Task.Delay(_processDelayMs, stoppingToken);
      }
    }
  }

  private async Task ProcessQueue()
  {
    if (_queue.IsEmpty) return;

    var logs = new List<ProviderAuditLog>();
    while (_queue.TryDequeue(out var log))
    {
      logs.Add(log);
    }

    if (logs.Count == 0) return;

    _logger.LogDebug("Processing {Count} provider audit logs", logs.Count);

    await using var ctx = _contextFactory.Invoke();
    try
    {
      ctx.ProviderAuditLogs.AddRange(logs);
      await ctx.SaveChangesAsync();
      
      _logger.LogDebug("Successfully saved {Count} provider audit logs", logs.Count);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Failed to save {Count} provider audit logs", logs.Count);
      throw;
    }
  }
}