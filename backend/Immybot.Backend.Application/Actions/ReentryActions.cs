using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hangfire;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.SessionLogExtensions;
using Immybot.Backend.Application.Infrastructure;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Jobs;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Providers;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nito.AsyncEx;

namespace Immybot.Backend.Application.Actions;

internal class ReentryActions : IReentryActions
{
  private const string _everyMinuteCron = "* * * * *"; // every minute
  private const string _logMessageInitializingProvider = "Initializing provider: {ProviderLinkName}";
  private const string _logMessageFinishedProviderInit = "Finished initializing provider: {ProviderLinkName}";
  private const string _logMessageFailedProviderInit = "Failed initializing provider: {ProviderLinkName}";
  private const string _logMessageRegisteringSystemJob = "Registering system job: {JobName}";
  private static readonly List<SessionStatus> _cancellableSessionStatuses
    =
    [
      SessionStatus.Created,
      SessionStatus.Pending,
      SessionStatus.Running,
    ];

  private readonly IProviderRegistrationService _providerRegistrationService;
  private readonly Func<ImmybotDbContext> _contextFactory;
  private readonly IImmyScheduledJobRunner _immyJobRunner;

  //Di schedule
  private readonly IScheduleJobActions _scheduleJobActions;

  private readonly IMaintenanceSessionActions _maintenanceSessionActions;
  private readonly ILogger<ReentryActions> _logger;
  private readonly IServiceScopeFactory _serviceScopeFactory;
  private readonly ICachedSingleton<ApplicationPreferences> _cachedPrefs;
  private readonly IFeatureManager _featureManager;
  private readonly HangfireSettings _hangfireSettings;
  private readonly IInitializationStatus _initStatus;

  public ReentryActions(
    IProviderRegistrationService providerRegistrationService,
    Func<ImmybotDbContext> contextFactory,
    IImmyScheduledJobRunner immyJobRunner,
    IScheduleJobActions scheduleJobActions,
    IMaintenanceSessionActions maintenanceSessionActions,
    ILogger<ReentryActions> logger,
    IServiceScopeFactory serviceScopeFactory,
    ICachedSingleton<ApplicationPreferences> cachedPrefs,
    IInitializationStatus initStatus,
    IFeatureManager featureManager,
    IOptions<HangfireSettings> hangfireOptions)
  {
    _providerRegistrationService = providerRegistrationService;
    _contextFactory = contextFactory;
    _immyJobRunner = immyJobRunner;
    _scheduleJobActions = scheduleJobActions;
    _maintenanceSessionActions = maintenanceSessionActions;
    _logger = logger;
    _serviceScopeFactory = serviceScopeFactory;
    _cachedPrefs = cachedPrefs;
    _featureManager = featureManager;
    _hangfireSettings = hangfireOptions.Value;
    _initStatus = initStatus;
  }

  public async Task InitializeJobs(bool isStartup, CancellationToken token)
  {
    _logger.LogInformation("Initializing jobs");
    using var _ = await _initStatus.AcquireInitializationLockAsync(token);

    // We cannot start registering jobs for our providers until the provider registration service has started,
    // and the registrations have been created.
    await _providerRegistrationService.WaitForStartupAsync(token);

    if (_hangfireSettings.FailRunningSessions)
    {
      _logger.LogInformation("Failing all running sessions");
      // this is optional logic for MSPs who do not want sessions to ever be re-enqueued when the server restarts
      // fail all incomplete sessions so they do not requeue and run
      await _maintenanceSessionActions.FailAllRunningSessions(token);
      await _maintenanceSessionActions.FailAllRunningSessionStages(token);
      await _maintenanceSessionActions.FailAllPendingSessions(token);
      await _maintenanceSessionActions.FailAllCreatedSessions(token);
      await RegisterProviderLinkJobs(token);
      token.ThrowIfCancellationRequested();
    }
    else
    {
      _logger.LogInformation("Enqueuing / canceling incomplete sessions");
      // attempt to re-enqueue all valid created/pending/running sessions
      // and cancel anything that is invalid
      // validity is determined on the session's status
      // and when the session was last updated
      try
      {
        await RequeueOrCancelIncompleteSessions(isStartup, token);
      }
      catch (Exception ex) when (!ex.IsCancellationException(token) && !ex.IsRedisException())
      {
        _logger.LogError(ex, "Failed to requeue or cancel incomplete sessions.");
      }

    }

    try
    {
      RequeueSystemInventoryJobs(token);
    }
    catch (Exception ex) when (!ex.IsCancellationException(token) && !ex.IsRedisException())
    {
      _logger.LogError(ex, "Failed to requeue inventory jobs");
    }

    try
    {
      RescheduleJobs(token);
    }
    catch (Exception ex) when (!ex.IsCancellationException(token) && !ex.IsRedisException())
    {
      _logger.LogError(ex, "Failed to reschedule system jobs");
    }

    _initStatus.SetInitialized();

    _logger.LogInformation("... successfully initialized jobs.");
  }

  private void RequeueSystemInventoryJobs(CancellationToken token)
  {
    _logger.LogInformation($"Starting method {nameof(RequeueSystemInventoryJobs)}");

    using var scope = _serviceScopeFactory.CreateScope();
    using var ctx = _contextFactory();
    token.ThrowIfCancellationRequested();
    var outdatedDeviceIds = ctx.GetAllComputers()
      .Where(c => c.InventoryStartedDate != null)
      .Select(c => c.DeviceId)
      .ToList();
    token.ThrowIfCancellationRequested();
    ctx.SetComputerInventoryEndedForComputers(outdatedDeviceIds);
    token.ThrowIfCancellationRequested();
    _logger.LogInformation($"Finished method {nameof(RequeueSystemInventoryJobs)}");
  }

  private async Task RequeueOrCancelIncompleteSessions(
    bool isStartup,
    CancellationToken token)
  {
    _logger.LogInformation($"Starting method {nameof(RequeueOrCancelIncompleteSessions)}");
    await using var ctx = _contextFactory();
    token.ThrowIfCancellationRequested();
    // load all schedules into memory
    var allSchedules = await ctx.Schedules.AsNoTracking().ToListAsync(token);
    token.ThrowIfCancellationRequested();
    // load all incomplete sessions into memory
    var incompleteSessions = await ctx
      .ActiveSessions
      .Include(a => a.MaintenanceSession)
      .AsNoTracking()
      .Where(a => _cancellableSessionStatuses.Contains(a.SessionStatus))
      .Select(a => new ReentrySessionDto
      {
        Id = a.MaintenanceSession!.Id,
        SessionStatus = a.MaintenanceSession.SessionStatus,
        ScheduleId = a.MaintenanceSession.ScheduledId,
        ScheduledExecutionDate = a.MaintenanceSession.ScheduledExecutionDate,
        UpdatedDate = a.MaintenanceSession.UpdatedDate,
        JobId = a.MaintenanceSession.JobId,
        ExecutionStageJobId = a.MaintenanceSession.Stages.FirstOrDefault(b => b.Type == SessionStageType.Execution)!.JobId
      })
      .ToListAsync(token);
    token.ThrowIfCancellationRequested();

    if (isStartup)
      _logger.LogInformation("Found {IncompleteSessionsCount} incomplete sessions upon server starting up.",
        incompleteSessions.Count);
    else
      _logger.LogInformation("Found {IncompleteSessionsCount} incomplete sessions upon redis reconnecting.",
        incompleteSessions.Count);

    var sessionsToCancel = new HashSet<int>();
    var sessionsToRequeue = new HashSet<int>();

    // this will recreate all in-memory IProvider implementations, so we need to make sure we do
    // this before recreating immy session jobs, or else the sessions might end up with
    // references to disposed providers
    await RegisterProviderLinkJobs(token);

    // we want to re-enqueue running sessions first, then pending, then created
    var map = new Dictionary<SessionStatus, int>() {
      { SessionStatus.Running, 0 },
      { SessionStatus.Pending, 1 },
      { SessionStatus.Created, 2 },
    };
    var end = map.Count + 1;
    incompleteSessions = incompleteSessions
      .OrderBy(a => map.GetValueOrDefault(key: a.SessionStatus, defaultValue: end))
      .ToList();

    foreach (var session in incompleteSessions)
    {
      token.ThrowIfCancellationRequested();

      // if the schedule for this session no longer exists or it is disabled, then cancel it.
      var schedule = allSchedules.Find(a => a.Id == session.ScheduleId);
      if (session.ScheduleId is not null && (schedule?.Disabled is true || schedule is null))
      {
        sessionsToCancel.Add(session.Id);
        continue;
      }

      sessionsToRequeue.Add(session.Id);
    }
    token.ThrowIfCancellationRequested();

    // Handle session cancellations
    if (sessionsToCancel.Any())
    {
      await ctx.MaintenanceSessions
        .AsNoTracking()
        .Where(a => sessionsToCancel.Contains(a.Id))
        .ExecuteUpdateAsync(a =>
            a.SetProperty(b => b.SessionStatus, SessionStatus.Cancelled),
          token);
    }

    token.ThrowIfCancellationRequested();

    var prefix = isStartup ? "The ImmyBot server rebooted" : "A required service rebooted";
    var msg = $"{prefix} while the session was active";
    if (sessionsToCancel.Any())
      await ctx.CreateSessionLogs($"{msg}. The session has been considered outdated and canceled.", sessionsToCancel, token);

    if (sessionsToRequeue.Count != 0)
    {
      await ctx.CreateSessionLogs($"{msg}. The session will be restarted.", sessionsToRequeue, token);
      await ctx.MaintenanceSessions
        .AsNoTracking()
        .Where(a => sessionsToRequeue.Contains(a.Id))
        .ExecuteUpdateAsync(a =>
            a.SetProperty(b => b.SessionStatus, SessionStatus.PendingConnectivity),
          token);
    }

    _logger.LogTrace("Enqueued {count} sessions", sessionsToRequeue.Count);

    _logger.LogInformation($"Finished method {nameof(RequeueOrCancelIncompleteSessions)}");
  }

  public async Task RegisterProviderLinkJobs(CancellationToken token)
  {
    _logger.LogInformation($"Starting method {nameof(RegisterProviderLinkJobs)}");
    await using var ctx = _contextFactory();

    var links = await ctx.GetProviderLinks()
      .IgnoreQueryFilters()
      .Where(a => !a.Disabled)
      .ToListAsync(token);

    var tasks = links.Select(link => Task.Run(async () =>
    {
      using var scope = _serviceScopeFactory.CreateScope();
      var providerActions = scope.ServiceProvider.GetRequiredService<IProviderActions>();
      _logger.LogInformation(_logMessageInitializingProvider, link.Name);
      try
      {
        // Init each provider for each link in the db so that the
        // providers' sync jobs will get registered
        await providerActions.GetProvider(link, token, allowUnhealthy: true);
        _logger.LogInformation(_logMessageFinishedProviderInit, link.Name);
      }
      catch (MissingProviderTypeException ex)
      {
        _logger.LogInformation(_logMessageFailedProviderInit, link.Name);
        _logger.LogError(ex, "Error occurred while registering provider link jobs");
      }
      catch (ProviderConstructionFailedException ex)
      {
        _logger.LogInformation(_logMessageFailedProviderInit, link.Name);
        _logger.LogError(ex, "Error occurred while registering provider link jobs");
      }
      catch (InvalidProviderFormDataException ex)
      {
        _logger.LogInformation(_logMessageFailedProviderInit, link.Name);
        _logger.LogError(ex, "Error occurred while registering provider link jobs");
      }
      catch (GetProviderTimeoutException ex)
      {
        _logger.LogInformation(_logMessageFailedProviderInit, link.Name);
        _logger.LogError(ex, "Error occurred while registering provider link jobs");
      }
    }, token));

    await Task.WhenAll(tasks);

    _logger.LogInformation($"Finished method {nameof(RegisterProviderLinkJobs)}");
  }

  private void RescheduleJobs(CancellationToken token)
  {
    _logger.LogInformation($"Starting method {nameof(RescheduleJobs)}");
    using var ctx = _contextFactory();
    token.ThrowIfCancellationRequested();

    try
    {
      // re-schedule all immy schedules
      _scheduleJobActions.RescheduleAll();
    }
    catch (Exception ex) when (!ex.IsCancellationException() && !ex.IsRedisException())
    {
      _logger.LogError(ex, "Failed to reschedule immy schedule jobs");
    }

    token.ThrowIfCancellationRequested();

    using var scope = _serviceScopeFactory.CreateScope();

    _logger.LogInformation(_logMessageRegisteringSystemJob, nameof(ProviderHealthCheckJob));
    _immyJobRunner.RegisterJob(
      scope.ServiceProvider.GetRequiredService<ProviderHealthCheckJob>(),
      ProviderHealthCheckJob.JobId,
      _everyMinuteCron);
    token.ThrowIfCancellationRequested();

    _logger.LogInformation(_logMessageRegisteringSystemJob, nameof(ProviderClientSyncSchedulerJob));
    _immyJobRunner.RegisterJob(
      scope.ServiceProvider.GetRequiredService<ProviderClientSyncSchedulerJob>(),
      ProviderClientSyncSchedulerJob.JobId,
      CronHelpers.Every30Minutes(),
      retryWhenThrottled: true);
    token.ThrowIfCancellationRequested();

    _logger.LogInformation(_logMessageRegisteringSystemJob, nameof(SyncAzureTenantDataJob));
    _immyJobRunner.RegisterJob(
      scope.ServiceProvider.GetRequiredService<SyncAzureTenantDataJob>(),
      SyncAzureTenantDataJob.JobId,
      CronHelpers.OnceAnHour(),
      retryWhenThrottled: true);
    token.ThrowIfCancellationRequested();

    _logger.LogInformation(_logMessageRegisteringSystemJob, nameof(IntegrationTypeRefreshJob));
    _immyJobRunner.RegisterJob(
      scope.ServiceProvider.GetRequiredService<IntegrationTypeRefreshJob>(),
      IntegrationTypeRefreshJob.JobId,
      CronHelpers.OnceAnHour(),
      retryWhenThrottled: true);

    if (_featureManager.IsEnabled(FeatureEnum.RecurringInventoryJobFeature))
    {
      _logger.LogInformation(_logMessageRegisteringSystemJob, nameof(InventoryTaskSchedulerJob));
      _immyJobRunner.RegisterJob(
        scope.ServiceProvider.GetRequiredService<InventoryTaskSchedulerJob>(),
        InventoryTaskSchedulerJob.JobId,
        _everyMinuteCron);
      token.ThrowIfCancellationRequested();
    }

    if (_featureManager.IsEnabled(FeatureEnum.RecurringUserComputerAffinityJobFeature) && _cachedPrefs.Value.EnableUserAffinitySync)
    {
      _logger.LogInformation(_logMessageRegisteringSystemJob, nameof(UserAffinityJob));
      _immyJobRunner.RegisterJob(
        scope.ServiceProvider.GetRequiredService<UserAffinityJob>(),
        UserAffinityJob.JobId,
        CronHelpers.OnceADay(),
        retryWhenThrottled: true);
      token.ThrowIfCancellationRequested();
    }

    _logger.LogInformation(_logMessageRegisteringSystemJob, nameof(ProviderAuditLogPartitionCreatorJob));
    _immyJobRunner.RegisterJob(
      scope.ServiceProvider.GetRequiredService<ProviderAuditLogPartitionCreatorJob>(),
      ProviderAuditLogPartitionCreatorJob.JobId,
      "0 1 * * 0",
      retryWhenThrottled: true);
    token.ThrowIfCancellationRequested();

    _logger.LogInformation(_logMessageRegisteringSystemJob, nameof(ProviderAuditLogPartitionCleanupJob));
    _immyJobRunner.RegisterJob(
      scope.ServiceProvider.GetRequiredService<ProviderAuditLogPartitionCleanupJob>(),
      ProviderAuditLogPartitionCleanupJob.JobId,
      CronHelpers.OnceADay(),
      retryWhenThrottled: true);
    token.ThrowIfCancellationRequested();

    token.ThrowIfCancellationRequested();
    _logger.LogInformation($"Finished method {nameof(RescheduleJobs)}");
  }
}

public class ReentrySessionDto
{
  public int Id { get; set; }
  public SessionStatus SessionStatus { get; set; }
  public int? ScheduleId { get; set; }
  public DateTime? ScheduledExecutionDate { get; set; }
  public DateTime UpdatedDate { get; set; }
  public string? JobId { get; set; }
  public string? ExecutionStageJobId { get; set; }
}
