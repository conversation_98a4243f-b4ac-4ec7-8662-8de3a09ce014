using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.RoleManagement.Exceptions;
using Immybot.Backend.RBAC.Domain.RoleManagement.Interfaces;
using Immybot.Backend.RBAC.Domain.RoleManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.RBAC.RolesManagement.CustomRoles.Implementations;

/// <summary>
/// Operation for retrieving a role by ID.
/// </summary>
public class GetRoleOperation(
  Func<ImmybotDbContext> dbContextFactory)
  : IGetRoleOperation
{
  /// <summary>
  /// Executes the operation to retrieve a role by ID.
  /// </summary>
  /// <param name="roleId">The role ID.</param>
  /// <param name="cancellationToken">Cancellation token.</param>
  /// <returns>The role response.</returns>
  public async Task<GetRoleResponse> ExecuteAsync(int roleId, CancellationToken cancellationToken = default)
  {
    await using var dbContext = dbContextFactory();

    // Get all roles with their type information in a single query.
    // Load related RoleType
    var role = await dbContext.Roles
      .AsNoTracking() // Use AsNoTracking for read-only operations to improve performance
      .AsSplitQuery()
      .Select(a => new GetRoleResponse
        {
          Id = a.Id,
          Name = a.Name,
          Description = a.Description,
          RoleTypeId = a.RoleTypeId,
          RoleTypeName = a.RoleType!.Name,
          UserCount = a.UserRoles.Count,
          RoleClaims = a.RoleClaims.Select(r => new GetRoleClaimResponse
          {
            ClaimType = r.ClaimType, ClaimValue = r.ClaimValue
          }).ToList(),
          UpdatedDate = a.UpdatedDate,
          CreatedDate = a.CreatedDate,
          UpdatedBy = a.UpdatedByUser != null && a.UpdatedByUser.Person != null
            ? a.UpdatedByUser.Person.DisplayName
            : null,
        }).FirstOrDefaultAsync(a => a.Id == roleId, cancellationToken);

    if (role is null)
      throw new RoleNotFoundException(roleId);

    return role;
  }
}
