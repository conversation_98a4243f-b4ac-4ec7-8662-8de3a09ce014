using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Integrations.Permissions;

/// <summary>
/// Permission to link clients.
/// </summary>
public class IntegrationsLinkClientsPermission : PermissionMetadata, IIntegrationsLinkClientsPermission
{
  private readonly IntegrationsViewPermission _viewPermission;

  /// <summary>
  /// Initializes a new instance of the <see cref="IntegrationsLinkClientsPermission"/> class.
  /// </summary>
  /// <param name="subjectFactory">Factory to retrieve the integrations subject.</param>
  /// <param name="viewPermission">The view permission this permission depends on.</param>
  public IntegrationsLinkClientsPermission(
      SubjectFactory<IntegrationsSubject> subjectFactory,
      IntegrationsViewPermission viewPermission)
      : base(subjectFactory)
  {
    _viewPermission = viewPermission;
  }

  /// <inheritdoc/>
  public override string PermissionName => "link_clients";

  /// <inheritdoc/>
  public override string DisplayName => "Link Clients";

  /// <inheritdoc/>
  public override string Description => "Link provider clients to tenants";

  /// <inheritdoc/>
  public override PermissionCategory Category => PermissionCategory.CoreCapability;

  /// <inheritdoc/>
  public override IEnumerable<IPermissionMetadata> Dependencies => new IPermissionMetadata[] { _viewPermission };

  public override bool IsMspOnly => true;
}
