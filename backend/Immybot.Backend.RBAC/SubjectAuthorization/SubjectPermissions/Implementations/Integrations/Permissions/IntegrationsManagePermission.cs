using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Integrations.Permissions;

/// <summary>
/// Permission to manage integrations (create, edit, and delete).
/// </summary>
public class IntegrationsManagePermission : PermissionMetadata, IIntegrationsManagePermission
{
  private readonly IntegrationsViewPermission _viewPermission;

  /// <summary>
  /// Initializes a new instance of the <see cref="IntegrationsManagePermission"/> class.
  /// </summary>
  /// <param name="subjectFactory">Factory to retrieve the integrations subject.</param>
  /// <param name="viewPermission">The view permission this permission depends on.</param>
  public IntegrationsManagePermission(
      SubjectFactory<IntegrationsSubject> subjectFactory,
      IntegrationsViewPermission viewPermission)
      : base(subjectFactory)
  {
    _viewPermission = viewPermission;
  }

  /// <inheritdoc/>
  public override string PermissionName => "manage";

  /// <inheritdoc/>
  public override string DisplayName => "Manage Integrations";

  /// <inheritdoc/>
  public override string Description => "Create, edit, and delete provider integrations";

  /// <inheritdoc/>
  public override PermissionCategory Category => PermissionCategory.CoreCapability;

  /// <inheritdoc/>
  public override IEnumerable<IPermissionMetadata> Dependencies => new IPermissionMetadata[] { _viewPermission };
}
