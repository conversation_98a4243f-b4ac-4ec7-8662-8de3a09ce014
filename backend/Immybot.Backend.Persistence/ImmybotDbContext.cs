using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Text.Json;
using Immybot.Backend.Domain;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.Persistence.Shared;
using JetBrains.Annotations;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;
using Newtonsoft.Json;
using Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.Mapping;
using System.Reflection.Emit;
using Microsoft.AspNetCore.Identity;

namespace Immybot.Backend.Persistence;

#nullable enable
// This is a hack to fix the issue with NpgsqlJsonTypeMapping not being applied to JsonElement? properties
// Which causes the following error:
// System.InvalidOperationException: No coercion operator is defined between types 'System.IO.MemoryStream' and 'System.Nullable`1[System.Text.Json.JsonElement]'
//
// See also ImmybotDbContext.ConfigureConventions below
// We should remove these after this issue is resolved: https://github.com/npgsql/efcore.pg/issues/2977
public class FixNpgsqlJsonElementHackConvention : IPropertyAddedConvention
{
  private NpgsqlJsonTypeMapping? _jsonTypeMapping;

  public void ProcessPropertyAdded(IConventionPropertyBuilder propertyBuilder, IConventionContext<IConventionPropertyBuilder> context)
  {
    var property = propertyBuilder.Metadata;

    if (property.ClrType == typeof(JsonElement?) && property.GetColumnType() is null)
    {
      _jsonTypeMapping ??= new NpgsqlJsonTypeMapping("jsonb", typeof(JsonElement?));
      property.SetTypeMapping(_jsonTypeMapping);
    }
  }
}
#nullable restore

// RBAC notes
// - The User class extends IdentityUser<int>
// - IdentityDbContext<User, ...> replaces DbContext to extend ImmybotDbcontext with Identity tables
public class ImmybotDbContext : IdentityDbContext<
    User,                   // Custom User class
    Role,                   // Custom Role class
    int,                    // Key type for user
    UserClaim,              // Custom UserClaim class
    UserRole,               // Custom UserRole class
    IdentityUserLogin<int>, // TUserLogin - using base implementation (not in use)
    RoleClaim,              // Custom RoleClaim class
    IdentityUserToken<int>  // TUserToken - using base implementation (not in use)
  >, IUserIdBearingContext, IAuditLoggableContext
{
  protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
  {
    if (Database.IsNpgsql())
    {
      // See FixNpgsqlJsonElementHackConvention above for why this is needed
      configurationBuilder.Conventions.Add(_ => new FixNpgsqlJsonElementHackConvention());
    }
  }

  public int? UserId { get; private set; }
  public string UserDisplayName { get; private set; }
  public string UserAzureId { get; private set; }

  public ImmybotDbContext() { }
  public ImmybotDbContext(DbContextOptions<ImmybotDbContext> options) : base(options) { }

  public void SetUser(
    int? userId = null,
    string userDisplayName = null,
    string userAzureId = null)
  {
    UserId = userId;
    UserDisplayName = userDisplayName;
    UserAzureId = userAzureId;
  }

  public void SetUser([CanBeNull] AuthUserDto user)
  {
    UserId = user?.Id;
    UserDisplayName = string.IsNullOrWhiteSpace(user?.DisplayName)
      ? (user?.Email ?? user?.Id.ToString())
      : user.DisplayName;

    if (user?.ImpersonatorUser is { } impersonatorUser)
    {
      var impersonatorUserDisplayName = string.IsNullOrWhiteSpace(impersonatorUser.DisplayName)
        ? (impersonatorUser.Email ?? impersonatorUser.Id.ToString())
        : impersonatorUser.DisplayName;
      UserDisplayName = $"{UserDisplayName} (Impersonated by #{impersonatorUser.Id} {impersonatorUserDisplayName})";
    }

    UserAzureId = user?.PrincipalId;
  }
  protected override void OnModelCreating(ModelBuilder builder)
  {
    builder.ApplyConfigurationsFromAssembly(typeof(ImmybotDbContext).Assembly);
    // The sqlite db provider cannot natively JsonDocument as a type
    // so we need a custom value converter to handle it
    // postgres provider handles it natively
    if (Database.IsSqlite())
    {
      var jsonSerializerOptions = new JsonSerializerOptions
      {
        AllowTrailingCommas = true,
        ReadCommentHandling = JsonCommentHandling.Disallow
      };

      JsonSerializerOptions jsonSerializer = null;
      builder.Entity<ComputerInventoryTaskScriptResult>()
        .Property(a => a.LatestSuccessResult)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => JsonDocument.Parse(a, new JsonDocumentOptions() { AllowTrailingCommas = true, CommentHandling = JsonCommentHandling.Disallow }));
      builder.Entity<ComputerInventoryTaskScriptResult>()
        .Property(a => a.LatestErrorResult)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => JsonDocument.Parse(a, new JsonDocumentOptions() { AllowTrailingCommas = true, CommentHandling = JsonCommentHandling.Disallow }));
      builder.Entity<HistoricalComputerInventoryTaskScriptResult>()
        .Property(a => a.InventoryTaskResult)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => JsonDocument.Parse(a, new JsonDocumentOptions() { AllowTrailingCommas = true, CommentHandling = JsonCommentHandling.Disallow }));
      builder.Entity<ProviderLink>()
        .Property(a => a.ProviderTypeFormData)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => System.Text.Json.JsonSerializer.Deserialize<JsonElement>(a, jsonSerializer));
      builder.Entity<ProviderLinkInternalData>()
        .Property(a => a.InternalData)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => System.Text.Json.JsonSerializer.Deserialize<JsonElement>(a, jsonSerializer));
      builder.Entity<ProviderClient>()
        .Property(a => a.InternalData)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => System.Text.Json.JsonSerializer.Deserialize<JsonElement>(a, jsonSerializer));
      builder.Entity<ProviderAgent>()
        .Property(a => a.InternalData)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => System.Text.Json.JsonSerializer.Deserialize<JsonElement>(a, jsonSerializer));
      builder.Entity<Notification>()
        .Property(a => a.InputData)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => System.Text.Json.JsonSerializer.Deserialize<JsonElement>(a, jsonSerializer));
      builder.Entity<Notification>()
        .Property(a => a.OutputData)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => System.Text.Json.JsonSerializer.Deserialize<JsonElement>(a, jsonSerializer));

      builder.Entity<TargetAssignment>()
        .Property(a => a.TaskParameterValues)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => System.Text.Json.JsonSerializer.Deserialize<ImmutableDictionary<string, DeploymentParameterValue>>(a, jsonSerializer),
          new ValueComparer<ImmutableDictionary<string, DeploymentParameterValue>>(
            (c1, c2) => c1.Equals(c2),
            c => c.GetHashCode(),

            // Since this is an ImmutableDictionary, we can just use the reference as the snapshot
            c => c));

      // sqlite db cannot handle List like pg can, so just convert it to string in tests
      builder.Entity<ComputerListViewModel>()
        .Property(a => a.ProviderLinkIds)
        .HasConversion(
          a => JsonConvert.SerializeObject(a),
          a => JsonConvert.DeserializeObject<List<int>>(a));
      builder.Entity<ComputerListViewModel>()
        .Property(a => a.ComputerTagIds)
        .HasConversion(
          a => JsonConvert.SerializeObject(a),
          a => JsonConvert.DeserializeObject<List<int>>(a));
      builder.Entity<ComputerListViewModel>().Property(c => c.ChassisTypes)
        .HasConversion(
          a => JsonConvert.SerializeObject(a),
          a => JsonConvert.DeserializeObject<List<int>>(a));
      builder.Entity<Computer>().Property(c => c.ChassisTypes)
        .HasConversion(
          a => JsonConvert.SerializeObject(a),
          a => JsonConvert.DeserializeObject<List<int>>(a));

      builder.Entity<ChangeRequest>()
        .Property(a => a.NewValuesJson)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => System.Text.Json.JsonSerializer.Deserialize<JsonElement>(a, jsonSerializer));

      builder.Entity<DynamicIntegrationType>()
        .Property(a => a.Secrets)
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => System.Text.Json.JsonSerializer.Deserialize<JsonElement>(a, jsonSerializer));

      builder.Entity<AzureErrorLogItem>()
        .Property(a => a.AzureError)
        .HasColumnType("text")
        .HasConversion(
          a => System.Text.Json.JsonSerializer.Serialize(a, jsonSerializerOptions),
          a => System.Text.Json.JsonSerializer.Deserialize<AzureError>(a, jsonSerializer));
    }
    else
    {
      builder.Entity<LocalSoftware>().Property(a => a.RelativeCacheSourcePath).HasDefaultValueSql("uuid_generate_v4()");
      builder.Entity<ApplicationPreferences>().Property(a => a.ImmyScriptPath).HasDefaultValueSql("REPLACE(uuid_generate_v4()::text,'-','')");

    }
    builder.Entity<LocalSoftware>().Ignore(a => a.MaintenanceSpecifier);

    // RBAC
    // Ignore the tables we don't want to create as part of the Identity framework setup
    builder.Ignore<IdentityUserLogin<int>>();
    builder.Ignore<IdentityUserToken<int>>();
  }

  public DbSet<ApplicationPreferences> ApplicationPreferences { get; set; }
  public DbSet<Audit> Audits { get; set; }
  public DbSet<AzureErrorLogItem> AzureErrors { get; set; }
  public DbSet<AzureTenant> AzureTenants { get; set; }
  public DbSet<Branding> Brandings { get; set; }
  public DbSet<Computer> Computers { get; set; }
  public DbSet<ComputerNote> ComputerNotes { get; set; }
  public DbSet<ComputerInventoryTaskScriptResult> ComputerInventoryTaskScriptResults { get; set; }
  public DbSet<ComputerLatestProviderEvent> ComputerLatestProviderEvents { get; set; }
  public DbSet<DetectedComputerSoftware> DetectedComputerSoftware { get; set; }
  public DbSet<ComputerPerson> ComputerPersons { get; set; }
  public DbSet<FeatureUsage> FeatureUsages { get; set; }
  public DbSet<HistoricalComputerInventoryTaskScriptResult> HistoricalComputerInventoryTaskScriptResults { get; set; }
  public DbSet<InventoryTask> InventoryTasks { get; set; }
  public DbSet<InventoryTaskScript> InventoryTaskScripts { get; set; }
  public DbSet<License> Licenses { get; set; }
  public DbSet<MaintenanceAction> MaintenanceActions { get; set; }
  public DbSet<MaintenanceActionDependency> MaintenanceActionDependencies { get; set; }
  public DbSet<MaintenanceSession> MaintenanceSessions { get; set; }
  public DbSet<MaintenanceSessionStage> MaintenanceSessionStages { get; set; }
  public DbSet<MaintenanceTask> MaintenanceTasks { get; set; }
  public DbSet<MaintenanceTaskParameter> MaintenanceTaskParameters { get; set; }
  public DbSet<MaintenanceTaskParameterValue> MaintenanceTaskParameterValues { get; set; }
  public DbSet<ImmyMigration> ImmyMigrations { get; set; }
  public DbSet<Oauth2AccessToken> OauthAccessTokens { get; set; }
  public DbSet<Person> Persons { get; set; }
  public DbSet<ProviderClient> ProviderClients { get; set; }
  public DbSet<ProviderAgent> ProviderAgents { get; set; }
  public DbSet<ProviderAuditLog> ProviderAuditLogs { get; set; }
  public DbSet<AgentIdentificationFailure> AgentIdentificationFailures { get; set; }
  public DbSet<AgentIdentificationLog> AgentIdentificationLogs { get; set; }
  public DbSet<ProviderLink> ProviderLinks { get; set; }
  public DbSet<ProviderLinkInternalData> ProviderLinkInternalData { get; set; }
  public DbSet<Schedule> Schedules { get; set; }
  public DbSet<ScheduledEmail> ScheduledEmails { get; set; }
  public DbSet<Script> Scripts { get; set; }
  public DbSet<SessionLog> SessionLogs { get; set; }
  public DbSet<SessionPhase> SessionPhases { get; set; }
  public DbSet<SmtpConfig> SmtpConfigs { get; set; }
  public DbSet<LocalSoftware> Software { get; set; }
  public DbSet<LocalSoftwareVersion> SoftwareVersions { get; set; }
  public DbSet<TargetAssignment> TargetAssignments { get; set; }
  public DbSet<TargetAssignmentNotes> TargetAssignmentNotes { get; set; }
  public DbSet<TargetAssignmentVisibility> TargetAssignmentVisibilities { get; set; }
  public DbSet<Tenant> Tenants { get; set; }
  public DbSet<TenantDeletion> TenantDeletions { get; set; }
  public DbSet<TenantMaintenanceTask> TenantMaintenanceTasks { get; set; }
  public DbSet<TenantPreferences> TenantPreferences { get; set; }
  public DbSet<TenantScript> TenantScripts { get; set; }
  public DbSet<TenantMedia> TenantMedia { get; set; }
  public DbSet<TenantSearch> TenantSearches { get; set; }
  public DbSet<TenantSoftware> TenantSoftware { get; set; }

  public DbSet<UserAffinity> UserAffinities { get; set; }
  public DbSet<UserPreferences> UserPreferences { get; set; }
  public DbSet<Media> Media { get; set; }
  public DbSet<RecommendedTargetAssignmentApproval> RecommendedTargetAssignmentApprovals { get; set; }
  public DbSet<OptionalTargetAssignmentApproval> OptionalTargetAssignmentApprovals { get; set; }
  public DbSet<RemoteControlRecording> RemoteControlRecordings { get; set; }
  public DbSet<MaintenanceItemOrder> MaintenanceItemOrders { get; set; }
  public DbSet<AccessRequest> AccessRequests { get; set; }
  public DbSet<TimelineEvent> TimelineEvents { get; set; }
  public DbSet<ImmyAgentInstaller> ImmyAgentInstallers { get; set; }
  public DbSet<Tag> Tags { get; set; }
  public DbSet<ComputerTag> ComputerTags { get; set; }
  public DbSet<TenantTag> TenantTags { get; set; }
  public DbSet<PersonTag> PersonTags { get; set; }
  public DbSet<TenantTagAuthorization> TenantTagAuthorizations { get; set; }
  public DbSet<DynamicIntegrationType> DynamicIntegrationTypes { get; set; }

  /// <summary>
  /// A lookup of software display names that have been matched
  /// to a <see cref="GlobalSoftware"/> record.
  /// </summary>
  public DbSet<GlobalSoftwareMatch> GlobalSoftwareMatches { get; set; }

  // Views
  public DbSet<ComputerListViewModel> ComputerListViewModels { get; set; }
  public DbSet<ComputerListViewModelCount> ComputerListViewModelCount { get; set; }
  public DbSet<ComputerInventorySoftware> ComputerInventorySoftware { get; set; }
  public DbSet<ComputerInventoryAllSoftware> ComputerInventoryAllSoftware { get; set; }
  public DbSet<ComputerInventoryKeyViewModel> ComputerInventoryKeys { get; set; }
  public DbSet<ActiveSession> ActiveSessions { get; set; }

  public DbSet<Notification> Notifications { get; set; }
  public DbSet<UserSilencedNotification> UserSilencedNotifications { get; set; }

  public DbSet<ChangeRequest> ChangeRequests { get; set; }
  public DbSet<ChangeRequestComment> ChangeRequestComments { get; set; }
  public DbSet<MaintenanceActionActivity> MaintenanceActionActivities { get; set; }
  public DbSet<DisabledPreflightScript> DisabledPreflightScripts { get; set; }

  public DbSet<UserImpersonation> UserImpersonations { get; set; }
  public DbSet<GettingStartedStep> GettingStartedSteps { get; set; }

  // RBAC and Identity tables
  public override DbSet<User> Users { get; set; }
  public override DbSet<Role> Roles { get; set; }
  public DbSet<RoleType> RoleTypes { get; set; }
  public override DbSet<UserRole> UserRoles { get; set; }
  public override DbSet<UserClaim> UserClaims { get; set; }
  public override DbSet<RoleClaim> RoleClaims { get; set; }

  public bool NeedsInitialization()
  {
    return !Tenants.Any() || !Users.Any() || !ApplicationPreferences.Any() || !Brandings.Any();
  }

  /// <summary>
  /// If we have no recommended approvals or target assignments, then we need to initialize the approvals
  /// </summary>
  /// <returns></returns>
  public bool NeedsGlobalAssignmentApprovalInitialization() => !RecommendedTargetAssignmentApprovals.Any() && !TargetAssignments.Any();

  public override ValueTask DisposeAsync()
  {
    UserId = null;
    UserAzureId = null;
    UserDisplayName = string.Empty;
    return base.DisposeAsync();
  }
}
