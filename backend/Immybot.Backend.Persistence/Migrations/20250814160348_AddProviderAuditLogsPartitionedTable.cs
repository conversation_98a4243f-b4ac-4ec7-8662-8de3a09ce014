using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Immybot.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddProviderAuditLogsPartitionedTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "provider_audit_log_retention_months",
                table: "application_preferences",
                type: "integer",
                nullable: false,
                defaultValue: 1);

            // Create partitioned table using raw SQL (EF Core doesn't support PARTITION BY syntax)
            migrationBuilder.Sql(@"
                CREATE TABLE provider_audit_logs (
                    id uuid NOT NULL,
                    provider_link_id integer NOT NULL,
                    provider_link_name character varying(500) NOT NULL,
                    method_name character varying(200) NOT NULL,
                    error_message character varying(4000),
                    input jsonb,
                    output jsonb,
                    correlation_id uuid,
                    time_utc timestamp without time zone NOT NULL
                ) PARTITION BY RANGE (time_utc);
            ");

            // Create initial partition for current month
            var currentDate = DateTime.UtcNow;
            var partitionStart = new DateTime(currentDate.Year, currentDate.Month, 1, 0, 0, 0, DateTimeKind.Utc);
            var partitionEnd = partitionStart.AddMonths(1);

            migrationBuilder.Sql($@"
                CREATE TABLE provider_audit_logs_{partitionStart:yyyy}_{partitionStart:MM}
                PARTITION OF provider_audit_logs
                FOR VALUES FROM ('{partitionStart:yyyy-MM-dd}') TO ('{partitionEnd:yyyy-MM-dd}');
            ");

            // Create per-partition indexes for the initial current-month partition
            migrationBuilder.Sql($@"
                CREATE INDEX IF NOT EXISTS ix_provider_audit_logs_time_utc__{partitionStart:yyyy}_{partitionStart:MM} ON provider_audit_logs_{partitionStart:yyyy}_{partitionStart:MM} (time_utc);
                CREATE INDEX IF NOT EXISTS ix_provider_audit_logs_provider_link_id_time_utc__{partitionStart:yyyy}_{partitionStart:MM} ON provider_audit_logs_{partitionStart:yyyy}_{partitionStart:MM} (provider_link_id, time_utc);
                CREATE INDEX IF NOT EXISTS ix_provider_audit_logs_correlation_id__{partitionStart:yyyy}_{partitionStart:MM} ON provider_audit_logs_{partitionStart:yyyy}_{partitionStart:MM} (correlation_id) WHERE correlation_id IS NOT NULL;
            ");


            // Create indexes on partitioned table
            migrationBuilder.Sql(@"
                CREATE INDEX ix_provider_audit_logs_time_utc ON provider_audit_logs (time_utc);
                CREATE INDEX ix_provider_audit_logs_provider_link_id_time_utc ON provider_audit_logs (provider_link_id, time_utc);
                CREATE INDEX ix_provider_audit_logs_correlation_id ON provider_audit_logs (correlation_id) WHERE correlation_id IS NOT NULL;
            ");

            // Attach initial partition indexes to the parent partitioned indexes
            migrationBuilder.Sql($@"
                ALTER INDEX ix_provider_audit_logs_time_utc ATTACH PARTITION ix_provider_audit_logs_time_utc__{partitionStart:yyyy}_{partitionStart:MM};
                ALTER INDEX ix_provider_audit_logs_provider_link_id_time_utc ATTACH PARTITION ix_provider_audit_logs_provider_link_id_time_utc__{partitionStart:yyyy}_{partitionStart:MM};
                ALTER INDEX ix_provider_audit_logs_correlation_id ATTACH PARTITION ix_provider_audit_logs_correlation_id__{partitionStart:yyyy}_{partitionStart:MM};
            ");

            // Create function for automatic partition creation
            migrationBuilder.Sql(@"
                CREATE OR REPLACE FUNCTION create_provider_audit_log_partition(partition_date date)
                RETURNS void AS $$
                DECLARE
                    partition_name text;
                    start_date date;
                    end_date date;
                BEGIN
                    start_date := date_trunc('month', partition_date);
                    end_date := start_date + interval '1 month';
                    partition_name := 'provider_audit_logs_' || to_char(start_date, 'YYYY') || '_' || to_char(start_date, 'MM');

                    -- Check if partition already exists
                    IF to_regclass('public.' || partition_name) IS NULL THEN
                        EXECUTE format('CREATE TABLE %I PARTITION OF provider_audit_logs FOR VALUES FROM (%L) TO (%L)',
                            partition_name, start_date, end_date);

                        -- Create per-partition indexes and attach them to parent partitioned indexes
                        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (time_utc)', 'ix_provider_audit_logs_time_utc__' || partition_name, partition_name);
                        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (provider_link_id, time_utc)', 'ix_provider_audit_logs_provider_link_id_time_utc__' || partition_name, partition_name);
                        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (correlation_id) WHERE correlation_id IS NOT NULL', 'ix_provider_audit_logs_correlation_id__' || partition_name, partition_name);

                        EXECUTE format('ALTER INDEX %I ATTACH PARTITION %I', 'ix_provider_audit_logs_time_utc', 'ix_provider_audit_logs_time_utc__' || partition_name);
                        EXECUTE format('ALTER INDEX %I ATTACH PARTITION %I', 'ix_provider_audit_logs_provider_link_id_time_utc', 'ix_provider_audit_logs_provider_link_id_time_utc__' || partition_name);
                        EXECUTE format('ALTER INDEX %I ATTACH PARTITION %I', 'ix_provider_audit_logs_correlation_id', 'ix_provider_audit_logs_correlation_id__' || partition_name);
                    END IF;
                END;
                $$ LANGUAGE plpgsql;
            ");

            // Create function for partition cleanup
            migrationBuilder.Sql(@"
                CREATE OR REPLACE FUNCTION cleanup_provider_audit_log_partitions(cutoff_date date)
                RETURNS void AS $$
                DECLARE
                    partition_record record;
                BEGIN

                    FOR partition_record IN
                        SELECT schemaname, tablename
                        FROM pg_tables
                        WHERE tablename LIKE 'provider_audit_logs_%'
                        AND schemaname = 'public'
                    LOOP
                        -- Extract date from partition name (format: provider_audit_logs_YYYY_MM')
                        DECLARE
                            year_part text;
                            month_part text;
                            partition_date date;
                        BEGIN
                            year_part := split_part(partition_record.tablename, '_', 4);
                            month_part := split_part(partition_record.tablename, '_', 5);

                            IF year_part IS NOT NULL AND month_part IS NOT NULL THEN
                                partition_date := (year_part || '-' || month_part || '-01')::date;

                                IF partition_date < cutoff_date THEN
                                    EXECUTE format('DROP TABLE %I.%I', partition_record.schemaname, partition_record.tablename);
                                    RAISE NOTICE 'Dropped partition: %', partition_record.tablename;
                                END IF;
                            END IF;
                        EXCEPTION
                            WHEN OTHERS THEN
                                RAISE WARNING 'Error processing partition %: %', partition_record.tablename, SQLERRM;
                        END;
                    END LOOP;
                END;
                $$ LANGUAGE plpgsql;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop all partitions and the main table
            migrationBuilder.Sql(@"
                DROP TABLE IF EXISTS provider_audit_logs CASCADE;
            ");

            // Drop partition management functions
            migrationBuilder.Sql(@"
                DROP FUNCTION IF EXISTS create_provider_audit_log_partition(date);
                DROP FUNCTION IF EXISTS cleanup_provider_audit_log_partitions(date);
            ");

            migrationBuilder.DropColumn(
                name: "provider_audit_log_retention_months",
                table: "application_preferences");
        }
    }
}
