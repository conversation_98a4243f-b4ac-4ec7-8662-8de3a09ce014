using Immybot.Backend.Domain.Models.Preferences;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Immybot.Backend.Persistence.Configurations;

public class ApplicationPreferencesConfiguration : IEntityTypeConfiguration<ApplicationPreferences>
{
  public void Configure(EntityTypeBuilder<ApplicationPreferences> builder)
  {
    builder
      .Property(a => a.DefaultEmailBccList)
      .HasConversion(a => JsonConvert.SerializeObject(a), a => JsonConvert.DeserializeObject<DefaultEmailBccList>(a));
    builder.HasOne(a => a.DefaultBranding);
    builder
      .OwnsOne(a => a.DefaultScriptTimeouts, defaultScriptTimeouts =>
      {
        defaultScriptTimeouts.Property(d => d.Install).HasDefaultValue(1800);
        defaultScriptTimeouts.Property(d => d.Action).HasDefaultValue(1800);
        defaultScriptTimeouts.Property(d => d.Uninstall).HasDefaultValue(1800);
        defaultScriptTimeouts.Property(d => d.Upgrade).HasDefaultValue(1800);
        defaultScriptTimeouts.Property(d => d.Detection).HasDefaultValue(120);
        #pragma warning disable CS0612 // Type or member is obsolete
#pragma warning disable CS0618 // Type or member is obsolete
        defaultScriptTimeouts.Property(d => d.AutoUpdateJob).HasDefaultValue(21600); // 6 hours
#pragma warning restore CS0618 // Type or member is obsolete
#pragma warning restore CS0612 // Type or member is obsolete
        defaultScriptTimeouts.Property(d => d.DynamicVersions).HasDefaultValue(120);
      });

    builder.Property(a => a.StaleComputersLastAgentConnectionAgeDays).HasDefaultValue(30);
    builder.Property(a => a.DaysToWaitBeforeAutomaticImmyBotUpdate).HasDefaultValue(7);
    builder.Property(a => a.EnableBetaDynamicIntegrationMigrations).HasDefaultValue(false);
    builder.Property(a => a.EnableMaintenanceActionActivities).HasDefaultValue(true);
    builder.Property(a => a.EnableProviderAuditLogging).HasDefaultValue(true);
    builder.Property(a => a.ProviderAuditLogRetentionDays).HasDefaultValue(90);
  }
}
