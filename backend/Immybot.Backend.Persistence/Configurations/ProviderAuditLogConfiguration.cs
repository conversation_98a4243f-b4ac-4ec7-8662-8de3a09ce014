using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ProviderAuditLogConfiguration : IEntityTypeConfiguration<ProviderAuditLog>
{
  public void Configure(EntityTypeBuilder<ProviderAuditLog> builder)
  {
    builder.HasKey(p => p.Id);

    builder.Property(p => p.Id)
      .HasColumnName("id")
      .IsRequired();

    builder.Property(p => p.ProviderLinkId)
      .HasColumnName("provider_link_id")
      .IsRequired();

    builder.Property(p => p.ProviderLinkName)
      .HasColumnName("provider_link_name")
      .HasMaxLength(500)
      .IsRequired();

    builder.Property(p => p.MethodName)
      .HasColumnName("method_name")
      .HasMaxLength(200)
      .IsRequired();

    builder.Property(p => p.ErrorMessage)
      .HasColumnName("error_message")
      .HasMaxLength(4000);

    builder.Property(p => p.TimeUtc)
      .HasColumnName("time_utc")
      .IsRequired()
      .HasConversion(
        t => t,
        t => DateTime.SpecifyKind(t, DateTimeKind.Utc));

    builder.Property(p => p.CorrelationId)
      .HasColumnName("correlation_id");

    // Configure JSONB columns for PostgreSQL
    builder.Property(p => p.Input)
      .HasColumnName("input")
      .HasColumnType("jsonb");

    builder.Property(p => p.Output)
      .HasColumnName("output")
      .HasColumnType("jsonb");

    // Configure table name (partitioned table)
    builder.ToTable("provider_audit_logs");

    // Primary index on time_utc for partition pruning
    builder.HasIndex(p => p.TimeUtc)
      .HasDatabaseName("ix_provider_audit_logs_time_utc");

    // Composite index for provider-specific queries
    builder.HasIndex(p => new { p.ProviderLinkId, p.TimeUtc })
      .HasDatabaseName("ix_provider_audit_logs_provider_link_id_time_utc");

    // Filtered index on correlation_id (only when not null)
    builder.HasIndex(p => p.CorrelationId)
      .HasDatabaseName("ix_provider_audit_logs_correlation_id")
      .HasFilter("correlation_id IS NOT NULL");
  }
}
