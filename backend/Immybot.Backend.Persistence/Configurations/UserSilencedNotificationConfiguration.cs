using System;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

internal class UserSilencedNotificationConfiguration : IEntityTypeConfiguration<UserSilencedNotification>
{
  public void Configure(EntityTypeBuilder<UserSilencedNotification> builder)
  {
    builder.<PERSON><PERSON>ey(x => x.Id);

    builder.Property(a => a.NotificationObjectId).IsRequired((false));

    builder.HasIndex(x => new { x.UserId, x.NotificationType, x.NotificationObjectId }).IsUnique();

    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder
      .HasOne(a => a.User)
      .WithMany(a => a.SilencedNotifications)
      .OnDelete(DeleteBehavior.Cascade);
  }
}
