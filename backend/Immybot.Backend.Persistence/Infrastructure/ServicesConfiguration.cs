using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence.Shared;
using Immybot.Shared.Abstractions.Device;
using Immybot.Shared.Extensions;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Npgsql;

namespace Immybot.Backend.Persistence.Infrastructure;

public static class ServicesConfiguration
{
  public static void AddPersistenceServices(
    this IHostApplicationBuilder builder)
  {
    var services = builder.Services;
    services.AddPooledDbContextFactory<ImmybotDbContext>((sp, dbOptionsBuilder) =>
    {
      var databaseOptions = sp.GetRequiredService<IOptions<DatabaseOptions>>().Value;
      if (databaseOptions.UseSqlite)
      {
        var softwareDbConnection = new SqliteConnection("Data Source=:memory:");
        softwareDbConnection.Open();
        dbOptionsBuilder.UseSqlite(softwareDbConnection);
      }
      else
      {
        var connectionString = databaseOptions.LocalPgDbConnectionStringV2;
        var dataSourceBuilder = new NpgsqlDataSourceBuilder(connectionString);
        dataSourceBuilder.EnableDynamicJson();
        var dataSource = dataSourceBuilder.Build();
        dbOptionsBuilder
          .UseNpgsql(
            dataSource,
            npgsqlOptionsAction: (sqlOptions) =>
            {
              sqlOptions.CommandTimeout(360);
              sqlOptions.MaxBatchSize(100);
              sqlOptions.EnableRetryOnFailure();
              sqlOptions.SetPostgresVersion(12, 0);
            });
      }
      dbOptionsBuilder
        .UseSnakeCaseNamingConvention()
        .EnableSensitiveDataLogging()
        .AddInterceptors(
          // Audit log table interceptor automatically audits all IAuditableLoggableEntity models
          // and updates thecreatedBy/updatedBy/etc. properties
          new AuditableEntitySaveChangesInterceptor(new SystemTime(),
            updateAuditableUserProperties: true),
          new SoftDeleteInterceptor(),
          new TelemetryDbInterceptor());
      });

    services.AddTransient(sp => sp.GetRequiredService<IDbContextFactory<ImmybotDbContext>>().CreateDbContext());

    services.AddSingleton<Func<ImmybotDbContext>>(serviceProvider => () =>
      serviceProvider.GetRequiredService<IDbContextFactory<ImmybotDbContext>>().CreateDbContext());

    if (builder.Environment.IsAspire())
    {
      builder.EnrichNpgsqlDbContext<ImmybotDbContext>(settings =>
      {
        // Healthchecks are conflicting with each other.  Disable for now.
        settings.DisableHealthChecks = true;
      });
    }

    // This is janky, but seems to work. Since our db context is registered in the service
    // collection as transient, the only way to get a db context from the collection and have it
    // automatically have the current user set (for auditing purposes) is to add a scoped delegate
    // service that gets the db context and the IUserService and wires them up
    services.AddScoped<UserBearingDbFactory<ImmybotDbContext>>(serviceProvider =>
    {
      var userService = serviceProvider.GetRequiredService<IUserService>();
      return () =>
      {
        var ctx = serviceProvider.GetRequiredService<IDbContextFactory<ImmybotDbContext>>().CreateDbContext();
        if (userService.TryGetCurrentUser(out var user)) ctx.SetUser(user);
        else if (userService.GetCurrentPrincipal(strict: false) is { } principal)
          ctx.SetUser(userDisplayName: principal.GetDisplayName(),
            userAzureId: principal.GetObjectId());
        return ctx;
      };
    });
  }
}
