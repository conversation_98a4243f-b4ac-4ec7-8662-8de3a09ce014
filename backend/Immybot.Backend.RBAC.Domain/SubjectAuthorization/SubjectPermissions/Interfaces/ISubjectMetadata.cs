namespace Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;

/// <summary>
/// Defines the contract for subject definitions in the RBAC system.
/// A subject represents a resource or area of functionality that can have permissions.
/// </summary>
public interface ISubjectMetadata
{
  /// <summary>
  /// Gets the unique identifier for this subject definition.
  /// </summary>
  string Id { get; }

  /// <summary>
  /// Gets the unique name of the subject (e.g., "deployments", "users").
  /// </summary>
  string Name { get; }

  /// <summary>
  /// Gets the human-readable display name of the subject (e.g., "Deployments", "Users").
  /// </summary>
  string DisplayName { get; }

  /// <summary>
  /// Gets the description of what the subject represents.
  /// </summary>
  string Description { get; }

  /// <summary>
  /// Gets the sort order for display purposes.
  /// </summary>
  int SortOrder { get; }

  /// <summary>
  /// Gets all permissions available for this subject.
  /// </summary>
  IEnumerable<IPermissionMetadata> Permissions { get; }

  /// <summary>
  /// Indicates whether this subject is only available to users belonging to an MSP tenant.
  /// </summary>
  bool IsMspOnly { get; }
  bool IsResourceBased { get; }

  /// <summary>
  /// Indicates whether this subject is managed by the system and cannot be modified by users.
  /// Examples: Manager and Global subjects are system-managed
  /// </summary>
  bool IsSystemManaged { get; }
}
